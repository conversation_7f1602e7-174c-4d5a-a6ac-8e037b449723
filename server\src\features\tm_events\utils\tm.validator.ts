// This module provides validation for TM event query parameters

import { TmEventQueryParams } from "../types/tm.types";
import ApiError from "@/utils/ApiError";

export const validateQueryParams = (queryParams: TmEventQueryParams): void => {
    const { page, size } = queryParams;
    if(page && isNaN(Number(page))) {
        throw new ApiError(400, 'Invalid page number, must be a number');
    }
    if(size && isNaN(Number(size))) {
        throw new ApiError(400, 'Invalid size number, must be a number');
    }
};
