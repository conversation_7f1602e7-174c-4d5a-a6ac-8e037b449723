"use client";

import React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const priceIndicatorVariants = cva(
  "relative flex flex-col items-center p-6 rounded-xl border bg-white shadow-lg transition-all duration-300", 
  {
    variants: {
      variant: {
        low: "border-green-200 shadow-green-100/50",
        medium: "border-yellow-200 shadow-yellow-100/50",
        high: "border-orange-200 shadow-orange-100/50",
        veryHigh: "border-red-200 shadow-red-100/50",
      },
    },
    defaultVariants: {
      variant: "medium",
    },
  }
);

const statusTextVariants = cva(
  "text-sm font-semibold ", 
  {
    variants: {
      variant: {
        low: "text-green-600",
        medium: "text-yellow-600",
        high: "text-orange-600",
        veryHigh: "text-red-600",
      },
    }
  }
);

const progressBarVariants = cva(
  "absolute bottom-0 left-0 h-2 transition-all duration-500 ease-out", 
  {
    variants: {
      variant: {
        low: "bg-green-500",
        medium: "bg-yellow-500",
        high: "bg-orange-500",
        veryHigh: "bg-red-500",
      },
    }
  }
);

const messages = {
  low: "Competitive Price",
  medium: "Market Average",
  high: "Above Average",
  veryHigh: "Premium Price",
};

interface PriceIndicatorProps extends VariantProps<typeof priceIndicatorVariants> {}

export const PriceIndicator: React.FC<PriceIndicatorProps> = ({ variant }) => {
  return (
    <div className="">
      <div className={cn(priceIndicatorVariants({ variant }))}>
        {/* Price Status */}
        <div className="flex items-center gap-2">
          <div className={cn(
            "w-2 h-2 rounded-full",
            variant === 'low' && "bg-green-500",
            variant === 'medium' && "bg-yellow-500",
            variant === 'high' && "bg-orange-500",
            variant === 'veryHigh' && "bg-red-500"
          )} />
          <span className={cn(statusTextVariants({ variant }))}>
            {variant && messages[variant as keyof typeof messages]}
          </span>
        </div>

        {/* Visual Scale Indicators */}
        <div className="flex justify-between w-full ">
          <div className="w- h-0 rounded-full bg-gray-200" />
          <div className="w-1 h-0 rounded-full bg-gray-200" />
          <div className="w-1 h-0 rounded-full bg-gray-200" />
          <div className="w-1 h-0 rounded-full bg-gray-200" />
        </div>

        {/* Progress Bar */}
        <div className={cn(
          progressBarVariants({ variant }),
          "rounded-b-xl"
        )} 
        style={{
          width: variant === 'low' ? '25%' : 
                variant === 'medium' ? '50%' : 
                variant === 'high' ? '75%' : '100%'
        }} />
      </div>
    </div>
  );
};
