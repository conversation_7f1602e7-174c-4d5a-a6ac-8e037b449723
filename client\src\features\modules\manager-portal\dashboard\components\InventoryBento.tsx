"use client";
import { Package } from "lucide-react";
import { BentoBox } from "../../../shared/widgets/BentoBox";
import { Progress } from "@/components/ui/progress";

export const InventoryBento = () => {
  // Dummy inventory data
  const inventory = [
    { event: "Summer Music Festival", total: 1000, sold: 750 },
    { event: "Sports Championship", total: 500, sold: 200 },
    { event: "Tech Conference", total: 300, sold: 280 },
  ];

  return (
    <BentoBox
      title="Ticket Inventory"
      className="col-span-1 row-span-1"
      header={<Package className="h-5 w-5 text-primary" />}
    >
      <div className="space-y-4">
        {inventory.map((item, index) => (
          <div key={index} className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="font-medium">{item.event}</span>
              <span className="text-muted-foreground">
                {item.sold}/{item.total}
              </span>
            </div>
            <Progress value={(item.sold / item.total) * 100} />
          </div>
        ))}
      </div>
    </BentoBox>
  );
};
