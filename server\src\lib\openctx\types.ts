// Define types for OpenCTX requests and responses.
// These types provide compile-time checks for data structure

import {  z } from 'zod';
import { openCTXSchema, singleValueFilterSchema, multiValueFilterSchema, sortSchema, paginationSchema  } from './schemas';


// Define types based on Zod schemas
export type OpenCTXRequest = z.infer<typeof openCTXSchema>;
export type SingleValueFilter = z.infer<typeof singleValueFilterSchema>;
export type MultiValueFilter = z.infer<typeof multiValueFilterSchema>;
export type SortOptions = z.infer<typeof sortSchema>;
export type PaginationOptions = z.infer<typeof paginationSchema>;

// exporting the openCTXSchema from types to fix the error
export { openCTXSchema }

// Define a generic type for providers (used for database and TM API)
export type Provider<T> = {
   fetch: (query: OpenCTXRequest) => Promise<T>;
};

// Define type for OpenCTX validator response
export type ValidationResult =
| { valid: true, data: OpenCTXRequest }
| { valid: false, error: string };

export type OpenCTXSearchRequest = OpenCTXRequest;

// Define interface for Context
// This interface represents the structure of a context object
export interface Context {
  type: string;
  text: string;
  metadata: {
    source: string;
    id?: string;
    name?: string;
    venue?: string;
    date?: string;
    image?: string;
    city?: string;
    // Add any other necessary metadata fields
    [key: string]: any;
  };
}

