import React, { useState, useEffect, useCallback } from 'react';
import { debounce } from 'lodash';
import { But<PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Calendar as CalendarIcon, Search } from 'lucide-react';
import { format } from 'date-fns';
import { setEventFilter, resetEventFilters } from '@/state/index';
import { useAppDispatch, useAppSelector } from '@/app/redux';

interface FilterBarProps {
  onFilterChange?: (filters: any) => void;
}

export const FilterBar: React.FC<FilterBarProps> = ({ onFilterChange }) => {
  const dispatch = useAppDispatch();
  
  // Get filters from Redux state
  const reduxFilters = useAppSelector((state) => state.global.eventFilters);
  
  // Initialize local state ONCE - not dependent on reduxFilters initially
  const [localFilters, setLocalFilters] = useState({
    segment: 'all',
    city: '',
    startDate: undefined as Date | undefined,
    keyword: ''
  });

  // Segments available for filtering
  const segments = ['Sports', 'Music', 'Arts', 'Theater'];

  // IMPORTANT: Memoize this to prevent recreation on each render
  const debouncedFilterChange = useCallback(
    debounce((filters) => {
      if (onFilterChange) onFilterChange(filters);
    }, 500),
    [onFilterChange]
  );

  // Memoize the handle change function
  const handleChange = useCallback((key: string, value: any) => {
    setLocalFilters(prev => {
      const newFilters = { ...prev, [key]: value };
      
      // Format value for Redux
      const formattedValue = key === 'startDate' && value 
        ? value.toISOString().split('T')[0] 
        : value;
      
      // Update Redux
      dispatch(setEventFilter({ key, value: formattedValue }));
      
      // Prepare for API
      const apiFilters = {...newFilters};
      if (newFilters.startDate) {
        apiFilters.startDate = newFilters.startDate;
      }
      
      // Notify parent (debounced)
      debouncedFilterChange(apiFilters);
      
      return newFilters;
    });
  }, [dispatch, debouncedFilterChange]);

  // CRITICAL FIX: Use a ref to track if this is the first render
  const isFirstRender = React.useRef(true);
  
  // Sync with Redux state when it changes (but not on first render)
  useEffect(() => {
    // Skip the first render to prevent initial update loop
    if (isFirstRender.current) {
      isFirstRender.current = false;
      
      // On first render, initialize local state from Redux if available
      if (reduxFilters) {
        setLocalFilters({
          segment: reduxFilters.segment || 'all',
          city: reduxFilters.city || '',
          startDate: reduxFilters.startDate ? new Date(reduxFilters.startDate) : undefined,
          keyword: reduxFilters.keyword || ''
        });
      }
      return;
    }
    
    // After first render, only update if Redux state changes differently than local state
    // and isn't caused by this component's own updates
    const reduxSegment = reduxFilters?.segment || 'all';
    const reduxCity = reduxFilters?.city || '';
    const reduxStartDate = reduxFilters?.startDate || null;
    const reduxKeyword = reduxFilters?.keyword || '';
    
    const localStartDateStr = localFilters.startDate 
      ? localFilters.startDate.toISOString().split('T')[0] 
      : null;
    
    // Only update local state if Redux state is different
    if (
      reduxSegment !== localFilters.segment ||
      reduxCity !== localFilters.city ||
      reduxStartDate !== localStartDateStr ||
      reduxKeyword !== localFilters.keyword
    ) {
      setLocalFilters({
        segment: reduxSegment,
        city: reduxCity,
        startDate: reduxStartDate ? new Date(reduxStartDate) : undefined,
        keyword: reduxKeyword
      });
    }
  }, [reduxFilters]); // Keep Redux filters as the only dependency

  // Rest of the component stays the same...
  return (
    <div className="flex flex-wrap gap-2 items-center">
      {/* Segment filter */}
      <Select 
        value={localFilters.segment} 
        onValueChange={(value) => handleChange('segment', value)}
      >
        <SelectTrigger className="w-[140px]">
          <SelectValue placeholder="Category" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Categories</SelectItem>
          {segments.map(segment => (
            <SelectItem key={segment} value={segment.toLowerCase()}>
              {segment}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* City search */}
      <div className="relative">
        <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          placeholder="City"
          className="pl-8 w-[140px]"
          value={localFilters.city}
          onChange={(e) => handleChange('city', e.target.value)}
        />
      </div>

      {/* Date picker */}
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" className="w-[130px]">
            <CalendarIcon className="mr-2 h-4 w-4" />
            {localFilters.startDate ? format(localFilters.startDate, 'PP') : 'From Date'}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0">
          <Calendar
            mode="single"
            selected={localFilters.startDate}
            onSelect={(date) => handleChange('startDate', date)}
            initialFocus
          />
        </PopoverContent>
      </Popover>

      {/* Reset filters button */}
      <Button 
        variant="ghost" 
        size="sm"
        onClick={() => {
          // Clear Redux first
          dispatch(resetEventFilters());
          
          // Then update local state
          setLocalFilters({
            segment: 'all',
            city: '',
            startDate: undefined,
            keyword: ''
          });
          
          // Notify parent if needed
          if (onFilterChange) {
            onFilterChange({
              segment: 'all',
              city: '',
              startDate: null,
              keyword: ''
            });
          }
        }}
      >
        Reset
      </Button>
    </div>
  );
};
