// Handles validation and processing of incoming Stripe webhooks.
import dotenv from 'dotenv'; // <-- Add import
dotenv.config(); // <-- Add this line VERY EARLY, ideally right after imports

import <PERSON><PERSON> from "stripe";
import { CheckoutSessionStatus, Prisma, MembershipTier } from "@prisma/client"; // Correct Prisma imports
import ApiError from "@/utils/ApiError";
import { prisma } from "@/lib/prisma";
import { StripeBaseService } from "./stripe-base.service";
import { StripeWebhookValidationResult } from "../types/stripe-payments.types";
// Keep InventoryItem interface here if only used by webhook handlers
interface InventoryItem { id: string; quantity: number; /* ... other fields ... */ }

export class StripeWebhookService {

    /**
     * Validates the raw webhook payload and signature.
     */
    static validateWebhookEvent(payload: string | Buffer, signature: string): StripeWebhookValidationResult {
        // console.log('➡️🔑 [WebhookSvc] Entering validateWebhookEvent...'); // Log entry
        const stripe = StripeBaseService.getStripeClient(); // Get client via base service
        try {
            // console.log('➡️🔑 [WebhookSvc] Checking for STRIPE_WEBHOOK_SECRET...'); // Log secret check
            const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
            if (!webhookSecret) {
                // console.error("❌ [WebhookSvc] STRIPE_WEBHOOK_SECRET not configured."); // Log if missing
                return { valid: false, error: "Webhook secret not configured on server." };
            }
            // console.log(`➡️🔑 [WebhookSvc] Webhook Secret found. Attempting constructEvent...`); // Log before construction
            const event = stripe.webhooks.constructEvent(payload, signature, webhookSecret);
            // console.log(`✅ [WebhookSvc] Webhook signature validated successfully. Event ID: ${event.id}, Type: ${event.type}`); // Log success
            return { valid: true, event };
        } catch (error) {
            if (error instanceof Stripe.errors.StripeSignatureVerificationError) {
                // console.error("❌ [WebhookSvc] Webhook signature verification failed:", error.message); // Log signature error
                return { valid: false, error: `Webhook signatureverification failed: ${error.message}` };
            }
            // console.error("❌ [WebhookSvc] Unexpected error during webhook construction:", error); // Log other errors
            return { valid: false, error: `Webhook construction error: ${error instanceof Error ? error.message : "Unknown"}` };
        }
    }

    /**
     * Main webhook event dispatcher.
     */
    static async handleWebhookEvent(event: Stripe.Event): Promise<{ success: boolean }> {
        // console.log(`➡️⚡ [WebhookSvc] Entering handleWebhookEvent for Type: ${event.type} [ID: ${event.id}]`); // Log entry
        try {
            let result = { success: false }; // Default result
            switch (event.type) {
                case 'payment_intent.succeeded':
                    // console.log(`➡️⚡ [WebhookSvc] Routing to handlePaymentIntentSucceeded for ${event.id}`); // Log routing
                    await this.handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
                    result = { success: true }; // Assume success if handler doesn't throw
                    break;
                case 'payment_intent.payment_failed':
                    // console.log(`➡️⚡[WebhookSvc] Routing to handlePaymentIntentFailed for ${event.id}`); // Log routing
                    await this.handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent);
                    result = { success: true }; // Assume success if handler doesn't throw
                    break;
                case 'checkout.session.completed':
                    // console.log(`➡️⚡ [WebhookSvc] Routing to handleCheckoutSessionCompleted for ${event.id}`); // Log routing
                    await this.handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session);
                    result = { success: true }; // Assume success if handler doesn't throw
                    break;
                // Add other handlers as needed
                default:
                    // console.log(`⏩ [WebhookSvc] Unhandled webhook event type: ${event.type} [ID: ${event.id}]`); // Log unhandled
                    result = { success: true }; // Still return success for unhandled types as per Stripe best practice
            }
            // console.log(`✅ [WebhookSvc] Finished processing event ${event.id} (Type: ${event.type}). Outcome: ${result.success}`); // Log outcome
            return result;
        } catch (error) {
            // console.error(`❌ [WebhookSvc] Error processing webhook event ${event.id} (Type: ${event.type}):`, error); // Log processing error
            return { success: false }; // Indicate processing failed
        }
    }

    // --- Private Handlers for Specific Events ---

    private static async handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent) {
        const { userId, checkoutSessionId } = paymentIntent.metadata || {};
        // console.log(`➡️💰 [WebhookSvc|PI Succeeded] Processing PI: ${paymentIntent.id}, User: ${userId}, Session: ${checkoutSessionId}`); // Log handler entry

        if (!userId || !checkoutSessionId) {
            // console.warn(`⚠️ [WebhookSvc|PI Succeeded] Missing userId or checkoutSessionId in metadata for PI: ${paymentIntent.id}. Skipping.`);
            return; // Exit early if essential metadata is missing
        }

        try {
            // console.log(`➡️💾 [WebhookSvc|PI Succeeded] Starting transaction for PI: ${paymentIntent.id}`);
            await prisma.$transaction(async (tx) => {
                 // console.log(`➡️💾 [WebhookSvc|PI Succeeded] Finding CheckoutSession: ${checkoutSessionId}`);
                 const session = await tx.checkoutSession.findUnique({ where: { id: checkoutSessionId }, select: { status: true, items: true, eventId: true }});
                 if (!session || session.status === CheckoutSessionStatus.COMPLETED || session.status === CheckoutSessionStatus.FAILED) {
                     // console.warn(`⏩ [WebhookSvc|PI Succeeded] Session ${checkoutSessionId} not found or already processed (${session?.status}). Skipping transaction.`);
                     // Throwing an error here would roll back, maybe just return is better to acknowledge webhook but not process duplicate
                     return; // Acknowledge webhook, but don't process duplicate/invalid state
                 }
                 // console.log(`➡️💾 [WebhookSvc|PI Succeeded] Found session, status: ${session.status}. Finding ManagerEvent: ${session.eventId}`);
                 const managerEvent = await tx.managerEvent.findUnique({ where: { id: session.eventId }, select: { inventory: true }});
                 if (!managerEvent) {
                      // console.error(`❌ [WebhookSvc|PI Succeeded] ManagerEvent ${session.eventId} not found.`);
                      throw new ApiError(404, `Event ${session.eventId} not found during webhook processing.`);
                 }
                 // console.log(`➡️💾 [WebhookSvc|PI Succeeded] Found event. Processing inventory update...`);

                 // Inventory update logic (keep existing logs/errors)
                 let updatedInventory: InventoryItem[];
                 try {
                     const currentInventory = managerEvent.inventory as unknown as InventoryItem[];
                     const purchasedItems = session.items as any[]; // Define this type based on session items
                     if (!Array.isArray(currentInventory) || !Array.isArray(purchasedItems)) throw new Error('Invalid inventory/items format.');
                     const inventoryMap = new Map(currentInventory.map(item => [item.id, { ...item }])); // Clone items
                     purchasedItems.forEach(pItem => {
                         const invItem = inventoryMap.get(pItem.inventoryId);
                         if (invItem) { invItem.quantity = Math.max(0, invItem.quantity - pItem.quantity); }
                         else { /* console.warn(`⚠️ [WebhookSvc|PI Succeeded] Inventory item ${pItem.inventoryId} not found during update.`); */ } // Commenting out warning too
                     });
                     updatedInventory = Array.from(inventoryMap.values());
                     // console.log(`➡️💾 [WebhookSvc|PI Succeeded] Inventory calculation complete.`);
                 } catch (parseError) {
                      // console.error(`❌ [WebhookSvc|PI Succeeded] Failed to process inventory:`, parseError);
                      throw new ApiError(500, "Failed to process inventory during webhook.");
                 }

                 // Prepare DB Updates
                 // console.log(`➡️💾 [WebhookSvc|PI Succeeded] Preparing DB updates...`);
                 const checkoutUpdatePromise = tx.checkoutSession.update({ where: { id: checkoutSessionId }, data: { status: CheckoutSessionStatus.COMPLETED, completedAt: new Date(), paymentStatus: 'succeeded', receiptUrl: typeof paymentIntent.latest_charge === 'string' ? undefined : paymentIntent.latest_charge?.receipt_url}});
                 const eventUpdatePromise = tx.managerEvent.update({ where: { id: session.eventId }, data: { inventory: updatedInventory as any }}); // Cast needed

                 let paymentMethodDetails = "";
                 if (paymentIntent.payment_method && typeof paymentIntent.payment_method ==='string') {
                     try {
                          // console.log(`➡️💾 [WebhookSvc|PI Succeeded] Retrieving PaymentMethod details: ${paymentIntent.payment_method}`);
                          const pm = await StripeBaseService.getStripeClient().paymentMethods.retrieve(paymentIntent.payment_method); // Use base client
                          if (pm.card) paymentMethodDetails = `${pm.card.brand} **** ${pm.card.last4}`;
                          // console.log(`➡️💾 [WebhookSvc|PI Succeeded] PaymentMethod details obtained: ${paymentMethodDetails || 'N/A'}`);
                     } catch (pmError) { /* console.warn("⚠️ [WebhookSvc|PI Succeeded] Could not retrieve PM details:", pmError); */ } // Commenting out warning too
                 }

                 // console.log(`➡️💾 [WebhookSvc|PI Succeeded] Creating PaymentRecord for PI: ${paymentIntent.id}`);
                 const paymentRecordCreatePromise = tx.paymentRecord.create({ data: { userId, checkoutSessionId, processor: 'STRIPE', processorPaymentId: paymentIntent.id, status: 'SUCCEEDED', amount: paymentIntent.amount, currency: paymentIntent.currency.toUpperCase(), paymentMethodDetails, processedAt: new Date(paymentIntent.created * 1000), description: paymentIntent.description || 'Payment successful', metadata: paymentIntent.metadata as any }});

                 // console.log(`➡️💾 [WebhookSvc|PI Succeeded] Executing transaction promises...`);
                 // Await all promises within the transaction
                 await Promise.all([checkoutUpdatePromise, eventUpdatePromise, paymentRecordCreatePromise]);
                 // console.log(`✅💾 [WebhookSvc|PI Succeeded] Transaction promises executed.`);
            }); // End of $transaction

            // console.log(`✅ [WebhookSvc|PI Succeeded] Transaction completed successfully for PI ${paymentIntent.id}`);

        } catch (error) {
             // Catch errors specifically from the transaction block
             // console.error(`❌ [WebhookSvc|PI Succeeded] Transaction failed for PI ${paymentIntent.id}:`, error);
             // Re-throw the error so handleWebhookEvent catches it and returns success: false
             throw error;
        }
    }

    private static async handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent) {
        const { userId, checkoutSessionId } = paymentIntent.metadata || {};
        // console.log(`➡️❌ [WebhookSvc|PI Failed] Processing PI: ${paymentIntent.id}, Reason: ${paymentIntent.last_payment_error?.message}`); // Log handler entry
        if (!userId) {
             // console.warn(`⚠️ [WebhookSvc|PI Failed] Missing userId in metadata for PI: ${paymentIntent.id}. Skipping DB updates.`);
             return; // Can't create record without user
        }

        try {
             // console.log(`➡️💾 [WebhookSvc|PI Failed] Starting transaction for PI: ${paymentIntent.id}`);
             await prisma.$transaction(async (tx) => {
                  if (checkoutSessionId) {
                      // console.log(`➡️💾 [WebhookSvc|PI Failed] Checking CheckoutSession: ${checkoutSessionId}`);
                      const session = await tx.checkoutSession.findUnique({ where: { id: checkoutSessionId }, select: { status: true }});
                      if (session && session.status !== CheckoutSessionStatus.COMPLETED && session.status !== CheckoutSessionStatus.FAILED) {
                          // console.log(`➡️💾 [WebhookSvc|PI Failed] Updating CheckoutSession ${checkoutSessionId} to FAILED.`);
                          await tx.checkoutSession.update({ where: { id: checkoutSessionId }, data: { status: CheckoutSessionStatus.FAILED, paymentStatus: 'failed', statusMessage: paymentIntent.last_payment_error?.message || 'Failed' }});
                      } else {
                         // console.log(`⏩ [WebhookSvc|PI Failed] Session ${checkoutSessionId} not found or already failed/completed. Skipping update.`);
                      }
                  } else {
                     // console.log(`⏩ [WebhookSvc|PI Failed] No checkoutSessionId found in metadata. Skipping session update.`);
                  }

                  // console.log(`➡️💾 [WebhookSvc|PI Failed] Checking if FAILED PaymentRecord already exists for PI: ${paymentIntent.id}`);
                  const existingRecord = await tx.paymentRecord.findFirst({ where: { processor: 'STRIPE', processorPaymentId: paymentIntent.id }});
                  if (!existingRecord) {
                      // console.log(`➡️💾 [WebhookSvc|PI Failed] Creating FAILED PaymentRecord for PI: ${paymentIntent.id}`);
                      await tx.paymentRecord.create({ data: { userId, checkoutSessionId: checkoutSessionId || null, processor: 'STRIPE', processorPaymentId: paymentIntent.id, status: 'FAILED', amount: paymentIntent.amount, currency: paymentIntent.currency.toUpperCase(), processedAt: new Date(paymentIntent.created * 1000), description: paymentIntent.last_payment_error?.message || 'Payment failed', metadata: paymentIntent.metadata as any }});
                      // console.log(`✅💾 [WebhookSvc|PI Failed] Created FAILED PaymentRecord.`);
                  } else { /* console.warn(`⏩ [WebhookSvc|PI Failed] FAILED PaymentRecord for PI ${paymentIntent.id} already exists. Skipping creation.`); */ } // Commenting out warning too
             }); // End transaction
              // console.log(`✅ [WebhookSvc|PI Failed] Transaction completed successfully for PI ${paymentIntent.id}`);
        } catch(error) {
             // console.error(`❌ [WebhookSvc|PI Failed] Transaction failed for PI ${paymentIntent.id}:`, error);
             throw error; // Let handler catch it
        }
    }

    private static async handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
        const { userId } = session.metadata || {};
        // console.log(`➡️📅 [WebhookSvc|Checkout Completed] Processing Session: ${session.id}, Mode: ${session.mode}, User: ${userId}`); // Log entry

        if (!userId) {
             // console.warn(`⚠️ [WebhookSvc|Checkout Completed] Missing userId in Checkout Session ${session.id}`);
             return;
        }

        // Only process if it's for a subscription
        if (session.mode === "subscription" && session.subscription) {
             const stripe = StripeBaseService.getStripeClient(); // Get client
             const subscriptionId = typeof session.subscription === 'string' ? session.subscription : session.subscription.id;
             // console.log(`📅 Processing subscription (WebhookSvc): ${subscriptionId}`);
             const subscription = await stripe.subscriptions.retrieve(subscriptionId);

             // Determine membership tier using Prisma Enum
             let membershipTier: MembershipTier = MembershipTier.STANDARD; // Use Prisma Enum
             if (subscription.items.data.length > 0) {
                 const priceId = subscription.items.data[0].price.id;
                 const priceNickname = subscription.items.data[0].price.nickname;
                 // Compare against environment variables and Prisma Enum values
                 if (priceId === process.env.STRIPE_VIP_PRICE_ID || priceNickname === 'VIP') {
                     membershipTier = MembershipTier.VIP;
                 } else if (priceId === process.env.STRIPE_MONTHLY_PRICE_ID || priceNickname === 'Monthly') {
                     membershipTier = MembershipTier.SUBSCRIBER;
                 }
             }

             // Update user using Prisma Enum
             await prisma.user.update({
                 where: { id: userId },
                 data: {
                    membershipTier: membershipTier, // Assign Prisma Enum value
                    stripeCustomerId: session.customer as string
                 }
             });
             // console.log(`✅ Updated user ${userId} membership to ${membershipTier}`);

             // Create initial payment record for the subscription
             if (subscription.latest_invoice) {
                 const invoice = typeof subscription.latest_invoice === 'string' ? await stripe.invoices.retrieve(subscription.latest_invoice) : subscription.latest_invoice;
                 if (invoice.status === 'paid') {
                      const paymentIntentId = typeof (invoice as any).payment_intent === 'string' ? (invoice as any).payment_intent : null;
                      // Check if payment record already exists by paymentIntentId or invoice.id
                      const existingRecord = await prisma.paymentRecord.findFirst({ where: { processor: 'STRIPE', OR: [{ processorPaymentId: paymentIntentId ?? invoice.id }]}});
                      if (!existingRecord) {
                           await prisma.paymentRecord.create({ data: { userId, processor: "STRIPE", processorPaymentId: paymentIntentId ?? invoice.id, status: "SUCCEEDED", amount: invoice.amount_paid, currency: invoice.currency.toUpperCase(), description: `Initial payment for ${subscription.items.data[0]?.price.nickname || "subscription"}`, processedAt: new Date(invoice.created * 1000), metadata: subscription.metadata as any }});
                           // console.log(`✅ Created payment record for initial subscription payment (WebhookSvc)`);
                      } else { /* console.warn("⏩ Subscription initial payment record already exists."); */ } // Commenting out warning too
                 } else { /* console.warn(`⚠️ Initial subscription invoice ${invoice.id} not paid.`); */ } // Commenting out warning too
             }
        } else {
             // console.log(`⏩ Checkout session ${session.id} completed, but mode is '${session.mode}', not 'subscription'. No subscription action taken.`);
        }
        // Catch errors at the handleWebhookEvent level
    }
}