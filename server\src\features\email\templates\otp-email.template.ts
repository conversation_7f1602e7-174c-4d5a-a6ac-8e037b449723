export const createOtpEmailTemplate = (otp: string, userName: string) => `
<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <title>Your Verification Code</title>
    <style>
      .container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        font-family: system-ui, -apple-system, sans-serif;
      }
      .code {
        font-size: 32px;
        font-weight: bold;
        letter-spacing: 8px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 6px;
        margin: 20px 0;
        text-align: center;
      }
      .expiry {
        color: #6c757d;
        font-size: 14px;
        text-align: center;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h2>Verify Your Email</h2>
      <p>Hi ${userName},</p>
      <p>Please use the following verification code to complete your email verification:</p>
      <div class="code">${otp}</div>
      <p class="expiry">This code will expire in 15 minutes.</p>
      <p>If you didn't request this code, you can safely ignore this email.</p>
    </div>
  </body>
</html>
`;