/**
 * Subscription Controller
 * 
 * Handles HTTP requests related to subscription management.
 */

import { Request, Response } from 'express';
import { asyncHandler } from '@/utils/asyncHandler';
import ApiError from '@/utils/ApiError';
import { SubscriptionService } from '../services/subscription.service';
import { SubscriptionPlanType, ChangeSubscriptionPlanRequest, CancelSubscriptionRequest } from '../types/subscription.types';

export class SubscriptionController {
  /**
   * Get subscription details for the current user
   * GET /api/v1/payments/subscriptions/status
   */
  static getSubscriptionStatus = asyncHandler(async (req: Request, res: Response) => {
    console.log('🌟 SubscriptionController: Hit getSubscriptionStatus handler');

    const userId = req.user?.userId;
    
    if (!userId) {
      throw ApiError.unauthorized('User must be authenticated');
    }
    
    const subscriptionStatus = await SubscriptionService.getUserSubscription(userId);
    
    return res.status(200).json({
      success: true,
      data: subscriptionStatus
    });
  });

  /**
   * Create a subscription checkout session
   * POST /api/v1/payments/subscriptions/create-checkout
   */
  static createSubscriptionCheckout = asyncHandler(async (req: Request, res: Response) => {
    console.log('🌟 SubscriptionController: Hit createSubscriptionCheckout handler');

    const userId = req.user?.userId;
    
    if (!userId) {
      throw ApiError.unauthorized('User must be authenticated');
    }
    
    const { planType, successUrl, cancelUrl } = req.body;
    
    if (!planType || !successUrl || !cancelUrl) {
      throw ApiError.badRequest('Plan type, success URL, and cancel URL are required');
    }
    
    // Validate plan type
    if (!Object.values(SubscriptionPlanType).includes(planType)) {
      throw ApiError.badRequest(`Invalid plan type: ${planType}`);
    }
    
    const result = await SubscriptionService.createSubscriptionCheckout(
      userId,
      planType,
      successUrl,
      cancelUrl
    );
    
    return res.status(200).json(result);
  });

  /**
   * Cancel a subscription
   * POST /api/v1/payments/subscriptions/cancel
   */
  static cancelSubscription = asyncHandler(async (req: Request, res: Response) => {
    console.log('🌟 SubscriptionController: Hit cancelSubscription handler');

    const userId = req.user?.userId;
    
    if (!userId) {
      throw ApiError.unauthorized('User must be authenticated');
    }
    
    const { immediately } = req.body as CancelSubscriptionRequest;
    
    const success = await SubscriptionService.cancelSubscription(userId, immediately);
    
    return res.status(200).json({
      success,
      message: success ? 'Subscription canceled successfully' : 'Failed to cancel subscription'
    });
  });
}