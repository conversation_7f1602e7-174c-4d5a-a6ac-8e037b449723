/**
 * Redis Cache Implementation for TicketMaster Events (Refactored)
 *
 * This module provides a caching layer using Redis for TicketMaster event data.
 * It now utilizes the centralized Redis client instance for connection management.
 * Functionality includes setting, getting, and invalidating cached event data,
 * with support for pagination and query-based caching.
 */

// Import the centralized Redis client instance
import { redisClient } from '@/config/redis'; // Adjust path as needed based on your setup (e.g., ../../../../config/redis)
import { TmEvent } from '../types/tm.types';
import { NODE_ENV } from '@/constants';

//---------------Cache Key Management Section-------------------
// No changes needed here - key generation logic remains the same
/**
 * Builds a standardized cache key from query parameters
 * Format: tm_events/[encoded_query]/page:[page_number]/size:[page_size]
 */
const buildCacheKey = (query: string, page: string, size: string): string => {
  // Transform query parameters into URL-friendly format
  const encodedQuery = query.split(':').filter(Boolean).join('/');
  return `tm_events/${encodedQuery}/page:${page}/size:${size}`;
};

//---------------Cache Operations Section-------------------
// Uses the imported redisClient
export class TmCache {
  /**
   * Stores event data in cache with expiration
   * @param expiryInSeconds - Cache TTL in seconds
   */
  static async set(
      query: string,
      page: string,
      size: string,
      data: TmEvent[], // Assuming TmEvent is the correct type from the DB/transformed response
      expiryInSeconds: number
    ): Promise<void> {
    // Check if the client is connected before attempting to use it
    if (!redisClient.isOpen) {
      console.error('❌ Redis client is not connected. Cannot SET cache.');
      // Optionally throw an error or handle gracefully
      return;
    }
    try {
      const cacheKey = buildCacheKey(query, page, size);
      // Use the imported redisClient instance
      await redisClient.set(cacheKey, JSON.stringify(data), {
        EX: expiryInSeconds,
      });
      if (NODE_ENV === 'development') {
        console.log(`⚡️ [TmCache] Cache SET for key: ${cacheKey}`);
      }
    } catch (error) {
      console.error('❌ [TmCache] Failed to SET cache:', error);
    }
  }

  /**
   * Retrieves cached event data
   * Returns null if cache miss or error
   */
  static async get(
      query: string,
      page: string,
      size: string
      ): Promise<TmEvent[] | null> { // Return type matching the data stored in set()
    // Check if the client is connected
    if (!redisClient.isOpen) {
      console.error('❌ Redis client is not connected. Cannot GET cache.');
      return null;
    }
    try {
      const cacheKey = buildCacheKey(query, page, size);
      // Use the imported redisClient instance
      const cachedData = await redisClient.get(cacheKey);

      if (cachedData) {
         if (NODE_ENV === 'development') {
          console.log(`✅ [TmCache] Cache HIT for key: ${cacheKey}`);
        }
        // Ensure the parsed data conforms to the expected type
        return JSON.parse(cachedData) as TmEvent[];
      }
      if (NODE_ENV === 'development') {
        console.log(`🟡 [TmCache] Cache MISS for key: ${cacheKey}`);
      }
      return null;
    } catch (error) {
      console.error('❌ [TmCache] Failed to GET cache:', error);
      return null;
    }
  }

  /**
   * Removes specific cache entry
   * Useful for clearing stale data
   */
  static async invalidate(query:string, page:string, size:string): Promise<void> {
    // Check if the client is connected
    if (!redisClient.isOpen) {
      console.error('❌ Redis client is not connected. Cannot INVALIDATE cache.');
      return;
    }
        try {
          const cacheKey = buildCacheKey(query,page, size);
          // Use the imported redisClient instance
           await redisClient.del(cacheKey);
           if(NODE_ENV === 'development'){
            console.log(`🗑️ [TmCache] Cache INVALIDATED for key: ${cacheKey}`);
           }
        } catch (error) {
            console.error('❌ [TmCache] Failed to INVALIDATE cache:', error);
        }
    }

  // NOTE: The static `quit` method has been removed.
  // Connection lifecycle is managed centrally in src/config/redis.ts.
  // Call `disconnectRedis` from your application's main shutdown sequence if needed.
}










// ! used before creating centralized redis client
// /**
//  * Redis Cache Implementation for TicketMaster Events
//  * 
//  * This module provides a caching layer using Redis for TicketMaster event data.
//  * It includes functionality for setting, getting, and invalidating cached event data,
//  * with support for pagination and query-based caching.
//  * 
//  * Key Features:
//  * - Configurable Redis connection with cloud support
//  * - Query-based cache key generation
//  * - Development mode logging
//  * - Automatic connection management
//  * - Error handling and graceful degradation
//  */

// import { createClient } from 'redis';
// import { TmEvent } from '../types/tm.types';
// import { NODE_ENV } from '@/constants';

// //---------------Redis Configuration Section-------------------
// // Redis connection parameters with fallback to local instance
// const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
// // Cloud-specific configuration parameters
// const redisPassword = process.env.REDIS_PASSWORD;
// const redisHost = process.env.REDIS_HOST;
// const redisPort = process.env.REDIS_PORT;

// // Initialize Redis client with cloud configuration if available
// const redisClient = createClient({
//   password: redisPassword,
//   socket: {
//     host: redisHost,
//     port: Number(redisPort)
//   }
// });

// //---------------Connection Management Section-------------------
// // Monitor Redis connection status for debugging and error handling
// redisClient.on('connect', () => {
//   if (NODE_ENV === 'development') {
//       console.log('Redis client connected');
//   }
// });

// redisClient.on('error', (err) => {
//   console.error('Redis client error:', err);
// });

// // Ensure single connection instance
// const connectRedis = async () => {
//   if (!redisClient.isOpen) {
//     await redisClient.connect();
//   }
// };

// connectRedis();

// //---------------Cache Key Management Section-------------------
// /**
//  * Builds a standardized cache key from query parameters
//  * Format: tm_events/[encoded_query]/page:[page_number]/size:[page_size]
//  */
// const buildCacheKey = (query: string, page: string, size: string): string => {
//   // Transform query parameters into URL-friendly format
//   const encodedQuery = query.split(':').filter(Boolean).join('/');
//   return `tm_events/${encodedQuery}/page:${page}/size:${size}`;
// };

// //---------------Cache Operations Section-------------------
// export class TmCache {
//   /**
//    * Stores event data in cache with expiration
//    * @param expiryInSeconds - Cache TTL in seconds
//    */
//   static async set(
//       query: string,
//       page: string,
//       size: string,
//       data: TmEvent[],
//       expiryInSeconds: number
//     ): Promise<void> {
//     try {
//         const cacheKey = buildCacheKey(query, page, size);
//       await redisClient.set(cacheKey, JSON.stringify(data), {
//         EX: expiryInSeconds,
//       });
//       if (NODE_ENV === 'development') {
//         console.log(`Cache set for key: ${cacheKey}`);
//       }
//     } catch (error) {
//       console.error('Failed to set cache:', error);
//     }
//   }

//   /**
//    * Retrieves cached event data
//    * Returns null if cache miss or error
//    */
//   static async get(
//       query: string,
//       page: string,
//       size: string
//       ): Promise<TmEvent[] | null> {
//     try {
//       const cacheKey = buildCacheKey(query, page, size);
//       const cachedData = await redisClient.get(cacheKey);

//       if (cachedData) {
//          if (NODE_ENV === 'development') {
//           console.log(`Cache hit for key: ${cacheKey}`);
//         }
//         return JSON.parse(cachedData) as TmEvent[];
//       }
//       return null;
//     } catch (error) {
//       console.error('Failed to get cache:', error);
//       return null;
//     }
//   }

//   /**
//    * Removes specific cache entry
//    * Useful for clearing stale data
//    */
//   static async invalidate(query:string, page:string, size:string): Promise<void> {
//         try {
//           const cacheKey = buildCacheKey(query,page, size);
//            await redisClient.del(cacheKey);
//            if(NODE_ENV === 'development'){
//             console.log(`Cache invalidated for key ${cacheKey}`);
//            }
//         } catch (error) {
//             console.error('Failed to invalidate cache:', error);
//         }
//     }

//   /**
//    * Gracefully closes Redis connection
//    * Should be called during application shutdown
//    */
//   static async quit(): Promise<void> {
//     try {
//       await redisClient.quit();
//       if (NODE_ENV === 'development') {
//         console.log('Redis client disconnected.');
//       }
//     } catch (error) {
//       console.error('Error disconnecting Redis client:', error);
//     }
//   }
// }
