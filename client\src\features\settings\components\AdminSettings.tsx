/**
 * The AdminSettings component is responsible for managing and configuring event retrieval settings in the application.
 * It includes the following functionality:
 * - Displaying a global search component for searching event details
 * - Showing selected event details after a search
 * - Rendering the PriorityEvents component for managing priority events
 * - Displaying DefaultSettings component
 * - Allowing users to add events to priority events list
 * - Fetching and managing priority events data
 */

"use client";

import { useEffect, useRef } from "react";
import { GlobalSearch } from "@/features/global-search/components/GlobalSearch";
import { Card, CardHeader, CardContent, CardTitle } from "@/components/ui/card";
import { DefaultSettings } from "./DefaultSettings";
import { useAppSelector, useAppDispatch } from "@/app/redux";
import { Context } from "@/types/openctx.types";
import {
  addToPriorityEvents,
  EventCategory,
  normalizeCategory,
  setPriorityEvents,
  setPriorityEventsError,
  setPriorityEventsLoading,
} from "@/state/priorityEventsSlice";
import { PriorityEvents } from "./PriorityEvents/PriorityEvents";
import {
  useCreatePriorityEventMutation,
  useGetPriorityEventsQuery,
} from "@/state/api";
import { EventDetailCard } from "@/components/shared/EventDetailCard";
import { debounce } from "@/utils/debounce"; // Import the debounce function
import {
  CreatePriorityEventPayload,
  PriorityEventData,
} from "./PriorityEvents/types/priority-events.types";

export function AdminSettings() {
  const selectedEvent = useAppSelector(
    (state) => state.global.selectedSearchEvent
  );
  const dispatch = useAppDispatch();
  const [createPriorityEvent] = useCreatePriorityEventMutation();
  const {
    data: priorityEvents,
    isLoading,
    isError,
    error,
  } = useGetPriorityEventsQuery();
  // Added useRef to store the latest priorityEvents
  const priorityEventsRef = useRef<any[]>([]);

  // Effect to handle priority events fetching and error handling
  useEffect(() => {
    dispatch(setPriorityEventsLoading());
    if (priorityEvents) {
      console.log("📥 Received initial priority events:", priorityEvents);
      console.log("Priority Events extracted: ", priorityEvents.data || []);

      const eventsData = priorityEvents.data || [];
      dispatch(setPriorityEvents({ events: eventsData }));
      priorityEventsRef.current = priorityEvents.data;
      console.log("✅ Initial events loaded into state");
      // TODO: Toast.success('Priority events loaded')
    }
    if (isError) {
      // Type guard for FetchBaseQueryError
      if ("status" in error) {
        dispatch(
          setPriorityEventsError(
            `Error ${error.status}: ${JSON.stringify(error.data)}`
          )
        );
      }
      // Type guard for SerializedError
      else if ("message" in error) {
        dispatch(
          setPriorityEventsError(error.message || "An unknown error occurred")
        );
      }
      // Fallback error message
      else {
        dispatch(setPriorityEventsError("An unknown error occurred"));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [priorityEvents, isError, error, dispatch]);

  interface Classification {
    primary: boolean;
    segment?: {
      name?: string;
    };
  }
  // Function to handle adding an event to priority events
  const handleAddToPriority = async (event: Context) => {
    console.log("Raw event data handleAddToPrioroty:", event);
    console.log("🎯 Starting single event creation process");

    if (!event) return;

    // Extract category based on event source
    let rawCategory = "";
    let normalizedCategory = "";

    if (event.metadata.source === "manager") {
      // For manager events
      rawCategory =
        event.metadata.category || event.metadata.rawEvent?.category || "";
      normalizedCategory = normalizeCategory(rawCategory) ?? "";
    } else {
      // For TM events
      rawCategory =
        event.metadata.rawEvent?.classifications
          ?.find((c: Classification) => c.primary)
          ?.segment?.name?.toLowerCase() || "";
      normalizedCategory = normalizeCategory(rawCategory) ?? "";
    }

    console.log("Extracted raw category:", rawCategory);
    console.log("Normalized category:", normalizedCategory);

    if (normalizedCategory) {
      // Get event date from appropriate source
      const eventDate =
        event.metadata.source === "ticketmaster" &&
        event.metadata.rawEvent?.dates?.start?.dateTime
          ? event.metadata.rawEvent.dates.start.dateTime
          : event.metadata.date || new Date().toISOString();

      // Get city from appropriate source
      const city =
        event.metadata.source === "ticketmaster" &&
        event.metadata.rawEvent?._embedded?.venues?.[0]?.city?.name
          ? event.metadata.rawEvent._embedded.venues[0].city.name
          : event.metadata.city || "Unknown";

      // Get country from appropriate source
      const country =
        event.metadata.source === "ticketmaster" &&
        event.metadata.rawEvent?._embedded?.venues?.[0]?.country?.name
          ? event.metadata.rawEvent._embedded.venues[0].country.name
          : event.metadata.country ||
            event.metadata.rawEvent?.country ||
            "Unknown";

      // 🆕 Extract seatmap URL from Ticketmaster events
      let seatmapUrl: string | null = null;
      if (event.metadata.source === "ticketmaster") {
        // Check for seatmap in the raw event data
        seatmapUrl = event.metadata.rawEvent?.seatmap?.staticUrl || null;

        // 🔍 DEBUG: Log seatmap extraction
        console.log("🗺️ [AdminSettings] Extracting seatmap for TM event:");
        console.log("- Event Name:", event.metadata.name);
        console.log("- Raw Event Seatmap:", event.metadata.rawEvent?.seatmap);
        console.log("- Extracted Seatmap URL:", seatmapUrl);
      }

      // Create backend payload with proper type checking
      const backendPayload: CreatePriorityEventPayload = {
        eventId: event.metadata.id as string, // Type assertion since we know it exists
        name: event.metadata.name || "Untitled Event", // Fallback value
        category: normalizedCategory.toUpperCase() as
          | "SPORTS"
          | "MUSIC"
          | "ARTS",
        source: event.metadata.source,
        date: eventDate,
        venue: event.metadata.venue || "TBD",
        city: city,
        country: country,
        image: event.metadata.image,
        seatmapUrl: seatmapUrl, // 🆕 ADD: Include seatmap URL
        rawEventData: event.metadata.rawEvent,
      };

      console.log("📦 Preparing event payload:", backendPayload);

      // Create the event data for Redux
      const eventData: PriorityEventData = {
        ...backendPayload,
        id: crypto.randomUUID(),
        batchId: null,
        isPopular: false,
        isActive: true,
        viewCount: 0,
        clickCount: 0,
        addedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      dispatch(
        addToPriorityEvents({
          category: normalizedCategory as EventCategory,
          event: eventData,
        })
      );

      console.log("Backend API payload:", backendPayload);
      debouncedCreatePriorityEvent(backendPayload);
    }
  };

  // Debounced API call function
  const debouncedCreatePriorityEvent = debounce(async (payload: any) => {
    try {
      const response = await createPriorityEvent(payload).unwrap();
      console.log("✅ Single event creation successful:", response);
      // TODO:   Toast.success('Event added successfully')
    } catch (error) {
      console.error(" ❌ Error creating priority event:", error);
      // Added error handling to revert to the previous state
      if (priorityEventsRef.current) {
        dispatch(setPriorityEvents({ events: priorityEventsRef.current }));
      }
    }
  }, 500);

  return (
    <div className="container mx-auto py-4 sm:py-6 px-2 sm:px-4">
      <div className="grid grid-cols-1 gap-4 sm:gap-6">
        {/* Priority Events - Shows first on mobile */}
        <div className="lg:hidden">
          <Card>
            <CardHeader className="py-4 sm:py-6">
              <CardTitle className="text-xl sm:text-2xl font-semibold">
                Priority Events
              </CardTitle>
            </CardHeader>
            <CardContent>
              <PriorityEvents />
            </CardContent>
          </Card>
        </div>

        {/* Desktop Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 sm:gap-6">
          {/* Left Column */}
          <div className="lg:col-span-8 space-y-4 sm:space-y-6">
            <Card>
              <CardHeader className="py-4 sm:py-6">
                <CardTitle className="text-xl sm:text-2xl font-semibold">
                  Event Search
                </CardTitle>
              </CardHeader>
              <CardContent className="overflow-x-auto">
                <GlobalSearch />
                {selectedEvent && (
                  <div className="mt-4">
                    <EventDetailCard
                      event={selectedEvent}
                      onAddToPriority={() => handleAddToPriority(selectedEvent)}
                    />
                  </div>
                )}
              </CardContent>
            </Card>
            <DefaultSettings />
          </div>

          {/* Right Column - Priority Events (Desktop Only) */}
          <div className="hidden lg:block lg:col-span-4">
            <Card>
              <CardHeader className="py-4 sm:py-6">
                <CardTitle className="text-xl sm:text-2xl font-semibold">
                  Priority Events
                </CardTitle>
              </CardHeader>
              <CardContent>
                <PriorityEvents />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
