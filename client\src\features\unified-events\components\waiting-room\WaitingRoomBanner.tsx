import React from 'react';
import { useAppDispatch, useAppSelector } from '@/app/redux';
import { acknowledgeQueue, setShowQueueBanner } from '@/state/queueSlice';
import { X } from 'lucide-react';
import { useRouter } from 'next/navigation';

/**
 * A global banner that appears when there are active queues
 * This can be shown in the layout to notify users about waiting rooms
 */
export const WaitingRoomBanner: React.FC = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const showBanner = useAppSelector(state => state.queue.showQueueBanner);
  
  // Get the first active event that needs attention
  const activeEventQueues = useAppSelector(state => state.queue.activeEventQueues);
  const eventIds = Object.keys(activeEventQueues).filter(id => 
    activeEventQueues[id].isActive && !activeEventQueues[id].isAdmitted
  );
  
  // Don't show anything if there are no active queues or banner is hidden
  if (!showBanner || eventIds.length === 0) {
    return null;
  }
  
  // Take the first event that needs attention
  const eventId = eventIds[0];
  
  const handleClose = () => {
    dispatch(setShowQueueBanner(false));
    dispatch(acknowledgeQueue(eventId));
  };
  
  const handleViewQueue = () => {
    router.push(`/events/${eventId}`);
    dispatch(acknowledgeQueue(eventId));
  };
  
  return (
    <div className="fixed top-0 left-0 right-0 bg-amber-500 text-white p-3 z-50 flex justify-between items-center">
      <div className="flex-1 text-center">
        <span className="font-medium">Event Waiting Room Active!</span>
        <span className="ml-2">Join the queue to secure your tickets.</span>
      </div>
      <div className="flex items-center gap-3">
        <button
          onClick={handleViewQueue}
          className="bg-white text-amber-600 px-3 py-1 rounded-md text-sm font-medium hover:bg-amber-50"
        >
          View Queue
        </button>
        <button
          onClick={handleClose}
          className="text-white hover:text-amber-100"
          aria-label="Close banner"
        >
          <X size={20} />
        </button>
      </div>
    </div>
  );
};
