/**
 * Queue Types Module
 * 
 * Defines types related to the event waiting room system, including
 * interfaces for queue operations, status responses, and DTOs.
 */

import { Prisma } from '@prisma/client'

// Define our own enum to match the schema (not relying on Prisma namespace)
export enum QueueUserStatus {
  WAITING = 'WAITING',
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  COMPLETED = 'COMPLETED'
}

// Use Prisma's type generator for Queue model
export type Queue = Prisma.QueueGetPayload<{
  include: {
    users: true
  }
}>

// Use Prisma's type generator for QueueUser model
export type QueueUser = Prisma.QueueUserGetPayload<{
  include: {
    user: true
  }
}>

// Basic queue info without including related entities
export type QueueBasic = Prisma.QueueGetPayload<{}>
export type QueueUserBasic = Prisma.QueueUserGetPayload<{}>

/**
 * Request to join a queue
 */
export interface JoinQueueRequest {
  eventId: string
  userId: string
}

/**
 * Response when checking queue status
 */
export interface QueueStatusResponse {
  eventId: string
  isActive: boolean
  queueId?: string  // Added this field at the top level
  queueState?: {
    totalWaiting: number
    estimatedWaitTime?: number; // in minutes, if calculable
  }
  userStatus?: {
    status: QueueUserStatus
    position?: number; // Only available for WAITING status
    enteredAt: string; // ISO date string
    admittedAt?: string; // ISO date string, present if status is ACTIVE
    expiresAt?: string; // ISO date string, when active status expires
    queueId?: string; // Added this field here too for user-specific queue entries
  }
}

/**
 * Response when joining a queue
 */
export interface JoinQueueResponse {
  success: boolean
  queueId: string
  userId: string
  status: QueueUserStatus
  position?: number
  estimatedWaitTime?: number; // in minutes
  message: string
}

/**
 * Demand score calculation result
 */
export interface DemandScoreResult {
  eventId: string
  score: number
  viewRate: number
  checkoutRate: number
  availableInventory: number
  timestamp: string; // ISO date string
  isHighDemand: boolean
}

/**
 * Queue service interface for abstraction
 */
export interface QueueServiceInterface {
  getQueueStatus(eventId: string, userId?: string): Promise<QueueStatusResponse>
  joinQueue(eventId: string, userId: string, priority: number): Promise<JoinQueueResponse>
  activateQueue(eventId: string): Promise<void>
  deactivateQueue(eventId: string): Promise<void>
  processNextBatch(eventId: string, batchSize?: number): Promise<number>
}