import React, { useState, useEffect } from "react";
import { useAppDispatch, useAppSelector } from "@/app/redux";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { useNavigation } from "../../hooks/useNavigation";
import { SidebarSection } from "./SidebarSection";
import { setIsSidebarCollapsed } from "@/state";
import { Button } from "@/components/ui/button";
import { X, Menu, ChevronLeft, ChevronRight } from "lucide-react";
import { OnboardingWidget } from "@/features/onboarding/components/OnboardingWidget";

export const SidebarPrimary = () => {
  const dispatch = useAppDispatch();
  const isSidebarCollapsed = useAppSelector(
    (state) => state.global.isSidebarCollapsed
  );
  const { navigationItems } = useNavigation();
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const toggleSidebar = () => {
    dispatch(setIsSidebarCollapsed(!isSidebarCollapsed));
  };
  // Logo component definition
  const Logo = ({ className }: { className?: string }) => (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M12 2L2 7L12 12L22 7L12 2Z"
        className="fill-primary stroke-primary"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2 17L12 22L22 17"
        className="fill-none stroke-primary"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2 12L12 17L22 12"
        className="fill-none stroke-primary"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );

  
  if (!isMobile) {
    return (
      <motion.aside
        initial={false}
        animate={{
          width: isSidebarCollapsed ? "64px" : "256px",
        }}
        className={cn(
          "fixed inset-y-0 left-0 z-50 flex flex-col h-full",
          "bg-background/80 backdrop-blur-[12px]",
          "shadow-lg transition-all duration-300 ease-in-out",
          "border-r border-border/40"
        )}
      >
        {/* Desktop View Header */}
        <div className="p-4 border-b bg-gradient-to-r from-background to-accent/10">
          <div className="flex items-center">
            {/* Logo - always visible, centered when collapsed */}
            <Logo
              className={cn(
                "h-8 w-8 transition-all duration-300",
                isSidebarCollapsed ? "mx-auto" : "mr-3"
              )}
            />

            {/* Text - only visible when expanded */}
            {!isSidebarCollapsed && (
              <h2 className="text-xl font-bold bg-gradient-to-r from-primary to-primary/50 bg-clip-text text-transparent">
                Fanseatmaster
              </h2>
            )}
          </div>
        </div>

        <nav className="flex-1 overflow-y-auto p-4">
          {navigationItems.map((section, index) => (
            <SidebarSection
              key={section.title || index}
              section={section}
              collapsed={isSidebarCollapsed}
            />
          ))}
        </nav>


        <div className="mt-auto p-4 border-t bg-gradient-to-r from-background to-accent/10 space-y-3">
          <OnboardingWidget collapsed={isSidebarCollapsed} />
          <Button
            className="w-full font-bold flex items-center justify-center gap-2"
            variant="ghost"
            onClick={toggleSidebar}
          >
            {isSidebarCollapsed ? (
              <ChevronRight className="h-5 w-5" />
            ) : (
              <>
                <ChevronLeft className="h-5 w-5" />
                <span>Collapse</span>
              </>
            )}
          </Button>
        </div>
      </motion.aside>
    );
  }

  // Mobile sidebar
  return (
    <>
      {!isSidebarCollapsed && (
        <div
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={toggleSidebar}
        />
      )}
      <motion.aside
        initial={{ x: -300 }}
        animate={{ x: isSidebarCollapsed ? -300 : 0 }}
        transition={{ type: "spring", stiffness: 200, damping: 20 }}
        className={cn(
          "fixed inset-y-0 left-0 z-50 flex flex-col h-full",
          "bg-background/80 backdrop-blur-[12px]",
          "shadow-lg w-[280px]",
          "border-r border-border/40"
        )}
      >
        {/* Mobile View Header */}
        <div className="flex items-center justify-between p-4 border-b bg-gradient-to-r from-background to-accent/10">
          <div className="flex items-center gap-3">
            <Logo className="h-8 w-8" />
            <h2 className="text-2xl md:text-xl font-bold bg-gradient-to-r from-primary to-primary/50 bg-clip-text text-transparent">
              Fanseatmaster
            </h2>
          </div>
          <Button variant="ghost" size="icon" onClick={toggleSidebar}>
            <X className="h-5 w-5" />
          </Button>
        </div>

        <nav className="flex-1 overflow-y-auto p-4">
          {navigationItems.map((section, index) => (
            <SidebarSection
              key={section.title || index}
              section={section}
              collapsed={false}
            />
          ))}
        </nav>

        <div className="mt-auto p-4 border-t bg-gradient-to-r from-background to-accent/10">
          <OnboardingWidget collapsed={false} />
        </div>
      </motion.aside>
    </>
  );
};
