// client/src/lib/ai/hooks/useSearch.ts
// Custom hook for handling search functionality using server-side search agent

import { useState, useCallback } from 'react';
import { OpenCTXRequest } from '@/lib/ai/types/tools.types';
import { SearchResult } from '@/features/global-search/types/search.types';
import { Context } from '@/types/openctx.types';

// Custom hook for handling search
export const useSearch = () => {
    // Updated state to use SearchResult[] type
    const [results, setResults] = useState<SearchResult[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const performSearch = useCallback(async (userQuery: string, openCTXFilters?: any) => {
        setIsLoading(true);
        setError(null);

        try {
            const response = await fetch(`/api/agents/search`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ userQuery, openCTXFilters })
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(`Search request failed: ${error?.message || response.statusText}`);
            }

            const data: OpenCTXRequest = await response.json();

            // Transform the OpenCTX response into SearchResult format
            const transformedResults = transformToSearchResults(data);
        console.log("useSearch.ts - transformedResults:", transformedResults);
            setResults(transformedResults);
        } catch (error: any) {
            console.error('Error in performSearch:', error);
            setError(error.message);
            setResults([]);
        } finally {
            setIsLoading(false);
        }
    }, []);

    return {
        results,
        isLoading,
        error,
        performSearch
    };
};

// Helper function to transform OpenCTX response to SearchResult format
// Helper function to transform OpenCTX response to SearchResult format
function transformToSearchResults(data: OpenCTXRequest): SearchResult[] {
    if (!data || !Array.isArray(data)) return [];

    // Group events by segment/category
    const groupedEvents = data.reduce((acc: Record<string, Context[]>, event: Context) => {
        const segment = event.metadata?.rawEvent?.classifications?.[0]?.segment?.name || 'Other';
        if (!acc[segment]) {
            acc[segment] = [];
        }
        acc[segment].push(event);
        return acc;
    }, {});

    // Explicitly type the events as Context[]
    return Object.entries(groupedEvents).map(([category, events]): SearchResult => ({
        category,
        events: events as Context[]
    }));
}
