import { Permission, UserRole, EventOperations, UserOperations, TicketOperations, AnalyticsOperations, SettingsOperations } from "./types";

// Define all granular event permissions
export const EVENT_PERMISSIONS = {
  CREATE: "events:create",
  READ: "events:read",
  UPDATE: "events:update",
  DELETE: "events:delete",
  MANAGE: "events:manage",
  PUBLISH: "events:publish",
  SETTINGS: "events:settings",
} as const;

// Define all granular user permissions
export const USER_PERMISSIONS = {
  CREATE: "users:create",
  READ: "users:read",
  UPDATE: "users:update",
  DELETE: "users:delete",
  MANAGE: "users:manage",
} as const;

// Define all granular ticket permissions
export const TICKET_PERMISSIONS = {
    CREATE: "tickets:create",
    READ: "tickets:read",
    UPDATE: "tickets:update",
    DELETE: "tickets:delete",
    MANAGE: "tickets:manage",
    BOOK: "tickets:book",
    CANCEL: "tickets:cancel"
} as const;


// Define all granular analytics permissions
export const ANALYTICS_PERMISSIONS = {
  READ: "analytics:read",
  EXPORT: "analytics:export",
  MANAGE: "analytics:manage",
} as const;

// Define all granular settings permissions
export const SETTINGS_PERMISSIONS = {
  READ: "settings:read",
  MANAGE: "settings:manage",
} as const;


// Define user role-based permission sets
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  ADMIN: [
    // Event permissions
    EVENT_PERMISSIONS.CREATE,
    EVENT_PERMISSIONS.READ,
    EVENT_PERMISSIONS.UPDATE,
    EVENT_PERMISSIONS.DELETE,
    EVENT_PERMISSIONS.MANAGE,
    EVENT_PERMISSIONS.PUBLISH,
    EVENT_PERMISSIONS.SETTINGS,
    // User permissions
    USER_PERMISSIONS.CREATE,
    USER_PERMISSIONS.READ,
    USER_PERMISSIONS.UPDATE,
    USER_PERMISSIONS.DELETE,
    USER_PERMISSIONS.MANAGE,
     // Ticket permissions
    TICKET_PERMISSIONS.CREATE,
    TICKET_PERMISSIONS.READ,
    TICKET_PERMISSIONS.UPDATE,
    TICKET_PERMISSIONS.DELETE,
    TICKET_PERMISSIONS.MANAGE,
    TICKET_PERMISSIONS.BOOK,
    TICKET_PERMISSIONS.CANCEL,
    // Analytics permissions
    ANALYTICS_PERMISSIONS.READ,
    ANALYTICS_PERMISSIONS.EXPORT,
    ANALYTICS_PERMISSIONS.MANAGE,
    // Settings permissions
    SETTINGS_PERMISSIONS.READ,
    SETTINGS_PERMISSIONS.MANAGE,
  ],
  MANAGER: [
    // Event permissions
    EVENT_PERMISSIONS.CREATE,
    EVENT_PERMISSIONS.READ,
    EVENT_PERMISSIONS.UPDATE,
    EVENT_PERMISSIONS.MANAGE,
    EVENT_PERMISSIONS.PUBLISH,
    // User permissions
    USER_PERMISSIONS.READ,
    USER_PERMISSIONS.UPDATE,
     // Ticket permissions
    TICKET_PERMISSIONS.READ,
    TICKET_PERMISSIONS.UPDATE,
     TICKET_PERMISSIONS.MANAGE,
     TICKET_PERMISSIONS.BOOK,
     TICKET_PERMISSIONS.CANCEL,
    // Analytics permissions
     ANALYTICS_PERMISSIONS.READ,
     ANALYTICS_PERMISSIONS.EXPORT,
    // Settings permissions
     SETTINGS_PERMISSIONS.READ,
  ],
  VISITOR: [
    // Event permissions
    EVENT_PERMISSIONS.READ,
    // Ticket permissions
    TICKET_PERMISSIONS.READ,
    TICKET_PERMISSIONS.BOOK,
    TICKET_PERMISSIONS.CANCEL,
    // Settings permissions
    SETTINGS_PERMISSIONS.READ,
  ],
};


// Define pre-set event operations for each role
export const EVENT_OPERATIONS: Record<UserRole, EventOperations> = {
    ADMIN: {
        canCreate: true,
        canUpdate: true,
        canDelete: true,
        canManage: true,
    },
    MANAGER: {
        canCreate: true,
        canUpdate: true,
        canDelete: false,
        canManage: true,
    },
    VISITOR: {
        canCreate: false,
        canUpdate: false,
        canDelete: false,
        canManage: false,
    },
};

// Define pre-set user operations for each role
export const USER_OPERATIONS: Record<UserRole, UserOperations> = {
  ADMIN: {
      canCreate: true,
      canRead: true,
      canUpdate: true,
      canDelete: true,
      canManage: true
  },
  MANAGER: {
    canCreate: false,
    canRead: true,
    canUpdate: true,
    canDelete: false,
    canManage: false
  },
  VISITOR: {
    canCreate: false,
    canRead: false,
    canUpdate: false,
    canDelete: false,
    canManage: false
  }
};

// Define pre-set ticket operations for each role
export const TICKET_OPERATIONS: Record<UserRole, TicketOperations> = {
    ADMIN: {
        canCreate: true,
        canRead: true,
        canUpdate: true,
        canDelete: true,
        canManage: true,
        canBook: true,
        canCancel: true
    },
    MANAGER: {
        canCreate: false,
        canRead: true,
        canUpdate: true,
        canDelete: false,
        canManage: true,
        canBook: true,
        canCancel: true
    },
    VISITOR: {
      canCreate: false,
        canRead: true,
        canUpdate: false,
        canDelete: false,
        canManage: false,
        canBook: true,
        canCancel: true
    }
};


// Define pre-set analytics operations for each role
export const ANALYTICS_OPERATIONS: Record<UserRole, AnalyticsOperations> = {
    ADMIN: {
        canRead: true,
        canExport: true,
        canManage: true
    },
    MANAGER: {
        canRead: true,
        canExport: true,
        canManage: false
    },
    VISITOR: {
        canRead: false,
        canExport: false,
        canManage: false
    }
};

// Define pre-set settings operations for each role
export const SETTINGS_OPERATIONS: Record<UserRole, SettingsOperations> = {
    ADMIN: {
        canRead: true,
        canManage: true
    },
    MANAGER: {
        canRead: true,
        canManage: false
    },
  VISITOR: {
      canRead: true,
        canManage: false
    }
};
// Define logical permission groups
export const PERMISSION_GROUPS = {
  EVENT_MANAGEMENT: [
    EVENT_PERMISSIONS.CREATE,
    EVENT_PERMISSIONS.READ,
    EVENT_PERMISSIONS.UPDATE,
    EVENT_PERMISSIONS.DELETE,
    EVENT_PERMISSIONS.MANAGE,
  ],
   USER_MANAGEMENT: [
    USER_PERMISSIONS.CREATE,
    USER_PERMISSIONS.READ,
    USER_PERMISSIONS.UPDATE,
    USER_PERMISSIONS.DELETE,
    USER_PERMISSIONS.MANAGE
   ],
  TICKET_OPERATIONS: [
    TICKET_PERMISSIONS.CREATE,
    TICKET_PERMISSIONS.READ,
    TICKET_PERMISSIONS.UPDATE,
    TICKET_PERMISSIONS.DELETE,
     TICKET_PERMISSIONS.MANAGE,
    TICKET_PERMISSIONS.BOOK,
    TICKET_PERMISSIONS.CANCEL
  ],
  ANALYTICS: [
    ANALYTICS_PERMISSIONS.READ,
    ANALYTICS_PERMISSIONS.EXPORT,
    ANALYTICS_PERMISSIONS.MANAGE,
   ],
   SETTINGS:[
    SETTINGS_PERMISSIONS.READ,
    SETTINGS_PERMISSIONS.MANAGE
   ]
} as const;
