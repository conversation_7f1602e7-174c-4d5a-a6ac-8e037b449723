/**
 * VisitorProfile Component
 * 
 * Default profile view for the visitor role.
 * Shows profile information with limited functionality.
 */

import React from 'react';
import { ProfileComponentProps } from '../../types/profile.types';
import { ProfileHeader } from '../common/ProfileHeader';
import { ProfileInfo } from '../common/ProfileInfo';
import { ProfileActions } from '../common/ProfileActions';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CalendarDays, MapPin, Ticket, UserCircle } from 'lucide-react';
import { useSession } from 'next-auth/react';

export function VisitorProfile({ profile, isEditable = false }: ProfileComponentProps) {
  const { data: session } = useSession();
  const isCurrentUser = session?.user?.email === profile.email;

  return (
    <div className="container max-w-4xl mx-auto px-4 py-8 space-y-6">
      {/* Profile Header with Avatar */}
      <ProfileHeader profile={profile} isEditable={isEditable} />
      
      {/* Profile Actions (Follow, Message, etc.) */}
      <div className="w-full">
        <ProfileActions 
          profile={profile} 
          isCurrentUser={isCurrentUser}
          isFollowing={false} // This would come from your actual data
        />
      </div>
      
      {/* Main Content Area with Tabs */}
      <Tabs defaultValue="about" className="w-full">
        <TabsList className="grid grid-cols-3 max-w-md">
          <TabsTrigger value="about">About</TabsTrigger>
          <TabsTrigger value="events">Events</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>
        
        {/* About Tab */}
        <TabsContent value="about" className="space-y-4 mt-6">
          <ProfileInfo profile={profile} isEditable={isEditable} />
        </TabsContent>
        
        {/* Events Tab */}
        <TabsContent value="events" className="space-y-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Upcoming Events</CardTitle>
              <CardDescription>Events you are registered to attend</CardDescription>
            </CardHeader>
            <CardContent>
              {(profile.stats?.eventsAttended || 0) > 0 ? (
                <div className="space-y-4">
                  {/* Example event - This would be dynamically generated */}
                  <div className="flex items-start space-x-4 p-4 rounded-lg border">
                    <div className="bg-primary/10 p-2 rounded">
                      <CalendarDays className="h-6 w-6 text-primary" />
                    </div>
                    <div className="space-y-1">
                      <h3 className="font-medium">Tech Conference 2023</h3>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <CalendarDays className="h-4 w-4 mr-1" />
                        <span>Jun 15, 2023</span>
                      </div>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <MapPin className="h-4 w-4 mr-1" />
                        <span>San Francisco, CA</span>
                      </div>
                      <div className="mt-2">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <Ticket className="h-3 w-3 mr-1" />
                          Registered
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Ticket className="h-12 w-12 mx-auto mb-4 opacity-30" />
                  <p>No events to display</p>
                  <p className="text-sm mt-1">Register for events to see them here</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Activity Tab */}
        <TabsContent value="activity" className="space-y-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Recent Activity</CardTitle>
              <CardDescription>Your latest interactions on the platform</CardDescription>
            </CardHeader>
            <CardContent>
              {/* This would be populated with actual activity data */}
              <div className="text-center py-8 text-muted-foreground">
                <UserCircle className="h-12 w-12 mx-auto mb-4 opacity-30" />
                <p>No recent activity</p>
                <p className="text-sm mt-1">Your actions will appear here</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Add debugging if needed
console.log('👤 VisitorProfile loaded');
