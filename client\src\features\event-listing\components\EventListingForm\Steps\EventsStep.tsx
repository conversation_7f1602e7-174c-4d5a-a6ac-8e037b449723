// Client-side component for selecting an event
"use client";

import React, { useState, useEffect } from "react";
import { GlobalSearch } from "@/features/global-search/components/GlobalSearch";
import { useAppDispatch, useAppSelector } from "@/app/redux";
import { useEventListing } from "../EventListingContext";
import { setSelectedSearchEvent } from "@/state";
import { Context } from "@/types/openctx.types";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export const EventsStep: React.FC = () => {
  const dispatch = useAppDispatch();
  const { setEventListingData } = useEventListing();
  const [localSelectedEvent, setLocalSelectedEvent] = useState<Context | null>(
    null
  );

  // Get selected event from Redux
  const selectedEventRedux = useAppSelector(
    (state) => state.global.selectedSearchEvent
  );

  // Update context and local state when Redux state changes
  useEffect(() => {
    if (selectedEventRedux) {
      setLocalSelectedEvent(selectedEventRedux); // Update local state
      setEventListingData((prevData) => ({
        ...prevData,
        selectedEvent: selectedEventRedux,
      }));
    }
  }, [selectedEventRedux, setEventListingData]);

  return (
    <div>
      <div className="mb-4">
        <GlobalSearch />
      </div>

      {/* Display selected event details */}
      {localSelectedEvent && (
        <Card>
          <CardHeader>
            <CardTitle>{localSelectedEvent.metadata.name}</CardTitle>
            <CardDescription>
              Venue: {localSelectedEvent.metadata.venue}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {localSelectedEvent.metadata.image && (
              <Image
                src={localSelectedEvent.metadata.image}
                alt={localSelectedEvent.metadata.name || "Event Image"}
                width={100}
                height={100}
                className="rounded"
/>
            )}
            {/* Add more details as needed */}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
