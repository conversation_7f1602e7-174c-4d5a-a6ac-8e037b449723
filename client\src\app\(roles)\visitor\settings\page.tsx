'use client'

import { getRoleSpecificSettingsComponent } from '@/features/settings/utils/settings-resolver'

export default function DynamicSettingsPage() {
  const role = "VISITOR"
  const SettingsComponent = getRoleSpecificSettingsComponent(role)

  return (
    <div className="container mx-auto py-6 px-4 min-h-screen">
      <h1 className="text-3xl font-semibold mb-2">
        Visitor Settings
      </h1>
      <div className="m-8">
        <SettingsComponent />
      </div>
    </div>
  )
}
