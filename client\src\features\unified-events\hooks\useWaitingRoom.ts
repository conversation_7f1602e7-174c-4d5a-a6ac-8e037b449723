import { useState, useCallback, useEffect } from "react";
import { api } from "@/apiAxios/axios"; // Use your existing axios instance with auth
import { toast } from "sonner";
import {
  QueueStatusResponse,
  JoinQueueResponse,
  QueueUserStatus,
} from "../types/queue.types";
import { useAppDispatch } from "@/app/redux";
import { updateQueueStatus } from "@/state/queueSlice";

/**
 * Custom hook for interacting with the waiting room/queue API
 * Uses the authenticated axios instance
 */
export const useWaitingRoom = (eventId?: string) => {
  const dispatch = useAppDispatch();
  const [queueStatus, setQueueStatus] = useState<QueueStatusResponse | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(false);
  const [isRefetching, setIsRefetching] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isJoining, setIsJoining] = useState(false);

  // Function to fetch queue status
  const fetchQueueStatus = useCallback(
    async (silent = false) => {
      if (!eventId) return;

      if (!silent) setIsLoading(true);
      else setIsRefetching(true);

      try {
        const response = await api.get<{
          success: boolean;
          message: string;
          data: QueueStatusResponse;
          //Todo: // !  testing the waiting room queue with dummy response
        }>(`/api/v1/queue/status/${eventId}`);
        // }>(`/api/v1/queue/test/status/${eventId}`);

        if (!response.data.success) {
          throw new Error(
            response.data.message || "Failed to fetch queue status"
          );
        }

        const queueStatusData = response.data.data;
        setQueueStatus(queueStatusData);
        setError(null);

        // Update Redux state
        dispatch(
          updateQueueStatus({
            eventId: eventId as string,
            isActive: queueStatusData.isActive,
            userStatus: queueStatusData.userStatus
              ? {
                  status: queueStatusData.userStatus.status,
                  position: queueStatusData.userStatus.position,
                }
              : undefined,
          })
        );
      } catch (err) {
        setError(
          err instanceof Error ? err : new Error("Failed to fetch queue status")
        );
        console.error("Queue status error:", err);
      } finally {
        setIsLoading(false);
        setIsRefetching(false);
      }
    },
    [dispatch, eventId]
  );

  // Function to join queue
  const joinQueue = useCallback(async () => {
    if (!eventId) {
      toast.error("Cannot join queue", { description: "Event ID is missing" });
      return;
    }

    setIsJoining(true);
    try {
      const response = await api.post<{
        success: boolean;
        message: string;
        data: JoinQueueResponse;
        // todo : //! testing the join logic
      }>("/api/v1/queue/join", { eventId });
      // }>("/api/v1/queue/test/join?simulate=true", { eventId });

      if (!response.data.success) {
        throw new Error(response.data.message || "Failed to join queue");
      }

      const data = response.data.data;

      // Update queue status after joining
      fetchQueueStatus(true);

      // Show success toast with position
      toast.success(
        data.position
          ? `Joined waiting room at position ${data.position}`
          : "Joined waiting room",
        {
          description: data.message,
        }
      );

      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      toast.error("Failed to join waiting room", { description: errorMessage });
      setError(err instanceof Error ? err : new Error(errorMessage));
      console.error("Join queue error:", err);
    } finally {
      setIsJoining(false);
    }
  }, [eventId, fetchQueueStatus]);

  // Set up polling if in queue
  useEffect(() => {
    if (!eventId) return;

    // Initial fetch
    fetchQueueStatus();

    // Set up polling if user is in queue or queue is active
    const shouldPoll =
      queueStatus?.isActive &&
      queueStatus?.userStatus?.status === QueueUserStatus.WAITING;

    if (shouldPoll) {
      const interval = setInterval(() => fetchQueueStatus(true), 5000); // Poll every 5 seconds
      return () => clearInterval(interval);
    }
  }, [
    eventId,
    fetchQueueStatus,
    queueStatus?.isActive,
    queueStatus?.userStatus?.status,
  ]);

  // Derived state for easier consumption
  const isInQueue = !!queueStatus?.userStatus;
  const isAdmitted = queueStatus?.userStatus?.status === QueueUserStatus.ACTIVE;
  const needsToJoin = queueStatus?.isActive && !isInQueue;
  const canProceedToCheckout = !queueStatus?.isActive || isAdmitted;

  return {
    // Raw data
    queueStatus,
    isLoading: isLoading || isRefetching,
    error,

    // Derived state
    isActive: !!queueStatus?.isActive,
    isInQueue,
    isAdmitted,
    needsToJoin,
    canProceedToCheckout,
    position: queueStatus?.userStatus?.position,
    status: queueStatus?.userStatus?.status,
    totalWaiting: queueStatus?.queueState?.totalWaiting,

    // Actions
    joinQueue,
    refetchStatus: () => fetchQueueStatus(true),

    // Mutation state
    isJoining,
  };
};
