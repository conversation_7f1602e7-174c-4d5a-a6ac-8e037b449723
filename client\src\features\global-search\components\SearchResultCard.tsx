  // SearchResultCard component for displaying individual search results
  import React from 'react';
  import { motion } from 'framer-motion';
  import { useAppDispatch } from '@/app/redux';
  import { setSelectedSearchEvent } from '@/state';
  import Image from 'next/image';
  import { Context } from '@/types/openctx.types';
  import { Ticket, Calendar, ExternalLink } from 'lucide-react';
  import { useSession } from 'next-auth/react';

  interface SearchResultCardProps {
      event: Context;
      onClose: () => void; // Added onClose prop
  }

  export const SearchResultCard: React.FC<SearchResultCardProps> = ({ event, onClose }) => {
      const dispatch = useAppDispatch();
      // Get user session to check role
      const { data: session } = useSession();
      const userRole = session?.user?.role?.toUpperCase();
      
      // Destructure metadata for easier access
      const { name, image, venue, date, source, city } = event.metadata;
      
      // Identify event sources
      const isExternalEvent = source === 'ticketmaster' || source === 'external';
      const isManagerEvent = source === 'manager';
      
      // Check if this is another manager's event and the current user is a manager
      const isOtherManagerEvent = isManagerEvent && userRole === "MANAGER";
      
      // If this is another manager's event and the user is a manager, don't render the card
      if (isOtherManagerEvent) {
          return null;
      }
      
      // Format date if available
      const formattedDate = date ? new Date(date).toLocaleDateString() : '';

      // Call the onClose prop when a card is clicked
      const handleClick = () => {
          dispatch(setSelectedSearchEvent(event));
          onClose();
      }

      return (
          <motion.div
              className="p-3 hover:bg-gray-100 rounded-lg cursor-pointer relative"
              onClick={handleClick} // Updated to use new handleClick function
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.1 }}
              // Add a unique key attribute using a combination of properties
              key={`${event.type}-${source}-${event.metadata.id || ''}-${name || event.text}`}
          >
              {/* Source indicator icons */}
              <div className="absolute top-2 right-2">
                  {isExternalEvent && <ExternalLink className="w-4 h-4 text-blue-500" />}
                  {isManagerEvent && <Calendar className="w-4 h-4 text-green-500" />}
              </div>
              
              <div className="flex items-center gap-4">
                  {image ? (
                      <Image
                          src={image}
                          alt={name || ''}
                          className="w-16 h-16 object-cover rounded"
                          width={64}
                          height={64}
                      />
                  ) : (
                      <div className="w-16 h-16 bg-gray-200 rounded flex items-center justify-center">
                          {isManagerEvent ? (
                              <Calendar className="w-8 h-8 text-gray-400" />
                          ) : (
                              <Ticket className="w-8 h-8 text-gray-400" />
                          )}
                      </div>
                  )}
                  
                  <div>
                      {/* Use name or fallback to event.text */}
                      <h4 className="font-medium">{name || event.text}</h4>
                      
                      {/* Display venue and date */}
                      <p className="text-sm text-gray-600">
                          {venue} • {formattedDate} • {city}
                      </p>
                      
                      {/* Source badge */}
                      <div className="mt-1">
                          <span className={`text-xs px-2 py-0.5 rounded-full ${
                              isManagerEvent 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-blue-100 text-blue-800'
                          }`}>
                              {isManagerEvent ? 'Ticket Available' : 'Sell Tickets'}
                          </span>
                      </div>
                  </div>
              </div>
          </motion.div>
      );
  };
