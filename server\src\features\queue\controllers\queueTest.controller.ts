import { Request, Response } from 'express';
import { QueueUserStatus } from '@prisma/client';
import { asyncHand<PERSON> } from '@/utils/asyncHandler';
import ApiError from '@/utils/ApiError';

// Global test state - persists between requests during server lifetime
let testState = {
  // Default starting state
  isActive: true,
  userStatus: null as any, // Will be populated on join
  queueState: {
    totalWaiting: 50
  },
  // For simulating automatic progression
  simulationMode: false,
  simulationInterval: null as NodeJS.Timeout | null,
  // Configuration settings
  admitAfterSeconds: 60, // How quickly to admit users after joining
  expireAfterSeconds: 120, // How long after admission before expiring
  processingRate: 5, // Users processed per minute (for estimation)
};

/**
 * Reset the test state to defaults
 */
const resetTestState = () => {
  if (testState.simulationInterval) {
    clearInterval(testState.simulationInterval);
  }
  
  testState = {
    isActive: true,
    userStatus: null,
    queueState: {
      totalWaiting: 50
    },
    simulationMode: false,
    simulationInterval: null,
    admitAfterSeconds: 60,
    expireAfterSeconds: 120,
    processingRate: 5,
  };
};

/**
 * Start automatic simulation of queue progress
 */
const startSimulation = (userId: string) => {
  // Clear any existing simulation
  if (testState.simulationInterval) {
    clearInterval(testState.simulationInterval);
  }
  
  // Set initial state if not already set
  if (!testState.userStatus) {
    testState.userStatus = {
      status: QueueUserStatus.WAITING,
      position: 25,
      enteredAt: new Date().toISOString(),
    };
  }
  
  const startTime = Date.now();
  let admissionTime = startTime + (testState.admitAfterSeconds * 1000);
  let expiryTime = admissionTime + (testState.expireAfterSeconds * 1000);
  
  testState.simulationMode = true;
  testState.simulationInterval = setInterval(() => {
    const now = Date.now();
    
    // If in waiting state, decrease position and eventually admit
    if (testState.userStatus?.status === QueueUserStatus.WAITING) {
      // Calculate progress percentage
      const progressPercent = Math.min(1, (now - startTime) / (admissionTime - startTime));
      // Decrease position based on progress
      testState.userStatus.position = Math.max(1, Math.floor(25 * (1 - progressPercent)));
      
      // When we reach admission time, change status
      if (now >= admissionTime) {
        testState.userStatus.status = QueueUserStatus.ACTIVE;
        testState.userStatus.admittedAt = new Date().toISOString();
        testState.userStatus.expiresAt = new Date(expiryTime).toISOString();
        delete testState.userStatus.position;
      }
    }
    
    // If active and past expiry time, expire the session
    else if (testState.userStatus?.status === QueueUserStatus.ACTIVE && now >= expiryTime) {
      testState.userStatus.status = QueueUserStatus.EXPIRED;
      delete testState.userStatus.expiresAt;
    }
    
    // Decrease waiting count gradually
    if (testState.queueState.totalWaiting > 0) {
      testState.queueState.totalWaiting = Math.max(0, 
        testState.queueState.totalWaiting - Math.floor(Math.random() * 3)
      );
    }
    
  }, 5000); // Update every 5 seconds
};

export class QueueTestController {
  /**
   * Gets the mocked queue status for testing UI
   */
  static getQueueStatus = asyncHandler(async (req: Request, res: Response) => {
    const { eventId } = req.params;
    if (!eventId) {
      throw new ApiError(400, 'Event ID parameter is required');
    }
    
    // Get userId for test status
    const userId = (req as any).user?.userId || 'test-user-id';
    
    // Special URL parameter to control test status
    const testStatus = req.query.status as string;
    if (testStatus) {
      if (testStatus.toUpperCase() === 'RESET') {
        resetTestState();
        return res.status(200).json({
          success: true,
          message: 'Test state has been reset.',
          data: {
            isActive: testState.isActive,
            queueState: testState.queueState,
            userStatus: null
          }
        });
      }
      
      // Manual status override
      if (Object.values(QueueUserStatus).includes(testStatus.toUpperCase() as QueueUserStatus)) {
        const status = testStatus.toUpperCase() as QueueUserStatus;
        
        // Create appropriate user status based on requested state
        if (status === QueueUserStatus.WAITING) {
          testState.userStatus = {
            status,
            position: parseInt(req.query.position as string) || 15,
            enteredAt: new Date().toISOString(),
          };
        } else if (status === QueueUserStatus.ACTIVE) {
          const now = new Date();
          const expiresAt = new Date(now.getTime() + (15 * 60 * 1000)); // 15 minutes from now
          
          testState.userStatus = {
            status,
            enteredAt: new Date(now.getTime() - (5 * 60 * 1000)).toISOString(), // 5 minutes ago
            admittedAt: now.toISOString(),
            expiresAt: expiresAt.toISOString(),
          };
        } else if (status === QueueUserStatus.EXPIRED) {
          const now = new Date();
          testState.userStatus = {
            status,
            enteredAt: new Date(now.getTime() - (30 * 60 * 1000)).toISOString(), // 30 minutes ago
            admittedAt: new Date(now.getTime() - (15 * 60 * 1000)).toISOString(), // 15 minutes ago
          };
        }
        
        return res.status(200).json({
          success: true,
          message: `Test status set to ${status}`,
          data: {
            isActive: testState.isActive,
            queueState: testState.queueState,
            userStatus: testState.userStatus
          }
        });
      }
      
      // Toggle active state
      if (testStatus.toUpperCase() === 'TOGGLE') {
        testState.isActive = !testState.isActive;
        return res.status(200).json({
          success: true,
          message: `Queue active state set to ${testState.isActive}`,
          data: {
            isActive: testState.isActive,
            queueState: testState.isActive ? testState.queueState : null,
            userStatus: testState.userStatus
          }
        });
      }
      
      // Start simulation mode
      if (testStatus.toUpperCase() === 'SIMULATE') {
        if (!testState.simulationMode) {
          startSimulation(userId);
          return res.status(200).json({
            success: true,
            message: 'Simulation mode activated. Queue state will change automatically.',
            data: {
              isActive: testState.isActive,
              queueState: testState.queueState,
              userStatus: testState.userStatus
            }
          });
        } else {
          // Stop simulation
          if (testState.simulationInterval) {
            clearInterval(testState.simulationInterval);
            testState.simulationInterval = null;
          }
          testState.simulationMode = false;
          return res.status(200).json({
            success: true,
            message: 'Simulation mode deactivated.',
            data: {
              isActive: testState.isActive,
              queueState: testState.queueState,
              userStatus: testState.userStatus
            }
          });
        }
      }
    }

    // Return current test state (default behavior)
    res.status(200).json({
      success: true,
      message: 'Test queue status retrieved successfully.',
      data: {
        isActive: testState.isActive,
        queueState: testState.queueState,
        userStatus: testState.userStatus
      }
    });
  });

  /**
   * Allows joining the test queue
   */
  static joinQueue = asyncHandler(async (req: Request, res: Response) => {
    const { eventId } = req.body;
    if (!eventId) {
      throw new ApiError(400, 'Event ID is required in the request body');
    }

    const userId = (req as any).user?.userId || 'test-user-id';
    
    // If queue is not active, report error like real system would
    if (!testState.isActive) {
      throw new ApiError(400, "Waiting room is not currently active for this event.");
    }
    
    // If already in queue, return existing status
    if (testState.userStatus) {
      let position: number | undefined;
      let message = "You are already in this queue.";

      if (testState.userStatus.status === QueueUserStatus.WAITING) {
        position = testState.userStatus.position || 1;
        message = `You are already in the waiting room at position ${position}.`;
      } else if (testState.userStatus.status === QueueUserStatus.ACTIVE) {
        message = "You have already been admitted. Proceed to purchase.";
      } else if (testState.userStatus.status === QueueUserStatus.EXPIRED) {
        message = "Your previous session expired. Please rejoin if needed.";
      }

      return res.status(200).json({
        success: true,
        message,
        data: {
          queueId: 'test-queue-id',
          userId,
          status: testState.userStatus.status,
          position: testState.userStatus.position,
          message
        }
      });
    }
    
    // Create new queue entry - simulate priority based logic
    const position = Math.floor(Math.random() * 20) + 5; // Random position between 5-25
    
    testState.userStatus = {
      status: QueueUserStatus.WAITING,
      position: position,
      enteredAt: new Date().toISOString(),
    };
    
    // If simulation mode requested via query param, start it
    if (req.query.simulate === 'true') {
      startSimulation(userId);
    }

    return res.status(200).json({
      success: true,
      message: `You have joined the waiting room at position ${position}.`,
      data: {
        success: true,
        queueId: 'test-queue-id',
        userId,
        status: QueueUserStatus.WAITING,
        position,
        message: `You have joined the waiting room at position ${position}.`
      }
    });
  });
}
