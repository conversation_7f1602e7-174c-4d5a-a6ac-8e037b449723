  // Import necessary modules and types
  import { Request, Response } from 'express';
  import { asyncHandler } from '../../../utils/asyncHandler';
  import ApiError from '../../../utils/ApiError';
  import { PriorityEventService } from '../services/priorityEvent.service';
  import {
      BulkPriorityEventPayload,
      PriorityEventPayload,
      UpdatePriorityEventPayload,
      TogglePopularPayload,
      DeletePriorityEventPayload
  } from '../types/priorityEvent.types';

  export class PriorityEventController {
      // Create a single priority event
      static createPriorityEvents = asyncHandler(
          async (req: Request, res: Response) => {
              const payload = req.body as PriorityEventPayload;
            
              // Validate payload
              if (!payload) {
                  throw ApiError.badRequest('Invalid priority event payload');
              }

              const newEvent = await PriorityEventService.createPriorityEvent(payload);
            
              // Return success response
              res.status(201).json({
                  success: true,
                  data: newEvent
              });
          }
      );

      // Create multiple priority events in bulk
      static createBulkPriorityEvents = asyncHandler(
          async (req: Request, res: Response) => {
              const payload = req.body as BulkPriorityEventPayload;
            
              // Validate payload
              if (!payload || !Array.isArray(payload.events)) {
                  throw ApiError.badRequest('Invalid bulk priority events payload');
              }

              const bulkResult = await PriorityEventService.createBulkPriorityEvents(payload);
            
              // Return success response
              res.status(201).json({
                  success: true,
                  data: bulkResult
              });
          }
      );

      // Get all priority events
      static getPriorityEvents = asyncHandler(
          async (req: Request, res: Response) => {
              const events = await PriorityEventService.getPriorityEvents();
            
              // Return success response
              res.status(200).json({
                  success: true,
                  data: events
              });
          }
      );

      // Get a single priority event by ID
      static getPriorityEventById = asyncHandler(
          async (req: Request, res: Response) => {
              const id = req.params.id;
              const event = await PriorityEventService.getPriorityEventById(id);

              // Check if event exists
              if (!event) {
                  throw ApiError.notFound('Priority event not found');
              }

              // Return success response
              res.status(200).json({
                  success: true,
                  data: event
              });
          }
      );

      // Update a priority event
      static updatePriorityEvent = asyncHandler(
          async (req: Request, res: Response) => {
              const id = req.params.id;
              const payload = req.body as UpdatePriorityEventPayload;

              // Validate ID match
              if (id !== payload.id) {
                  throw ApiError.badRequest('ID mismatch between params and payload');
              }

              const updatedEvent = await PriorityEventService.updatePriorityEvent(payload);

              // Check if event exists
              if (!updatedEvent) {
                  throw ApiError.notFound('Priority event not found');
              }

              // Return success response
              res.status(200).json({
                  success: true,
                  data: updatedEvent
              });
          }
      );

      // Delete a priority event
      static deletePriorityEvent = asyncHandler(
          async (req: Request, res: Response) => {
              const id = req.params.id;
              const payload: DeletePriorityEventPayload = { id };

              await PriorityEventService.deletePriorityEvent(payload);
            
              // Return no content response
              res.status(204).send();
          }
      );

      // Toggle popular status
      static togglePopular = asyncHandler(
          async (req: Request, res: Response) => {
              const id = req.params.id;
              const payload: TogglePopularPayload = { id };

              const updatedEvent = await PriorityEventService.togglePopularStatus(payload);

              // Check if event exists
              if (!updatedEvent) {
                  throw ApiError.notFound('Priority event not found');
              }

              // Return success response
              res.status(200).json({
                  success: true,
                  data: updatedEvent
              });
          }
      );
  }
