'use client';

import { useEffect, useState } from 'react';
import { AlertCircle, CheckCircle, XCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { cn } from '@/lib/utils';

type TransactionStatus = 'success' | 'error' | 'pending' | 'none';

interface TransactionStatusBannerProps {
  status: TransactionStatus;
  title?: string;
  message?: string;
  transactionId?: string;
  amount?: string;
  className?: string;
  autoDismiss?: boolean;
  dismissAfter?: number; // milliseconds
}

export function TransactionStatusBanner({
  status,
  title,
  message,
  transactionId,
  amount,
  className,
  autoDismiss = false,
  dismissAfter = 5000,
}: TransactionStatusBannerProps) {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    setVisible(true);
    
    if (autoDismiss && status !== 'none') {
      const timer = setTimeout(() => {
        setVisible(false);
      }, dismissAfter);
      
      return () => clearTimeout(timer);
    }
  }, [status, autoDismiss, dismissAfter]);
  
  if (!visible || status === 'none') {
    return null;
  }
  
  const getTitleAndIcon = () => {
    switch (status) {
      case 'success':
        return {
          icon: <CheckCircle className="h-5 w-5" />,
          defaultTitle: 'Payment Successful',
          variant: 'success',
        };
      case 'error':
        return {
          icon: <XCircle className="h-5 w-5" />,
          defaultTitle: 'Payment Failed',
          variant: 'destructive',
        };
      case 'pending':
        return {
          icon: <AlertCircle className="h-5 w-5" />,
          defaultTitle: 'Payment Processing',
          variant: 'default',
        };
      default:
        return {
          icon: null,
          defaultTitle: '',
          variant: 'default',
        };
    }
  };
  
  const { icon, defaultTitle, variant } = getTitleAndIcon();
  const displayTitle = title || defaultTitle;
  
  return (
    <Alert
      variant={variant as any}
      className={cn(
        "mb-6 animate-in fade-in-50 duration-300",
        status === 'success' && "border-green-400 bg-green-50 text-green-800",
        status === 'error' && "border-red-400 bg-red-50 text-red-800",
        status === 'pending' && "border-yellow-400 bg-yellow-50 text-yellow-800",
        className
      )}
    >
      {icon}
      <AlertTitle className="flex items-center gap-2 font-medium">
        {displayTitle}
        {amount && <span className="ml-2 font-semibold">{amount}</span>}
      </AlertTitle>
      <AlertDescription className="mt-1">
        {message}
        {transactionId && (
          <div className="mt-1 text-xs">
            Transaction ID: <span className="font-mono">{transactionId}</span>
          </div>
        )}
      </AlertDescription>
    </Alert>
  );
}