{"version": 2, "builds": [{"src": "package.json", "use": "@vercel/next"}], "routes": [{"src": "/api/auth/(.*)", "dest": "/api/authlogic/auth/$1"}, {"handle": "filesystem"}, {"src": "/api/v1/(.*)", "dest": "https://fanseatmaster-heroku-temp-cicd-2ee5e3b3ef24.herokuapp.com/api/v1/$1"}, {"src": "/api/v2/(.*)", "dest": "https://fanseatmaster-heroku-temp-cicd-2ee5e3b3ef24.herokuapp.com/api/v2/$1"}, {"src": "^(?!/api/)(.*)", "dest": "/"}], "buildCommand": "pnpm run build", "installCommand": "npm i -g pnpm@10.0.0 && pnpm install", "framework": "nextjs"}