/**
 * Utility functions for formatting data
 */

/**
 * Formats a number as currency
 * @param amount The amount to format
 * @param currency The currency code (default: 'USD')
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number | null | undefined, currency: string = 'USD'): string {
  // Check if amount is undefined, null, or NaN
  if (amount === undefined || amount === null || isNaN(amount)) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD'
    }).format(0); // Return formatted 0 instead of NaN
  }
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency || 'USD'
  }).format(amount);
};

/**
 * Formats a date string to a human-readable format
 * @param dateString ISO date string
 * @param format Format type ('short', 'medium', 'long')
 * @returns Formatted date string
 */
export const formatDate = (dateString: string, format: 'short' | 'medium' | 'long' = 'medium'): string => {
  try {
    const date = new Date(dateString);
    
    if (isNaN(date.getTime())) {
      return 'Invalid date';
    }
    
    const options: Intl.DateTimeFormatOptions = 
      format === 'short' ? { month: 'short', day: 'numeric' } :
      format === 'long' ? { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' } :
      { year: 'numeric', month: 'long', day: 'numeric' };
      
    return new Intl.DateTimeFormat('en-US', options).format(date);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid date';
  }
};