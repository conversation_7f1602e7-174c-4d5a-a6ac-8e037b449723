import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { PriorityEventData } from "@/features/settings/components/PriorityEvents/types/priority-events.types";

// Update EventCategory type to match exactly what we expect
export type EventCategory = "sports" | "music" | "arts";

// Helper function to normalize category names
export const normalizeCategory = (category: string): EventCategory | null => {
  const normalized = category.toLowerCase();
  if (normalized === "sports") return "sports";
  if (normalized === "music") return "music";
  if (
    normalized.includes("art") ||
    normalized.includes("theatre") ||
    normalized.includes("theater")
  )
    return "arts";
  return null;
};

interface PriorityEventsState {
  sports: PriorityEventData[];
  music: PriorityEventData[];
  arts: PriorityEventData[];
  loading: boolean;
  error: string | null;
}

const initialState: PriorityEventsState = {
  sports: [],
  music: [],
  arts: [],
  loading: false,
  error: null,
};

export const priorityEventsSlice = createSlice({
  name: "priorityEvents",
  initialState,
  reducers: {
    addToPriorityEvents: (
      state,
      action: PayloadAction<{
        category: EventCategory;
        event: PriorityEventData;
      }>
    ) => {
      const { category, event } = action.payload;
      const exists = state[category].some((e) => e.eventId === event.eventId);

      if (!exists && state[category].length < 10) {
        state[category].unshift(event);
      }
    },
    // Updated reducer to set priority events
    setPriorityEvents: (
      state,
      action: PayloadAction<{ events: PriorityEventData[] }>
    ) => {
      state.loading = false;
      state.error = null;

      // Clear existing arrays
      state.sports = [];
      state.music = [];
      state.arts = [];

      // Sort events by date or addedAt before adding
      const sortedEvents = [...action.payload.events].sort(
        (a, b) => new Date(b.addedAt).getTime() - new Date(a.addedAt).getTime()
      );

      sortedEvents.forEach((event) => {
        const normalizedCategory = normalizeCategory(
          event.category.toLowerCase()
        );
        if (normalizedCategory) {
          state[normalizedCategory].unshift(event); // Now using unshift consistently
        }
      });
    },

    // New reducer to set loading state
    setPriorityEventsLoading: (state) => {
      state.loading = true;
      state.error = null;
    },
    // New reducer to set error state
    setPriorityEventsError: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },
    // Add to existing reducer object
    removeFromPriorityEvents: (
      state,
      action: PayloadAction<{ category: EventCategory; eventId: string }>
    ) => {
      console.log("🗑️ Removing event from state:", action.payload);
      const { category, eventId } = action.payload;
      state[category] = state[category].filter(
        (event) => event.eventId !== eventId
      );
    },

    togglePopularStatus: (
      state,
      action: PayloadAction<{ category: EventCategory; eventId: string }>
    ) => {
      console.log("⭐ Toggling popular status in state:", action.payload);
      const { category, eventId } = action.payload;
      const event = state[category].find((e) => e.eventId === eventId);
      if (event) {
        event.isPopular = !event.isPopular;
      }
    },
  },
});

export const {
  addToPriorityEvents,
  removeFromPriorityEvents,
  setPriorityEvents,
  setPriorityEventsError,
  setPriorityEventsLoading,
  togglePopularStatus,
} = priorityEventsSlice.actions;

export default priorityEventsSlice.reducer;
