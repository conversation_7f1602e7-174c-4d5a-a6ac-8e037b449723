"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { InfoTooltipIcon } from "@/components/shared/InfoTooltipIcon";
import { seatingDetailsTooltips } from "./tooltipContent";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useFormContext } from "react-hook-form";

const renderMandatoryLabel = (
  label: string,
  tooltipKey?: keyof typeof seatingDetailsTooltips
) => (
  <div className="flex items-center">
    <FormLabel>
      {label} <span className="text-red-500">*</span>
    </FormLabel>
    {tooltipKey && (
      <InfoTooltipIcon content={seatingDetailsTooltips[tooltipKey] || ""} />
    )}
  </div>
);

export const SeatingDetails: React.FC = () => {
  const form = useFormContext();

  return (
    <div className="border-y-2 py-4 px-2 rounded-md">
      <h2 className="text-lg font-semibold">Seating Details</h2>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        {/* Quantity */}
        <FormField
          control={form.control}
          name="quantity"
          render={({ field }) => (
            <FormItem>
              {renderMandatoryLabel("Quantity", "quantity")}
              <FormControl>
                <Input
                  type="number"
                  // Convert string-to-number and ensure a fallback default value (0) when empty
                  onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                  // Always supply a defined value using fallback to empty string if falsy
                  value={field.value !== undefined ? field.value : ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Section */}
        <FormField
          control={form.control}
          name="section"
          render={({ field }) => (
            <FormItem>
              {renderMandatoryLabel("Section", "section")}
              <FormControl>
                <Input
                  // If section is undefined, default to an empty string
                  value={field.value || ""}
                  onChange={field.onChange}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Row */}
        <FormField
          control={form.control}
          name="row"
          render={({ field }) => (
            <FormItem>
              {renderMandatoryLabel("Row", "row")}
              <FormControl>
                <Input
                  type="number"
                  // Convert value to a number; fallback to 0 if blank
                  onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                  value={field.value !== undefined ? field.value : ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Low Seat Number */}
        <FormField
          control={form.control}
          name="lowSeatNumber"
          render={({ field }) => (
            <FormItem>
              <div className="flex items-center">
                <FormLabel htmlFor="lowSeatNumber">Low Seat No.</FormLabel>
                <InfoTooltipIcon
                  content={seatingDetailsTooltips.lowSeatNumber || ""}
                />
              </div>
              <FormControl>
                <Input
                  type="number"
                  // Only update value if present, otherwise set undefined explicitly
                  onChange={(e) =>
                    field.onChange(e.target.value ? Number(e.target.value) : undefined)
                  }
                  // Allow empty value (controlled as empty string when undefined)
                  value={field.value !== undefined ? field.value : ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Seating Type */}
        <FormField
          control={form.control}
          name="seatingType"
          render={({ field }) => (
            <FormItem>
              <div className="flex items-center">
                <FormLabel htmlFor="seatingTypet">Seating Type</FormLabel>
                <InfoTooltipIcon
                  content={seatingDetailsTooltips.seatingType || ""}
                />
              </div>
              <Select
                onValueChange={field.onChange}
                // Using field.value with a fallback if not set
                defaultValue={field.value || "Consecutive"}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="Consecutive">Consecutive</SelectItem>
                  <SelectItem value="Odd-even">Odd-Even</SelectItem>
                  <SelectItem value="GA">GA</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Ticket Format */}
        <FormField
          control={form.control}
          name="ticketFormat"
          render={({ field }) => (
            <FormItem>
              <div className="flex items-center">
                <FormLabel htmlFor="ticketFormat">Ticket Format</FormLabel>
                <InfoTooltipIcon
                  content={seatingDetailsTooltips.ticketFormat || ""}
                />
              </div>
              <Select onValueChange={field.onChange} defaultValue={field.value || "E-ticket (PDF)"}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="E-ticket (PDF)">E-ticket (PDF)</SelectItem>
                  <SelectItem value="Mobile Transfer">Mobile Transfer</SelectItem>
                  <SelectItem value="Hard (Printed Tickets)">Hard (Printed Tickets)</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Selling Preference */}
        <FormField
          control={form.control}
          name="sellingPreference"
          render={({ field }) => (
            <FormItem>
              {renderMandatoryLabel("Selling Preference", "sellingPreference")}
              <Select onValueChange={field.onChange} defaultValue={field.value || "Any"}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="Any">Any</SelectItem>
                  <SelectItem value="Pairs">Pairs</SelectItem>
                  <SelectItem value="Full">Full</SelectItem>
                  <SelectItem value="Avoid Leaving Single">Avoid Leaving Single</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};
