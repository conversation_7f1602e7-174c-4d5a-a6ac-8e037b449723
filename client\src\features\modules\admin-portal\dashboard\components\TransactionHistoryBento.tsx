"use client";
import { Receipt, Search } from "lucide-react";
import { Bento<PERSON>ox } from "../../../shared/widgets/BentoBox";
import { Input } from "@/components/ui/input";

export const TransactionHistoryBento = () => {
  const transactions = [
    {
      id: "1",
      eventName: "Summer Festival",
      manager: "<PERSON>",
      visitor: "<PERSON>",
      amount: 250,
      status: "completed",
      timestamp: "2024-01-15 14:30",
    },
    {
      id: "2",
      eventName: "Tech Conference",
      manager: "<PERSON>",
      visitor: "<PERSON>",
      amount: 180,
      status: "pending",
      timestamp: "2024-01-15 13:45",
    },
    {
      id: "3",
      eventName: "Sports Championship",
      manager: "<PERSON>",
      visitor: "<PERSON>",
      amount: 320,
      status: "completed",
      timestamp: "2024-01-15 12:15",
    },
  ];

  return (
    <BentoBox
      title="Recent Transactions"
      className="col-span-2 row-span-2"
      header={
        <div className="flex items-center gap-4 w-full">
          <Receipt className="h-5 w-5 text-primary" />
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input placeholder="Search transactions..." className="pl-8" />
          </div>
        </div>
      }
    >
      <div className="space-y-4 mt-4">
        {transactions.map((tx) => (
          <div
            key={tx.id}
            className="p-4 rounded-lg bg-accent/50 hover:bg-accent/70 transition-colors"
          >
            <div className="flex justify-between items-start mb-2">
              <div>
                <h4 className="font-medium">{tx.eventName}</h4>
                <p className="text-sm text-muted-foreground">
                  Manager: {tx.manager} | Visitor: {tx.visitor}
                </p>
              </div>
              <div className="text-right">
                <p className="font-medium">${tx.amount}</p>
                <span
                  className={`text-sm ${
                    tx.status === "completed"
                      ? "text-green-600"
                      : "text-orange-600"
                  }`}
                >
                  {tx.status}
                </span>
              </div>
            </div>
            <p className="text-xs text-muted-foreground">{tx.timestamp}</p>
          </div>
        ))}
      </div>
    </BentoBox>
  );
};
