import React from "react";
import { Context } from "@/types/openctx.types";

interface EventPreviewCardProps {
  event: Context | null;
}

export const EventPreviewCard: React.FC<EventPreviewCardProps> = ({
  event,
}) => {
  if (!event) {
    return null; // Don't render anything if there's no event
  }

  return (
    <div className="border rounded-lg p-4">
      <h2 className="text-lg font-semibold">{event.metadata.name}</h2>
      <p className="text-sm">
        Date & Time: {event.metadata.date}
      </p>
      <p className="text-sm">Venue: {event.metadata.venue}</p>
      <p className="text-sm">
        Country:{" "}
        {event.metadata.rawEvent?._embedded?.venues?.[0]?.country?.name ||
          "Unknown"}
      </p>
    </div>
  );
};
