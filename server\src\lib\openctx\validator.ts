// Validator for validating OpenCTX requests
// Validates the request based on the schema

import { OpenCTXRequest, ValidationResult, openCTXSchema } from './types';

export const validateOpenCTXRequest = (
  request: any,
  userRole?: string
): ValidationResult => {
  try {
      // Parse the request based on the OpenCTX schema
      const parsedRequest = openCTXSchema.parse(request);

      // Validate entity and user role
    if (parsedRequest.entity === 'users' && userRole !== 'admin') {
            return { valid: false, error: 'Unauthorized access to users entity' };
        }
        // Return valid if parsing is successful and user is authorized
        return { valid: true, data: parsedRequest }
    }
    catch (error) {
      // Return invalid with error message if parsing fails
      return { valid: false, error: error instanceof Error ? error.message: 'Validation failed' };
    }
};