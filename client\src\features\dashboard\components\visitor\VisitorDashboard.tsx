"use client";
import { GridLayout } from "../../../modules/shared/layout/GridLayout";
import { EventsDiscoveryGrid } from "./EventsDiscoveryGrid";
import { TicketStatusBento } from "./TicketStatusBento";
import { MetricCard } from "../../../modules/shared/stats/MetricCard";
import { motion } from "framer-motion";

export const VisitorDashboard = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-8"
    >
      {/* Dashboard Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold tracking-tight">Welcome Back!</h1>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        <MetricCard
          title="Total Events"
          value="12"
          trend={{ value: 8, isPositive: true }}
        />
        <MetricCard
          title="Active Tickets"
          value="3"
          trend={{ value: 2, isPositive: true }}
        />
        <MetricCard
          title="Upcoming Events"
          value="5"
          trend={{ value: 1, isPositive: true }}
        />
      </div>

      {/* Main Content Grid */}
      <GridLayout className="grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
        <EventsDiscoveryGrid />
        <TicketStatusBento />
      </GridLayout>
    </motion.div>
  );
};
