import { useState } from 'react';
import { PaymentStatus } from '../types/payment.types';
import { useCreatePaymentIntent } from './usePaymentApi';

interface UsePaymentProps {
  onSuccess?: (paymentIntentId: string) => void;
  onError?: (error: Error) => void;
}

/**
 * Hook for managing the payment flow UI state
 * Uses usePaymentApi hooks internally for API calls
 */
export function usePayment({ onSuccess, onError }: UsePaymentProps = {}) {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [paymentIntentId, setPaymentIntentId] = useState<string | null>(null);
  const [status, setStatus] = useState<PaymentStatus | null>(null);
  
  // Use React Query mutation from usePaymentApi
  const paymentIntentMutation = useCreatePaymentIntent();
  
  /**
   * Initialize a payment intent for the given checkout session
   */
  const initiatePayment = async (sessionId: string) => {
    try {
      const response = await paymentIntentMutation.mutateAsync({ sessionId });
      
      if (!response.success || !response.clientSecret) {
        throw new Error(response.error || 'Failed to create payment intent');
      }
      console.log('usePyament.ts clientSecret finding 🔑🔑:', response.clientSecret);
      setClientSecret(response.clientSecret);
      console.log('usePyament.ts clientSecret setup succeful 🌻🌻🔑🔑:');
      if (response.paymentIntentId) {
        setPaymentIntentId(response.paymentIntentId);
      }
      setStatus(PaymentStatus.PENDING);
      
      return response.clientSecret;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred');
      onError?.(error);
      return null;
    }
  };
  
  // Other methods remain similar...
  const handlePaymentSuccess = (paymentIntentId: string) => {
    setStatus(PaymentStatus.SUCCEEDED);
    setPaymentIntentId(paymentIntentId);
    onSuccess?.(paymentIntentId);
  };
  
  const handlePaymentFailure = (error: Error) => {
    setStatus(PaymentStatus.FAILED);
    onError?.(error);
  };
  
  const resetPayment = () => {
    setClientSecret(null);
    setPaymentIntentId(null);
    setStatus(null);
  };
  
  return {
    initiatePayment,
    handlePaymentSuccess,
    handlePaymentFailure,
    resetPayment,
    clientSecret,
    paymentIntentId,
    isLoading: paymentIntentMutation.isPending,
    error: paymentIntentMutation.error as Error | null,
    status,
    isReady: !!clientSecret && !paymentIntentMutation.isPending,
  };
}