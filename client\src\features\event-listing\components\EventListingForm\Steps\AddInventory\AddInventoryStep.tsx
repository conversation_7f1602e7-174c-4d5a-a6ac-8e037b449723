"use client";

import React from "react";
import { useEventListing } from "../../EventListingContext";
import { <PERSON><PERSON> } from "@/components/ui/button";
// import { InventoryItem } from "@/features/event-listing/types/eventListing"; // Not directly used, type inference from schema
import { SeatingDetails } from "./SeatingDetails";
import { PricingDetails } from "./PricingDetails";
import { AdditionalInformation } from "./AdditionalInformation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, FormProvider } from "react-hook-form";
import * as z from "zod";

// Zod schema for InventoryItem
export const inventoryItemSchema = z.object({
  id: z.string(),
  quantity: z.coerce.number().min(1, "Quantity must be at least 1"),
  section: z.string().min(1, "Section is required"),
  row: z.coerce.number().min(1, "Row is required"), // Assuming row can be 0 for some GA or general sections, adjust if needed
  lowSeatNumber: z.coerce.number().optional(),
  seatingType: z.enum(["Consecutive", "Odd-even", "GA"]),
  ticketFormat: z.enum([
    "E-ticket (PDF)",
    "Mobile Transfer",
    "Hard (Printed Tickets)",
  ]),
  listPrice: z.coerce.number().min(0, "List price must be 0 or greater"),
  serviceFee: z.coerce
    .number()
    .min(0, "Service fee must be 0 or greater")
    .optional(),
  publicNote: z.string().optional(),
  internalNote: z.string().optional(),
  termsAndConditions: z.boolean().refine(val => val === true, { message: "You must accept the terms and conditions" }), // Ensure terms are accepted
  sellingPreference: z.enum(["Any", "Pairs", "Full", "Avoid Leaving Single"]),
  disclosures: z.string().array().optional(), // Made optional, as they might not always be selected
  attributes: z.string().array().optional(), // Made optional
});

// Add type definition for form values
type InventoryFormValues = z.infer<typeof inventoryItemSchema>;

// Default form values (for adding new items)
const defaultValues: InventoryFormValues = {
  id: crypto.randomUUID(), // id will be regenerated if it's a new item
  quantity: 1,
  section: "",
  row: 0, // Default to 0, or adjust if a different default is more appropriate
  lowSeatNumber: undefined,
  seatingType: "Consecutive",
  ticketFormat: "E-ticket (PDF)",
  listPrice: 0,
  serviceFee: 0,
  publicNote: "", // Default to empty string
  internalNote: "", // Default to empty string
  termsAndConditions: true, // Default to true, or false if you want explicit user action
  sellingPreference: "Any",
  disclosures: [], // Default to empty array
  attributes: [], // Default to empty array
};

// Component for adding or editing inventory items
export const AddInventoryStep: React.FC = () => {
  const { eventListingData, setEventListingData } = useEventListing();
  
  const form = useForm<InventoryFormValues>({
    resolver: zodResolver(inventoryItemSchema),
    // If editing (tempInventoryItem exists), use its values, otherwise use defaultValues
    defaultValues: eventListingData.tempInventoryItem
      ? { ...eventListingData.tempInventoryItem } 
      : { ...defaultValues, id: crypto.randomUUID() }, // Ensure new items get a fresh UUID
    mode: "onChange", // Validate on change for better UX
  });
  
  // Watch termsAndConditions value
  const termsAccepted = form.watch("termsAndConditions");

  const onSubmit = (data: InventoryFormValues) => {
    console.log("📝 Inventory Item Form Data Submitted:", data); // Added console log
    setEventListingData((prevData) => {
      if (prevData.tempInventoryItem && prevData.inventory.some(item => item.id === prevData.tempInventoryItem!.id)) {
        // Update existing item if tempInventoryItem ID matches an existing item
        const updatedInventory = prevData.inventory.map((item) => 
          item.id === prevData.tempInventoryItem!.id
            ? {
                ...data,
                id: item.id,
                disclosures: data.disclosures ?? [],
                attributes: data.attributes ?? [],
              }
            : item
        );
        
        return {
          ...prevData,
          inventory: updatedInventory,

          tempInventoryItem: undefined, // Clear temp item after update
        };
      }
      

      // Add new item (assign a new ID to be certain, though data.id should be new from defaultValues)
      return {
        ...prevData,

        inventory: [
          ...prevData.inventory,
          {
            ...data,
            id: data.id || crypto.randomUUID(),
            disclosures: data.disclosures ?? [],
            attributes: data.attributes ?? [],
          },
        ], // Ensure ID and arrays
        tempInventoryItem: undefined, // Clear temp item if it was somehow set without matching
      };
    });

    // Reset form with default values for a new item, ensuring a new ID
    form.reset({ ...defaultValues, id: crypto.randomUUID() });
  };

  // If tempInventoryItem changes (e.g., user clicks edit on an item), reset the form with its values
  React.useEffect(() => {
    if (eventListingData.tempInventoryItem) {
      form.reset({ ...eventListingData.tempInventoryItem });
    } else {
      // If tempInventoryItem is cleared (e.g., after adding/updating or navigating away), reset to defaults for a new item
      form.reset({ ...defaultValues, id: crypto.randomUUID() });
    }
  }, [eventListingData.tempInventoryItem, form.reset]);


  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <SeatingDetails />
        <PricingDetails />
        <AdditionalInformation />
        <Button 
          type="submit" 
          // Disable if terms are not accepted OR if form is submitting OR if form is not valid
          disabled={!termsAccepted || form.formState.isSubmitting || !form.formState.isValid}
          className="w-full"
        >
          {form.formState.isSubmitting 
            ? "Saving..." 
            : eventListingData.tempInventoryItem ? "Update Item" : "Add Item to Listing"}
        </Button>
      </form>
    </FormProvider>
  );
};
