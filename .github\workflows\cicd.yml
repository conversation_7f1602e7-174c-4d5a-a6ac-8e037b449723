

name: CI/CD Pipeline for Fanseatmaster

on:
  push:
    branches: 
      - main  # For production deployments
      - develop # For staging deployments
  # pull_request:
  #   branches: 
  #     - develop # For Vercel client previews when PR targets develop

jobs:
  client-deploy:
    runs-on: ubuntu-latest
    environment: # This links the job to the GitHub Environment based on the branch name
      name: ${{ github.ref_name == 'main' && 'main' || github.ref_name == 'develop' && 'develop' || '' }}
    #   # The deployment URL will be an output from the Vercel action
    
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20.x'
      
      - name: Install pnpm
        run: npm install -g pnpm@10 # Consistent pnpm version
      
      - name: Install client dependencies
        run: pnpm install
        working-directory: ./client
      
      - name: Run ESLint
        run: pnpm run lint
        working-directory: ./client
      
      - name: Build client
        run: pnpm run clean && pnpm run build
        working-directory: ./client
        env:
          # This secret (NEXT_PUBLIC_API_BASE_URL_CLIENT) will be pulled from the active GitHub Environment (main or develop)
          NEXT_PUBLIC_API_BASE_URL: ${{ secrets.NEXT_PUBLIC_API_BASE_URL}}
          NODE_ENV: production
      
      - name: Install Vercel CLI
        run: npm install -g vercel

      - name: Deploy to Vercel
        uses: BetaHuhn/deploy-to-vercel-action@v1
        with:
          GITHUB_TOKEN: ${{ secrets.GH_PAT }} # Repository-level secret
          VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN_FANSEATMASTER }} # Repository-level secret
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }} # Repository-level secret
          # This secret (VERCEL_PROJECT_ID) will be pulled from the active GitHub Environment (main or develop)
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
          PRODUCTION: true
          # Sets this as a Vercel "Production" deployment if the branch is 'main' only
          # PRODUCTION: ${{ github.ref_name == 'main' }}
         
          # If it's a push to 'develop', it will deploy to the staging Vercel project.
          # If it's a push to 'main', it will deploy to the production Vercel project.

  server-deploy:
    runs-on: ubuntu-latest
    environment: # This links the job to the GitHub Environment based on the branch name
      name: ${{ github.ref_name == 'main' && 'main' || github.ref_name == 'develop' && 'develop' || '' }}
    #   # The deployment URL will be your Heroku app URL
    
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20.x'
      
      - name: Install pnpm
        run: npm install -g pnpm@10 # Consistent pnpm version
      
      - name: Install server dependencies
        run: pnpm install
        working-directory: ./server
        
      - name: Install Heroku CLI
        run: npm install -g heroku
      
      - name: Build server
        run: pnpm run build
        working-directory: ./server
        env:
          # NODE_ENV for the build process itself if needed
          NODE_ENV: production
          DATABASE_URL: ${{ secrets.DATABASE_URL_AWS }} 
      - name: Deploy to Heroku
        uses: akhileshns/heroku-deploy@v3.12.14
        with:
          heroku_api_key: ${{ secrets.HEROKU_API_KEY }} # Repository-level secret
          heroku_email: ${{ secrets.HEROKU_EMAIL }} # Repository-level secret
          # This secret (HEROKU_APP_NAME) will be pulled from the active GitHub Environment (main or develop)
          heroku_app_name: ${{ secrets.HEROKU_APP_NAME }}
          appdir: "./server"
          procfile: "server/Procfile" # Assuming Procfile is in server/
          # These variables will be set as Heroku config vars for the deployed app's runtime





























# name: CI/CD Pipeline for Fanseatmaster 

# on:
#   push:
#     branches: [main]
#   pull_request:
#     branches: [main]

# jobs:
#   client-deploy:
#     runs-on: ubuntu-latest
#     steps:
#       - uses: actions/checkout@v3
#       - uses: actions/setup-node@v3
#         with:
#           node-version: '20.x'
#       - name: Install pnpm
#         run: npm install -g pnpm@10
#       - name: Install client dependencies
#         run:  pnpm install
#         working-directory: ./client
#       - name: Run ESLint
#         run: pnpm run lint
#         working-directory: ./client
#       - name: Build client
#         run: pnpm run clean && pnpm run build
#         working-directory: ./client
#       # - name: Install Vercel CLI
     
#         env:
#           NEXT_PUBLIC_API_BASE_URL: ${{ secrets.NEXT_PUBLIC_API_BASE_URL_HEROKU }}
#           NODE_ENV: production
#       - name: Deploy to Vercel
#         uses: BetaHuhn/deploy-to-vercel-action@v1
#         with:
#           GITHUB_TOKEN: ${{ secrets.GH_PAT }}
#           VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN_FANSEATMASTER }}
#           VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
#           VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
        
          

 

#   server-deploy:
#     runs-on: ubuntu-latest
#     steps:
#       - uses: actions/checkout@v3
#       - uses: actions/setup-node@v3
#         with:
#           node-version: '20.x'
#       - name: Install pnpm 9.x
#         run: npm install -g pnpm@9
#       - name: Install server dependencies
#         run: cd server && pnpm install
#       - name: Build server
#         run: cd server && pnpm run build
#       - name: Deploy to Heroku
#         uses: akhileshns/heroku-deploy@v3.12.14
#         with:
#           heroku_api_key: ${{ secrets.HEROKU_API_KEY }}
#           heroku_app_name: ${{ secrets.HEROKU_APP_NAME }}
#           heroku_email: ${{ secrets.HEROKU_EMAIL }}
#           appdir: "./server"
#           procfile: "server/Procfile"
#         env:
#           DATABASE_URL: ${{ secrets.DATABASE_URL_AWS }} 