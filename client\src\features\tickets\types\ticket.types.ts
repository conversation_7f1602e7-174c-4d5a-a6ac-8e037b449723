// Types for visitor tickets feature
// Matched with backend structure

/**
 * Enum matching the server's CheckoutSessionStatus
 * Defined directly in client code instead of importing from Prisma
 */
export enum CheckoutSessionStatus {
  PENDING = 'PENDING',
  RESERVED = 'RESERVED',
  PROCESSING = 'PROCESSING',
  EXPIRED = 'EXPIRED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  FAILED = 'FAILED',
  ACTIVE = 'ACTIVE'
}

/**
 * Represents a single ticket item within a purchase
 */
export interface PurchasedTicketItem {
  inventoryId: string;
  quantity: number;
  name: string;
  price: number;
  section?: string;
  row?: string | number;
}

/**
 * Represents a complete ticket purchase (CheckoutSession)
 */
export interface VisitorTicketDTO {
  checkoutSessionId: string;
  eventId: string;
  eventName: string;
  eventDate: string; // ISO string format
  eventVenue: string;
  eventCity: string;
  eventCountry: string;
  eventImageUrl?: string | null;
  purchaseDate: string; // ISO string format
  tickets: PurchasedTicketItem[];
  totalAmount: number;
  currency: string;
  status: CheckoutSessionStatus;
  receiptUrl?: string | null;
  downloadUrl?: string | null;
}

/**
 * API response with pagination for tickets
 */
export interface VisitorTicketsResponse {
  data: VisitorTicketDTO[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

/**
 * Info for downloading a ticket
 */
export interface TicketDownloadInfo {
  success: boolean;
  message: string;
  downloadUrl?: string | null;
}

/**
 * Filter options for tickets list
 */
export interface TicketFilterOptions {
  sortBy: 'date' | 'eventName' | 'price';
  sortOrder: 'asc' | 'desc';
  dateRange?: {
    start: Date | null;
    end: Date | null;
  };
}

/**
 * Hook return type
 */
export interface UseVisitorTicketsResult {
  tickets: VisitorTicketDTO[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => Promise<any>;
  goToPage: (page: number) => void;
  updateFilterOptions: (options: Partial<TicketFilterOptions>) => void;
  requestTicketDownload: (sessionId: string) => Promise<TicketDownloadInfo>;
  filterOptions: TicketFilterOptions;
  isDownloading: boolean;
}