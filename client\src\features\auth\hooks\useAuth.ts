

import { signIn, signOut, useSession } from "next-auth/react";
import { useState } from "react";
import { authApi } from "../api/credentialApi";
import { toast } from "sonner";

// useAuth.register -- form submisson triggers
// authApi.register-- send data to express backend

/*  
UI layer (AuthModal)
Business logic (useAuth)
API communication (authApi)
Authentication state (NextAuth)
*/
import {
  LoginCredentials,
  RegisterCredentials,
} from "../types/credential.types";

export const useAuth = () => {
  const { data: session, status } = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const register = async (credentials: RegisterCredentials) => {
    try {
      setIsLoading(true);
      // Register with backend
      const registerResponse = await authApi.register(credentials);

      // Initialize NextAuth session
      const signInResult = await signIn("credentials", {
        email: credentials.email,
        password: credentials.password,
        action: "register",
        userData: JSON.stringify(registerResponse.user),
        token: registerResponse.token,
        redirect: false,
      });

      if (signInResult?.error) {
        throw new Error(signInResult.error);
      }

      return registerResponse;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (credentials: LoginCredentials) => {
    try {
      setIsLoading(true);

      // Login with backend
      const loginResponse = await authApi.login(credentials);

      // Initialize NextAuth session
      const signInResult = await signIn("credentials", {
        email: credentials.email,
        password: credentials.password,
        action: "login",
        userData: JSON.stringify(loginResponse.user),
        token: loginResponse.token,
        redirect: false,
      });

      if (signInResult?.error) {
        toast.error("Authentication failed. Please try again.");
        throw new Error(signInResult.error);
      }

      toast.success("Welcome back!");
      return loginResponse;
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Authentication failed";
      toast.error(errorMessage);
      setError(err as Error);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const socialLogin = async (provider: string) => {
    try {
      setIsLoading(true);
      await signIn(provider, { redirect: false });
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    session,
    isAuthenticated: status === "authenticated",
    isLoading: status === "loading" || isLoading,
    error,
    login,
    register,
    socialLogin,
    logout: () => signOut(),
  };
};
