
export interface Event {
    id: number
    title: string
    date: string
    location: string
    imageUrl: string
    features: string[]
    category: string[]
  }
  
  export interface Category {
    name: string
    color: string
  }


  export interface TmEvent {
    id: string;
    name: string;
    type: string;
    url: string | null;
    locale: string | null;
    primaryImage: string | null;
    startDateTime: Date | null;
    endDateTime: Date | null;
    timezone: string | null;
    status: string | null;
    segment: string | null;
    genre: string | null;
    subGenre: string | null;
    venueName: string | null;
    venueCity: string | null;
    venueState: string | null;
    venueCountry: string | null;
    priceRangeMin: number | null;
    priceRangeMax: number | null;
    currency: string | null;
}

  export interface WorkflowConfig {
    defaultQuery?: string;
    dataSource?: 'database' | 'ticketmaster';
}