/**
 * Checkout Background Job Service
 * 
 * Provides automated background processing for checkout sessions:
 * - Marks expired sessions as EXPIRED
 * - Cleans up old sessions (optional)
 */

import cron from 'node-cron';
import { PrismaClient, CheckoutSessionStatus } from '@prisma/client';
import { NODE_ENV } from '@/constants';
// Import centralized schedules
import { CRON_SCHEDULES, CRON_JOB_NAMES } from '@/config/cronJob.config';

const prisma = new PrismaClient();

// Configuration constants for session cleanup
const CLEANUP_DAYS = {
    EXPIRED: 30,     // Keep expired sessions for 30 days
    CANCELLED: 30,   // Keep cancelled sessions for 30 days
    FAILED: 30,      // Keep failed sessions for 30 days
    PENDING: 7,      // Keep pending sessions for 7 days
    RESERVED: 7,     // Keep reserved (but not expired) sessions for 7 days
};

// Add to CRON_SCHEDULES in your config file if not already there:
// checkoutCleanup: '0 0 * * *', // Run cleanup once daily at midnight

class CheckoutBackgroundJobService {
    private expirationTask: cron.ScheduledTask | null = null;
    private cleanupTask: cron.ScheduledTask | null = null;

    /**
     * Task that finds and updates expired checkout sessions
     */
    private async expireCheckoutSessionsTask(): Promise<void> {
        if (NODE_ENV === 'development') {
            // Use job name from config for logging
            console.log(`⏳ [CheckoutBgJob] Running ${CRON_JOB_NAMES.checkoutExpiration}...`);
        }
        
        try {
            const now = new Date();
            
            // Find all sessions that are RESERVED but have passed their expiresAt time
            const expiredSessions = await prisma.checkoutSession.findMany({
                where: {
                    status: CheckoutSessionStatus.RESERVED,
                    expiresAt: { lt: now }
                },
                select: {
                    id: true,
                    expiresAt: true,
                    userId: true
                }
            });

            if (expiredSessions.length === 0) {
                if (NODE_ENV === 'development') {
                    console.log('ℹ️ [CheckoutBgJob] No expired checkout sessions found.');
                }
                return;
            }

            if (NODE_ENV === 'development') {
                console.log(`🔍 [CheckoutBgJob] Found ${expiredSessions.length} expired checkout sessions.`);
            }

            // Update all expired sessions in a single batch operation
            const updateResult = await prisma.checkoutSession.updateMany({
                where: {
                    id: { in: expiredSessions.map(session => session.id) },
                    status: CheckoutSessionStatus.RESERVED // Double-check to avoid race conditions
                },
                data: {
                    status: CheckoutSessionStatus.EXPIRED,
                    updatedAt: now,
                    statusMessage: 'Automatically expired by system'
                }
            });

            if (NODE_ENV === 'development') {
                console.log(`✅ [CheckoutBgJob] Updated ${updateResult.count} expired checkout sessions.`);
                
                // Log discrepancy if any
                if (updateResult.count !== expiredSessions.length) {
                    console.log(`⚠️ [CheckoutBgJob] Note: ${expiredSessions.length - updateResult.count} sessions were not updated (may have changed status concurrently).`);
                }
            }
        } catch (error) {
            console.error('❌ [CheckoutBgJob] Error during checkout expiration check:', error);
        }
    }

    /**
     * Task that cleans up old checkout sessions from the database
     * Preserves COMPLETED sessions for reporting/auditing purposes
     * Only removes old sessions with statuses EXPIRED, CANCELLED, FAILED, etc.
     */
    private async cleanupOldSessionsTask(): Promise<void> {
        if (NODE_ENV === 'development') {
            console.log(`🧹 [CheckoutBgJob] Running checkout session cleanup...`);
        }

        try {
            const now = new Date();
            const cleanupStats = {
                total: 0,
                byStatus: {} as Record<string, number>
            };

            // Process each status type separately to apply appropriate retention policies
            for (const [status, retentionDays] of Object.entries(CLEANUP_DAYS)) {
                // Skip if retentionDays is set to 0 (keep forever)
                if (retentionDays <= 0) continue;

                // Calculate cutoff date based on retention days
                const cutoffDate = new Date(now);
                cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

                // Delete records older than the cutoff date for this status
                // Note: For RESERVED status, we only delete those that somehow weren't caught by the expiration job
                const result = await prisma.checkoutSession.deleteMany({
                    where: {
                        status: status as CheckoutSessionStatus,
                        updatedAt: { lt: cutoffDate },
                        // Don't delete COMPLETED sessions
                        NOT: {
                            status: CheckoutSessionStatus.COMPLETED
                        }
                    }
                });

                // Track statistics
                cleanupStats.total += result.count;
                cleanupStats.byStatus[status] = result.count;

                // Log detailed results in development
                if (NODE_ENV === 'development' && result.count > 0) {
                    console.log(`🗑️ [CheckoutBgJob] Deleted ${result.count} ${status} sessions older than ${retentionDays} days.`);
                }
            }

            // Log summary
            if (cleanupStats.total > 0) {
                console.log(`✅ [CheckoutBgJob] Cleanup complete. Removed ${cleanupStats.total} sessions total.`);
                console.log(`📊 [CheckoutBgJob] Breakdown by status:`, cleanupStats.byStatus);
            } else if (NODE_ENV === 'development') {
                console.log(`ℹ️ [CheckoutBgJob] No sessions met cleanup criteria.`);
            }
        } catch (error) {
            console.error('❌ [CheckoutBgJob] Error during checkout session cleanup:', error);
        }
    }

    /**
     * Starts the background jobs
     */
    public start(): void {
        if (NODE_ENV === 'development') {
            console.log('🚀 [CheckoutBgJob] Starting CHECKOUT background job service...');
        }

        // Start checkout session expiration task using centralized schedule
        this.expirationTask = cron.schedule(CRON_SCHEDULES.checkoutExpiration, () => {
            this.expireCheckoutSessionsTask().catch(err => {
                console.error(`❌ [CheckoutBgJob] Unhandled error in ${CRON_JOB_NAMES.checkoutExpiration} task:`, err);
            });
        });

        // Start checkout session cleanup task (daily at midnight)
        // Uses 'checkoutCleanup' schedule or falls back to daily if not defined
        const cleanupSchedule = CRON_SCHEDULES.checkoutCleanup || '0 0 * * *';
        this.cleanupTask = cron.schedule(cleanupSchedule, () => {
            this.cleanupOldSessionsTask().catch(err => {
                console.error(`❌ [CheckoutBgJob] Unhandled error in cleanup task:`, err);
            });
        });

        if (NODE_ENV === 'development') {
            console.log('✅ [CheckoutBgJob] CHECKOUT background jobs started successfully.');
            console.log(`ℹ️ [CheckoutBgJob] Expiration check schedule: ${CRON_SCHEDULES.checkoutExpiration}`);
            console.log(`ℹ️ [CheckoutBgJob] Cleanup schedule: ${cleanupSchedule}`);
        }
    }

    /**
     * Stops all background jobs
     */
    public stop(): void {
        if (this.expirationTask) {
            this.expirationTask.stop();
            this.expirationTask = null;
        }
        
        if (this.cleanupTask) {
            this.cleanupTask.stop();
            this.cleanupTask = null;
        }

        if (NODE_ENV === 'development') {
            console.log('🛑 [CheckoutBgJob] CHECKOUT background jobs stopped.');
        }
    }
}

// Create and export a singleton instance
export const checkoutBackgroundJobService = new CheckoutBackgroundJobService();