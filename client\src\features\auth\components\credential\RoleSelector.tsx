import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Ticket, Store, Shield } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface RoleSelectorProps {
  selectedRole: string;
  onRoleSelect: (role: string) => void;
}

export const RoleSelector = ({ selectedRole, onRoleSelect }: RoleSelectorProps) => {
  // Unchanged role IDs to match backend schema
  const roles = [
    { 
      id: 'VISITOR', 
      label: 'Visitor', 
      icon: Ticket, 
      description: 'Buy tickets', 
      accentColor: 'border-emerald-400 text-emerald-300', 
      tooltip: 'Access event tickets, manage your bookings, and track your attendance history' 
    },
    { 
      id: 'MANAGER', 
      label: 'Manager', 
      icon: Store, 
      description: 'Sell tickets', 
      accentColor: 'border-blue-400 text-blue-300', 
      tooltip: 'Create and manage events, sell tickets, and track sales performance' 
    },
    { 
      id: 'ADMIN', 
      label: 'Admin', 
      icon: Shield, 
      description: 'Handle operations', 
      accentColor: 'border-purple-400 text-purple-300', 
      tooltip: 'Full platform access with ability to manage users, events, and system settings' 
    },
  ];

  return (
    <TooltipProvider>
      <motion.div 
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col space-y-4"
      >
        <label className="text-sm font-medium text-blue-200">
          Select your role:
        </label>
        
        <div className="grid grid-cols-1 gap-4">
          {roles.map(({ id, label, icon: Icon, description, accentColor, tooltip }) => {
            const isSelected = selectedRole === id;
            
            return (
              <Tooltip key={id}>
                <TooltipTrigger asChild>
                  <motion.div
                    whileHover={{ scale: 1.01 }}
                    whileTap={{ scale: 0.98 }}
                    transition={{ duration: 0.2 }}
                    className="w-full"
                  >
                    <Button
                      type="button"
                      variant="ghost"
                      onClick={() => onRoleSelect(id)}
                      className={`
                        w-full py-6 px-5 rounded-xl 
                        transition-all duration-300
                        ${isSelected 
                          ? `border-2 ${accentColor.split(' ')[0]} bg-white/5` 
                          : 'border border-white/10 '}
                      `}

                    >
                      <div className="flex items-center gap-4 w-full">
                        <motion.div
                          animate={{ 
                            rotate: isSelected ? [0, -5, 5, 0] : 0,
                          }}
                          transition={{ duration: 0.5 }}
                          className={`
                            p-3 rounded-lg flex-shrink-0 border
                            ${isSelected 
                              ? `${accentColor}` 
                              : 'border-white/20 text-white/70'}
                          `}
                        >
                          <Icon className="w-5 h-5" />
                        </motion.div>
                        
                        <div className="flex-1 text-left">
                          <span className={`font-semibold block ${isSelected ? accentColor.split(' ')[1] : 'text-white'}`}>
                            {label}
                          </span>
                          <span className="text-sm mt-1 block text-white/70">
                            {description}
                          </span>
                        </div>
                        
                        {isSelected && (
                          <motion.div 
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="flex items-center gap-2"
                          >
                            <div className={`w-2.5 h-2.5 rounded-full ${accentColor.split(' ')[1]}`}></div>
                          </motion.div>
                        )}
                      </div>
                    </Button>
                  </motion.div>
                </TooltipTrigger>
                <TooltipContent 
                  side="right"
                  className="max-w-[200px] p-3 text-xs bg-primary/90 backdrop-blur-sm border-white/20"
                >
                  <p>{tooltip}</p>
                </TooltipContent>
              </Tooltip>
            );
          })}
        </div>
      </motion.div>
    </TooltipProvider>
  );
};
