import winston from 'winston';
import { NODE_ENV } from '@/constants';
import { LOGGER_CONFIG } from './config';
import { consoleTransport } from './transports/console.transport';
import { fileTransports } from './transports/file.transport';

/**
 * Main logger instance
 */
const logger = winston.createLogger({
  level: LOGGER_CONFIG.level,
  levels: LOGGER_CONFIG.levels,
  defaultMeta: {
    service: 'fanseatmaster-api',
    environment: NODE_ENV,
    timestamp: new Date().toISOString()
  },
  transports: [
    consoleTransport,
    ...(NODE_ENV === 'production' ? fileTransports : [])
  ],
  exitOnError: false
});

/**
 * Service-specific logger creator
 */
export const createServiceLogger = (serviceName: string) => {
  return {
    error: (message: string, meta?: any) => 
      logger.error(message, { service: serviceName, ...meta }),
    
    warn: (message: string, meta?: any) => 
      logger.warn(message, { service: serviceName, ...meta }),
    
    info: (message: string, meta?: any) => 
      logger.info(message, { service: serviceName, ...meta }),
    
    http: (message: string, meta?: any) => 
      logger.http(message, { service: serviceName, ...meta }),
    
    debug: (message: string, meta?: any) => 
      logger.debug(message, { service: serviceName, ...meta }),
    
    verbose: (message: string, meta?: any) => 
      logger.verbose(message, { service: serviceName, ...meta })
  };
};

export default logger;
export { logger };