import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import DashboardWrapper from "./dashboardWrapper";
import Providers from "@/providers/Providers";
import { Toaster } from "sonner";
import { InitialDataProvider } from "@/providers/InitialDataProvider";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "fanseatmaster",
  description: "Book your ticket online and get a seat and enjoy the event",
};

// Root layout component for the application
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {


  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className} suppressHydrationWarning>
        <Providers>
          <DashboardWrapper>
            <InitialDataProvider>{children}</InitialDataProvider>
          </DashboardWrapper>

          <Toaster richColors position="top-center" />
        </Providers>
      </body>
    </html>
  );
}
