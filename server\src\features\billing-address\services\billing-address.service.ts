// server/src/features/billing-address/services/billing-address.service.ts
import { prisma } from '@/lib/prisma';
import ApiError from '@/utils/ApiError';
import { Prisma, BillingAddress } from '@prisma/client';
import { CreateBillingAddressPayload, UpdateBillingAddressPayload } from '../types/billing-address.types';

export class BillingAddressService {
  /**
   * Get all billing addresses for a user.
   */
  static async getAllByUserId(userId: string): Promise<BillingAddress[]> {
    console.log(`🛍️ [BillingAddressService] Fetching addresses for user: ${userId}`);
    return prisma.billingAddress.findMany({
      where: { userId },
      orderBy: { isDefault: 'desc' }, // Show default address first
    });
  }

  /**
   * Get a single billing address by ID, ensuring it belongs to the user.
   */
  static async getByIdAndUserId(id: string, userId: string): Promise<BillingAddress | null> {
    console.log(`🛍️ [BillingAddressService] Fetching address by ID: ${id} for user: ${userId}`);
    const address = await prisma.billingAddress.findUnique({
      where: { id },
    });
    if (address && address.userId !== userId) {
      throw new ApiError(403, 'Access denied to this billing address.');
    }
    return address;
  }

  /**
   * Create a new billing address for a user.
   */
  static async create(userId: string, payload: CreateBillingAddressPayload): Promise<BillingAddress> {
    console.log(`🛍️ [BillingAddressService] Creating address for user: ${userId}`, payload);
    return prisma.$transaction(async (tx) => {
      if (payload.isDefault) {
        // Unset other default addresses for this user
        await tx.billingAddress.updateMany({
          where: { userId, isDefault: true },
          data: { isDefault: false },
        });
      }
      const newAddress = await tx.billingAddress.create({
        data: {
          ...payload,
          userId,
        },
      });
      // If no other addresses, make this one default
      const count = await tx.billingAddress.count({ where: { userId } });
      if (count === 1 && !newAddress.isDefault) {
        return tx.billingAddress.update({
          where: { id: newAddress.id },
          data: { isDefault: true }
        });
      }
      return newAddress;
    });
  }

  /**
   * Update an existing billing address.
   */
  static async update(id: string, userId: string, payload: UpdateBillingAddressPayload): Promise<BillingAddress> {
    console.log(`🛍️ [BillingAddressService] Updating address ID: ${id} for user: ${userId}`, payload);
    const existingAddress = await this.getByIdAndUserId(id, userId);
    if (!existingAddress) {
      throw new ApiError(404, 'Billing address not found.');
    }

    return prisma.$transaction(async (tx) => {
      if (payload.isDefault === true && !existingAddress.isDefault) {
        // Unset other default addresses for this user
        await tx.billingAddress.updateMany({
          where: { userId, isDefault: true, NOT: { id } },
          data: { isDefault: false },
        });
      }
      // If unsetting default, ensure at least one default exists if there are other addresses
      else if (payload.isDefault === false && existingAddress.isDefault) {
        const otherAddressesCount = await tx.billingAddress.count({
          where: { userId, NOT: { id } },
        });
        if (otherAddressesCount > 0) {
            const currentDefault = await tx.billingAddress.findFirst({
                where: { userId, isDefault: true, NOT: {id} }
            });
            if(!currentDefault) { // if unsetting the only default, and others exist, make another default
                const nextDefault = await tx.billingAddress.findFirst({
                    where: { userId, NOT: {id}},
                    orderBy: { createdAt: 'asc'}
                });
                if(nextDefault) {
                     await tx.billingAddress.update({
                        where: { id: nextDefault.id },
                        data: { isDefault: true }
                    });
                }
            }
        } else { // if it's the only address, it must remain default
            payload.isDefault = true; // Force it to remain default
             console.log(`ℹ️ [BillingAddressService] Cannot unset default for the only address. ID: ${id}`);
        }
      }


      return tx.billingAddress.update({
        where: { id },
        data: payload,
      });
    });
  }

  /**
   * Delete a billing address.
   */
  static async delete(id: string, userId: string): Promise<BillingAddress> {
    console.log(`🛍️ [BillingAddressService] Deleting address ID: ${id} for user: ${userId}`);
    const existingAddress = await this.getByIdAndUserId(id, userId);
    if (!existingAddress) {
      throw new ApiError(404, 'Billing address not found.');
    }
    if (existingAddress.isDefault) {
      throw new ApiError(400, 'Cannot delete the default billing address. Set another address as default first.');
    }
    return prisma.billingAddress.delete({
      where: { id },
    });
  }

  /**
   * Set a billing address as default for the user.
   */
  static async setDefault(id: string, userId: string): Promise<BillingAddress> {
    console.log(`🛍️ [BillingAddressService] Setting address ID: ${id} as default for user: ${userId}`);
    const addressToSetDefault = await this.getByIdAndUserId(id, userId);
    if (!addressToSetDefault) {
      throw new ApiError(404, 'Billing address not found.');
    }

    return prisma.$transaction(async (tx) => {
      // Unset previous default address
      await tx.billingAddress.updateMany({
        where: { userId, isDefault: true, NOT: { id } },
        data: { isDefault: false },
      });
      // Set new default address
      return tx.billingAddress.update({
        where: { id },
        data: { isDefault: true },
      });
    });
  }
}