/**
 * AdminEventsTab Component
 * 
 * Displays event management functionality for administrators.
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Search, Calendar, Filter, MoreHorizontal, 
  PlusCircle, MapPin, Users, DollarSign, Clock, Eye,
  CheckCircle, AlertCircle, Clock3
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface AdminEventsTabProps {
  isCurrentUser: boolean;
}

export function AdminEventsTab({ isCurrentUser }: AdminEventsTabProps) {
  // Sample events data
  const events = [
    {
      id: 1,
      name: 'Tech Conference 2023',
      date: 'Jun 15, 2023',
      location: 'San Francisco, CA',
      status: 'active',
      attendees: 324,
      revenue: '$15,750',
      manager: '<PERSON> Cooper'
    },
    {
      id: 2,
      name: 'Product Launch',
      date: 'Aug 22, 2023',
      location: 'New York, NY',
      status: 'upcoming',
      attendees: 156,
      revenue: '$8,200',
      manager: '<PERSON>'
    },
    {
      id: 3,
      name: 'Annual Charity Gala',
      date: 'Dec 10, 2023',
      location: 'Chicago, IL',
      status: 'planning',
      attendees: 0,
      revenue: '$0',
      manager: 'Sarah Johnson'
    },
    {
      id: 4,
      name: 'Marketing Workshop',
      date: 'Apr 05, 2023',
      location: 'Virtual',
      status: 'completed',
      attendees: 215,
      revenue: '$4,300',
      manager: 'Robert Chen'
    },
  ];

  // Get appropriate badge style based on status
  const getStatusBadge = (status: string) => {
    switch(status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100"><CheckCircle className="h-3 w-3 mr-1" />Active</Badge>;
      case 'upcoming':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100"><Calendar className="h-3 w-3 mr-1" />Upcoming</Badge>;
      case 'planning':
        return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100"><Clock3 className="h-3 w-3 mr-1" />Planning</Badge>;
      case 'completed':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Completed</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100"><AlertCircle className="h-3 w-3 mr-1" />Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
            <div>
              <CardTitle className="text-xl flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Event Management
              </CardTitle>
              <CardDescription>
                Manage events across the platform
              </CardDescription>
            </div>
            
            {isCurrentUser && (
              <Button className="w-full sm:w-auto">
                <PlusCircle className="h-4 w-4 mr-2" />
                Create New Event
              </Button>
            )}
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-4">
            {/* Search and Filter */}
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input 
                  placeholder="Search events..." 
                  className="pl-8"
                />
              </div>
              
              <div className="flex gap-2">
                <Select defaultValue="all-status">
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all-status">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="upcoming">Upcoming</SelectItem>
                    <SelectItem value="planning">Planning</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select defaultValue="all-locations">
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Filter by location" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all-locations">All Locations</SelectItem>
                    <SelectItem value="san-francisco">San Francisco</SelectItem>
                    <SelectItem value="new-york">New York</SelectItem>
                    <SelectItem value="chicago">Chicago</SelectItem>
                    <SelectItem value="virtual">Virtual</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            {/* Events List */}
            <div className="rounded-md border">
              <div className="relative overflow-x-auto">
                <table className="w-full text-sm text-left">
                  <thead className="bg-muted/50 text-muted-foreground">
                    <tr>
                      <th scope="col" className="px-4 py-3 font-medium">Event</th>
                      <th scope="col" className="px-4 py-3 font-medium">Status</th>
                      <th scope="col" className="px-4 py-3 font-medium">Attendees</th>
                      <th scope="col" className="px-4 py-3 font-medium">Revenue</th>
                      <th scope="col" className="px-4 py-3 font-medium">Manager</th>
                      <th scope="col" className="px-4 py-3 font-medium text-right">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    {events.map((event) => (
                      <tr key={event.id} className="bg-card hover:bg-muted/50 transition-colors">
                        <td className="px-4 py-3">
                          <div className="flex flex-col">
                            <div className="font-medium">{event.name}</div>
                            <div className="text-xs text-muted-foreground flex items-center mt-1">
                              <Calendar className="h-3 w-3 mr-1" />
                              {event.date}
                            </div>
                            <div className="text-xs text-muted-foreground flex items-center mt-0.5">
                              <MapPin className="h-3 w-3 mr-1" />
                              {event.location}
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3">{getStatusBadge(event.status)}</td>
                        <td className="px-4 py-3">
                          <div className="flex items-center">
                            <Users className="h-3.5 w-3.5 mr-1.5 text-muted-foreground" />
                            {event.attendees}
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex items-center">
                            <DollarSign className="h-3.5 w-3.5 mr-1.5 text-muted-foreground" />
                            {event.revenue}
                          </div>
                        </td>
                        <td className="px-4 py-3 text-muted-foreground">{event.manager}</td>
                        <td className="px-4 py-3 text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <Eye className="h-4 w-4" />
                              <span className="sr-only">View</span>
                            </Button>
                            
                            {isCurrentUser && (
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="h-8 w-8">
                                    <MoreHorizontal className="h-4 w-4" />
                                    <span className="sr-only">Actions</span>
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem>Edit Event</DropdownMenuItem>
                                  <DropdownMenuItem>Manage Attendees</DropdownMenuItem>
                                  <DropdownMenuItem>View Analytics</DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  {event.status === 'active' || event.status === 'upcoming' ? (
                                    <DropdownMenuItem className="text-amber-600">Cancel Event</DropdownMenuItem>
                                  ) : (
                                    <DropdownMenuItem className="text-blue-600">Reactivate Event</DropdownMenuItem>
                                  )}
                                  <DropdownMenuItem className="text-destructive">Delete Event</DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </CardContent>
        
        <CardFooter className="border-t pt-6 flex justify-between">
          <div className="text-sm text-muted-foreground">
            Showing <span className="font-medium">{events.length}</span> of <span className="font-medium">{events.length}</span> events
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" size="sm" disabled>Previous</Button>
            <Button variant="outline" size="sm" disabled>Next</Button>
          </div>
        </CardFooter>
      </Card>
      
      {/* Event Statistics Card */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Event Statistics</CardTitle>
          <CardDescription>Overview of events and activity</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-muted/20 p-4 rounded-lg">
              <div className="text-2xl font-bold">42</div>
              <div className="text-sm text-muted-foreground">Total Events</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-700">8</div>
              <div className="text-sm text-green-700/70">Active Events</div>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-700">12</div>
              <div className="text-sm text-blue-700/70">Upcoming Events</div>
            </div>
            <div className="bg-amber-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-amber-700">$56.8K</div>
              <div className="text-sm text-amber-700/70">Monthly Revenue</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
