'use client'

import { getRoleSpecificSettingsComponent } from '@/features/settings/utils/settings-resolver'

export default function DynamicSettingsPage() {
  // Hardcode the role since this is the manager route
  const role = 'manager'
  const SettingsComponent = getRoleSpecificSettingsComponent(role)

  return (
    <div className="container mx-auto py-6 px-4 min-h-screen">
      <h1 className="text-3xl font-semibold mb-2">
        Manager Settings
      </h1>
      <div className="m-8">
        <SettingsComponent />
      </div>
    </div>
  )
}
