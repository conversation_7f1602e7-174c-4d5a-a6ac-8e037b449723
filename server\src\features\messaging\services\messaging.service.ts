/**
 * Messaging Service
 * 
 * Core business logic for the ticket messaging system.
 * Handles all CRUD operations for messages and conversations
 * between Visitors, Managers, and Admin oversight.
 */

import { PrismaClient, UserRole, MessageStatus, CheckoutSessionStatus } from '@prisma/client';
import { prisma } from '@/lib/prisma';
import ApiError from '@/utils/ApiError';
import {
  SendMessageRequest,
  GetConversationRequest,
  AdminMessagesRequest,
  MessageDTO,
  ConversationDTO,
  AdminConversationOverviewDTO,
  TicketMessageWithRelations,
  GetConversationResponse,
  AdminConversationsResponse,
  IMessagingService
} from '../types/messaging.types';

export class MessagingService implements IMessagingService {

  // ============================================================================
  // CORE MESSAGING OPERATIONS
  // ============================================================================

  /**
   * Send a new message in a ticket conversation
   */
  async sendMessage(userId: string, data: SendMessageRequest): Promise<MessageDTO> {
    try {
      // Validate that the checkout session exists and user has access
      const checkoutSession = await this.validateUserAccess(userId, data.checkoutSessionId);
      
      // Get user role for validation
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, role: true, fullName: true }
      });

      if (!user) {
        throw new ApiError(404, 'User not found');
      }

      // Generate conversation ID if not provided (use checkout session ID as base)
      const conversationId = data.conversationId || `conv_${data.checkoutSessionId}`;

      // Create the message - ✅ FIXED: Include all required relations
      const newMessage = await prisma.ticketMessage.create({
        data: {
          checkoutSessionId: data.checkoutSessionId,
          senderId: userId,
          senderRole: user.role,
          message: data.message.trim(),
          conversationId,
          metadata: data.ipAddress || data.userAgent ? {
            ipAddress: data.ipAddress,
            userAgent: data.userAgent
          } : undefined,
          parentMessageId: data.parentMessageId || undefined,
        },
        include: {
          sender: {
            select: {
              id: true,
              role: true,
              fullName: true,
              email: true,
            }
          },
          checkoutSession: {
            select: {
              id: true,
              userId: true,
              eventId: true,
              status: true,
              createdAt: true,
              user: {
                select: {
                  id: true,
                  fullName: true,
                  email: true,
                }
              }
            }
          },
          escalatedByUser: {
            select: {
              id: true,
              fullName: true,
              role: true,
            }
          }
        }
      });

      return this.transformMessageToDTO(newMessage);

    } catch (error) {
      console.error('❌ [MessagingService] Error sending message:', error);
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, 'Failed to send message');
    }
  }

  /**
   * Get all messages in a conversation
   */
  async getConversation(
    userId: string, 
    checkoutSessionId: string, 
    options: GetConversationRequest = {}
  ): Promise<ConversationDTO> {
    try {
      // Validate user access to this conversation
      await this.validateUserAccess(userId, checkoutSessionId);

      const { page = 1, limit = 50, includeResolved = true } = options;
      const skip = (page - 1) * limit;

      // Build status filter
      const statusFilter: MessageStatus[] = includeResolved 
        ? [MessageStatus.UNREAD, MessageStatus.READ, MessageStatus.RESOLVED]
        : [MessageStatus.UNREAD, MessageStatus.READ];

      // Get messages with pagination
      const [messages, totalCount] = await Promise.all([
        prisma.ticketMessage.findMany({
          where: {
            checkoutSessionId,
            status: { in: statusFilter }
          },
          include: {
            sender: {
              select: {
                id: true,
                role: true,
                fullName: true,
                email: true,
              }
            },
            checkoutSession: {
              select: {
                id: true,
                userId: true,
                eventId: true,
                status: true,
                createdAt: true,
                user: {
                  select: {
                    id: true,
                    fullName: true,
                    email: true,
                  }
                }
              }
            },
            escalatedByUser: {
              select: {
                id: true,
                fullName: true,
                role: true,
              }
            }
          },
          orderBy: { createdAt: 'asc' }, // Chronological order for conversation
          skip,
          take: limit,
        }),
        prisma.ticketMessage.count({
          where: {
            checkoutSessionId,
            status: { in: statusFilter }
          }
        })
      ]);

      // Get unread count for this user
      const unreadCount = await prisma.ticketMessage.count({
        where: {
          checkoutSessionId,
          status: MessageStatus.UNREAD,
          senderId: { not: userId } // Don't count own messages as unread
        }
      });

      // Get participant info
      const participants = await this.getConversationParticipants(checkoutSessionId, messages);

      return {
        checkoutSessionId,
        ticketId: checkoutSessionId.substring(0, 8) + '...',
        eventName: `Event ${messages[0]?.checkoutSession.eventId || 'Unknown'}`,
        buyerName: participants.visitor.name,
        managerName: participants.manager?.name,
        messages: messages.map(msg => this.transformMessageToDTO(msg)),
        unreadCount,
        lastMessageDate: messages[messages.length - 1]?.createdAt.toISOString() || new Date().toISOString(),
        status: this.getConversationStatus(messages, unreadCount),
        participants
      };

    } catch (error) {
      console.error('❌ [MessagingService] Error getting conversation:', error);
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, 'Failed to retrieve conversation');
    }
  }

  /**
   * Mark a specific message as read
   */
  async markAsRead(userId: string, messageId: string): Promise<void> {
    try {
      // Get the message to validate access
      const message = await prisma.ticketMessage.findUnique({
        where: { id: messageId },
        include: { checkoutSession: true }
      });

      if (!message) {
        throw new ApiError(404, 'Message not found');
      }

      // Validate user access to this conversation
      await this.validateUserAccess(userId, message.checkoutSessionId);

      // Only mark as read if user is NOT the sender (can't mark own messages as read)
      if (message.senderId !== userId) {
        await prisma.ticketMessage.update({
          where: { id: messageId },
          data: { 
            status: MessageStatus.READ,
            updatedAt: new Date()
          }
        });
      }

    } catch (error) {
      console.error('❌ [MessagingService] Error marking message as read:', error);
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, 'Failed to mark message as read');
    }
  }

  /**
   * Mark entire conversation as resolved
   */
  async markConversationAsResolved(userId: string, checkoutSessionId: string): Promise<void> {
    try {
      // Validate user access
      await this.validateUserAccess(userId, checkoutSessionId);

      // Update all unread/read messages to resolved
      await prisma.ticketMessage.updateMany({
        where: {
          checkoutSessionId,
          status: { in: [MessageStatus.UNREAD, MessageStatus.READ] }
        },
        data: {
          status: MessageStatus.RESOLVED,
          updatedAt: new Date()
        }
      });

    } catch (error) {
      console.error('❌ [MessagingService] Error resolving conversation:', error);
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, 'Failed to resolve conversation');
    }
  }

  // ============================================================================
  // ADMIN OPERATIONS
  // ============================================================================

  /**
   * Get all conversations for admin oversight - ✅ FIXED: Return correct type
   */
  async getAllConversations(options: AdminMessagesRequest = {}): Promise<AdminConversationsResponse['data']> {
    try {
      const { page = 1, limit = 20, status, senderRole, eventId, dateFrom, dateTo } = options;
      const skip = (page - 1) * limit;

      // Build filters
      const whereClause: any = {};
      
      if (status) whereClause.status = status;
      if (senderRole) whereClause.senderRole = senderRole;
      if (eventId) whereClause.checkoutSession = { eventId };
      
      if (dateFrom || dateTo) {
        whereClause.createdAt = {};
        if (dateFrom) whereClause.createdAt.gte = new Date(dateFrom);
        if (dateTo) whereClause.createdAt.lte = new Date(dateTo);
      }

      // Get conversations grouped by checkout session
      const conversations = await prisma.ticketMessage.groupBy({
        by: ['checkoutSessionId'],
        where: whereClause,
        _count: {
          id: true
        },
        _max: {
          createdAt: true
        },
        orderBy: {
          _max: {
            createdAt: 'desc'
          }
        },
        skip,
        take: limit,
      });

      // ✅ FIXED: Return ConversationDTO[] instead of AdminConversationOverviewDTO[]
      const conversationDetails: ConversationDTO[] = await Promise.all(
        conversations.map(async (conv): Promise<ConversationDTO> => {
          // Get full conversation data for admin
          const fullConversation = await this.getConversation('admin', conv.checkoutSessionId, {
            page: 1,
            limit: 100,
            includeResolved: true
          });

          return fullConversation;
        })
      );

      // Get total count for pagination
      const totalConversations = await prisma.ticketMessage.groupBy({
        by: ['checkoutSessionId'],
        where: whereClause,
        _count: { id: true }
      });

      // Calculate stats
      const summary = await this.calculateAdminStats();

      const totalPages = Math.ceil(totalConversations.length / limit);

      return {
        conversations: conversationDetails,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: totalConversations.length,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
        },
        summary,
      };

    } catch (error) {
      console.error('❌ [MessagingService] Error getting admin conversations:', error);
      throw new ApiError(500, 'Failed to retrieve admin conversations');
    }
  }

  /**
   * Escalate a conversation for admin intervention
   */
  async escalateConversation(checkoutSessionId: string, adminId: string): Promise<void> {
    try {
      // Validate admin user
      const admin = await prisma.user.findUnique({
        where: { id: adminId },
        select: { id: true, role: true }
      });

      if (!admin || admin.role !== UserRole.ADMIN) {
        throw new ApiError(403, 'Admin access required');
      }

      // Update conversation status
      await prisma.ticketMessage.updateMany({
        where: { checkoutSessionId },
        data: {
          status: MessageStatus.ESCALATED,
          isEscalated: true,
          escalatedBy: adminId,
          escalatedAt: new Date(),
          updatedAt: new Date()
        }
      });

    } catch (error) {
      console.error('❌ [MessagingService] Error escalating conversation:', error);
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, 'Failed to escalate conversation');
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Validate that a user has access to a specific checkout session
   * Uses the EXACT same logic as the sales service
   */
  private async validateUserAccess(userId: string, checkoutSessionId: string) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, role: true }
    });

    if (!user) {
      throw new ApiError(404, 'User not found');
    }

    const checkoutSession = await prisma.checkoutSession.findUnique({
      where: { id: checkoutSessionId },
      select: { 
        id: true, 
        userId: true, 
        eventId: true,
        status: true,
      }
    });

    if (!checkoutSession) {
      throw new ApiError(404, 'Checkout session not found');
    }

    // Admin can access everything
    if (user.role === UserRole.ADMIN) {
      console.log(`✅ [MessagingService] Admin access granted for session: ${checkoutSessionId}`);
      return checkoutSession;
    }

    // Visitor can access their own checkout sessions
    if (user.role === UserRole.VISITOR && checkoutSession.userId === userId) {
      console.log(`✅ [MessagingService] Visitor access granted - owns session: ${checkoutSessionId}`);
      return checkoutSession;
    }

    // Manager can access checkout sessions that appear in their sales
    if (user.role === UserRole.MANAGER) {
      // 🎯 NEW APPROACH: Directly check if this checkout session would be in manager's sales results
      // This replicates the exact query logic from the sales service
    
      console.log(`🔍 [MessagingService] Checking if manager can access session via sales logic...`);
    
      const salesAccessCheck = await prisma.checkoutSession.findFirst({
        where: {
          id: checkoutSessionId,
          status: 'COMPLETED',
          // Use the same join logic as sales service
          eventId: {
            in: await prisma.managerEvent.findMany({
              where: { 
                managerId: userId,
                approvalStatus: 'APPROVED' // Add this if your sales service filters by approval status
              },
              select: { eventId: true }
            }).then(events => events.map(e => e.eventId))
          }
        },
        include: {
          user: {
            select: { id: true, fullName: true, email: true }
          }
        }
      });

      console.log(`🔍 [MessagingService] Sales access check result:`, {
        managerId: userId,
        checkoutSessionId,
        checkoutEventId: checkoutSession.eventId,
        salesAccessFound: !!salesAccessCheck,
        buyerInfo: salesAccessCheck?.user ? {
          id: salesAccessCheck.user.id,
          name: salesAccessCheck.user.fullName
        } : null
      });

      if (salesAccessCheck) {
        console.log(`✅ [MessagingService] Manager access granted - session appears in sales: ${checkoutSessionId}`);
        return checkoutSession;
      }

      // 🔧 FALLBACK: Try without approval status filter (in case that's the issue)
      console.log(`🔍 [MessagingService] Trying fallback check without approval status...`);
    
      const fallbackCheck = await prisma.checkoutSession.findFirst({
        where: {
          id: checkoutSessionId,
          status: 'COMPLETED',
          eventId: {
            in: await prisma.managerEvent.findMany({
              where: { managerId: userId }, // No approval status filter
              select: { eventId: true }
            }).then(events => events.map(e => e.eventId))
          }
        }
      });

      console.log(`🔍 [MessagingService] Fallback check result:`, {
        managerId: userId,
        checkoutSessionId,
        fallbackFound: !!fallbackCheck
      });

      if (fallbackCheck) {
        console.log(`✅ [MessagingService] Manager access granted via fallback - session found: ${checkoutSessionId}`);
        return checkoutSession;
      }

      // 🔧 FINAL FALLBACK: Check if ANY manager event has this eventId (debug)
      const anyManagerWithEvent = await prisma.managerEvent.findFirst({
        where: { eventId: checkoutSession.eventId },
        select: { managerId: true, eventId: true, name: true, approvalStatus: true }
      });

      console.log(`🔍 [MessagingService] Debug - Who owns this event?`, {
        checkoutEventId: checkoutSession.eventId,
        eventOwner: anyManagerWithEvent ? {
          managerId: anyManagerWithEvent.managerId,
          eventName: anyManagerWithEvent.name,
          approvalStatus: anyManagerWithEvent.approvalStatus,
          isCurrentManager: anyManagerWithEvent.managerId === userId
        } : 'No manager found for this event'
      });
    }

    console.log(`❌ [MessagingService] Access denied:`, {
      userId,
      userRole: user.role,
      checkoutSessionId,
      checkoutEventId: checkoutSession.eventId,
      reason: 'User does not have access to this checkout session'
    });

    throw new ApiError(403, 'Access denied to this conversation');
  }

  /**
   * Transform database message to DTO
   */
  private transformMessageToDTO(message: TicketMessageWithRelations): MessageDTO {
    return {
      id: message.id,
      checkoutSessionId: message.checkoutSessionId,
      senderId: message.senderId,
      senderRole: message.senderRole,
      senderName: this.generateDisplayName(message.senderRole, message.senderId),
      message: message.message,
      status: message.status,
      conversationId: message.conversationId || undefined,
      parentMessageId: message.parentMessageId || undefined,
      isEscalated: message.isEscalated,
      escalatedBy: message.escalatedBy || undefined,
      escalatedAt: message.escalatedAt?.toISOString(),
      createdAt: message.createdAt.toISOString(),
      updatedAt: message.updatedAt.toISOString(),
      metadata: message.metadata as any || undefined,
    };
  }

  /**
   * Generate display name based on user role and ID
   */
  private generateDisplayName(role: UserRole, userId: string): string {
    const shortId = userId.substring(0, 8);
    switch (role) {
      case UserRole.VISITOR:
        return `Buyer #${shortId}`;
      case UserRole.MANAGER:
        return `Seller #${shortId}`;
      case UserRole.ADMIN:
        return `Support #${shortId}`;
      default:
        return `User #${shortId}`;
    }
  }

  /**
   * Get conversation participants
   */
  private async getConversationParticipants(checkoutSessionId: string, messages: TicketMessageWithRelations[]) {
    const visitorMessage = messages.find(msg => msg.senderRole === UserRole.VISITOR);
    const managerMessage = messages.find(msg => msg.senderRole === UserRole.MANAGER);
    const adminMessage = messages.find(msg => msg.senderRole === UserRole.ADMIN);

    return {
      visitor: {
        id: visitorMessage?.senderId || 'unknown',
        name: visitorMessage ? this.generateDisplayName(UserRole.VISITOR, visitorMessage.senderId) : 'Unknown Buyer'
      },
      manager: managerMessage ? {
        id: managerMessage.senderId,
        name: this.generateDisplayName(UserRole.MANAGER, managerMessage.senderId)
      } : { id: 'unknown', name: 'No Seller' },
      admin: adminMessage ? {
        id: adminMessage.senderId,
        name: this.generateDisplayName(UserRole.ADMIN, adminMessage.senderId)
      } : undefined
    };
  }

  /**
   * Get manager display name from messages
   */
  private getManagerDisplayName(messages: any[]): string {
    const managerMessage = messages.find(msg => msg.sender.role === UserRole.MANAGER);
    return managerMessage 
      ? `Seller #${managerMessage.senderId.substring(0, 8)}`
      : 'No Seller';
  }

  /**
   * Determine conversation status based on message content and unread count
   */
  private getConversationStatus(messages: TicketMessageWithRelations[], unreadCount: number): 'ACTIVE' | 'RESOLVED' | 'ESCALATED' {
    if (messages.some(msg => msg.status === MessageStatus.ESCALATED)) {
      return 'ESCALATED';
    }
    if (messages.some(msg => msg.status === MessageStatus.RESOLVED)) {
      return 'RESOLVED';
    }
    return unreadCount > 0 ? 'ACTIVE' : 'RESOLVED';
  }

  /**
   * Determine conversation status for admin view
   */
  private getConversationStatusForAdmin(unreadCount: number): 'ACTIVE' | 'RESOLVED' | 'ESCALATED' {
    return unreadCount > 0 ? 'ACTIVE' : 'RESOLVED';
  }

  /**
   * Calculate admin statistics
   */
  private async calculateAdminStats() {
    const [totalActive, totalResolved, totalEscalated, totalUnread] = await Promise.all([
      prisma.ticketMessage.groupBy({
        by: ['checkoutSessionId'],
        where: { status: { in: [MessageStatus.UNREAD, MessageStatus.READ] } },
        _count: { checkoutSessionId: true }
      }).then(result => result.length),

      prisma.ticketMessage.groupBy({
        by: ['checkoutSessionId'],
        where: { status: MessageStatus.RESOLVED },
        _count: { checkoutSessionId: true }
      }).then(result => result.length),

      prisma.ticketMessage.groupBy({
        by: ['checkoutSessionId'],
        where: { status: MessageStatus.ESCALATED },
        _count: { checkoutSessionId: true }
      }).then(result => result.length),

      prisma.ticketMessage.count({
        where: { status: MessageStatus.UNREAD }
      })
    ]);

    return {
      totalConversations: totalActive + totalResolved + totalEscalated,
      unreadMessages: totalUnread,
      escalatedConversations: totalEscalated,
      activeConversations: totalActive,
    };
  }
}
