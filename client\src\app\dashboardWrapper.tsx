"use client";

// Import necessary dependencies and components
import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import Sidebar from "@/features/navigation/components/Sidebar";
import Navbar from "@/app/(components)/Navbar";
import Footer from "@/app/(components)/Footer";
import { AuthModal } from "@/features/auth/components/credential/AuthModal";
import StoreProvider, { useAppSelector } from "./redux";
import { cn } from "@/lib/utils";
import { WelcomeModal } from "@/components/shared/WelcomeModal";

// DashboardLayout component: Renders the main dashboard structure
const DashboardLayout = ({ children }: { children: React.ReactNode }) => {
  const isSidebarCollapsed = useAppSelector(
    (state) => state.global.isSidebarCollapsed
  );
  const isDarkMode = useAppSelector((state) => state.global.isDarkMode);

  // 🆕 ADD: Sequential modal states
  const { data: session, status } = useSession();
  const [showWelcomeModal, setShowWelcomeModal] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);

  // Effect to handle dark mode changes
  useEffect(() => {
    if (isDarkMode) {
      document.documentElement.classList.add("dark");
      document.documentElement.classList.remove("light");
    } else {
      document.documentElement.classList.add("light");
      document.documentElement.classList.remove("dark");
    }
  }, [isDarkMode]);

  // 🎯 Sequential Modal Logic
  useEffect(() => {
    // Don't show if still loading session
    if (status === 'loading') return;

    // Don't show if user is already authenticated
    if (session?.user) return;

    // Check if user dismissed the prompt recently (15 minutes) 🎯 Changed to 15 minutes
    const dismissed = localStorage.getItem('auth-prompt-dismissed');
    const dismissedTime = dismissed ? parseInt(dismissed) : 0;
    const now = Date.now();
    const fifteenMinutes = 15 * 60 * 1000; // 🎯 15 minutes instead of 12 hours
    
    // Don't show if dismissed recently
    if (now - dismissedTime < fifteenMinutes) return;

    // 🎯 STEP 1: Show welcome modal after 3 seconds
    const welcomeTimer = setTimeout(() => {
      setShowWelcomeModal(true);
    }, 3000);

    return () => clearTimeout(welcomeTimer);
  }, [session, status]);

  // 🎯 Handle Welcome Modal Continue (triggers auth modal)
  const handleWelcomeContinue = () => {
    setShowWelcomeModal(false);
    
    // 🎯 STEP 2: Show auth modal after a short delay
    setTimeout(() => {
      setShowAuthModal(true);
    }, 500); // Small delay for smooth transition
  };

  // 🎯 Handle Welcome Modal Close (dismiss everything)
  const handleWelcomeClose = () => {
    setShowWelcomeModal(false);
    // Remember that user dismissed it for 15 minutes
    localStorage.setItem('auth-prompt-dismissed', Date.now().toString());
  };

  // 🎯 Handle Auth Modal Close
  const handleAuthModalClose = () => {
    setShowAuthModal(false);
    // Remember that user dismissed it for 15 minutes
    localStorage.setItem('auth-prompt-dismissed', Date.now().toString());
  };

  return (
    <div className="flex h-screen overflow-hidden bg-background text-foreground">
      <Sidebar />
      <div className={cn(
        "flex flex-col flex-grow min-h-screen",
        "transition-all duration-300 ease-in-out",
        "md:ml-[64px]",
        !isSidebarCollapsed && "md:ml-64"
      )}>
        <Navbar />
        <main className="flex flex-col flex-grow p-6 overflow-y-auto">
          <div className="flex-grow">
            {children}
          </div>
          <Footer />
        </main>
      </div>

      {/* 🎯 MODAL SEQUENCE */}
      
      {/* Step 1: Welcome Modal */}
      <WelcomeModal 
        open={showWelcomeModal}
        onOpenChange={handleWelcomeClose}
        onContinue={handleWelcomeContinue}
      />

      {/* Step 2: Auth Modal */}
      <AuthModal 
        open={showAuthModal} 
        onOpenChange={handleAuthModalClose}
      />
    </div>
  );
};

// DashboardWrapper component: Wraps the DashboardLayout with StoreProvider
const DashboardWrapper = ({ children }: { children: React.ReactNode }) => {
  return (
    <StoreProvider>
      <DashboardLayout>{children}</DashboardLayout>
    </StoreProvider>
  );
};

export default DashboardWrapper;