import { ManagerTrustLevel } from "../../types/manager.types";

interface TrustLevelBadgeProps {
  level: ManagerTrustLevel;
}

export const TrustLevelBadge: React.FC<TrustLevelBadgeProps> = ({ level }) => {
  const badgeStyles = {
    [ManagerTrustLevel.TRUSTED]: 'bg-emerald-100 text-emerald-800 border-emerald-200',
    [ManagerTrustLevel.REGULAR]: 'bg-blue-100 text-blue-800 border-blue-200',
  };

  return (
    <span className={`
      px-2.5 py-0.5 
      rounded-full 
      text-xs 
      font-medium 
      border 
      ${badgeStyles[level]}
    `}>
      {level}
    </span>
  );
};
