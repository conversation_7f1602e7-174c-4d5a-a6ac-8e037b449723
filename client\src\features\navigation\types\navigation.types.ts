// Navigation types and interfaces for the application

import { LucideIcon } from 'lucide-react'
import { Permission, UserRole } from '@/utils/permissions/types'

interface Badge {
  label: string;
  color: string;
}

interface BadgeConfig {
  [key: string]: Badge;
}

// Defines the structure for navigation items
export interface NavItem {
  label: string
  icon: LucideIcon
  path: string
  permission?: Permission // Optional single permission
  permissions?: Permission[] // Optional array of permissions
  badges?: BadgeConfig
  // Updated: Use UserRole as key type for roleBasedPath
  roleBasedPath?: {
    [key in UserRole]: string
  }
  children?: NavItem[]
}

export interface NavSection {
  title?: string
  items: NavItem[]
}

export interface NavigationConfig {
  default: NavSection[]
  roleSpecific: Record<UserRole, NavSection[]>
}