import React from 'react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ShoppingCart, Loader2, AlertTriangle, UserCheck, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useWaitingRoom } from '../../hooks/useWaitingRoom';
import { WaitingRoomDisplay } from '../waiting-room/WaitingRoomDisplay';
import { VerificationStatusDisplay } from './VerificationStatusDisplay';

interface EventCheckoutFooterProps {
  eventId: string | undefined;
  hasSelections: boolean;
  hasInvalidSelections: boolean;
  canProceedInventory: boolean;
  totalTickets: number;
  totalCost: number;
  animateCost: boolean;
  isProfileLoading: boolean;
  onCheckout: () => void;
  onClose: () => void;
  isCreatingReservation?: boolean;
}

export const EventCheckoutFooter: React.FC<EventCheckoutFooterProps> = ({
  eventId,
  totalTickets,
  totalCost,
  animateCost,
  hasSelections,
  hasInvalidSelections,
  canProceedInventory,
  isProfileLoading,
  onCheckout,
  onClose,
  isCreatingReservation = false,
}) => {
  // Use our custom hook to get queue status
  const { 
    isActive: isQueueActive,
    isAdmitted,
    canProceedToCheckout: canSkipQueue,
    needsToJoin
  } = useWaitingRoom(eventId);
  
  // Determine if checkout is disabled
  const isCheckoutDisabled = !canProceedInventory || isProfileLoading || isCreatingReservation;
  
  // Handle checkout with queue check
  const handleCheckout = () => {
    // If user needs to join queue, this button should be disabled
    // But as a safety check:
    if (isQueueActive && !isAdmitted) {
      return;
    }
    
    // Otherwise proceed with normal checkout
    onCheckout();
  };
  
  return (
    <>
      {/* Only show the waiting room when needed */}
      {isQueueActive && needsToJoin && hasSelections && (
        <WaitingRoomDisplay 
          eventId={eventId ?? ''}
          onContinueToCheckout={onCheckout}
          className="mb-4"
        />
      )}
    
      <div className="border-t bg-gradient-to-r from-background to-muted/20">
        {/* Main Footer Content */}
        <div className="p-4 md:p-5 flex flex-col sm:flex-row justify-between items-center gap-4">
          {/* Left Side: Verification Status & Total Cost */}
          <div className="flex flex-col sm:flex-row items-center gap-4 w-full sm:w-auto">
            {/* Verification Status */}
            <VerificationStatusDisplay />
            
            {/* Vertical Separator on larger screens */}
            <Separator orientation="vertical" className="h-8 hidden sm:block mx-1"/>
            
            {/* Total Cost/Tickets Display */}
            <div className="flex items-center gap-3 w-full sm:w-auto justify-center sm:justify-start">
              {hasSelections ? (
                <>
                  {/* Animated Cart Icon */}
                  <div className="bg-primary/10 p-2 rounded-full">
                    <ShoppingCart className={cn(
                      "h-5 w-5 text-primary transition-transform duration-300",
                      animateCost && "scale-110 rotate-[-5deg]" // Enhanced animation
                    )} />
                  </div>
                  {/* Cost and Ticket Count */}
                  <div>
                    <div className="text-sm text-muted-foreground">
                      {totalTickets} {totalTickets === 1 ? 'ticket' : 'tickets'} selected
                    </div>
                    <div className={cn(
                      "text-xl font-bold transition-all duration-300 flex items-baseline gap-1",
                      animateCost ? "text-primary scale-105" : "text-foreground"
                    )}>
                      <span>${totalCost.toFixed(2)}</span>
                      <span className="text-xs font-normal text-muted-foreground">total</span>
                    </div>
                  </div>
                </>
              ) : (
                // Message when no tickets are selected
                <div className="text-sm text-muted-foreground text-center sm:text-left h-[46px] flex items-center">
                  Select tickets to proceed
                </div>
              )}
            </div>
          </div>

          {/* Right Side: Action Buttons */}
          <div className="flex gap-3 w-full sm:w-auto justify-center sm:justify-end">
            <Button 
              variant="outline" 
              onClick={onClose}
              className="flex-1 sm:flex-initial"
              disabled={isCreatingReservation}
            >
              Close
            </Button>
            
            {/* Checkout Button with dynamic state */}
            <Button 
              disabled={isCheckoutDisabled}
              onClick={handleCheckout}
              className={cn(
                "flex-1 sm:flex-initial transition-all duration-300 w-[150px]",
                !canProceedInventory && hasSelections ? "bg-yellow-500 hover:bg-yellow-600 text-yellow-950" : // Indicate invalid state clearly
                !canProceedInventory && !hasSelections ? "bg-muted text-muted-foreground" : // Standard disabled
                !canSkipQueue ? "bg-amber-500 hover:bg-amber-600 text-amber-950" : // Queue required
                "bg-primary hover:bg-primary/90 text-primary-foreground" // Active state
              )}
              aria-live="polite" // Announce changes for accessibility
            >
              {isCreatingReservation ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Reserving...
                </>
              ) : isProfileLoading ? (
                 <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : !canSkipQueue ? (
                <span className="flex items-center justify-center gap-1.5">
                  <Clock className="h-4 w-4" />
                  <span>Queue Required</span>
                </span>
              ) : (
                <span className="flex items-center justify-center gap-1.5">
                  {/* Show check only when ready and verified */}
                  {!isProfileLoading && canProceedInventory && canSkipQueue && <UserCheck className="h-4 w-4" />}
                  <span>Checkout</span>
                </span>
              )}
            </Button>
          </div>
        </div>
        
        {/* Warning message for invalid selections */}
        {hasSelections && hasInvalidSelections && (
          <div className="px-5 py-2 text-sm text-yellow-900 bg-yellow-400/30 border-t border-yellow-400/50 flex items-center gap-2">
            <AlertTriangle className="h-4 w-4 shrink-0"/>
            <span className="leading-tight">One or more selections conflict with seller requirements. Please review and adjust quantities.</span>
          </div>
        )}
        
        {/* Queue warning message */}
        {isQueueActive && !isAdmitted && hasSelections && (
          <div className="px-5 py-2 text-sm text-amber-900 bg-amber-400/30 border-t border-amber-400/50 flex items-center gap-2">
            <AlertTriangle className="h-4 w-4 shrink-0"/>
            <span className="leading-tight">This event requires joining a waiting room before checkout due to high demand.</span>
          </div>
        )}
      </div>
    </>
  );
};
