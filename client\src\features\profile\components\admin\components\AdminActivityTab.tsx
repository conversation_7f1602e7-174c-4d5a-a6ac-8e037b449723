/**
 * AdminActivityTab Component
 * 
 * Displays system activity, audit logs, and important events for admin users.
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Search, RefreshCw, Clock, User, Calendar, Database,
  Settings, Shield, AlertTriangle, CheckCircle, Lock,
  FileText, Download, Filter, Eye
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface AdminActivityTabProps {
  isCurrentUser: boolean;
}

export function AdminActivityTab({ isCurrentUser }: AdminActivityTabProps) {
  // Sample activity data
  const activities = [
    {
      id: 1,
      action: 'User Account Created',
      details: 'New user "<PERSON>" was created',
      user: '<PERSON>',
      timestamp: '2 hours ago',
      category: 'user',
      severity: 'info'
    },
    {
      id: 2,
      action: 'System Backup Completed',
      details: 'Weekly database backup completed successfully',
      user: 'System',
      timestamp: '6 hours ago',
      category: 'system',
      severity: 'success'
    },
    {
      id: 3,
      action: 'Failed Login Attempts',
      details: 'Multiple failed login attempts detected for user "<EMAIL>"',
      user: 'System',
      timestamp: '12 hours ago',
      category: 'security',
      severity: 'warning'
    },
    {
      id: 4,
      action: 'Event Cancelled',
      details: '"Marketing Workshop - June Edition" was cancelled',
      user: 'Sarah Johnson',
      timestamp: '1 day ago',
      category: 'event',
      severity: 'info'
    },
    {
      id: 5,
      action: 'Permission Changes',
      details: 'Admin privileges granted to "Robert Chen"',
      user: 'Jane Cooper',
      timestamp: '2 days ago',
      category: 'security',
      severity: 'important'
    },
    {
      id: 6,
      action: 'Database Error',
      details: 'Critical error in transaction processing - Error ID: DB-5523',
      user: 'System',
      timestamp: '3 days ago',
      category: 'system',
      severity: 'critical'
    }
  ];

  // Get icon based on category
  const getCategoryIcon = (category: string) => {
    switch(category) {
      case 'user':
        return <User className="h-full w-full" />;
      case 'system':
        return <Database className="h-full w-full" />;
      case 'security':
        return <Lock className="h-full w-full" />;
      case 'event':
        return <Calendar className="h-full w-full" />;
      default:
        return <Settings className="h-full w-full" />;
    }
  };

  // Get background color based on severity
  const getSeverityStyle = (severity: string): {bg: string, text: string, icon: React.ReactNode} => {
    switch(severity) {
      case 'critical':
        return {
          bg: 'bg-red-100',
          text: 'text-red-800',
          icon: <AlertTriangle className="h-3.5 w-3.5 mr-1" />
        };
      case 'warning':
        return {
          bg: 'bg-amber-100',
          text: 'text-amber-800',
          icon: <AlertTriangle className="h-3.5 w-3.5 mr-1" />
        };
      case 'success':
        return {
          bg: 'bg-green-100',
          text: 'text-green-800',
          icon: <CheckCircle className="h-3.5 w-3.5 mr-1" />
        };
      case 'important':
        return {
          bg: 'bg-purple-100',
          text: 'text-purple-800',
          icon: <Shield className="h-3.5 w-3.5 mr-1" />
        };
      case 'info':
      default:
        return {
          bg: 'bg-blue-100',
          text: 'text-blue-800',
          icon: <Eye className="h-3.5 w-3.5 mr-1" />
        };
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
            <div>
              <CardTitle className="text-xl flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                System Activity Log
              </CardTitle>
              <CardDescription>
                Recent actions and events across the system
              </CardDescription>
            </div>
            
            {isCurrentUser && (
              <Button variant="outline" className="w-full sm:w-auto">
                <Download className="h-4 w-4 mr-2" />
                Export Logs
              </Button>
            )}
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-4">
            {/* Search and Filter */}
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input 
                  placeholder="Search activity logs..." 
                  className="pl-8"
                />
              </div>
              
              <div className="flex gap-2">
                <Select defaultValue="all-categories">
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Filter by category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all-categories">All Categories</SelectItem>
                    <SelectItem value="user">User</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                    <SelectItem value="security">Security</SelectItem>
                    <SelectItem value="event">Event</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select defaultValue="all-severity">
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Filter by severity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all-severity">All Severities</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                    <SelectItem value="warning">Warning</SelectItem>
                    <SelectItem value="important">Important</SelectItem>
                    <SelectItem value="info">Info</SelectItem>
                    <SelectItem value="success">Success</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            {/* Activity Timeline */}
            <div className="space-y-4 mt-2">
              {activities.map((activity) => {
                const severityStyle = getSeverityStyle(activity.severity);
                
                return (
                  <div key={activity.id} className="flex gap-4">
                    {/* Icon Column */}
                    <div className="mt-1">
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center ${severityStyle.bg} ${severityStyle.text}`}>
                        {getCategoryIcon(activity.category)}
                      </div>
                    </div>
                    
                    {/* Content Column */}
                    <div className="flex-1 space-y-1.5">
                      <div className="flex flex-wrap items-center gap-2">
                        <h4 className="font-medium text-base">{activity.action}</h4>
                        <Badge className={`${severityStyle.bg} ${severityStyle.text} hover:${severityStyle.bg}`}>
                          {severityStyle.icon}
                          {activity.severity.charAt(0).toUpperCase() + activity.severity.slice(1)}
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-muted-foreground">
                        {activity.details}
                      </p>
                      
                      <div className="flex items-center gap-3 text-xs text-muted-foreground">
                        <span className="flex items-center">
                          <User className="h-3 w-3 mr-1" />
                          {activity.user}
                        </span>
                        <span className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {activity.timestamp}
                        </span>
                        <span className="capitalize">
                          {activity.category}
                        </span>
                      </div>
                    </div>
                    
                    {/* Actions Column */}
                    {isCurrentUser && (
                      <div>
                        <Button variant="ghost" size="sm" className="h-8 px-2">
                          <Eye className="h-4 w-4" />
                          <span className="sr-only">View Details</span>
                        </Button>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </CardContent>
        
        <CardFooter className="border-t pt-6 flex justify-between">
          <div className="text-sm text-muted-foreground">
            Showing <span className="font-medium">6</span> of <span className="font-medium">124</span> activities
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline" size="sm">Load More</Button>
          </div>
        </CardFooter>
      </Card>
      
      {/* System Audit Card */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            Audit Summary
          </CardTitle>
          <CardDescription>
            Important audit events requiring attention
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex p-3 rounded-md border-l-4 border-amber-500 bg-amber-50">
              <AlertTriangle className="h-5 w-5 text-amber-600 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-sm text-amber-800">Security Policy Updated</h4>
                <p className="text-xs text-amber-700">
                  The system security policy was updated 3 days ago. All admins should review the changes.
                </p>
                <div className="mt-2">
                  <Button variant="outline" size="sm" className="h-7 text-xs">
                    Review Changes
                  </Button>
                </div>
              </div>
            </div>
            
            <div className="flex p-3 rounded-md border-l-4 border-red-500 bg-red-50">
              <AlertTriangle className="h-5 w-5 text-red-600 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-sm text-red-800">Data Compliance Alert</h4>
                <p className="text-xs text-red-700">
                  5 user accounts have incomplete data required by the latest compliance standards.
                </p>
                <div className="mt-2">
                  <Button variant="outline" size="sm" className="h-7 text-xs">
                    View Affected Accounts
                  </Button>
                </div>
              </div>
            </div>
            
            <div className="flex p-3 rounded-md border-l-4 border-green-500 bg-green-50">
              <CheckCircle className="h-5 w-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-sm text-green-800">Monthly Security Scan Complete</h4>
                <p className="text-xs text-green-700">
                  The latest security scan was completed with no critical issues detected.
                </p>
                <div className="mt-2">
                  <Button variant="outline" size="sm" className="h-7 text-xs">
                    View Report
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
