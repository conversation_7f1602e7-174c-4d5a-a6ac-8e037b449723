import Image, { ImageProps } from 'next/image';
import { useState } from 'react';

type SafeImageProps = ImageProps & {
  fallbackSrc?: string;
};

const SafeImage = ({ src, alt, fallbackSrc = '/images/placeholder.jpg', ...props }: SafeImageProps) => {
  const [imgSrc, setImgSrc] = useState(src);
  const [hasError, setHasError] = useState(false);

  console.log("trigering SafeImage 🌻🌻");

  return (
    <>
      {hasError ? (
        // Fallback to an <img> tag when error occurs.
        // eslint-disable-next-line @next/next/no-img-element
        <img
          src={typeof fallbackSrc === 'string' ? fallbackSrc : '/images/placeholder.jpg'}
          alt={alt}
          {...props}
          style={{ ...props.style }}
        />
      ) : (
        <Image
          {...props}
          src={imgSrc}
          alt={alt}
          onError={() => {
            setHasError(true);
            console.warn(`Next/Image error: ${imgSrc} could not be loaded. Falling back to placeholder.`);
          }}
        />
      )}
    </>
  );
};

export default SafeImage;
