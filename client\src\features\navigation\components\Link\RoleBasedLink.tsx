
'use client'

import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { NavItem } from '../../types/navigation.types'
import { cn } from '@/lib/utils'

interface RoleBasedLinkProps {
  item: NavItem
  collapsed?: boolean
}

export function RoleBasedLink({ item, collapsed }: RoleBasedLinkProps) {
  const pathname = usePathname()
  const { data: session } = useSession()
  const userRole = session?.user?.role?.toUpperCase()

  // Get the correct path based on user role and roleBasedPath config
  const getPath = () => {
    if (item.roleBasedPath && userRole) {
      return item.roleBasedPath[userRole as keyof typeof item.roleBasedPath] || item.path
    }
    return item.path
  }

  // Check if current path matches the link path
  const isActive = pathname === getPath()

  return (
    <Link
      href={getPath()}
      className={cn(
        'flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium transition-colors',
        'hover:bg-gray-100 dark:hover:bg-gray-800',
        isActive
          ? 'bg-gray-100 dark:bg-gray-800 text-indigo-500 dark:text-indigo-400'
          : 'text-gray-600 dark:text-gray-300'
      )}
    >
     <span className="flex-shrink-0">
        <item.icon className="h-6 w-6 md:h-4 md:w-4" />
      </span>
      {!collapsed && <span className="flex-1 text-xl md:text-sm">{item.label}</span>}
    </Link>
  )
}
