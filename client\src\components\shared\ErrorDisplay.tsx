interface ErrorDisplayProps {
  title: string
  message?: string
  actions?: React.ReactNode
  className?: string
}

export function ErrorDisplay({ 
  title,
  message,
  actions,
  className 
}: ErrorDisplayProps) {
  return (
    <div className={`flex flex-col items-center justify-center min-h-[400px] space-y-4 ${className}`}>
      <h2 className="text-xl font-semibold">{title}</h2>
      {message && <p className="text-muted-foreground">{message}</p>}
      {actions && <div className="flex space-x-4">{actions}</div>}
    </div>
  )
}
