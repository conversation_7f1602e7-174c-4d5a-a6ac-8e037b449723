// Routes for Ticketmaster API
import express from 'express';
import { TicketmasterController } from '../controllers/ticketmaster.controller';

const router = express.Router();
const ticketmasterController = new TicketmasterController();

router.get('/test', ticketmasterController.testEvents.bind(ticketmasterController));
// Added route for testing single event
router.get('/test/:eventId', ticketmasterController.testSingleEvent.bind(ticketmasterController));

export default router;
