//* A cron job for your application would automatically update the geoip-lite database monthly, ensuring accurate geolocation data.
//! modify or add this feature in future

// import cron from 'node-cron';
// import { exec } from 'child_process';

// // Run at 1 AM on the first day of each month
// cron.schedule('0 1 1 * *', () => {
//   exec('npm run update-geoip', (error, stdout, stderr) => {
//     if (error) console.error('GeoIP update failed:', error);
//     else console.log('GeoIP database updated successfully');
//   });
// });
