"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { 
  Eye, 
  EyeOff, 
  Loader2, 
  Lock,
  CheckCircle2, 
  XCircle 
} from "lucide-react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { checkPasswordStrength } from "./checkPasswordStrength";
import { containerVariants, itemVariants } from "./animations";
import { usePasswordReset } from "../../hooks/usePasswordReset";

// Enhanced password validation schema
const resetPasswordSchema = z.object({
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .regex(/[A-Z]/, "Must contain uppercase letter")
    .regex(/[a-z]/, "Must contain lowercase letter")
    .regex(/[0-9]/, "Must contain number")
    .regex(/[^A-Za-z0-9]/, "Must contain special character"),
  confirmPassword: z.string()
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
});

type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;

export function ResetPasswordForm({ token }: { token: string }) {
  const router = useRouter();
  const { resetPassword, isLoading } = usePasswordReset();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(checkPasswordStrength(""));

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors }
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema)
  });

  const password = watch("password");
  useEffect(() => {
    if (password) {
      setPasswordStrength(checkPasswordStrength(password));
    }
  }, [password]);

  const onSubmit = async (data: ResetPasswordFormData) => {
    try {
      const success = await resetPassword(token, data.password);
      if (success) {
        toast.success("Password reset successful! Please sign in.");
        router.push("/");
      }
    } catch (error) {
      // Error handling by usePasswordReset hook
    }
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      exit="exit"
      variants={containerVariants}
      className="w-full space-y-4 bg-gradient-to-br from-primary/90 via-primary/80 to-primary/90 p-6 rounded-lg shadow-xl"
    >
      <motion.form
        onSubmit={handleSubmit(onSubmit)}
        variants={itemVariants}
        className="space-y-4"
      >
        {/* Password Field */}
        <div className="space-y-2">
          <label className="flex gap-2 items-center text-sm text-blue-200">
            <Lock className="w-4 h-4" /> New Password
          </label>
          <div className="relative">
            <Input
              type={showPassword ? "text" : "password"}
              {...register("password")}
              className="pr-10 text-white bg-white/10 border-white/20"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2"
            >
              {showPassword ? (
                <EyeOff className="w-4 h-4 text-blue-200" />
              ) : (
                <Eye className="w-4 h-4 text-blue-200" />
              )}
            </button>
          </div>
          {errors.password && (
            <p className="text-sm text-red-400">{errors.password.message}</p>
          )}
        </div>

        {/* Password Strength Indicator */}
        {password && passwordStrength.score < 4 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="p-3 space-y-2 rounded-lg bg-white/5"
          >
            <div className="flex justify-between items-center">
              <span className="text-sm text-blue-200">Password Strength:</span>
              <span className={`text-sm font-medium text-${passwordStrength.color}-400`}>
                {passwordStrength.label}
              </span>
            </div>
            <div className="space-y-1">
              {Object.entries(passwordStrength.criteria).map(([key, met]) => (
                <div key={key} className="flex gap-2 items-center">
                  {met ? (
                    <CheckCircle2 className="w-4 h-4 text-green-400" />
                  ) : (
                    <XCircle className="w-4 h-4 text-red-400" />
                  )}
                  <span className="text-xs text-blue-200">
                    {key === "hasUpperCase" && "Uppercase letter"}
                    {key === "hasLowerCase" && "Lowercase letter"}
                    {key === "hasNumber" && "Number"}
                    {key === "hasSpecialChar" && "Special character"}
                    {key === "isLongEnough" && "Minimum 8 characters"}
                  </span>
                </div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Confirm Password Field */}
        <div className="space-y-2">
          <label className="flex gap-2 items-center text-sm text-blue-200">
            <Lock className="w-4 h-4" /> Confirm Password
          </label>
          <div className="relative">
            <Input
              type={showConfirmPassword ? "text" : "password"}
              {...register("confirmPassword")}
              className="pr-10 text-white bg-white/10 border-white/20"
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2"
            >
              {showConfirmPassword ? (
                <EyeOff className="w-4 h-4 text-blue-200" />
              ) : (
                <Eye className="w-4 h-4 text-blue-200" />
              )}
            </button>
          </div>
          {errors.confirmPassword && (
            <p className="text-sm text-red-400">{errors.confirmPassword.message}</p>
          )}
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          className="relative w-full bg-blue-500 hover:bg-blue-600"
          disabled={isLoading || passwordStrength.score < 3}
        >
          {isLoading ? (
            <div className="flex gap-2 items-center">
              <Loader2 className="w-4 h-4 animate-spin" />
              Resetting Password...
            </div>
          ) : (
            "Reset Password"
          )}
        </Button>
      </motion.form>
    </motion.div>
  );
}