// Hook for managing billing addresses

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { useSession } from "next-auth/react"; // 🆕 ADD: Import useSession
import axiosInstance from "@/apiAxios/axios";
import {
  BillingAddress,
  BillingAddressResponse,
  BillingAddressSingleResponse,
  CreateBillingAddressRequest,
  UpdateBillingAddressRequest,
  BillingAddressState,
} from "../types/billing-address.types";
import { toast } from "sonner";

// Query keys
const BILLING_ADDRESSES_KEY = "billingAddresses";

// 🎯 Create a stable empty array outside the component
const EMPTY_ADDRESSES: BillingAddress[] = [];

export function useBillingAddresses(): BillingAddressState {
  const queryClient = useQueryClient();
  
  // 🆕 ADD: Authentication check (internal use only)
  const { data: session, status } = useSession();
  const isAuthenticated = status === "authenticated";

  // Fetch addresses with authentication check
  const { data, isLoading, isError, error, refetch } = useQuery<
    BillingAddress[],
    Error
  >({
    queryKey: [BILLING_ADDRESSES_KEY],
    queryFn: async (): Promise<BillingAddress[]> => {
      try {
        console.log("🏠 Fetching billing addresses...");

        const response = await axiosInstance.get<BillingAddressResponse>(
          "/api/v1/billing-addresses"
        );

        console.log("✅ Billing addresses received:", response.data);

        if (response.data.success && Array.isArray(response.data.data)) {
          // Ensure all addresses have consistent field values - never undefined
          return response.data.data.map((address) => ({
            ...address,
            // Ensure string fields are never undefined (always string or empty string)
            name: address.name || "",
            email: address.email || "",
            addressLine1: address.addressLine1 || "",
            addressLine2: address.addressLine2 || "",
            city: address.city || "",
            state: address.state || "",
            postalCode: address.postalCode || "",
            country: address.country || "",
          }));
        } else {
          throw new Error(
            response.data.message || "Failed to fetch billing addresses"
          );
        }
      } catch (err: any) {
        console.error("❌ Error fetching billing addresses:", err);
        const errorMessage =
          err.response?.data?.message ||
          err.message ||
          "Failed to fetch billing addresses";
        toast.error(errorMessage);
        throw new Error(errorMessage);
      }
    },
    // 🎯 ADD: Authentication check to control query execution
    enabled: isAuthenticated, // Only run the query if the user is authenticated
    refetchOnWindowFocus: isAuthenticated ? "always" : false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on authentication errors
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        console.log(
          "🚫 Not retrying billing addresses fetch due to auth error:",
          error.response.status
        );
        return false;
      }
      return failureCount < 2;
    },
  });

  // Create address mutation
  const { mutateAsync: createAddress, isPending: isCreating } = useMutation<
    BillingAddress,
    Error,
    CreateBillingAddressRequest
  >({
    mutationFn: async (
      newAddress: CreateBillingAddressRequest
    ): Promise<BillingAddress> => {
      try {
        console.log("🏠 Creating new billing address...");

        const response = await axiosInstance.post<BillingAddressSingleResponse>(
          "/api/v1/billing-addresses",
          newAddress
        );

        console.log("✅ Billing address created:", response.data);

        if (response.data.success && response.data.data) {
          return response.data.data;
        } else {
          throw new Error(
            response.data.message || "Failed to create billing address"
          );
        }
      } catch (err: any) {
        console.error("❌ Error creating billing address:", err);
        const errorMessage =
          err.response?.data?.message ||
          err.message ||
          "Failed to create billing address";
        toast.error(errorMessage);
        throw new Error(errorMessage);
      }
    },
    onSuccess: () => {
      toast.success("Billing address added successfully!");
      queryClient.invalidateQueries({ queryKey: [BILLING_ADDRESSES_KEY] });
    },
  });

  // Update address mutation
  const { mutateAsync: updateAddress, isPending: isUpdating } = useMutation<
    BillingAddress,
    Error,
    UpdateBillingAddressRequest
  >({
    mutationFn: async (
      updatedAddress: UpdateBillingAddressRequest
    ): Promise<BillingAddress> => {
      try {
        console.log(`🏠 Updating billing address ${updatedAddress.id}...`);

        const response = await axiosInstance.put<BillingAddressSingleResponse>(
          `/api/v1/billing-addresses/${updatedAddress.id}`,
          updatedAddress
        );

        console.log("✅ Billing address updated:", response.data);

        if (response.data.success && response.data.data) {
          return response.data.data;
        } else {
          throw new Error(
            response.data.message || "Failed to update billing address"
          );
        }
      } catch (err: any) {
        console.error("❌ Error updating billing address:", err);
        const errorMessage =
          err.response?.data?.message ||
          err.message ||
          "Failed to update billing address";
        toast.error(errorMessage);
        throw new Error(errorMessage);
      }
    },
    onSuccess: () => {
      toast.success("Billing address updated successfully!");
      queryClient.invalidateQueries({ queryKey: [BILLING_ADDRESSES_KEY] });
    },
  });

  // Delete address mutation
  const { mutateAsync: deleteAddressMutation, isPending: isDeleting } =
    useMutation<void, Error, string>({
      mutationFn: async (addressId: string): Promise<void> => {
        try {
          console.log(`🏠 Deleting billing address ${addressId}...`);

          const response = await axiosInstance.delete<{
            success: boolean;
            message: string;
          }>(`/api/v1/billing-addresses/${addressId}`);

          console.log("✅ Billing address deleted:", response.data);

          if (!response.data.success) {
            throw new Error(
              response.data.message || "Failed to delete billing address"
            );
          }
        } catch (err: any) {
          console.error("❌ Error deleting billing address:", err);
          const errorMessage =
            err.response?.data?.message ||
            err.message ||
            "Failed to delete billing address";
          toast.error(errorMessage);
          throw new Error(errorMessage);
        }
      },
      onSuccess: () => {
        toast.success("Billing address deleted successfully!");
        queryClient.invalidateQueries({ queryKey: [BILLING_ADDRESSES_KEY] });
      },
    });

  // Set default address mutation
  const { mutateAsync: setDefaultAddressMutation } = useMutation<
    BillingAddress,
    Error,
    string
  >({
    mutationFn: async (addressId: string): Promise<BillingAddress> => {
      try {
        console.log(`🏠 Setting billing address ${addressId} as default...`);

        const response =
          await axiosInstance.patch<BillingAddressSingleResponse>(
            `/api/v1/billing-addresses/${addressId}/set-default`
          );

        console.log("✅ Default billing address set:", response.data);

        if (response.data.success && response.data.data) {
          return response.data.data;
        } else {
          throw new Error(
            response.data.message || "Failed to set default billing address"
          );
        }
      } catch (err: any) {
        console.error("❌ Error setting default billing address:", err);
        const errorMessage =
          err.response?.data?.message ||
          err.message ||
          "Failed to set default billing address";
        toast.error(errorMessage);
        throw new Error(errorMessage);
      }
    },
    onSuccess: () => {
      toast.success("Default billing address updated successfully!");
      queryClient.invalidateQueries({ queryKey: [BILLING_ADDRESSES_KEY] });
    },
  });

  // Helper methods
  const deleteAddress = async (id: string) => {
    // Check if it's the default address
    const addressToDelete = data?.find((addr) => addr.id === id);
    if (addressToDelete?.isDefault) {
      toast.error(
        "You cannot delete your default billing address. Please set another address as default first."
      );
      return;
    }

    // Confirm deletion
    if (confirm("Are you sure you want to delete this billing address?")) {
      await deleteAddressMutation(id);
    }
  };

  const setDefaultAddress = async (id: string) => {
    await setDefaultAddressMutation(id);
  };

  return {
    addresses: data || EMPTY_ADDRESSES,
    isLoading,
    isError,
    error: error as Error,
    isCreating,
    isUpdating,
    isDeleting,
    refetch,
    createAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress,
  };
}
