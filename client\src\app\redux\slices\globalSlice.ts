// // Global state management for search, events, and workflow configuration
// import { Context } from "@/types/openctx.types";
// import { createSlice, PayloadAction } from "@reduxjs/toolkit";
// // Global state management for search, events, and workflow configuration

// interface GlobalState {
//     searchQuery: string;
//     selectedSearchEvent: Context | null; // Changed from Context to TmEventContext
//     workflowConfig: {
//         defaultQuery?: string;
//         dataSource?: 'database' | 'ticketmaster';
//     }
// }

// const initialState: GlobalState = {
//     searchQuery: '',
//     selectedSearchEvent: null,
//     workflowConfig: {
//         defaultQuery: "",
//         dataSource: "ticketmaster"
//     }
// };

// const globalSlice = createSlice({
//     name: 'global',
//     initialState,
//     reducers: {
//         setSearchQuery: (state, action: PayloadAction<string>) => {
//             state.searchQuery = action.payload;
//         },
//         resetSearchQuery: (state) => {
//             state.searchQuery = '';
//         },
//         setSelectedSearchEvent: (state, action: PayloadAction<Context | null>) => {
//             state.selectedSearchEvent = action.payload;
//         },
//         setWorkflowConfig: (state, action: PayloadAction<{ defaultQuery?: string; dataSource?: 'database' | 'ticketmaster' }>) => {
//             state.workflowConfig = {
//                 ...state.workflowConfig,
//                 ...action.payload
//             };
//         },
//         resetWorkflowConfig: (state) => {
//             state.workflowConfig = initialState.workflowConfig;
//         }
//     }
// });

// export const {
//     setSearchQuery,
//     resetSearchQuery,
//     setSelectedSearchEvent,
//     setWorkflowConfig,
//     resetWorkflowConfig,
// } = globalSlice.actions;

// export default globalSlice.reducer;
