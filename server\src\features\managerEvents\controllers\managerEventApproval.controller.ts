import { Request, Response } from "express";
import { ManagerEventService } from "../services/managerEvent.service";
import { asyncHandler } from "@/utils/asyncHandler";
import { UpdateManagerEventApprovalPayload } from "../types/managerEvent.types";
import ApiError from "@/utils/ApiError";

export class ManagerEventApprovalController {
  // GET endpoint to fetch all pending manager events
  static getPendingEvents = asyncHandler(async (req: Request, res: Response) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 4;
    
    const filters = {
      category: req.query.category as string,
      city: req.query.city as string,
      dateFrom: req.query.dateFrom as string,
      dateTo: req.query.dateTo as string,
    };

    const { events, pagination } = await ManagerEventService.getManagerEventsByStatus(
      "PENDING",
      page,
      limit,
      filters
    );

    res.status(200).json({
      success: true,
      message: "Pending manager events retrieved successfully",
      data: events,
      pagination,
    });
  });



// PATCH endpoint to update a manager event approval status
static updateApprovalStatus = asyncHandler(async (req: Request, res: Response) => {
  const eventId = req.params.id;
  const { approvalStatus, approvalNotes, approvedBy } = req.body;

  console.log('📝 Approval Request:', {
    eventId,
    approvalStatus,
    approvalNotes,
    approvedBy
  });

  // Validate required fields
  if (!eventId) {
    console.log('❌ Validation Failed: Missing eventId');
    throw ApiError.badRequest("Event ID is required");
  }

  if (!approvalStatus) {
    console.log('❌ Validation Failed: Missing approvalStatus');
    throw ApiError.badRequest("approvalStatus is required");
  }

  if (!["APPROVED", "REJECTED", "PENDING"].includes(approvalStatus)) {
    console.log('❌ Validation Failed: Invalid approvalStatus', approvalStatus);
    throw ApiError.badRequest("Invalid approvalStatus value");
  }

  if (approvalStatus === "APPROVED" && !approvedBy) {
    console.log('❌ Validation Failed: Missing approvedBy for approval');
    throw ApiError.badRequest("approvedBy is required for approval");
  }

  console.log('✅ Validation Passed, preparing payload');
  
  const payload: UpdateManagerEventApprovalPayload = {
    id: eventId,
    approvalStatus,
    approvedBy,
    approvalNotes,
  };

  console.log('🔄 Updating event status with payload:', payload);

  const updatedEvent = await ManagerEventService.updateApprovalStatus(payload);

  console.log('✨ Event successfully updated:', {
    eventId: updatedEvent.id,
    newStatus: updatedEvent.approvalStatus
  });

  res.status(200).json({
    success: true,
    message: "Manager event approval status updated successfully",
    data: updatedEvent,
  });
});

// Add this method to the ManagerEventApprovalController class

// GET endpoint to fetch approved manager events with pagination
static getApprovedEvents = asyncHandler(async (req: Request, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 4;
  
  // Extract filter parameters
  const filters = {
    category: req.query.category as string,
    city: req.query.city as string,
    dateFrom: req.query.dateFrom as string,
    dateTo: req.query.dateTo as string,
  };

  const { events, pagination } = await ManagerEventService.getManagerEventsByStatus(
    "APPROVED",
    page,
    limit,
    filters
  );

  res.status(200).json({
    success: true,
    message: "Approved manager events retrieved successfully",
    data: events,
    pagination,
  });
});

// Add new method for rejected events
static getRejectedEvents = asyncHandler(async (req: Request, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 4;
  
  const filters = {
    category: req.query.category as string,
    city: req.query.city as string,
    dateFrom: req.query.dateFrom as string,
    dateTo: req.query.dateTo as string,
  };

  const { events, pagination } = await ManagerEventService.getManagerEventsByStatus(
    "REJECTED",
    page,
    limit,
    filters
  );

  res.status(200).json({
    success: true,
    message: "Rejected manager events retrieved successfully",
    data: events,
    pagination,
  });
});
}
