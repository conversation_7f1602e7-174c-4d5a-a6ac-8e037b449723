/**
 * Stripe Payments Routes
 * 
 * Defines API endpoints for Stripe payment functionality.
 */

import express from 'express';
import { StripePaymentsController } from '../controllers/stripe-payments.controller';
import { authMiddleware } from '@/middleware/auth.middleware'; // Adjust path as needed

const router = express.Router();

// Create a payment intent from checkout session (requires authentication)
router.post(
  '/create-intent-from-checkout',
  authMiddleware,
  StripePaymentsController.createPaymentIntentFromCheckout
);

// Create a subscription checkout session (requires authentication)
// router.post(
//   '/create-subscription-checkout',
//   authMiddleware,
//   StripePaymentsController.createSubscriptionCheckout
// );

// Create a customer portal session (requires authentication)
router.post(
  '/create-customer-portal',
  authMiddleware,
  StripePaymentsController.createCustomerPortal
);

// Get payment details (requires authentication)
router.get(
  '/payment/:paymentIntentId',
  authMiddleware,
  StripePaymentsController.getPaymentDetails
);

// Stripe webhook endpoint (public, secured by signature verification)
router.post(
  '/webhook',
// Important: use raw body for signature verification
  StripePaymentsController.handleWebhook
);

export default router;