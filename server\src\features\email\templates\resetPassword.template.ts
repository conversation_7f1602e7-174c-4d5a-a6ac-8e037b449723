export const createResetPasswordTemplate = (resetUrl: string, userName: string) => `
<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <title>Reset Your Password</title>
    <style>
      .container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        font-family: system-ui, -apple-system, sans-serif;
      }
      .button {
        background-color: #3b82f6;
        color: white;
        padding: 12px 24px;
        border-radius: 6px;
        text-decoration: none;
        display: inline-block;
        margin: 20px 0;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h2>Reset Your Password</h2>
      <p>Hi ${userName},</p>
      <p>We received a request to reset your password for your Fanseatmaster account.</p>
      <a href="${resetUrl}" class="button">Reset Password</a>
      <p>If you didn't request this, you can safely ignore this email.</p>
      <p>This link will expire in 1 hour.</p>
    </div>
  </body>
</html>
`;
