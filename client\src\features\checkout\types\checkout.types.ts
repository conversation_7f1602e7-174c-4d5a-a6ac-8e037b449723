/**
 * Types for the checkout feature
 * Defines interfaces for checkout sessions, reservations, and API responses
 */

// Inventory item reference for checkout - Client-side type
export interface CheckoutItem {
  inventoryId: string;
  quantity: number;
  name: string;        // Added/Ensured
  price: number;       // Base price per ticket
  subtotal: number;    // price * quantity for this line item
  section?: string;
  row?: number | string;
  attributes?: string[];
  ticketFormat?: string;
  // Removed serviceFee per item, as it's calculated at the session level
  // serviceFee?: number;
}

// Status of a checkout session
export enum CheckoutSessionStatus {
  PENDING = 'PENDING',
  RESERVED = 'RESERVED',
  PROCESSING = 'PROCESSING',
  EXPIRED = 'EXPIRED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  FAILED = 'FAILED',
  ACTIVE = 'ACTIVE' // Keep these if they are used, otherwise remove
  // RESERVED is now used in the backend model and frontend enum
}

// Represents a discount from a coupon
export interface CouponDiscount {
  couponId: string;
  couponCode: string;
  description: string;
  discountAmount: number;
  discountType: 'PERCENTAGE' | 'FIXED';
  discountValue: number; // Original value (percentage or fixed amount)
}

// Represents applied credit points
export interface AppliedPoints {
  pointsUsed: number;
  pointsValuePerUnit: number; // How much each point is worth in currency
  discountAmount: number; // Total discount amount
  refereeStatus?: {
    isReferee: boolean;
    orderCount: number;
    discountMultiplier: number; // 0.5 for first/second orders, 1.0 otherwise
  };
}

// Represents a checkout session - Client-side type
export interface CheckoutSession {
  id: string;
  userId: string;
  eventId: string;
  status: CheckoutSessionStatus;
  items: CheckoutItem[]; // Uses the updated CheckoutItem type
  createdAt: string;
  expiresAt: string;
  subtotal: number;     // Total subtotal of all items (sum of item.subtotal)
  serviceFee: number;  // *** CHANGED: Renamed from serviceFees to serviceFee (singular) to match backend response ***
  tax?: number | null;   // Ensure this allows null or number for tax
  total: number;        // subtotal + serviceFee + tax - discounts
  couponDiscount?: CouponDiscount;
  appliedPoints?: AppliedPoints;
  paymentIntentId?: string;
  error?: string;
  currency?: string;
  ipAddress?: string;
  userAgent?: string;
  
  // Add event details fields
  eventName?: string;
  eventDate?: string | null;
  eventVenue?: string;
  eventCity?: string;
  eventCountry?: string;
  eventImage?: string | null;

  // Billing address information stored with the session
  billingAddress?: { // This matches the structure stored by the backend
    id?: string;
    name: string;
    email: string;
    addressLine1: string;
    addressLine2?: string | null;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  } | null; // It can be null if no address was provided
}

// Request/Response types for API calls (Backend-driven types)
// Ensure these align with what the backend *actually* sends/expects

// Item structure for a reservation request
export interface CheckoutItemRequest {
  inventoryId: string;
  quantity: number;
}

// Request to create a reservation
// MODIFIED: Added optional billingAddressId
export interface CreateReservationRequest {
  eventId: string;
  items: CheckoutItemRequest[]; // Matches backend request format
  billingAddressId?: string;    // Optional: ID of the selected billing address
}

// Response when creating a checkout session (reservation)
export interface CreateReservationResponse {
  success: boolean;
  message: string;
  data?: {
    // The session object returned by the backend
    session: CheckoutSession; // Use the client-side CheckoutSession type
    availableInventory?: { // Optional: if backend returns this
      [inventoryId: string]: number;
    };
    unavailableItems?: CheckoutItemRequest[]; // Matches backend response format
  };
  error?: string;
}

// Request to apply a coupon code
export interface ApplyCouponRequest {
  sessionId: string;
  couponCode: string;
}

// Response when applying a coupon
export interface ApplyCouponResponse {
  success: boolean;
  message: string;
  data: {
    isValid: boolean;
    session?: CheckoutSession; // Updated session with coupon applied
    error?: string;
  };
}

// Request to apply credit points
export interface ApplyPointsRequest {
  sessionId: string;
  pointsToApply: number;
}

// Response when applying credit points
export interface ApplyPointsResponse {
  success: boolean;
  message: string;
  data: {
    success: boolean;
    session?: CheckoutSession; // Updated session with points applied
    userPointsBalance: number; // Remaining points after application
    error?: string;
  };
}

// Request to refresh a session (extend expiry)
export interface RefreshSessionRequest {
  sessionId: string;
}

// Response when refreshing session
export interface RefreshSessionResponse {
  success: boolean;
  message: string;
  data: {
    session?: { // This might be a partial session object, depending on backend response
      id: string;
      expiresAt: string;
      // Add other fields if the backend refresh endpoint returns them
      // e.g., total?: number;
    };
    error?: string;
  };
}

// Type for items when creating a session (used in useCheckoutSession hook)
export interface CreateCheckoutSessionItem {
  inventoryId: string;
  quantity: number;
}

// Type for the payload to create a checkout session (used in useCheckoutSession hook)
export interface CreateCheckoutSessionPayload {
  eventId: string;
  items: CreateCheckoutSessionItem[];
  billingAddressId?: string; // Added billingAddressId here
  // Add other optional fields if needed for session creation
  // couponCode?: string;
  // pointsToApply?: number;
}
