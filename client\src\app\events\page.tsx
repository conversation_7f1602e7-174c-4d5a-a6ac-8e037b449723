"use client";
import React, { useState, useEffect, useCallback } from "react";
import { useUnifiedEvents } from "@/features/unified-events/hooks/useUnifiedEvents";
import { UnifiedEvent } from "@/features/unified-events/adapters/eventAdapter";
import { ClientSideFilterDrawer } from "@/components/custom-components/FilterComponents/ClientSideFilterDrawer";

// Import our components
import { EventModal } from "@/features/unified-events/components/event-details/EventModal";
import { EventsHeader } from "@/features/unified-events/event-list/EventsHeader";
import { EventGrid } from "@/features/unified-events/event-list/EventGrid";
import { EventPagination } from "@/features/unified-events/event-list/EventPagination";
import { LoadingState } from "@/features/unified-events/components/common/LoadingState";
import { EmptyState } from "@/features/unified-events/components/common/EmptyState";
import { useAppDispatch, useAppSelector } from "../redux";
import { setAllEventFilters } from "@/state";
import { useRouter, useSearchParams } from "next/navigation";

const EventsPage: React.FC = () => {
  const dispatch = useAppDispatch();
  // Get filter state from Redux
  const reduxFilters = useAppSelector((state) => state.global.eventFilters);
  
  // Use our unified hook for events with loadAllForFiltering=true
  const {
    events,
    allManagerEvents, // This contains all manager events for filtering
    isLoading,
    error,
    pagination,
    setPage,
    setPageSize,
    filters,
    setFilters,
  } = useUnifiedEvents({}, true); // Pass true to load all events for filtering

  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<UnifiedEvent | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [shouldAutoFocus, setShouldAutoFocus] = useState(false);
  const router = useRouter();
const searchParams = useSearchParams();
  // State for client-side filtered events
  const [filteredEvents, setFilteredEvents] = useState<UnifiedEvent[]>([]);
  // State for filtered events to display (paginated subset of filteredEvents)
  const [displayEvents, setDisplayEvents] = useState<UnifiedEvent[]>([]);
  // Local pagination state
  const [localPagination, setLocalPagination] = useState({
    currentPage: 1,
    pageSize: 10,
    totalItems: 0,
    totalPages: 1
  });
  
  // Initialize filtered events with all events on load
  useEffect(() => {
    if (events && events.length > 0) {
      setFilteredEvents(events);
      updateDisplayEvents(events, 1);
    }
  }, [events]);


  useEffect(() => {
    // Check if user came from navbar (you can detect this via referrer or add a URL param)
    const fromNavbar = searchParams.get('from') === 'navbar' || document.referrer.includes('/');
    if (fromNavbar) {
      setShouldAutoFocus(true);
    }
  }, [searchParams]);
  // Update the useEffect that combines manager and ticketmaster events
  useEffect(() => {
    if (allManagerEvents && allManagerEvents.length > 0) {
      // Include both manager events and Ticketmaster events
      const combinedEvents = [
        ...allManagerEvents, 
        ...events.filter(event => event.source === 'ticketmaster')
      ];
      console.log(`Combined ${combinedEvents.length} events (${allManagerEvents.length} manager, ${events.filter(event => event.source === 'ticketmaster').length} ticketmaster)`);
      setFilteredEvents(combinedEvents);
      updateDisplayEvents(combinedEvents, 1);
    }
  }, [allManagerEvents, events]);

  // Update display events based on current page
  const updateDisplayEvents = (events: UnifiedEvent[], page: number) => {
    const pageSize = 10; // Show 10 events per page
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const eventsToDisplay = events.slice(startIndex, endIndex);
    
    setDisplayEvents(eventsToDisplay);
    setLocalPagination({
      currentPage: page,
      pageSize,
      totalItems: events.length,
      totalPages: Math.ceil(events.length / pageSize)
    });
  };

  // Debug output for the first event
  useEffect(() => {
    if (events && events.length > 0) {
      console.log(" First event from useUnifiedEvents 🌻🌻🔑🔑:", events[0]);
    }
  }, [events]);

  // Debug output for monitoring event sources
  useEffect(() => {
    if (allManagerEvents && allManagerEvents.length > 0) {
      console.log(`Total manager events available for filtering: ${allManagerEvents.length}`);
    }
  }, [allManagerEvents]);

  // Handle local page change (for filtered results)
  const handleLocalPageChange = (newPage: number) => {
    console.log(`Changing to local page ${newPage}`);
    console.log(`Total filtered events: ${filteredEvents.length}`);
    console.log(`Total pages: ${Math.ceil(filteredEvents.length / 10)}`);
    
    // Make sure we're actually changing the page
    updateDisplayEvents(filteredEvents, newPage);
    
    // After updating, log what we're displaying
    console.log(`Now displaying ${displayEvents.length} events on page ${newPage}`);
    
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const handleRefresh = () => {
    // Reset filters to include both manager and ticketmaster events
    if (allManagerEvents) {
      const combinedEvents = [
        ...allManagerEvents,
        ...events.filter(event => event.source === 'ticketmaster')
      ];
      setFilteredEvents(combinedEvents);
      updateDisplayEvents(combinedEvents, 1);
    }
  };

  const toggleFilterDrawer = () => {
    setIsFilterOpen(!isFilterOpen);
  };

  // Function to handle filter changes from components
  const handleFilteredEventsChange = useCallback((newFilteredEvents: UnifiedEvent[]) => {
    setFilteredEvents(newFilteredEvents);
    // Reset to page 1 when filters change
    updateDisplayEvents(newFilteredEvents, 1);
  }, []);

  // Function to open the modal with selected event
  const openModal = (event: UnifiedEvent) => {
    setSelectedEvent(event);
    setIsModalOpen(true);
  };

  // Function to close the modal
  const closeModal = () => {
    setSelectedEvent(null);
    setIsModalOpen(false);
  };

  if (isLoading) {
    return <LoadingState />;
  }

  if (error) {
    return (
      <div className="container p-4">
        <EventsHeader
          toggleFilterDrawer={toggleFilterDrawer}
          allEvents={[]}
          onFilteredEventsChange={() => {}}
          shouldAutoFocus={shouldAutoFocus}
        />
        <div className="text-red-500 p-4 bg-red-50 rounded-lg mt-4">
          Error fetching events. Please try again later.
        </div>
      </div>
    );
  }

  return (
    <div className="container p-4 min-h-screen">
      {/* Header with search and filters */}
      <EventsHeader
        toggleFilterDrawer={toggleFilterDrawer}
        allEvents={allManagerEvents}
        onFilteredEventsChange={handleFilteredEventsChange}
        shouldAutoFocus={shouldAutoFocus}
      />

      {/* Mobile filter drawer */}
      <div className="md:hidden">
        <ClientSideFilterDrawer
          isOpen={isFilterOpen}
          onClose={toggleFilterDrawer}
          allEvents={allManagerEvents}
          onFilteredEventsChange={handleFilteredEventsChange}
        />
      </div>

      {/* Event detail modal */}
      <EventModal
        isOpen={isModalOpen}
        onClose={closeModal}
        event={selectedEvent}
      />

      {/* Event grid or empty state - now using display events */}
      {displayEvents.length > 0 ? (
        <EventGrid events={displayEvents} onEventClick={openModal} />
      ) : (
        <EmptyState onReset={handleRefresh} />
      )}

      {/* Pagination - Now using local pagination */}
      <div className="mt-6">
        {/* Debug info - remove this in production */}
        <div className="mb-4 p-3 bg-gray-50 rounded-md text-xs">
          <p className="font-medium mb-1">Pagination Debug:</p>
          <ul>
            <li>Current Page: {localPagination.currentPage}</li>
            <li>Total Pages: {localPagination.totalPages}</li>
            <li>Total Items: {localPagination.totalItems}</li>
            <li>Page Size: {localPagination.pageSize}</li>
            <li>Filtered Items: {filteredEvents.length}</li>
            <li>Display Items: {displayEvents.length}</li>
          </ul>
        </div>

        {/* Always show pagination if there are events */}
        {filteredEvents.length > 0 && (
          <EventPagination
            currentPage={localPagination.currentPage}
            totalPages={Math.max(1, localPagination.totalPages)}
            onPageChange={handleLocalPageChange}
          />
        )}
      </div>
    </div>
  );
};

export default EventsPage;
