/**
 * ConversationList Component
 * 
 * Displays a list of conversation summaries with selection capability.
 * Used in Admin dashboards, Manager sales views, and Visitor ticket views.
 */

import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  MessageCircle, 
  User, 
  UserCheck, 
  Shield, 
  Clock,
  AlertCircle,
  CheckCircle,
  Flame
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useMessageValidation } from '../hooks/useMessaging';
import { ConversationListProps, UserRole } from '../types/messaging.types';

export const ConversationList: React.FC<ConversationListProps> = ({
  conversations,
  onSelectConversation,
  selectedConversationId,
  isLoading = false,
}) => {
  const { formatDisplayName, formatMessageTime } = useMessageValidation();

  // Loading skeleton
  if (isLoading) {
    return (
      <div className="space-y-3">
        {Array.from({ length: 5 }).map((_, index) => (
          <Card key={index} className="p-4">
            <div className="flex items-center space-x-3">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="space-y-2 flex-1">
                <Skeleton className="h-4 w-[200px]" />
                <Skeleton className="h-3 w-[150px]" />
              </div>
              <Skeleton className="h-6 w-6 rounded-full" />
            </div>
          </Card>
        ))}
      </div>
    );
  }

  // Empty state
  if (!conversations.length) {
    return (
      <Card className="p-8 text-center">
        <MessageCircle className="h-12 w-12 mx-auto text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          No conversations yet
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Conversations will appear here when visitors contact you about their tickets.
        </p>
      </Card>
    );
  }

  // Get role-based avatar and styling
  const getRoleAvatar = (role: UserRole) => {
    switch (role) {
      case 'ADMIN':
        return {
          icon: <Shield className="h-4 w-4" />,
          bgClass: "bg-purple-500",
          textClass: "text-purple-600 dark:text-purple-400"
        };
      case 'MANAGER':
        return {
          icon: <UserCheck className="h-4 w-4" />,
          bgClass: "bg-green-500",
          textClass: "text-green-600 dark:text-green-400"
        };
      case 'VISITOR':
      default:
        return {
          icon: <User className="h-4 w-4" />,
          bgClass: "bg-blue-500",
          textClass: "text-blue-600 dark:text-blue-400"
        };
    }
  };

  // Get conversation status styling
  const getStatusStyling = (unreadCount: number, lastMessageDate: string) => {
    const now = new Date();
    const lastMessage = new Date(lastMessageDate);
    const hoursAgo = (now.getTime() - lastMessage.getTime()) / (1000 * 60 * 60);

    if (unreadCount > 0) {
      if (unreadCount >= 3) {
        return {
          priority: 'high',
          indicator: <Flame className="h-4 w-4 text-red-500" />,
          badgeClass: "bg-red-500 text-white animate-pulse"
        };
      }
      return {
        priority: 'normal',
        indicator: <AlertCircle className="h-4 w-4 text-orange-500" />,
        badgeClass: "bg-orange-500 text-white"
      };
    }

    if (hoursAgo < 24) {
      return {
        priority: 'recent',
        indicator: <CheckCircle className="h-4 w-4 text-green-500" />,
        badgeClass: "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
      };
    }

    return {
      priority: 'resolved',
      indicator: <Clock className="h-4 w-4 text-gray-400" />,
      badgeClass: "bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400"
    };
  };

  return (
    <ScrollArea className="h-full">
      <div className="space-y-2 p-1">
        {conversations.map((conversation) => {
          const isSelected = selectedConversationId === conversation.checkoutSessionId;
          const statusStyling = getStatusStyling(conversation.unreadCount, conversation.lastMessageDate);
          const lastMessage = conversation.latestMessage;
          const lastMessageRole = lastMessage ? getRoleAvatar(lastMessage.senderRole) : null;

          return (
            <Card
              key={conversation.checkoutSessionId}
              className={cn(
                "cursor-pointer transition-all duration-200 hover:shadow-md",
                isSelected && "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20",
                statusStyling.priority === 'high' && "border-red-200 dark:border-red-800",
                statusStyling.priority === 'normal' && "border-orange-200 dark:border-orange-800"
              )}
              onClick={() => onSelectConversation(conversation.checkoutSessionId)}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  {/* Main Content */}
                  <div className="flex items-start space-x-3 flex-1 min-w-0">
                    {/* Status Indicator */}
                    <div className="flex-shrink-0 pt-1">
                      {statusStyling.indicator}
                    </div>

                    {/* Conversation Info */}
                    <div className="flex-1 min-w-0">
                      {/* Header: Ticket ID + Participants */}
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                            Ticket #{conversation.ticketId}
                          </h4>
                          
                          {/* Event Name Badge */}
                          {conversation.eventName && (
                            <Badge variant="outline" className="text-xs">
                              {conversation.eventName.length > 20 
                                ? `${conversation.eventName.substring(0, 20)}...`
                                : conversation.eventName
                              }
                            </Badge>
                          )}
                        </div>

                        {/* Unread Count */}
                        {conversation.unreadCount > 0 && (
                          <Badge className={cn("text-xs px-2 py-1", statusStyling.badgeClass)}>
                            {conversation.unreadCount}
                          </Badge>
                        )}
                      </div>

                      {/* Participants */}
                      <div className="flex items-center space-x-4 mb-2 text-xs text-gray-500 dark:text-gray-400">
                        {conversation.buyerName && (
                          <div className="flex items-center space-x-1">
                            <User className="h-3 w-3" />
                            <span>{conversation.buyerName}</span>
                          </div>
                        )}
                        
                        {conversation.managerName && (
                          <div className="flex items-center space-x-1">
                            <UserCheck className="h-3 w-3" />
                            <span>{conversation.managerName}</span>
                          </div>
                        )}
                      </div>

                      {/* Last Message Preview */}
                      {lastMessage && (
                        <div className="flex items-start space-x-2">
                          {/* Last Message Sender Avatar */}
                          <Avatar className="h-5 w-5 flex-shrink-0">
                            <AvatarFallback className={cn("text-xs", lastMessageRole?.bgClass)}>
                              {lastMessageRole?.icon}
                            </AvatarFallback>
                          </Avatar>

                          {/* Message Text */}
                          <div className="flex-1 min-w-0">
                            <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2 leading-relaxed">
                              {lastMessage.message.length > 80
                                ? `${lastMessage.message.substring(0, 80)}...`
                                : lastMessage.message
                              }
                            </p>
                          </div>
                        </div>
                      )}

                      {/* Time and Stats */}
                      <div className="flex items-center justify-between mt-2 text-xs text-gray-400">
                        <span>{formatMessageTime(conversation.lastMessageDate)}</span>
                        
                        <div className="flex items-center space-x-2">
                          <span>{conversation.messageCount} messages</span>
                          
                          {/* Status Indicators */}
                          {statusStyling.priority === 'high' && (
                            <Badge variant="outline" className="text-red-600 border-red-200">
                              High Priority
                            </Badge>
                          )}
                          
                          {statusStyling.priority === 'resolved' && (
                            <Badge variant="outline" className="text-green-600 border-green-200">
                              Resolved
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Quick Actions (on hover) */}
                <div className={cn(
                  "flex items-center justify-end space-x-2 mt-3 pt-3 border-t opacity-0 group-hover:opacity-100 transition-opacity",
                  isSelected && "opacity-100"
                )}>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-7 px-3 text-xs"
                    onClick={(e) => {
                      e.stopPropagation();
                      onSelectConversation(conversation.checkoutSessionId);
                    }}
                  >
                    <MessageCircle className="h-3 w-3 mr-1" />
                    {conversation.unreadCount > 0 ? 'Reply' : 'View'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </ScrollArea>
  );
};

// Loading skeleton component for reuse
export const ConversationListSkeleton: React.FC<{ count?: number }> = ({ count = 5 }) => {
  return (
    <div className="space-y-3">
      {Array.from({ length: count }).map((_, index) => (
        <Card key={index} className="p-4">
          <div className="flex items-start space-x-3">
            <Skeleton className="h-4 w-4 mt-1" />
            <div className="space-y-2 flex-1">
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-[120px]" />
                <Skeleton className="h-5 w-5 rounded-full" />
              </div>
              <Skeleton className="h-3 w-[200px]" />
              <div className="flex items-center space-x-2">
                <Skeleton className="h-4 w-4 rounded-full" />
                <Skeleton className="h-3 w-[150px]" />
              </div>
              <div className="flex justify-between">
                <Skeleton className="h-3 w-[80px]" />
                <Skeleton className="h-3 w-[60px]" />
              </div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

// Default export for easier imports
export default ConversationList;