"use client";

/**
 * Custom hook for managing user permissions and role-based access control.
 * Provides utility functions to check user permissions based on their role.
 * Integrates with the authentication system to determine user roles and their associated permissions.
 * @returns {Object} Object containing permission check functions, user role, and available permissions
 */
import { useAuth } from "@/features/auth/hooks/useAuth";
import { Permission, UserRole, EventOperations, UserOperations, TicketOperations, AnalyticsOperations, SettingsOperations } from "../types";
import { ROLE_PERMISSIONS, EVENT_OPERATIONS, USER_OPERATIONS, TICKET_OPERATIONS, ANALYTICS_OPERATIONS, SETTINGS_OPERATIONS } from "../mappings";
  export const usePermissions = () => {
    // --------------- Authentication Integration ---------------
    const { session } = useAuth();
    // Fallback to VISITOR role if no user role is found in session
    const userRole = (session?.user?.role as UserRole) || null;

    // --------------- Permission Check Functions ---------------
    /**
   * Checks if the user has a specific permission
   * @param permission - The permission to check
   * @returns boolean indicating if user has the permission
   */
    const hasPermission = (permission: Permission): boolean => {
      return ROLE_PERMISSIONS[userRole].includes(permission);
    };

    /**
   * Checks if the user has all specified permissions
   * @param permissions - Array of permissions to check
   * @returns boolean indicating if user has all permissions
   */
    const hasAllPermissions = (permissions: Permission[]): boolean => {
      return permissions.every((permission) => hasPermission(permission));
    };

    // --------------- Operations Retrieval Functions ---------------
    /**
   * Get event operations for the current user role
   * @returns EventOperations for the current user role
   */
    const getEventOperations = (): EventOperations => {
      return EVENT_OPERATIONS[userRole];
    };

    /**
   * Get user operations for the current user role
   * @returns UserOperations for the current user role
   */
    const getUserOperations = (): UserOperations => {
      return USER_OPERATIONS[userRole];
    };

    /**
   * Get ticket operations for the current user role
   * @returns TicketOperations for the current user role
   */
    const getTicketOperations = (): TicketOperations => {
      return TICKET_OPERATIONS[userRole];
    };

    /**
   * Get analytics operations for the current user role
   * @returns AnalyticsOperations for the current user role
   */
    const getAnalyticsOperations = (): AnalyticsOperations => {
      return ANALYTICS_OPERATIONS[userRole];
    }

    /**
   * Get settings operations for the current user role
   * @returns SettingsOperations for the current user role
   */
    const getSettingsOperations = (): SettingsOperations => {
      return SETTINGS_OPERATIONS[userRole];
    }

    // --------------- Hook Return Value ---------------
    return {
      hasPermission,
      hasAllPermissions, // Added new function to check multiple permissions
      getEventOperations,
      getUserOperations,
      getTicketOperations,
      getAnalyticsOperations,
      getSettingsOperations,
      userRole,
      permissions: ROLE_PERMISSIONS[userRole], // Current user's permission set based on their role
    };
  };
