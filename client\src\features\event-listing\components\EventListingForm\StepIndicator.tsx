"use client";

import React from "react";
import { useEventListing } from "./EventListingContext";
import { motion } from "framer-motion"; // We'll add this for animations

export const StepIndicator: React.FC = () => {
  const { currentStep, totalSteps } = useEventListing();
  const stepNames = ["Event", "Add Inventory", "Publish"];

  return (
    <div className="relative flex items-center justify-center my-8 w-full max-w-2xl mx-auto">
      {/* Background Line */}
      <div className="absolute top-1/2 left-0 w-full h-[2px] bg-gray-200 -translate-y-1/2" />

      {/* Progress Line */}
      <motion.div 
        className="absolute top-1/2 left-0 h-[2px] bg-green-500 -translate-y-1/2"
        initial={{ width: "0%" }}
        animate={{ width: `${((currentStep - 1) / (totalSteps - 1)) * 100}%` }}
        transition={{ duration: 0.5, ease: "easeInOut" }}
      />

      {/* Step Indicators */}
      <div className="relative flex justify-between w-full">
        {stepNames.map((step, index) => (
          <div key={index} className="flex flex-col items-center">
            {/* Step Box */}
            <motion.div
              className={`
                relative flex items-center justify-center
                w-32 h-12 rounded-lg shadow-md
                transition-colors duration-300
                ${index + 1 < currentStep ? 'bg-green-500 text-white' : ''}
                ${index + 1 === currentStep ? 'bg-blue-500 text-white' : ''}
                ${index + 1 > currentStep ? 'bg-white border-2 border-gray-200' : ''}
              `}
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: index * 0.2 }}
            >
              {/* Step Number */}
              <div className={`
                absolute -left-2 -top-2
                w-6 h-6 rounded-full 
                flex items-center justify-center text-xs font-bold
                ${index + 1 <= currentStep ? 'bg-white text-blue-500' : 'bg-gray-200 text-gray-600'}
              `}>
                {index + 1}
              </div>

              {/* Step Name */}
              <span className="text-sm font-medium z-10">
                {step}
              </span>
            </motion.div>
          </div>
        ))}
      </div>
    </div>
  );
};
