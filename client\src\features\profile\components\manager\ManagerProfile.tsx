/**
 * ManagerProfile Component
 * 
 * Profile view for users with manager role.
 * Extends visitor profile with management-specific features.
 */

import React from 'react';
import { ProfileComponentProps } from '../../types/profile.types';
import { ProfileHeader } from '../common/ProfileHeader';
import { ProfileInfo } from '../common/ProfileInfo';
import { ProfileActions } from '../common/ProfileActions';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { CalendarDays, MapPin, Ticket, UserCircle, BarChart, Users, Shield } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';

export function ManagerProfile({ profile, isEditable = false }: ProfileComponentProps) {
  const { data: session } = useSession();
  const isCurrentUser = session?.user?.email === profile.email;

  return (
    <div className="container max-w-4xl mx-auto px-4 py-8 space-y-6">
      {/* Profile Header with Avatar */}
      <ProfileHeader profile={profile} isEditable={isEditable} />
      
      {/* Profile Actions */}
      <div className="w-full">
        <ProfileActions 
          profile={profile} 
          isCurrentUser={isCurrentUser}
          isFollowing={false}
        />
      </div>
      
      {/* Manager Stats Summary - Only shown for the manager's own profile */}
      {isCurrentUser && (
        <Card className="bg-primary/5 border-primary/20">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Shield className="h-5 w-5 mr-2 text-primary" />
              Manager Dashboard Summary
            </CardTitle>
            <CardDescription>
              Quick overview of your management statistics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>Events Managed</span>
                  <span className="font-medium">12</span>
                </div>
                <Progress value={75} className="h-2" />
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>Team Members</span>
                  <span className="font-medium">8</span>
                </div>
                <Progress value={60} className="h-2" />
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>Revenue Target</span>
                  <span className="font-medium">65%</span>
                </div>
                <Progress value={65} className="h-2" />
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" size="sm" className="w-full sm:w-auto">
              <BarChart className="h-4 w-4 mr-2" />
              View Full Dashboard
            </Button>
          </CardFooter>
        </Card>
      )}
      
      {/* Main Content Area with Tabs */}
      <Tabs defaultValue="about" className="w-full">
        <TabsList className="grid grid-cols-4 max-w-md">
          <TabsTrigger value="about">About</TabsTrigger>
          <TabsTrigger value="events">Events</TabsTrigger>
          <TabsTrigger value="team">Team</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>
        
        {/* About Tab */}
        <TabsContent value="about" className="space-y-4 mt-6">
          <ProfileInfo profile={profile} isEditable={isEditable} />
        </TabsContent>
        
        {/* Events Tab */}
        <TabsContent value="events" className="space-y-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Managed Events</CardTitle>
              <CardDescription>Events you are managing or have managed</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Example event - This would be dynamically generated */}
                <div className="flex items-start space-x-4 p-4 rounded-lg border bg-card hover:bg-accent/5 transition-colors">
                  <div className="bg-primary/10 p-2 rounded">
                    <CalendarDays className="h-6 w-6 text-primary" />
                  </div>
                  <div className="space-y-1 flex-1">
                    <div className="flex justify-between">
                      <h3 className="font-medium">Tech Conference 2023</h3>
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Active</span>
                    </div>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <CalendarDays className="h-4 w-4 mr-1" />
                      <span>Jun 15, 2023</span>
                    </div>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <MapPin className="h-4 w-4 mr-1" />
                      <span>San Francisco, CA</span>
                    </div>
                    <div className="flex items-center text-sm text-muted-foreground mt-1">
                      <Users className="h-4 w-4 mr-1" />
                      <span>324 Attendees</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4 p-4 rounded-lg border bg-card hover:bg-accent/5 transition-colors">
                  <div className="bg-primary/10 p-2 rounded">
                    <CalendarDays className="h-6 w-6 text-primary" />
                  </div>
                  <div className="space-y-1 flex-1">
                    <div className="flex justify-between">
                      <h3 className="font-medium">Product Launch</h3>
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Planning</span>
                    </div>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <CalendarDays className="h-4 w-4 mr-1" />
                      <span>Aug 22, 2023</span>
                    </div>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <MapPin className="h-4 w-4 mr-1" />
                      <span>New York, NY</span>
                    </div>
                    <div className="flex items-center text-sm text-muted-foreground mt-1">
                      <Users className="h-4 w-4 mr-1" />
                      <span>156 Attendees</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm">
                View All Events
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        {/* Team Tab */}
        <TabsContent value="team" className="space-y-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Team Members</CardTitle>
              <CardDescription>People working with you on events</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Example team members - This would be dynamically generated */}
                <div className="flex items-center gap-4 p-4 rounded-lg border">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src="/avatars/01.png" alt="Team member" />
                    <AvatarFallback>JD</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <h3 className="font-medium">Jane Doe</h3>
                    <p className="text-sm text-muted-foreground">Event Coordinator</p>
                  </div>
                  <Button variant="ghost" size="sm">
                    Message
                  </Button>
                </div>
                
                <div className="flex items-center gap-4 p-4 rounded-lg border">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src="/avatars/02.png" alt="Team member" />
                    <AvatarFallback>MS</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <h3 className="font-medium">Mark Smith</h3>
                    <p className="text-sm text-muted-foreground">Marketing Specialist</p>
                  </div>
                  <Button variant="ghost" size="sm">
                    Message
                  </Button>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" size="sm">
                View All Team Members
              </Button>
              {isCurrentUser && (
                <Button variant="default" size="sm">
                  <Users className="h-4 w-4 mr-1" />
                  Add Team Member
                </Button>
              )}
            </CardFooter>
          </Card>
        </TabsContent>
        
        {/* Activity Tab */}
        <TabsContent value="activity" className="space-y-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Recent Activity</CardTitle>
              <CardDescription>Your latest interactions on the platform</CardDescription>
            </CardHeader>
            <CardContent>
              {/* This would be populated with actual activity data */}
              <div className="space-y-4">
                <div className="flex items-start gap-3 p-3 rounded-lg border-l-2 border-l-blue-500">
                  <div className="bg-blue-100 text-blue-800 p-2 rounded-full">
                    <CalendarDays className="h-4 w-4" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Created a new event: Product Launch</p>
                    <p className="text-xs text-muted-foreground">2 hours ago</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3 p-3 rounded-lg border-l-2 border-l-green-500">
                  <div className="bg-green-100 text-green-800 p-2 rounded-full">
                    <Users className="h-4 w-4" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Added Mark Smith to your team</p>
                    <p className="text-xs text-muted-foreground">1 day ago</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Missing imports
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';

// Add debugging if needed
console.log('👨‍💼 ManagerProfile loaded');
