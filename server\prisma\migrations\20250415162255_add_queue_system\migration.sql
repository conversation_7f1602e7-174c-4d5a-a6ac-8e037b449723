-- CreateEnum
CREATE TYPE "MembershipTier" AS ENUM ('STANDARD', 'SUBSCRIBER', 'VIP');

-- CreateEnum
CREATE TYPE "QueueUserStatus" AS ENUM ('WAITING', 'ACTIVE', 'EXPIRED', 'COMPLETED');

-- AlterTable
ALTER TABLE "users" ADD COLUMN     "membershipTier" "MembershipTier" NOT NULL DEFAULT 'STANDARD';

-- CreateTable
CREATE TABLE "Queue" (
    "id" TEXT NOT NULL,
    "eventId" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT false,
    "activatedAt" TIMESTAMP(3),
    "deactivatedAt" TIMESTAMP(3),
    "lastAdmittedAt" TIMESTAMP(3),
    "batchSize" INTEGER NOT NULL DEFAULT 10,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Queue_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "QueueUser" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "queueId" TEXT NOT NULL,
    "entryTime" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" "QueueUserStatus" NOT NULL DEFAULT 'WAITING',
    "priority" INTEGER NOT NULL DEFAULT 1,
    "admittedAt" TIMESTAMP(3),
    "expiresAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "QueueUser_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Queue_eventId_idx" ON "Queue"("eventId");

-- CreateIndex
CREATE INDEX "Queue_isActive_idx" ON "Queue"("isActive");

-- CreateIndex
CREATE INDEX "QueueUser_queueId_status_priority_entryTime_idx" ON "QueueUser"("queueId", "status", "priority", "entryTime");

-- CreateIndex
CREATE INDEX "QueueUser_userId_status_idx" ON "QueueUser"("userId", "status");

-- CreateIndex
CREATE UNIQUE INDEX "QueueUser_userId_queueId_key" ON "QueueUser"("userId", "queueId");

-- AddForeignKey
ALTER TABLE "QueueUser" ADD CONSTRAINT "QueueUser_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "QueueUser" ADD CONSTRAINT "QueueUser_queueId_fkey" FOREIGN KEY ("queueId") REFERENCES "Queue"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
