import { Badge } from "@/components/ui/badge";
import { CheckCircle, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface SubscriptionStatusProps {
  isActive: boolean;
  isCanceled: boolean;
  className?: string;
}

export function SubscriptionStatus({
  isActive,
  isCanceled,
  className,
}: SubscriptionStatusProps) {
  if (!isActive) {
    return (
      <Badge 
        variant="outline" 
        className={cn("bg-muted border-muted-foreground text-muted-foreground", className)}
      >
        <AlertCircle className="h-3 w-3 mr-1" />
        Inactive
      </Badge>
    );
  }

  if (isCanceled) {
    return (
      <Badge 
        variant="outline" 
        className={cn("bg-amber-50 border-amber-200 text-amber-600", className)}
      >
        <AlertCircle className="h-3 w-3 mr-1" />
        Canceling Soon
      </Badge>
    );
  }

  return (
    <Badge 
      variant="outline" 
      className={cn("bg-green-50 border-green-200 text-green-600", className)}
    >
      <CheckCircle className="h-3 w-3 mr-1" />
      Active
    </Badge>
  );
}