"use client";

// Importing necessary dependencies and components
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Search } from "lucide-react";
import { SearchModal } from "./SearchModal";
import { Dialog } from "@/components/ui/dialog";
import { useAppSelector, useAppDispatch } from "@/app/redux";
import { X } from "lucide-react";
import { setSelectedSearchEvent } from "@/state";
import { useSession } from "next-auth/react";

export const GlobalSearch = () => {
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  // Use Redux hooks for state management
  const selectedEvent = useAppSelector(
    (state) => state.global.selectedSearchEvent
  );
  const dispatch = useAppDispatch();
  
  // Get user session to check role
  const { data: session } = useSession();
  const userRole = session?.user?.role?.toUpperCase();
  console.log("userRole", userRole)
  // Check if user has Manager or Admin role
  const hasSearchAccess = userRole === "MANAGER" || userRole === "ADMIN";

  const toggleSearch = () => setIsSearchOpen(!isSearchOpen);

  // Add this function to handle search
  const handleSearch = (query: string) => {
    // This function can be empty or you can add logic if needed
    console.log("Search query:", query);
    toggleSearch(); // Close the modal after search
  };

  // If user doesn't have access, don't render the search button
  if (!hasSearchAccess) {
    return null;
  }

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="sm"
        onClick={toggleSearch}
        className="rounded-md w-40 h-10 bg-black/10 hover:bg-black/20 transition-colors flex items-center gap-2"
      >
        <Search className="h-5 w-5 text-gray-700" />
        <span className="text-gray-700">Search Events</span>
      </Button>
      <SearchModal isOpen={isSearchOpen} onClose={toggleSearch} onSearch={handleSearch} />
      {/* Use Redux dispatch to update state */}
      <div>
        {/* Dialog component temporarily commented out*/}
        <Dialog
          open={!!selectedEvent}
          onOpenChange={() => dispatch(setSelectedSearchEvent(null))}
        >
          <div className="p-6">
            {selectedEvent && (
              <div className="flex flex-col gap-4">
                <div className="flex justify-between items-start">
                  <h2 className="text-2xl font-bold">
                    {selectedEvent.metadata.name || selectedEvent.text}
                  </h2>
                  <Button
                    onClick={() => dispatch(setSelectedSearchEvent(null))}
                  >
                    <X className="h-6 w-6" />
                  </Button>
                </div>
                {/* {selectedEvent.metadata.image && (
                                 <Image
                                    src={selectedEvent.metadata.image}
                                     alt={selectedEvent.metadata.name || 'Event Image'}
                                     className="w-48 h-48 object-cover rounded"
                                     width={192}
                                    height={192}
                                 />
                            )} */}
                <div className="text-gray-700">
                  {/* <p><strong>Venue:</strong> {selectedEvent.metadata.venue}</p>
                                <p><strong>Date:</strong> {selectedEvent.metadata.date}</p> */}

                  {/* <div>
                    <strong>Full Context:</strong>
                    <pre className="overflow-x-auto whitespace-pre-wrap">
                      {JSON.stringify(selectedEvent, null, 2)}
                    </pre>
                  </div> */}
                </div>
              </div>
            )}
          </div>
        </Dialog>
      </div>
    </div>
  );
};
