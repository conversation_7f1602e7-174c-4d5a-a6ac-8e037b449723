"use client";

import { useParams, useRouter } from 'next/navigation';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';
// Import Elements
import { Elements } from '@stripe/react-stripe-js';
import { getStripe } from '@/lib/stripe';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle } from 'lucide-react';

import { useCheckoutSession } from '@/features/checkout/hooks/useCheckoutSession';
import { CheckoutSummary } from '@/features/checkout/components/CheckoutSummary';
import { useProfileQuery } from '@/features/profile/hooks/useProfileQuery';
import { LoadingSpinner } from '@/components/shared/LoadingSpinner';
import { StripePaymentForm } from '@/features/payments/components/StripePaymentForm';
import { useCreatePaymentIntent } from '@/features/payments/hooks/usePaymentApi';
import { CheckoutSessionStatus } from '@/features/checkout/types/checkout.types'; // Add import for enum

export default function CheckoutPage() {
  const params = useParams();
  const router = useRouter();
  const sessionId = params.sessionId as string;

  // Stripe initialization
  const stripePromise = getStripe(); // Keep this

  // Profile data
  const { profile, isLoading: isLoadingProfile } = useProfileQuery();

  // Checkout session state
  const {
    session,
    isLoadingSession,
    sessionError,
    sessionExpiresAt,
    isReservationActive,
    isReservationExpired,
    applyCoupon,
    applyPoints,
    refreshSession,
    isApplyingCoupon,
    isApplyingPoints,
    isRefreshingSession,
    updateSessionStatus, // Add this new function from the hook
    isUpdatingStatus,    // Add this new loading state 
  } = useCheckoutSession({ sessionId });

  // Payment state
  const [isInitiatingPayment, setIsInitiatingPayment] = useState(false);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [paymentError, setPaymentError] = useState<string | null>(null);

  // Create payment intent mutation
  const createPaymentIntentMutation = useCreatePaymentIntent();

  console.log("💰🌻 Checkout Page Render. Session Active:", isReservationActive, "Expired:", isReservationExpired);

  const handleInitiatePayment = useCallback(async () => {
    // ... (your existing handleInitiatePayment logic) ...
    // It correctly sets clientSecret and showPaymentForm on success
    if (!session) {
      console.warn("⚠️ Attempted to proceed to payment without a session.");
      return;
    }

    console.log("🚀 Initiating payment for session ID:", session.id);
    setIsInitiatingPayment(true);
    setPaymentError(null);

    try {
      const response = await createPaymentIntentMutation.mutateAsync({ sessionId: session.id });

      if (response.success && response.clientSecret) {
        console.log("✅ Payment intent created successfully");
        setClientSecret(response.clientSecret); // State is updated here
        setShowPaymentForm(true);            // State is updated here

        toast.info("Payment Form Ready", {
          description: "Please complete your payment details",
        });
      } else {
        throw new Error(response.error || "Failed to initialize payment");
      }
    } catch (error) {
      console.error("❌ Error creating payment intent:", error);
      const errorMessage = error instanceof Error ? error.message : "Payment initialization failed";
      setPaymentError(errorMessage);

      toast.error("Payment Initialization Failed", {
        description: errorMessage,
      });
    } finally {
      setIsInitiatingPayment(false);
    }
  }, [session, createPaymentIntentMutation]);

  // ... (other handlers: handlePaymentSuccess, handlePaymentFailure, handlePaymentCancel, handleReturnToEvent) ...
   const handlePaymentSuccess = useCallback((paymentIntentId: string) => {
    console.log(`✅ Payment successful! Payment Intent ID: ${paymentIntentId}`);

    toast.success("Payment Successful!", {
      description: "Your order has been processed",
    });

    // Redirect to payment history page with success status
    setTimeout(() => {
      router.push(`/visitor/payments?status=success&paymentId=${paymentIntentId}`); // Updated path
    }, 1500);
  }, [router]);

    // Construct the return URL based on the current page's origin and path
  // Ensure window is defined (runs client-side)
  const returnUrl = typeof window !== 'undefined'
    ? `${window.location.origin}/checkout/${sessionId}` // Point back to the checkout page itself
    : ''; // Provide a fallback or handle server-side rendering case if needed

  const handlePaymentFailure = useCallback((error: Error) => {
    console.error(`❌ Payment failed: ${error.message}`);
    setPaymentError(error.message); // Keep showing error on the form

    toast.error("Payment Failed", {
      description: error.message,
    });
  }, []);

  const handlePaymentCancel = useCallback(() => {
    console.log("🔄 Payment canceled by user. Updating session status.");

    // Call the mutation exposed by the hook to update backend status
    if (sessionId) {
      updateSessionStatus({
        currentSessionId: sessionId,
        status: CheckoutSessionStatus.CANCELLED
      }, {
        onSuccess: () => {
          // Toast is handled by the mutation hook's default onSuccess
        },
        onError: () => {
          // Error toast is handled by the mutation hook's default onError
        }
      });
    } else {
      console.warn("⚠️ Cannot update status for cancellation: Session ID is missing.");
    }

    setShowPaymentForm(false); // Hide the form
    setClientSecret(null);     // Clear the secret

    toast.info("Payment Canceled", {
      description: "You can proceed again when ready",
    });
  }, [sessionId, updateSessionStatus]);

  const handleReturnToEvent = useCallback(() => {
    const eventId = session?.eventId;
    console.log(`↩️ Attempting to return to event. Event ID: ${eventId || 'N/A'}. Session ID: ${session?.id || 'N/A'}`);

    if (eventId) {
      router.push(`/events/${eventId}`);
    } else {
      console.warn("⚠️ Event ID not found in session, redirecting to homepage.");
      router.push('/');
    }
  }, [session?.eventId, session?.id, router]);


  // ... (loading states, error states) ...
    if (!sessionId) {
    console.error("❌ Session ID is missing from URL parameters. Redirecting to homepage.");
    router.push('/');
    return null;
  }

  if (isLoadingSession || isLoadingProfile) {
    console.log(`⏳ Loading checkout session...`);
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner />
        <span className="ml-4 text-lg">Loading Checkout...</span>
      </div>
    );
  }

  if (sessionError) {
    console.error(`❗ Error loading checkout session ID ${sessionId}:`, sessionError);
    return (
      <div className="container mx-auto px-4 py-10 text-center">
        <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Checkout</h1>
        <p className="text-muted-foreground mb-6">
          Could not load your checkout session. It might have expired or there was a server error.
        </p>
        <pre className="text-xs bg-muted p-2 rounded text-left mb-6">
          {(sessionError as Error).message}
        </pre>
        <button
          onClick={handleReturnToEvent}
          className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
        >
          Return to Events
        </button>
      </div>
    );
  }


  // *** MODIFIED RETURN STATEMENT ***
  return (
    // No <Elements> wrapper here at the top level
    <div className="container mx-auto px-4 py-10">
      <div className="max-w-2xl mx-auto">
        {/* Checkout Summary Component - Always rendered */}
        <CheckoutSummary
          session={session ?? null}
          isLoading={isLoadingSession}
          expiresAt={sessionExpiresAt}
          isExpired={isReservationExpired}
          onApplyCoupon={(couponCode) => applyCoupon.mutate({ couponCode })}
          onApplyPoints={(points) => applyPoints.mutate({ pointsToApply: points })}
          onRefreshSession={() => refreshSession.mutate()}
          // Only show proceed button if payment form isn't already visible
          onProceedToPayment={!showPaymentForm ? handleInitiatePayment : () => {}}
          onReturnToEvent={handleReturnToEvent}
          isApplyingCoupon={isApplyingCoupon}
          isApplyingPoints={isApplyingPoints}
          isRefreshingSession={isRefreshingSession}
          isProceedingToPayment={isInitiatingPayment}
          userPointsBalance={profile?.pointsBalance ?? 0}
          className="shadow-lg"
        />

        {/* Error Alert - Show when payment initiation fails (before form shows) */}
        {paymentError && !showPaymentForm && (
          <Alert variant="destructive" className="mt-6">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{paymentError}</AlertDescription>
          </Alert>
        )}

        {/* --- Conditional Rendering of Elements and Form --- */}
        {/* Only render Elements when showPaymentForm AND clientSecret are available */}
        {showPaymentForm && clientSecret && (
          // Wrap the payment form area with Elements, passing options
          <Elements
            stripe={stripePromise}
            options={{ clientSecret: clientSecret }} // CRITICAL: Pass options here
          >
            <div className="mt-8 transition-all duration-300 animate-in fade-in slide-in-from-bottom-4">
              <StripePaymentForm
                // clientSecret prop is still needed by StripePaymentForm for internal logic if any,
                // but the primary configuration happens in the Elements options above.
                clientSecret={clientSecret}
                onSuccess={handlePaymentSuccess}
                onFailure={handlePaymentFailure} // Let the form handle showing errors during payment
                onCancel={handlePaymentCancel}
                returnUrl={returnUrl}
                amount={session?.total ? Math.round(session.total) : undefined}
                currency={session?.currency || 'USD'}
              />
            </div>
          </Elements> // End Elements wrapper
        )}
      </div>
    </div>
  );
}
