import { Permission, UserRole } from "./types";

/**
 * Role-Based Permission Mappings
 * Defines the permissions available to each user role
 */
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  // Administrator Role - Full System Access
  ADMIN: [
    // Dashboard Access
    "dashboard:read",
    "dashboard:manage",

    // Full User Management
    "users:create",
    "users:read",
    "users:update",
    "users:delete",
    "users:manage",

    // Full Event Management
    "events:create",
    "events:read",
    "events:update",
    "events:delete",
    "events:manage",
    "events:publish",
    "events:settings",

    // Full Ticket Management
    "tickets:create",
    "tickets:read",
    "tickets:update",
    "tickets:delete",
    "tickets:manage",
    "tickets:book",
    "tickets:cancel",

    // Full Analytics Access
    "analytics:read",
    "analytics:export",
    "analytics:manage",

    // Full Settings Access
    "settings:read",
    "settings:manage"
  ],

  // Manager Role - Limited Administrative Access
  MANAGER: [
    // Dashboard Access
    "dashboard:read",

    // Limited User Management
    "users:read",
    "users:update",

    // Event Management (No Deletion)
    "events:create",
    "events:read",
    "events:update",
    "events:publish",
    "events:manage",  //! Can manage but can't delete

    // Ticket Management
    "tickets:read",
    "tickets:update",
    "tickets:manage",
    "tickets:book",
    "tickets:cancel",

    // Analytics Access
    "analytics:read",
    "analytics:export",

    // Limited Settings Access
    "settings:read"
  ],

  // Visitor Role - Basic User Access
  VISITOR: [
    // Basic Dashboard Access
    "dashboard:read",

    // Event Viewing
    "events:read",

    // Basic Ticket Operations
    "tickets:read",
    "tickets:book",
    "tickets:cancel",

    // Basic Settings Access
    "settings:read"
  ]
};

/**
 * Permission Groups
 * Logical groupings of permissions for specific features
 */
export const PERMISSION_GROUPS = {
  EVENT_MANAGEMENT: [
    "events:create",
    "events:read",
    "events:update",
    "events:delete",
    "events:manage"
  ],
  TICKET_OPERATIONS: [
    "tickets:create",
    "tickets:read",
    "tickets:update",
    "tickets:delete",
    "tickets:manage",
    "tickets:book",
    "tickets:cancel"
  ]
} as const;

/**
 * Required Permissions for Routes
 * Maps routes to required permissions
 */
export const ROUTE_PERMISSIONS: Record<string, Permission[]> = {
  "/admin/dashboard": ["dashboard:manage"],
  "/admin/users": ["users:manage"],
  "/admin/events": ["events:manage"],
  "/admin/analytics": ["analytics:manage"],
  "/manager/events": ["events:read", "events:update"],
  "/visitor/tickets": ["tickets:read", "tickets:book"]
};