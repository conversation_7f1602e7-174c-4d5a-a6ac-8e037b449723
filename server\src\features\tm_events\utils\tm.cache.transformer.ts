import { TmApiEvent, TmEvent } from '../types/tm.types';
import { Prisma } from '@prisma/client';

export class TmCacheTransformer {
          
            static toCacheModel(apiEvent: TmApiEvent): Omit<TmEvent, 'createdAt' | 'updatedAt' |'images'|'classifications'| 'venue'| 'priceRanges' |'sales'|'seatmap' |'ticketLimit'| 'accessibility'|'promoter'|'promoters'|'products'|'links' > {
                 const venue = apiEvent._embedded?.venues?.[0];
                 const classification = apiEvent.classifications[0];
                 const priceRange = apiEvent.priceRanges?.[0];

                return {
                    id: apiEvent.id,
                    name: apiEvent.name,
                    type: apiEvent.type,
                   url: apiEvent.url ?? null,
                    locale: apiEvent.locale ?? null,
                  
                   primaryImage: this.extractPrimaryImage(apiEvent.images),

                  // Dates and Status
                    startDateTime: apiEvent.dates.start.dateTime ? new Date(apiEvent.dates.start.dateTime) : null,
                    endDateTime: null,
                    timezone: apiEvent.dates.timezone,
                    status: this.toJsonValue(apiEvent.dates.status),

                  // Classifications
                  segment: classification?.segment?.name || null,
                   genre: classification?.genre?.name || null,
                    subGenre: classification?.subGenre?.name || null,

                   // Venue
                    venueName: venue?.name || null,
                    venueCity: venue?.city?.name || null,
                    venueState: venue?.state?.name || null,
                   venueCountry: venue?.country?.name || null,

                     // Pricing
                    priceRangeMin: priceRange?.min || null,
                    priceRangeMax: priceRange?.max || null,
                    currency: priceRange?.currency || null,
              };
            }


            private static extractPrimaryImage(images: TmApiEvent['images']): string | null {
                return images.find(img => img.ratio === '16_9')?.url || null;
            }
    
            private static toJsonValue(data: any): Prisma.JsonValue | null {
                if (data === null || data === undefined) {
                    return null;
                }
                return data as Prisma.JsonValue;
            }
            
    
           static transformBatchForCache(apiEvents: TmApiEvent[]): Omit<TmEvent, 'createdAt' | 'updatedAt' |'images'|'classifications'| 'venue'| 'priceRanges' |'sales'|'seatmap' |'ticketLimit'| 'accessibility'|'promoter'|'promoters'|'products'|'links' >[] {
                return apiEvents.map(event => this.toCacheModel(event));
              }
}
