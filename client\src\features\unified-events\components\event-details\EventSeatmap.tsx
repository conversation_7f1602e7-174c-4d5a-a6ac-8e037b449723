import React from "react";
import { UnifiedEvent } from "../../adapters/eventAdapter";
import { SeatmapDisplay } from "@/features/event-listing/components/SeatmapDisplay";
import { MapPin, InfoIcon } from "lucide-react";

interface EventSeatmapProps {
  event: UnifiedEvent;
}

export const EventSeatmap: React.FC<EventSeatmapProps> = ({ event }) => {
  // 🆕 ADD THIS DEBUG BLOCK
  console.log("🗺️ [EventSeatmap] Processing event for seatmap:");
  console.log("- Event Name:", event.name);
  console.log("- Event Source:", event.source);
  console.log("- Event originalEvent:", event.originalEvent);

  // Seatmap URL extraction with priority order
  let seatmapUrl: string | null = null;

  console.log("🗺️ [EventSeatmap] Checking seatmap extraction logic...");

  // 🆕 PRIORITY 1: Check if seatmapUrl is directly available from database (for priority events)
  if ((event.originalEvent?.rawEventData as any)?.seatmapUrl) {
    seatmapUrl = (event.originalEvent?.rawEventData as any).seatmapUrl;
    console.log(
      "✅ [EventSeatmap] Seatmap found via database seatmapUrl field:",
      seatmapUrl
    );
  }
  // PRIORITY 2: Check original TM seatmap structure
  else if (
    event.source === "ticketmaster" &&
    event.originalEvent?.seatmap?.staticUrl
  ) {
    seatmapUrl = event.originalEvent.seatmap.staticUrl;
    console.log(
      "✅ [EventSeatmap] TM Seatmap found via originalEvent.seatmap:",
      seatmapUrl
    );
  }
  // PRIORITY 3: Check manager events or alternative locations
  else if (event.source === "manager") {
    seatmapUrl =
      (event.originalEvent?.rawEventData as any)?.seatmap?.staticUrl || null;
    console.log("✅ [EventSeatmap] Manager Seatmap check:", seatmapUrl);

    // 🆕 ADD ALTERNATIVE CHECKS
    if (!seatmapUrl) {
      // Check different possible locations for seatmap
      const rawData = event.originalEvent?.rawEventData as any;
      console.log(
        "🔍 [EventSeatmap] Seatmap not found in expected location, checking alternatives..."
      );
      console.log(
        "- rawEventData.rawEventData.seatmap:",
        rawData?.rawEventData?.seatmap
      );
      console.log("- rawEventData.seatmap:", rawData?.seatmap);
      console.log(
        "- rawEventData._embedded?.venues?.[0]?.images:",
        rawData?._embedded?.venues?.[0]?.images
      );

      // Try alternative paths
      seatmapUrl =
        rawData?.rawEventData?.seatmap?.staticUrl ||
        rawData?.seatmap?.staticUrl ||
        null;

      if (seatmapUrl) {
        console.log(
          "✅ [EventSeatmap] Found seatmap in alternative location:",
          seatmapUrl
        );
      }
    }
  }
  // PRIORITY 4: Check for TM events stored as priority events with seatmap in rawEventData
  else if (event.source === "ticketmaster") {
    const rawData = event.originalEvent?.rawEventData as any;
    seatmapUrl = rawData?.seatmap?.staticUrl || null;
    console.log(
      "✅ [EventSeatmap] TM Priority Event seatmap check:",
      seatmapUrl
    );
  }

  console.log("🎯 [EventSeatmap] Final seatmap URL:", seatmapUrl);

  return (
    <div className="space-y-2">
      <h3 className="text-base lg:text-lg font-semibold flex items-center">
        <MapPin className="h-5 w-5 mr-2 text-primary" /> Seat Map
      </h3>
      {seatmapUrl ? (
        <div className="border rounded-lg overflow-hidden bg-muted/20 aspect-square lg:aspect-auto">
          <SeatmapDisplay seatmapUrl={seatmapUrl} />
        </div>
      ) : (
        <div className="h-full flex flex-col justify-center items-center min-h-[200px] bg-muted/20 p-6 rounded-lg border text-center">
          <InfoIcon className="h-10 w-10 mb-3 text-muted-foreground/50" />
          <p className="text-sm font-medium text-muted-foreground">
            Seat map information
          </p>
          <p className="text-xs text-muted-foreground/80">
            is not currently available for this event.
          </p>
        </div>
      )}
    </div>
  );
};
