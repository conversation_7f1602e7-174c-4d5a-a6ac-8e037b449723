/**
 * Membership Service Module
 *
 * Provides functionality to retrieve user membership information and
 * determine their corresponding queue priority.
 */

import { PrismaClient } from '@prisma/client';
import {
  MembershipTier,
  UserPriorityResponse,
  getPriorityFromTier // Import the helper function
} from '../types/membership.types';
import ApiError from '@/utils/ApiError'; // Assuming standard error handling

// Initialize Prisma Client instance
const prisma = new PrismaClient();

export class MembershipService {

  /**
   * Retrieves a user's membership tier and calculates their queue priority.
   *
   * @param userId - The ID of the user.
   * @returns A promise resolving to the UserPriorityResponse containing tier and priority.
   * @throws {ApiError} If the user is not found (404).
   */
  static async getUserPriority(userId: string): Promise<UserPriorityResponse> {
    console.log(`⚙️ [MembershipService] Fetching priority for user: ${userId}`);
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          membershipTier: true // Select only the necessary fields
        }
      });

      if (!user) {
        console.error(`❌ [MembershipService] User not found: ${userId}`);
        throw ApiError.notFound(`User with ID ${userId} not found.`);
      }

      // Ensure the tier exists and is a valid enum value (though Prisma should guarantee this)
      const tier = user.membershipTier as MembershipTier; // Cast based on our enum definition

      // Calculate priority using the helper function from types
      const priority = getPriorityFromTier(tier);

      console.log(`✅ [MembershipService] Priority calculated for user ${userId}: Tier=${tier}, Priority=${priority}`);

      return {
        userId: user.id,
        tier: tier,
        priority: priority
      };

    } catch (error) {
      // Log the error and re-throw appropriately
      console.error(`❌ [MembershipService] Error fetching priority for user ${userId}:`, error);
      if (error instanceof ApiError) {
        throw error; // Re-throw known API errors
      }
      // Wrap unexpected errors
      throw new ApiError(500, `Failed to retrieve membership priority for user ${userId}`);
    }
  }
}
