









  // Component to display verification status badges
import React from 'react';
import { useProfileQuery } from '@/features/profile/hooks/useProfileQuery';
import { useSession } from 'next-auth/react';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { CheckCircle, XCircle, Mail, Phone, AlertCircle, ExternalLink } from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

export const VerificationStatusDisplay: React.FC = () => {
  const { profile, isLoading: profileLoading, isError: profileError, error } = useProfileQuery();
  const { data: session, status: sessionStatus } = useSession();

  // Combine loading states
  const isLoading = profileLoading || sessionStatus === 'loading';

  // --- Loading State ---
  if (isLoading) {
    return (
      <div className="flex items-center gap-2">
        <Skeleton className="h-5 w-20 rounded-full" />
        <Skeleton className="h-5 w-20 rounded-full" />
      </div>
    );
  }

  // --- Error State (Prioritize profile error, but check session too) ---
  if (profileError || sessionStatus === 'unauthenticated') {
    // Handle profile fetch error specifically
    if (profileError) {
      return (
        <TooltipProvider delayDuration={100}>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center text-xs text-destructive gap-1 cursor-help">
                <AlertCircle className="h-4 w-4" />
                <span>Verification Error</span>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-xs">Could not load verification status: {error instanceof Error ? error.message : 'Unknown error'}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }
     // If profile didn't error but session is unauthenticated, render nothing or login prompt
     // Since useProfileQuery usually fails/returns null for unauthenticated, this case might be redundant
     // but added for robustness.
     console.log("🕵️ VerificationStatusDisplay: User unauthenticated, hiding status.");
     return null;
  }

  // --- Not Logged In or Profile Missing ---
  // If session is authenticated but profile is somehow missing (edge case)
  if (!profile) {
     console.warn("🕵️ VerificationStatusDisplay: Session authenticated but profile data missing.");
     // Render nothing or a generic error
     return null;
  }

  // --- Profile Loaded and Session Authenticated - Display Status ---
  const isEmailVerified = !!profile.emailVerified;
  const isMobileVerified = !!profile.mobileVerified;
  const isFullyVerified = isEmailVerified && isMobileVerified;

  // --- Determine Profile URL ---
  const userRole = session?.user?.role?.toLowerCase(); // Get role like "visitor", "manager", "admin"
  const profileUrl = userRole ? `/${userRole}/profile` : '/'; // Construct URL, fallback to /profile

  const VerificationBadge = ({ isVerified, type }: { isVerified: boolean; type: 'email' | 'mobile' }) => {
    const Icon = isVerified ? CheckCircle : XCircle;
    const text = type === 'email' ? 'Email' : 'Mobile';
    const colorClasses = isVerified
      ? 'border-emerald-200 bg-emerald-50 text-emerald-700 hover:bg-emerald-100'
      : 'border-amber-200 bg-amber-50 text-amber-700 hover:bg-amber-100';
    const tooltipText = isVerified ? `${text} Verified` : `${text} Not Verified`;

    return (
      <TooltipProvider delayDuration={100}>
        <Tooltip>
          <TooltipTrigger asChild>
             <Badge
              variant="outline"
              className={cn('py-1 px-2 text-xs cursor-default', colorClasses)}
             >
              <Icon className="h-3.5 w-3.5 mr-1" />
              {text}
             </Badge>
          </TooltipTrigger>
          <TooltipContent side="top">
            <p className="text-xs">{tooltipText}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  return (
    <div className="flex items-center gap-2">
      <VerificationBadge isVerified={isEmailVerified} type="email" />
      <VerificationBadge isVerified={isMobileVerified} type="mobile" />

      {!isFullyVerified && (
        <TooltipProvider delayDuration={100}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Link href={profileUrl} passHref>
                 <Badge
                  variant="outline"
                  className="py-1 px-2 text-xs border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100 cursor-pointer"
                 >
                  Verify Now
                  <ExternalLink className="h-3 w-3 ml-1"/>
                 </Badge>
              </Link>
            </TooltipTrigger>
            <TooltipContent side="top">
              <p className="text-xs">Complete verification in your profile</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
};
