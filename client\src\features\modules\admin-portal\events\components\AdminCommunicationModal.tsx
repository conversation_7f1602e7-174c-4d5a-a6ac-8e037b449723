import React, { useState } from 'react';
import { 
  <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer, DialogDescription 
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { MessageSquare, Send, Shield } from "lucide-react";
import { toast } from "sonner";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

const messageSchema = z.object({
  adminMessage: z.string().min(10, "Message must be at least 10 characters").max(500, "Message cannot exceed 500 characters")
});

type MessageFormValues = z.infer<typeof messageSchema>;

// This will be expanded with actual message data from the API
interface Message {
  id: string;
  message: string;
  senderRole: 'VISITOR' | 'MANAGER' | 'ADMIN';
  senderName: string;
  createdAt: string;
  avatarUrl?: string;
}

interface AdminCommunicationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  ticketId: string;
  buyerName: string;
  managerName: string;
  eventName: string;
  messages: Message[];
}

export const AdminCommunicationModal: React.FC<AdminCommunicationModalProps> = ({
  open,
  onOpenChange,
  ticketId,
  buyerName,
  managerName,
  eventName,
  messages = []
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const form = useForm<MessageFormValues>({
    resolver: zodResolver(messageSchema),
    defaultValues: {
      adminMessage: ""
    }
  });

  const onSubmit = async (values: MessageFormValues) => {
    setIsSubmitting(true);
    try {
      // API call will be implemented later
      console.log("Admin intervening in ticket:", ticketId, values);
      toast.success("Message sent to both visitor and manager");
      form.reset();
    } catch (error) {
      toast.error("Failed to send message");
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch(role) {
      case 'VISITOR': return 'bg-blue-500';
      case 'MANAGER': return 'bg-green-500';
      case 'ADMIN': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white border border-gray-700 sm:max-w-2xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <MessageSquare className="h-5 w-5 text-primary" />
            <span>Ticket Communication Oversight</span>
          </DialogTitle>
          <DialogDescription className="text-gray-300">
            Monitoring communication between <span className="font-medium text-blue-400">{buyerName}</span> and 
            <span className="font-medium text-green-400"> {managerName}</span> for event: <span className="font-medium text-primary">{eventName}</span>
          </DialogDescription>
        </DialogHeader>
        
        <div className="bg-gray-800/50 p-3 rounded-md my-2 border border-gray-700/50">
          <div className="flex items-center gap-2">
            <Shield className="h-4 w-4 text-purple-400" />
            <span className="text-sm text-purple-300">Admin Oversight Mode</span>
          </div>
          <p className="text-xs text-gray-400 mt-1">
            As an admin, you can monitor this conversation and intervene if necessary. Your messages will be visible to both the visitor and manager.
          </p>
        </div>
        
        <Separator className="bg-gray-700/50" />
        
        {/* Message History */}
        <ScrollArea className="flex-1 pr-4 my-4">
          <div className="space-y-4">
            {messages.map((msg) => (
              <div 
                key={msg.id} 
                className={`flex gap-3 ${
                  msg.senderRole === 'ADMIN' 
                    ? 'justify-center' 
                    : msg.senderRole === 'MANAGER' 
                      ? 'justify-end' 
                      : 'justify-start'
                }`}
              >
                {msg.senderRole !== 'MANAGER' && msg.senderRole !== 'ADMIN' && (
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={msg.avatarUrl} />
                    <AvatarFallback className="bg-blue-900 text-xs">
                      {msg.senderName.slice(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                )}
                
                <div className={`max-w-[80%] ${
                  msg.senderRole === 'ADMIN' 
                    ? 'bg-purple-900/40 border border-purple-700/50' 
                    : msg.senderRole === 'MANAGER' 
                      ? 'bg-green-900/40' 
                      : 'bg-gray-800/80'
                } p-3 rounded-lg`}>
                  <div className="flex items-center gap-2 mb-1">
                    <Badge className={`${getRoleBadgeColor(msg.senderRole)} text-xs px-2 py-0`}>
                      {msg.senderRole}
                    </Badge>
                    <span className="text-xs text-gray-400">{msg.senderName}</span>
                  </div>
                  <p className="text-sm">{msg.message}</p>
                  <span className="text-xs text-gray-500 mt-1 block text-right">
                    {new Date(msg.createdAt).toLocaleString()}
                  </span>
                </div>
                
                {msg.senderRole === 'MANAGER' && (
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={msg.avatarUrl} />
                    <AvatarFallback className="bg-green-800 text-xs">
                      {msg.senderName.slice(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                )}
                
                {msg.senderRole === 'ADMIN' && (
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={msg.avatarUrl} />
                    <AvatarFallback className="bg-purple-800 text-xs">
                      {msg.senderName.slice(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}
          </div>
        </ScrollArea>
        
        <Separator className="bg-gray-700/50" />
        
        {/* Admin Intervention Form */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 mt-4">
            <FormField
              control={form.control}
              name="adminMessage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-200 flex items-center gap-2">
                    <Shield className="h-4 w-4 text-purple-400" />
                    Admin Intervention
                  </FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Type your message to both parties..." 
                      className="min-h-[100px] bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-500"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-red-400" />
                </FormItem>
              )}
            />
            
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="border-gray-600 text-gray-300 hover:bg-gray-800"
              >
                Close
              </Button>
              <Button 
                type="submit" 
                disabled={isSubmitting}
                className="bg-purple-600 hover:bg-purple-700 text-white flex items-center gap-2"
              >
                <Send className="h-4 w-4" />
                Send as Admin
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};