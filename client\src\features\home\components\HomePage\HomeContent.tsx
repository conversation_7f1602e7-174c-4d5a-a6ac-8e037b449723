"use client";
import { useGetPriorityEventsQuery } from "@/state/api";
import { useHomeEvents } from "../../hooks/useHomeEvents";
import { EventCarouselHomePage } from "@/components/custom-components/EventComponents/EventCarouselHomePage";
import { EventCategorySelector } from "@/components/custom-components/EventComponents/EventCategorySelector";
import { EventsSection } from "./EventsSection";

// HomeContent component: Renders the main content of the home page
export const HomeContent = () => {
  // Fetch priority events on component mount.  RTK Query automatically provides isLoading.
  const { isLoading: apiLoading } = useGetPriorityEventsQuery();
  // console.log("HomeContent - apiLoading:", apiLoading); // Debugging log

  // Use existing hook for filtered data
  const sportsEvents = useHomeEvents("sports", { popularOnly: false });
  const musicEvents = useHomeEvents("music", { popularOnly: false });
  const artsEvents = useHomeEvents("arts", { popularOnly: false });

  // 🆕 ADD THIS DEBUG BLOCK
  console.log('🏠 [HomeContent] Events data loaded:');
  console.log('- Sports Events Count:', sportsEvents.events.length);
  console.log('- Music Events Count:', musicEvents.events.length);
  console.log('- Arts Events Count:', artsEvents.events.length);
  
  // Check first event from each category for seatmap data
  if (sportsEvents.events.length > 0) {
    console.log('🏈 [HomeContent] First Sports Event:', sportsEvents.events[0]);
    console.log('🏈 [HomeContent] Sports Event rawEventData:', sportsEvents.events[0]?.rawEventData);
  }

  return (
    <div className="container mx-auto">
      <EventCarouselHomePage />
      <EventCategorySelector />
      {/* TODO: //! Auto fill events when less than 3 events */}

      {/* Render event sections with RTK Query's isLoading passed directly */}
      <EventsSection
        title="Sports Events"
        events={sportsEvents.events}
        isLoading={apiLoading} // Pass RTK Query's isLoading directly
      />
      <EventsSection
        title="Music Events"
        events={musicEvents.events}
        isLoading={apiLoading} // Pass RTK Query's isLoading directly
      />
      <EventsSection
        title="Arts & Theatre"
        events={artsEvents.events}
        isLoading={apiLoading} // Pass RTK Query's isLoading directly
      />
    </div>
  );
};
