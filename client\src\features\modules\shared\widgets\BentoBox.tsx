import { Children, useState } from "react";
import { cn } from "@/lib/utils";
import { BentoGridItemType } from "@/features/modules/shared/types/dashboard.types";
import { motion } from "framer-motion";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { ChevronDown, ChevronUp } from "lucide-react";

export const BentoBox = ({
  title,
  description,
  header,
  className,
  children,
  itemLimit = 10,
  showMoreButton = true,
}: BentoGridItemType) => {
  const [showAll, setShowAll] = useState(false);

  // Convert children to array for manipulation
  const childrenArray = Children.toArray(children);
  const hasMoreItems = childrenArray.length > itemLimit;
  const displayedChildren = showAll
    ? childrenArray
    : childrenArray.slice(0, itemLimit);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn(
        "group relative rounded-xl border bg-card p-4 shadow-md transition-all hover:shadow-xl",
        className
      )}
    >
      {header && <div className="mb-4">{header}</div>}
      <div className="space-y-2">
        <h3 className="font-semibold text-card-foreground">{title}</h3>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </div>

      <ScrollArea className="h-[400px] mt-4">
        <div className="pr-4">
          {displayedChildren}

          {hasMoreItems && showMoreButton && (
            <div className="pt-4 flex justify-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAll(!showAll)}
                className="flex items-center gap-2"
              >
                {showAll ? (
                  <>
                    Show Less <ChevronUp className="h-4 w-4" />
                  </>
                ) : (
                  <>
                    Show More <ChevronDown className="h-4 w-4" />
                  </>
                )}
              </Button>
            </div>
          )}
        </div>
      </ScrollArea>
    </motion.div>
  );
};
