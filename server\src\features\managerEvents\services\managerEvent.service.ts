import { EventApprovalStatus, ManagerEvent, Prisma } from "@prisma/client";
import { CreateManagerEventPayload, UpdateManagerEventApprovalPayload } from "../types/managerEvent.types";
import ApiError from "@/utils/ApiError"; // Import ApiError
import { prisma } from "@/lib/prisma";

export class ManagerEventService {
  // Service class for managing inventory events in the database
  static async createManagerEvent(data: CreateManagerEventPayload): Promise<ManagerEvent> {
    console.log('📝 Creating new manager event:', data.name);

    // First, get the user ID from email
    const user = await prisma.user.findUnique({
      where: { email: data.managerId },
      select: { id: true }
    });

    if (!user) {
      throw new Error('Manager not found');
    }

    // Transform inventory array to Prisma InputJsonValue
    const inventoryJson: Prisma.InputJsonValue = JSON.parse(
      JSON.stringify(data.inventory)
    );

    // Transform rawEventData to Prisma InputJsonValue
    const rawEventJson: Prisma.InputJsonValue = data.rawEventData 
      ? JSON.parse(JSON.stringify(data.rawEventData))
      : null;

    // Transform purchaseOrder to Prisma InputJsonValue
    const purchaseOrderJson: Prisma.InputJsonValue = data.purchaseOrder 
      ? JSON.parse(JSON.stringify(data.purchaseOrder))
      : null;

    return prisma.managerEvent.create({
      data: {
        managerId: user.id, // Use the actual user ID instead of email
        eventId: data.eventId,
        name: data.name,
        category: data.category,
        source: data.source,
        date: new Date(data.date),
        venue: data.venue,
        city: data.city,
        country: data.country,
        image: data.image,
        inventory: inventoryJson,
        purchaseOrder: purchaseOrderJson,
        rawEventData: rawEventJson,
      },
    });
  }

  // New function: Get pending Manager Events
  static async getPendingManagerEvents(): Promise<ManagerEvent[]> {
    return prisma.managerEvent.findMany({
      where: { approvalStatus: "PENDING" },
      orderBy: { addedAt: "desc" }, // Most recent first
    });
  }
    // Update approval status for a manager event
    static async updateApprovalStatus(payload: UpdateManagerEventApprovalPayload): Promise<ManagerEvent> {
      const { id, approvalStatus, approvedBy, approvalNotes } = payload;

      // Fetch the event
      const existingEvent = await prisma.managerEvent.findUnique({
        where: { id },
      });

      if (!existingEvent) {
        throw ApiError.notFound('Manager event not found');
      }

      // Validate admin condition if approving
      if (approvalStatus === "APPROVED" && !approvedBy) {
        throw ApiError.badRequest("approvedBy is required for approval");
      }

      // Perform update with conditional date update for approvedAt
      return prisma.managerEvent.update({
        where: { id },
        data: {
          approvalStatus,
          approvedBy: approvalStatus === "APPROVED" ? approvedBy : null,
          approvedAt: approvalStatus === "APPROVED" ? new Date() : undefined,
          approvalNotes,
        },
      });
    }

    // Add this method to the ManagerEventService class

// Get approved Manager Events with pagination
static async getApprovedManagerEvents(
  page: number = 1,
  limit: number = 10,
  filters: any = {}
): Promise<{ events: ManagerEvent[]; pagination: any }> {
  const skip = (page - 1) * limit;
  
  // Build where clause based on filters
  const where: any = { 
    approvalStatus: "APPROVED",
    // Only show future events (today or later)
    date: { gte: new Date() }
  };
  
  if (filters.category) {
    where.category = filters.category.toUpperCase();
  }
  
  if (filters.city) {
    where.city = { contains: filters.city, mode: 'insensitive' };
  }
  
  if (filters.dateFrom) {
    where.date = { 
      ...(where.date || {}),
      gte: new Date(filters.dateFrom)
    };
  }
  
  if (filters.dateTo) {
    where.date = { 
      ...(where.date || {}),
      lte: new Date(filters.dateTo)
    };
  }
  
  // Count total events matching the criteria
  const totalEvents = await prisma.managerEvent.count({ where });
  
  // Fetch events with pagination
  const events = await prisma.managerEvent.findMany({
    where,
    skip,
    take: limit,
    orderBy: { date: 'asc' }, // Show soonest events first
  });
  
  // Calculate total pages
  const totalPages = Math.ceil(totalEvents / limit);
  
  return {
    events,
    pagination: {
      page,
      pageSize: limit,
      totalItems: totalEvents,
      totalPages,
    }
  };
}

// Add this new method after the existing getApprovedManagerEvents method
static async getManagerEventsByStatus(
  status: EventApprovalStatus,
  page: number = 1,
  limit: number = 4,
  filters: any = {}
): Promise<{ events: ManagerEvent[]; pagination: any }> {
  const skip = (page - 1) * limit;
  
  // Build where clause based on status and filters
  const where: any = { 
    approvalStatus: status
  };
  
  // Only show future events for approved status
  if (status === "APPROVED") {
    where.date = { gte: new Date() };
  }
  
  if (filters.category) {
    where.category = filters.category.toUpperCase();
  }
  
  if (filters.city) {
    where.city = { contains: filters.city, mode: 'insensitive' };
  }
  
  if (filters.dateFrom) {
    where.date = { 
      ...(where.date || {}),
      gte: new Date(filters.dateFrom)
    };
  }
  
  if (filters.dateTo) {
    where.date = { 
      ...(where.date || {}),
      lte: new Date(filters.dateTo)
    };
  }
  
  // Count total events matching the criteria
  const totalEvents = await prisma.managerEvent.count({ where });
  
  // Fetch events with pagination
  const events = await prisma.managerEvent.findMany({
    where,
    skip,
    take: limit,
    orderBy: status === "PENDING" ? { addedAt: 'desc' } : { date: 'asc' },
  });
  
  // Calculate total pages
  const totalPages = Math.ceil(totalEvents / limit);
  
  return {
    events,
    pagination: {
      page,
      pageSize: limit,
      totalItems: totalEvents,
      totalPages,
    }
  };
}
  }
