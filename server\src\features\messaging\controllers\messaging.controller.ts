/**
 * Messaging Controller
 * 
 * HTTP request handlers for the ticket messaging system.
 * Handles validation, authentication, and response formatting
 * following the established API patterns.
 */

import { Request, Response } from 'express';
import { asyncHandler } from '@/utils/asyncHandler';
import ApiError from '@/utils/ApiError';
import { MessagingService } from '../services/messaging.service';
import { prisma } from '@/lib/prisma';
import { UserRole, MessageStatus } from '@prisma/client';
import {
  SendMessageRequest,
  GetConversationRequest,
  AdminMessagesRequest,
  SendMessageResponse,
  GetConversationResponse,
  AdminConversationsResponse
} from '../types/messaging.types';

export class MessagingController {
  private messagingService: MessagingService;

  constructor() {
    this.messagingService = new MessagingService();
  }

  // ============================================================================
  // VISITOR & MANAGER ENDPOINTS
  // ============================================================================

  /**
   * Send a new message in a ticket conversation
   * POST /api/v1/messaging/send
   */
  sendMessage = asyncHandler(async (req: Request, res: Response) => {
    const userId = this.extractUserId(req);
    console.log('💬 [MessagingController] Send message request - User ID:', userId);
    
    const { checkoutSessionId, message, conversationId, parentMessageId } = req.body as SendMessageRequest;
    
    if (!checkoutSessionId || !message) {
      throw new ApiError(400, 'Checkout session ID and message are required');
    }

    if (message.trim().length < 10) {
      throw new ApiError(400, 'Message must be at least 10 characters long');
    }

    if (message.length > 1000) {
      throw new ApiError(400, 'Message cannot exceed 1000 characters');
    }

    const ipAddress = req.ip || req.connection.remoteAddress || undefined;
    const userAgent = req.headers['user-agent'] || undefined;

    const messageData: SendMessageRequest = {
      checkoutSessionId,
      message: message.trim(),
      conversationId,
      parentMessageId,
      ipAddress,
      userAgent,
    };

    console.log('📤 [MessagingController] Sending message:', messageData);

    const newMessage = await this.messagingService.sendMessage(userId, messageData);

    const response: SendMessageResponse = {
      success: true,
      message: 'Message sent successfully',
      data: newMessage,
    };

    res.status(201).json(response);
  });

  /**
   * Get conversation messages for a specific checkout session
   * GET /api/v1/messaging/conversation/:checkoutSessionId
   */
  getConversation = asyncHandler(async (req: Request, res: Response) => {
    const userId = this.extractUserId(req);
    const { checkoutSessionId } = req.params;
    
    if (!checkoutSessionId) {
      throw new ApiError(400, 'Checkout session ID is required');
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 50, 100);
    const includeResolved = req.query.includeResolved === 'true';

    const options: GetConversationRequest = {
      page,
      limit,
      includeResolved,
    };

    const conversationData = await this.messagingService.getConversation(
      userId, 
      checkoutSessionId, 
      options
    );

    const response: GetConversationResponse = {
      success: true,
      message: 'Conversation retrieved successfully',
      data: {
        conversation: conversationData,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(conversationData.messages.length / limit),
          totalItems: conversationData.messages.length,
          hasNextPage: page * limit < conversationData.messages.length,
          hasPrevPage: page > 1,
        }
      },
    };

    res.status(200).json(response);
  });

  /**
   * Mark a specific message as read
   * PATCH /api/v1/messaging/message/:messageId/read
   */
  markMessageAsRead = asyncHandler(async (req: Request, res: Response) => {
    const userId = this.extractUserId(req);
    const { messageId } = req.params;

    if (!messageId) {
      throw new ApiError(400, 'Message ID is required');
    }

    await this.messagingService.markAsRead(userId, messageId);

    res.status(200).json({
      success: true,
      message: 'Message marked as read',
      data: null,
    });
  });

  /**
   * Mark entire conversation as resolved
   * PATCH /api/v1/messaging/conversation/:checkoutSessionId/resolve
   */
  resolveConversation = asyncHandler(async (req: Request, res: Response) => {
    const userId = this.extractUserId(req);
    const { checkoutSessionId } = req.params;

    if (!checkoutSessionId) {
      throw new ApiError(400, 'Checkout session ID is required');
    }

    await this.messagingService.markConversationAsResolved(userId, checkoutSessionId);

    res.status(200).json({
      success: true,
      message: 'Conversation marked as resolved',
      data: null,
    });
  });

  /**
   * ✅ FIXED: Get user's conversations summary (for dashboard)
   * GET /api/v1/messaging/my-conversations
   */
  getMyConversations = asyncHandler(async (req: Request, res: Response) => {
    const userId = this.extractUserId(req);
    
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 50);
    const statusFilter = req.query.status as string;

    // Get user role to determine access pattern
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, role: true }
    });

    if (!user) {
      throw new ApiError(404, 'User not found');
    }

    let conversationSummaries: Array<{
      checkoutSessionId: string;
      ticketId: string;
      lastMessageDate: string;
      messageCount: number;
      unreadCount: number;
      latestMessage: {
        message: string;
        senderRole: UserRole;
        createdAt: string;
      } | null;
    }> = [];

    try {
      if (user.role === UserRole.VISITOR) {
        const visitorConversations = await this.getVisitorConversations(userId, page, limit, statusFilter);
        conversationSummaries = visitorConversations;
        
      } else if (user.role === UserRole.MANAGER) {
        const managerConversations = await this.getManagerConversations(userId, page, limit, statusFilter);
        conversationSummaries = managerConversations;
        
      } else if (user.role === UserRole.ADMIN) {
        const adminConversations = await this.getAdminConversationsForOverview(page, limit, statusFilter);
        conversationSummaries = adminConversations;
      }

      res.status(200).json({
        success: true,
        message: 'Conversations retrieved successfully',
        data: {
          conversations: conversationSummaries,
          pagination: {
            currentPage: page,
            totalPages: Math.ceil(conversationSummaries.length / limit),
            totalItems: conversationSummaries.length,
            hasNextPage: page * limit < conversationSummaries.length,
            hasPrevPage: page > 1,
          }
        }
      });

    } catch (error) {
      console.error('❌ [MessagingController] Error getting conversations:', error);
      throw new ApiError(500, 'Failed to retrieve conversations');
    }
  });

  // ============================================================================
  // ADMIN ENDPOINTS
  // ============================================================================

  /**
   * Get all conversations for admin oversight
   * GET /api/v1/messaging/admin/conversations
   */
  getAdminConversations = asyncHandler(async (req: Request, res: Response) => {
    const userId = this.extractUserId(req);
    
    await this.validateAdminAccess(userId);

    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 50);
    const status = req.query.status as any;
    const senderRole = req.query.senderRole as any;
    const eventId = req.query.eventId as string;
    const dateFrom = req.query.dateFrom as string;
    const dateTo = req.query.dateTo as string;

    const options: AdminMessagesRequest = {
      page,
      limit,
      status,
      senderRole,
      eventId,
      dateFrom,
      dateTo,
    };

    const adminData = await this.messagingService.getAllConversations(options);

    const response: AdminConversationsResponse = {
      success: true,
      message: 'Admin conversations retrieved successfully',
      data: adminData,
    };

    res.status(200).json(response);
  });

  /**
   * Escalate a conversation for admin intervention
   * POST /api/v1/messaging/admin/escalate/:checkoutSessionId
   */
  escalateConversation = asyncHandler(async (req: Request, res: Response) => {
    const adminId = this.extractUserId(req);
    const { checkoutSessionId } = req.params;

    if (!checkoutSessionId) {
      throw new ApiError(400, 'Checkout session ID is required');
    }

    await this.validateAdminAccess(adminId);
    await this.messagingService.escalateConversation(checkoutSessionId, adminId);

    res.status(200).json({
      success: true,
      message: 'Conversation escalated successfully',
      data: null,
    });
  });

  /**
   * Get conversation details for admin intervention
   * GET /api/v1/messaging/admin/conversation/:checkoutSessionId
   */
  getAdminConversationDetails = asyncHandler(async (req: Request, res: Response) => {
    const adminId = this.extractUserId(req);
    const { checkoutSessionId } = req.params;

    if (!checkoutSessionId) {
      throw new ApiError(400, 'Checkout session ID is required');
    }

    await this.validateAdminAccess(adminId);

    const conversationData = await this.messagingService.getConversation(
      adminId, 
      checkoutSessionId,
      { page: 1, limit: 100, includeResolved: true }
    );

    const response: GetConversationResponse = {
      success: true,
      message: 'Admin conversation details retrieved successfully',
      data: {
        conversation: conversationData,
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalItems: conversationData.messages.length,
          hasNextPage: false,
          hasPrevPage: false,
        }
      },
    };

    res.status(200).json(response);
  });

  /**
   * Send admin message to a conversation
   * POST /api/v1/messaging/admin/send
   */
  sendAdminMessage = asyncHandler(async (req: Request, res: Response) => {
    const adminId = this.extractUserId(req);
    
    await this.validateAdminAccess(adminId);

    const { checkoutSessionId, message, conversationId } = req.body as SendMessageRequest;
    
    if (!checkoutSessionId || !message) {
      throw new ApiError(400, 'Checkout session ID and message are required');
    }

    if (message.trim().length < 5) {
      throw new ApiError(400, 'Admin message must be at least 5 characters long');
    }

    if (message.length > 1000) {
      throw new ApiError(400, 'Message cannot exceed 1000 characters');
    }

    const ipAddress = req.ip || req.connection.remoteAddress || undefined;
    const userAgent = req.headers['user-agent'] || undefined;

    const messageData: SendMessageRequest = {
      checkoutSessionId,
      message: `[ADMIN INTERVENTION] ${message.trim()}`,
      conversationId,
      ipAddress,
      userAgent,
    };

    const newMessage = await this.messagingService.sendMessage(adminId, messageData);

    const response: SendMessageResponse = {
      success: true,
      message: 'Admin message sent successfully',
      data: newMessage,
    };

    res.status(201).json(response);
  });

  /**
   * Get messaging system statistics for admin dashboard
   * GET /api/v1/messaging/admin/stats
   */
  getMessagingStats = asyncHandler(async (req: Request, res: Response) => {
    const adminId = this.extractUserId(req);
    
    await this.validateAdminAccess(adminId);

    const dateFrom = req.query.dateFrom as string;
    const dateTo = req.query.dateTo as string;

    const dateFilter: any = {};
    if (dateFrom) dateFilter.gte = new Date(dateFrom);
    if (dateTo) dateFilter.lte = new Date(dateTo);

    const whereClause = Object.keys(dateFilter).length > 0 ? { createdAt: dateFilter } : {};

    const [
      totalMessages,
      totalConversations,
      messagesByRole,
      messagesByStatus,
      recentActivity
    ] = await Promise.all([
      prisma.ticketMessage.count({ where: whereClause }),
      
      prisma.ticketMessage.groupBy({
        by: ['checkoutSessionId'],
        where: whereClause
      }).then(result => result.length),
      
      prisma.ticketMessage.groupBy({
        by: ['senderRole'],
        where: whereClause,
        _count: { id: true }
      }),
      
      prisma.ticketMessage.groupBy({
        by: ['status'],
        where: whereClause,
        _count: { id: true }
      }),
      
      prisma.ticketMessage.count({
        where: {
          ...whereClause,
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
          }
        }
      })
    ]);

    res.status(200).json({
      success: true,
      message: 'Messaging statistics retrieved successfully',
      data: {
        overview: {
          totalMessages,
          totalConversations,
          recentActivity24h: recentActivity,
          averageMessagesPerConversation: totalConversations > 0 ? Math.round(totalMessages / totalConversations) : 0
        },
        breakdown: {
          byRole: messagesByRole.reduce((acc, item) => {
            acc[item.senderRole] = item._count.id;
            return acc;
          }, {} as Record<string, number>),
          byStatus: messagesByStatus.reduce((acc, item) => {
            acc[item.status] = item._count.id;
            return acc;
          }, {} as Record<string, number>)
        },
        dateRange: {
          from: dateFrom || 'All time',
          to: dateTo || 'Present'
        }
      }
    });
  });

  // ============================================================================
  // PRIVATE HELPER METHODS - ✅ COMPLETELY REWRITTEN
  // ============================================================================

  /**
   * ✅ NEW: Get conversations for visitors
   */
  private async getVisitorConversations(
    userId: string, 
    page: number, 
    limit: number, 
    statusFilter?: string
  ) {
    const skip = (page - 1) * limit;

    // Get all checkout sessions for this visitor that have messages
    const checkoutSessions = await prisma.checkoutSession.findMany({
      where: {
        userId,
        ticketMessages: {
          some: {} // Only sessions that have messages
        }
      },
      select: { id: true },
      skip,
      take: limit,
    });

    const conversations = await Promise.all(
      checkoutSessions.map(async (session) => {
        return await this.buildConversationSummary(session.id, userId, statusFilter);
      })
    );

    return conversations.filter(conv => conv !== null) as Array<{
      checkoutSessionId: string;
      ticketId: string;
      lastMessageDate: string;
      messageCount: number;
      unreadCount: number;
      latestMessage: {
        message: string;
        senderRole: UserRole;
        createdAt: string;
      } | null;
    }>;
  }

  /**
   * ✅ NEW: Get conversations for managers
   */
  private async getManagerConversations(
    userId: string, 
    page: number, 
    limit: number, 
    statusFilter?: string
  ) {
    const skip = (page - 1) * limit;

    // Get manager's events
    const managerEvents = await prisma.managerEvent.findMany({
      where: { managerId: userId },
      select: { eventId: true }
    });

    const eventIds = managerEvents.map(event => event.eventId);

    if (eventIds.length === 0) {
      return [];
    }

    // Get checkout sessions for manager's events that have messages
    const checkoutSessions = await prisma.checkoutSession.findMany({
      where: {
        eventId: { in: eventIds },
        ticketMessages: {
          some: {} // Only sessions that have messages
        }
      },
      select: { id: true },
      skip,
      take: limit,
    });

    const conversations = await Promise.all(
      checkoutSessions.map(async (session) => {
        return await this.buildConversationSummary(session.id, userId, statusFilter);
      })
    );

    return conversations.filter(conv => conv !== null) as Array<{
      checkoutSessionId: string;
      ticketId: string;
      lastMessageDate: string;
      messageCount: number;
      unreadCount: number;
      latestMessage: {
        message: string;
        senderRole: UserRole;
        createdAt: string;
      } | null;
    }>;
  }

  /**
   * ✅ NEW: Get conversations for admin overview
   */
  private async getAdminConversationsForOverview(
    page: number, 
    limit: number, 
    statusFilter?: string
  ) {
    const skip = (page - 1) * limit;

    // Get all checkout sessions that have messages
    const checkoutSessions = await prisma.checkoutSession.findMany({
      where: {
        ticketMessages: {
          some: {} // Only sessions that have messages
        }
      },
      select: { id: true },
      skip,
      take: limit,
    });

    const conversations = await Promise.all(
      checkoutSessions.map(async (session) => {
        return await this.buildConversationSummary(session.id, 'admin', statusFilter);
      })
    );

    return conversations.filter(conv => conv !== null) as Array<{
      checkoutSessionId: string;
      ticketId: string;
      lastMessageDate: string;
      messageCount: number;
      unreadCount: number;
      latestMessage: {
        message: string;
        senderRole: UserRole;
        createdAt: string;
      } | null;
    }>;
  }

  /**
   * ✅ NEW: Build conversation summary for a checkout session
   */
  private async buildConversationSummary(
    checkoutSessionId: string, 
    userId: string, 
    statusFilter?: string
  ) {
    try {
      // Build status filter
      const statusFilterArray: MessageStatus[] = [];
      if (statusFilter) {
        if (Object.values(MessageStatus).includes(statusFilter as MessageStatus)) {
          statusFilterArray.push(statusFilter as MessageStatus);
        }
      } else {
        statusFilterArray.push(MessageStatus.UNREAD, MessageStatus.READ, MessageStatus.RESOLVED);
      }

      const [latestMessage, messageCount, unreadCount] = await Promise.all([
        // Get latest message
        prisma.ticketMessage.findFirst({
          where: { 
            checkoutSessionId,
            status: { in: statusFilterArray }
          },
          select: {
            message: true,
            senderRole: true,
            createdAt: true,
          },
          orderBy: { createdAt: 'desc' }
        }),

        // Get total message count
        prisma.ticketMessage.count({
          where: { 
            checkoutSessionId,
            status: { in: statusFilterArray }
          }
        }),

        // Get unread count (exclude own messages for non-admin)
        prisma.ticketMessage.count({
          where: {
            checkoutSessionId,
            status: MessageStatus.UNREAD,
            ...(userId !== 'admin' && { senderId: { not: userId } })
          }
        })
      ]);

      if (!latestMessage) {
        return null;
      }

      return {
        checkoutSessionId,
        ticketId: checkoutSessionId.substring(0, 8) + '...',
        lastMessageDate: latestMessage.createdAt.toISOString(),
        messageCount,
        unreadCount,
        latestMessage: {
          message: latestMessage.message.length > 100 
            ? latestMessage.message.substring(0, 100) + '...'
            : latestMessage.message,
          senderRole: latestMessage.senderRole,
          createdAt: latestMessage.createdAt.toISOString()
        }
      };

    } catch (error) {
      console.error(`❌ Error building conversation summary for ${checkoutSessionId}:`, error);
      return null;
    }
  }

  /**
   * 🔧 FIXED: Extract user ID from authenticated request with better debugging
   */
  private extractUserId(req: Request): string {
    // Add detailed logging to debug JWT payload structure
    const user = (req as any).user;
    console.log('🔐 [MessagingController] JWT payload:', JSON.stringify(user, null, 2));
    
    // Try multiple possible user ID fields based on different JWT structures
    const userId = user?.userId ||  // Standard userId field
                   user?.id ||      // Alternative id field  
                   user?.sub ||     // JWT standard subject field
                   user?.user_id;   // Another common variant
    
    console.log('👤 [MessagingController] Extracted user ID:', userId);
    
    if (!userId) {
      console.error('❌ [MessagingController] No user ID found in JWT payload:', user);
      throw new ApiError(401, 'Authentication required - No user ID in token');
    }
    
    return userId;
  }

  /**
   * Validate that the user has admin access
   */
  private async validateAdminAccess(userId: string): Promise<void> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, role: true }
    });

    if (!user) {
      throw new ApiError(404, 'User not found');
    }

    if (user.role !== UserRole.ADMIN) {
      throw new ApiError(403, 'Admin access required');
    }
  }
}
