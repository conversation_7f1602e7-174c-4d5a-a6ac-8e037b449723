/**
 * Hook for subscription management
 */
import { useCallback } from 'react';
import { 
  useSubscriptionStatus, 
  useCreateSubscriptionCheckout, 
  useCancelSubscription 
} from '../api/subscriptionApi';
import { SubscriptionPlanType } from '../types/subscription.types';

export function useSubscription() {
  // Query for current subscription status
  const {
    data: subscriptionData,
    isLoading: isLoadingSubscription,
    error: subscriptionError,
    refetch: refetchSubscription,
  } = useSubscriptionStatus();

  // Mutation to create checkout session
  const createCheckoutMutation = useCreateSubscriptionCheckout();

  // Mutation to cancel subscription
  const cancelSubscriptionMutation = useCancelSubscription();

  // Extract subscription status or provide default
  const subscriptionStatus = subscriptionData?.data?.status || {
    active: false,
    planType: null,
    currentPeriodEnd: undefined,
    cancelAtPeriodEnd: false,
  };

  // Function to subscribe to a plan
  const subscribeToPlan = useCallback(
    (planType: SubscriptionPlanType) => {
      return createCheckoutMutation.mutate({ planType });
    },
    [createCheckoutMutation]
  );

  // Function to cancel subscription
  const cancelSubscription = useCallback(
    (immediately: boolean = false) => {
      return cancelSubscriptionMutation.mutate({ immediately });
    },
    [cancelSubscriptionMutation]
  );

  // Format remaining time in subscription
  const getRemainingTime = useCallback(() => {
    if (!subscriptionStatus.currentPeriodEnd) return null;

    const endDate = new Date(subscriptionStatus.currentPeriodEnd);
    const now = new Date();
    const diffTime = endDate.getTime() - now.getTime();
    
    // If already ended
    if (diffTime <= 0) return 'Ended';
    
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 30) {
      const diffMonths = Math.floor(diffDays / 30);
      return `${diffMonths} month${diffMonths > 1 ? 's' : ''}`;
    }
    
    return `${diffDays} day${diffDays > 1 ? 's' : ''}`;
  }, [subscriptionStatus.currentPeriodEnd]);

  return {
    // Status
    isSubscribed: subscriptionStatus.active,
    currentPlan: subscriptionStatus.planType,
    isLoading: isLoadingSubscription,
    error: subscriptionError,
    cancelAtPeriodEnd: subscriptionStatus.cancelAtPeriodEnd,
    currentPeriodEnd: subscriptionStatus.currentPeriodEnd,
    remainingTime: getRemainingTime(),
    
    // Actions
    subscribeToPlan,
    cancelSubscription,
    refetchSubscription,
    
    // Mutation states
    isCreatingCheckout: createCheckoutMutation.isPending,
    isCanceling: cancelSubscriptionMutation.isPending,
  };
}