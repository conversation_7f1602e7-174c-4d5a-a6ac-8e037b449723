import { cn } from "@/lib/utils";
import { MetricCardType } from "@/features/modules/shared/types/dashboard.types";
import { ArrowDown, ArrowUp } from "lucide-react";
import { motion } from "framer-motion";

export const MetricCard = ({
  title,
  value,
  icon,
  trend,
  className,
}: MetricCardType) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className={cn(
        "rounded-xl border bg-card p-6 shadow-sm transition-all",
        className
      )}
    >
      <div className="flex items-center justify-between">
        <p className="text-sm font-medium text-muted-foreground">{title}</p>
        {icon && <div className="text-muted-foreground">{icon}</div>}
      </div>
      <div className="mt-2 flex items-baseline">
        <h3 className="text-2xl font-semibold text-card-foreground">{value}</h3>
        {trend && (
          <span
            className={cn(
              "ml-2 flex items-center text-sm",
              trend.isPositive ? "text-green-600" : "text-red-600"
            )}
          >
            {trend.isPositive ? (
              <ArrowUp className="h-4 w-4" />
            ) : (
              <ArrowDown className="h-4 w-4" />
            )}
            {trend.value}%
          </span>
        )}
      </div>
    </motion.div>
  );
};
