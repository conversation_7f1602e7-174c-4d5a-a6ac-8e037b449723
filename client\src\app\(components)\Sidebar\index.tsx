import { Home, Calendar, Ticket, Settings, LucideIcon } from "lucide-react";
import { useEffect } from "react";
import { useAppSelector, useAppDispatch } from "@/app/redux";
import { setIsSidebarCollapsed } from "@/state";
import { usePathname } from "next/navigation";

type NavItem = {
  icon: React.ReactElement<LucideIcon>;
  label: string;
};

const navItems = [
  { icon: <Home className="h-6 w-6" />, label: "Home" },
  { icon: <Calendar className="h-6 w-6" />, label: "Events" },
  { icon: <Ticket className="h-6 w-6" />, label: "My Tickets" },
  { icon: <Settings className="h-6 w-6" />, label: "Settings" },
];

const Sidebar = () => {
  const dispatch = useAppDispatch();
  const isSidebarCollapsed = useAppSelector(
    (state) => state.global.isSidebarCollapsed
  );
  const pathname = usePathname();

  // Auto-collapse on small screens
  useEffect(() => {
    const handleResize = () => {
      dispatch(setIsSidebarCollapsed(window.innerWidth < 768));
    };

    // Initial check
    handleResize();

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [dispatch]);

  return (
    <div
      className={`fixed inset-y-0 left-0 z-50 bg-background shadow-lg transition-all duration-300 ease-in-out border-r
        ${isSidebarCollapsed ? "w-16" : "w-64"}
        md:w-64 md:translate-x-0
      `}
    >
      <div className="flex items-center justify-between p-5 border-b">
        <h2
          className={`text-xl font-bold ${
            isSidebarCollapsed ? "hidden" : "block"
          } md:block`}
        >
          Fanseatmaster
        </h2>
        <span className="flex-shrink-0">logo</span>
      </div>

      <nav className="p-4">
        <ul className="space-y-2">
          {navItems.map((item) => (
            <li key={item.label}>
              <div
                className={`flex items-center p-3 rounded-lg hover:bg-accent/50 transition-all duration-200
                  ${
                    isSidebarCollapsed
                      ? "justify-center md:justify-start"
                      : "justify-start"
                  }
                  group cursor-pointer
                `}
              >
                <div className="relative flex items-center gap-3">
                  {item.icon}
                  {isSidebarCollapsed && (
                    <span className="absolute left-full ml-2 px-2 py-1 bg-accent text-accent-foreground text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                      {item.label}
                    </span>
                  )}
                  <span
                    className={`text-sm font-medium ${
                      isSidebarCollapsed ? "hidden" : "block"
                    } md:block`}
                  >
                    {item.label}
                  </span>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
};

export default Sidebar;
