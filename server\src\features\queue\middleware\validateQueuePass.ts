/**
 * Middleware to validate if a user is allowed to proceed with checkout
 * based on their queue status for an event.
 */

import { Request, Response, NextFunction } from 'express';
import { QueueService } from '../services/queue.service';
import { QueueUserStatus } from '@prisma/client';
import ApiError from '@/utils/ApiError';
import { asyncHandler } from '@/utils/asyncHandler';

const queueService = new QueueService();

/**
 * Extract user ID from authenticated request
 */
const extractUserId = (req: Request): string | null => {
  // This assumes your auth middleware adds user info to req
  return (req as any).user?.userId || null;
};

/**
 * Middleware that validates if a user can proceed past a waiting room queue.
 * This should be applied to checkout finalization routes.
 */
export const validateQueuePass = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    const eventId = req.params.eventId || req.body.eventId;
    const userId = extractUserId(req);

    if (!eventId) {
      return next(new ApiError(400, 'Event ID is required'));
    }

    if (!userId) {
      return next(new ApiError(401, 'Authentication required'));
    }

    try {
      // Get queue status for this event and user
      const queueStatus = await queueService.getQueueStatus(eventId, userId);

      // If queue is not active, allow the request to proceed
      if (!queueStatus.isActive) {
        return next();
      }

      // If queue is active but user status is missing, they need to join queue
      if (!queueStatus.userStatus) {
        throw new ApiError(403, 'Please join the waiting room for this event', [
          { 
            code: 'QUEUE_REQUIRED',
            detail: 'This event has a waiting room. Please join it first.'
          }
        ]);
      }

      // Check if user is admitted (ACTIVE status)
      if (queueStatus.userStatus.status !== QueueUserStatus.ACTIVE) {
        // Different message based on status
        if (queueStatus.userStatus.status === QueueUserStatus.WAITING) {
          throw new ApiError(403, 'You are in line but have not been admitted yet', [
            { 
              code: 'QUEUE_WAITING',
              detail: `Your current position is ${queueStatus.userStatus.position || 'unknown'}.`,
              position: queueStatus.userStatus.position
            }
          ]);
        } else if (queueStatus.userStatus.status === QueueUserStatus.EXPIRED) {
          throw new ApiError(403, 'Your session has expired', [
            { 
              code: 'QUEUE_EXPIRED',
              detail: 'Your place in line has expired. Please rejoin the waiting room.'
            }
          ]);
        } else {
          throw new ApiError(403, 'Waiting room validation failed', [
            { code: 'QUEUE_INVALID' }
          ]);
        }
      }

      // If we got here, user is ACTIVE in queue - allow them to proceed
      next();
    } catch (error) {
      // If it's already an ApiError, pass it through
      if (error instanceof ApiError) {
        return next(error);
      }
      
      // Otherwise wrap in a generic error
      console.error('Error validating queue pass:', error);
      next(new ApiError(500, 'Failed to validate waiting room status'));
    }
  }
);
