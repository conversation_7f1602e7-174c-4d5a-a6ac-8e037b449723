import { useState, useRef } from "react";
import { Star, Trash2 } from "lucide-react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import {
  useTogglePopularStatusMutation,
  useDeletePriorityEventMutation,
} from "@/state/api";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { useAppDispatch } from "@/app/redux";
import {
  togglePopularStatus,
  removeFromPriorityEvents,
  EventCategory,
} from "@/state/priorityEventsSlice";
import { debounce } from "@/utils/debounce";
import { PriorityEventData } from "./types/priority-events.types";
import { toast } from "sonner";

interface EventCardProps {
  event: PriorityEventData;
  categoryId: EventCategory;
  onRemove: (eventId: string) => void;
}

export const EventCard = ({ event, categoryId, onRemove }: EventCardProps) => {
  const [showDetail, setShowDetail] = useState(false);
  const [isPopular, setIsPopular] = useState(event.isPopular);
  const dispatch = useAppDispatch();
  const [togglePopularApi] = useTogglePopularStatusMutation();
  const [deletePriorityEvent] = useDeletePriorityEventMutation();

  const handleTogglePopular = async (e: React.MouseEvent) => {
    e.stopPropagation();
    console.log("🌟 Toggling popular status for:", event.id);

    // Optimistic update
    setIsPopular(!isPopular);
    dispatch(
      togglePopularStatus({ category: categoryId, eventId: event.eventId })
    );

    debouncedTogglePopular(event.id);
  };

  const debouncedTogglePopular = debounce(async (id: string) => {
    try {
      await togglePopularApi(id).unwrap();
      console.log("✅ Popular status updated successfully");
    } catch (error) {
      console.error("❌ Popular status update failed:", error);
      // Revert optimistic update
      setIsPopular(isPopular);
      dispatch(
        togglePopularStatus({ category: categoryId, eventId: event.eventId })
      );
    }
  }, 500);
  const handleRemove = async (e: React.MouseEvent) => {
    e.stopPropagation();
    console.log("🗑️ Starting event removal process");
    // console.log('📝 Event details:', {
    //     id: event.id,
    //     eventId: event.eventId,
    //     name: event.name
    // });

    try {
      // Log before optimistic update
      console.log("🔄 Initiating optimistic update");
      onRemove(event.eventId);

      // Log before API call
      console.log("📡 Sending delete request to API");
      const result = await deletePriorityEvent(event.id).unwrap();
      console.log("✅ Event removed successfully:", event.name);
      toast.success(`🗑️ Event "${event.name}" removed successfully`);
    } catch (error) {
      console.error("❌ Event removal failed:", {
        error,
        eventId: event.id,
        timestamp: new Date().toISOString(),
      });
      toast.error(`❌ Failed to remove "${event.name}". Please try again.`);
      // Consider adding state recovery logic here
    }
  };

  return (
    <>
      <div
        className="group relative flex flex-col rounded-xl border bg-card shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden"
        onClick={() => setShowDetail(true)}
      >
        <div className="h-16 flex">
          <div className="w-1/2 relative">
            {/* Updated Image component to use event.image and event.name */}
            <Image
              src={event.image || "/placeholder.jpg"}
              alt={event.name}
              className="object-cover"
              fill
              sizes="(max-width: 100%) 100vw"
            />
          </div>
          <div className="w-1/2 relative bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-sm flex items-center justify-end px-4">
            <div className="flex gap-2">
              <Button
                onClick={handleTogglePopular}
                size="icon"
                className={`hover:bg-white/20 transition-colors ${
                  isPopular ? "bg-yellow-500/20" : ""
                }`}
                title="Toggle popular status"
              >
                <Star
                  className={`h-6 w-6 ${
                    isPopular ? "text-yellow-400" : "text-gray-200"
                  } hover:text-yellow-400 transition-colors`}
                />
              </Button>
              {/* Updated remove button to use inline function */}
              <Button
                onClick={handleRemove}
                size="icon"
                className="text-red hover:text-red-600 transition-colors"
                title="Remove event"
              >
                <Trash2 className="h-6 w-6 text-gray-200 hover:text-red-400 transition-colors" />
              </Button>
            </div>
          </div>
        </div>
        {/* Updated card content to use event properties directly */}
        <div className="p-4">
          <h4 className="font-semibold text-lg mb-2">{event.name}</h4>
          <p className="text-sm text-muted-foreground">
            {event.venue} • {new Date(event.date).toLocaleDateString()}
          </p>
        </div>
      </div>

      <Dialog open={showDetail} onOpenChange={setShowDetail}>
        <DialogContent className="max-w-3xl">
          {/* <EventDetailCard event={event} showActions={false} /> */}
        </DialogContent>
      </Dialog>
    </>
  );
};
