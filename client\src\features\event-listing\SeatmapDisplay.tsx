  "use client";

import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import Image from "next/image";

interface SeatmapDisplayProps {
  seatmapUrl: string | null | undefined;
}

export const SeatmapDisplay: React.FC<SeatmapDisplayProps> = ({
  seatmapUrl,
}) => {
  if (!seatmapUrl) {
    return (
      <div className="text-gray-500 text-center py-4">
        Seatmap not available for this event.
      </div>
    );
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <div className="relative w-full cursor-pointer group">
          {/* Thumbnail container with aspect ratio */}
          <div className="relative w-full aspect-[16/9] rounded-lg overflow-hidden">
            <Image
              src={seatmapUrl}
              alt="Event Seatmap Thumbnail"
              fill
              className="object-contain group-hover:scale-105 transition-transform duration-200 ease-in-out"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              priority
            />
            {/* Overlay with zoom icon */}
            <div className="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
              <svg
                className="w-10 h-10 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"
                />
              </svg>
            </div>
          </div>
          <p className="text-sm text-gray-500 mt-2 text-center">
            Click to view full seatmap
          </p>
        </div>
      </DialogTrigger>

      <DialogContent className="max-w-[95vw] w-[1200px] h-[90vh] p-0 border-0">
        <DialogHeader className="absolute">
          <VisuallyHidden>
            <DialogTitle>Full Event Seatmap</DialogTitle>
          </VisuallyHidden>
        </DialogHeader>

        <div className="relative w-full h-full">
          <Image
            src={seatmapUrl}
            alt="Full Event Seatmap"
            fill
            className="object-contain"
            sizes="95vw"
            priority
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
