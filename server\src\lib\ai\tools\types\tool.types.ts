// server/src/lib/ai/tools/types/tool.types.ts
// Define common types for all server-side AI tools
import { OpenCTXRequest } from '@/lib/openctx/types';

// Define type for all tools
export interface Tool<T, R> {
    description: string;
    parameters: T;
    execute: (args: T) => Promise<R>;
}

// Define type for search tool
export type SearchTool  =  Tool<{
    userQuery: string;
    openCTXFilters: any | undefined
}, OpenCTXRequest>;
