"use client";

import React from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON><PERSON>,
  X,
  Heart,
  Users,
  Star,
  ArrowRight,
} from "lucide-react";
import { cn } from "@/lib/utils";

interface WelcomeModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onContinue: () => void; // Callback to show auth modal
}

export const WelcomeModal: React.FC<WelcomeModalProps> = ({
  open,
  onOpenChange,
  onContinue,
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className={cn(
          "sm:max-w-[480px] p-0 overflow-hidden",
          "bg-gradient-to-br from-background via-background to-primary/5",
          "border border-border/50 shadow-2xl"
        )}
      >
        {/* Header */}
        <DialogHeader
          className={cn(
            "relative p-6 pb-4",
            "bg-gradient-to-r from-primary/10 via-primary/5 to-transparent",
            "border-b border-border/30"
          )}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <motion.div
                initial={{ rotate: 0 }}
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className={cn(
                  "flex items-center justify-center w-10 h-10 rounded-full",
                  "bg-gradient-to-br from-primary to-primary/80",
                  "shadow-lg"
                )}
              >
                <Sparkles className="w-5 h-5 text-primary-foreground" />
              </motion.div>
              <div>
                <DialogTitle className="text-xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text">
                  Welcome to Fanseatmaster! 🎉
                </DialogTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  Your ultimate ticket marketplace
                </p>
              </div>
            </div>
            {/* <Button
              variant="ghost"
              size="icon"
              onClick={() => onOpenChange(false)}
              className="h-8 w-8 hover:bg-background/80"
            >
              <X className="h-4 w-4" />
            </Button> */}
          </div>
        </DialogHeader>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Welcome Message */}
          <div className="text-center space-y-3">
            <h2 className="text-lg font-semibold text-foreground">
              Get the Best Experience
            </h2>
            <p className="text-sm text-muted-foreground leading-relaxed">
              Join thousands of fans who trust Fanseatmaster for their ticket needs. 
              Create an account or sign in to unlock exclusive features and better deals!
            </p>
          </div>

          {/* Features Preview */}
          <div className="grid grid-cols-1 gap-3">
            <motion.div 
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="flex items-center gap-3 p-3 bg-accent/20 rounded-lg"
            >
              <Star className="w-5 h-5 text-primary" />
              <span className="text-sm font-medium">Exclusive member deals & early access</span>
            </motion.div>
            
            <motion.div 
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
              className="flex items-center gap-3 p-3 bg-accent/20 rounded-lg"
            >
              <Heart className="w-5 h-5 text-red-500" />
              <span className="text-sm font-medium">Save favorite events & get alerts</span>
            </motion.div>
            
            <motion.div 
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.6 }}
              className="flex items-center gap-3 p-3 bg-accent/20 rounded-lg"
            >
              <Users className="w-5 h-5 text-blue-500" />
              <span className="text-sm font-medium">Faster checkout & order history</span>
            </motion.div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col gap-3 pt-4">
            <Button 
              onClick={onContinue}
              className="w-full gap-2 font-medium"
              size="lg"
            >
              Get Started Now
              <ArrowRight className="w-4 h-4" />
            </Button>
            
            <Button 
              variant="ghost" 
              onClick={() => onOpenChange(false)}
              className="w-full text-sm"
            >
              Maybe Later
            </Button>
          </div>

          {/* Trust Badge */}
          <div className="flex justify-center pt-2">
            <Badge variant="outline" className="text-xs">
              🔒 Secure & Trusted by 50K+ Users
            </Badge>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
