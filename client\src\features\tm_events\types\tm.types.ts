// Main Event Interface (simplified)
export interface TmEvent {
    id: string;
    name: string;
    type: string;
    url: string | null;
    locale: string | null;
    primaryImage: string | null;
    startDateTime: Date | null;
    endDateTime: Date | null;
    timezone: string | null;
    status: any | null;
    segment: string | null;
    genre: string | null;
    subGenre: string | null;
    venueName: string | null;
    venueCity: string | null;
    venueState: string | null;
    venueCountry: string | null;
    priceRangeMin: number | null;
    priceRangeMax: number | null;
    currency: string | null;
}


// Custom Request Interface for Filtering, Pagination & Searching
export interface TmEventQueryParams {
  page?: string;
  size?: string;
  keyword?: string;
  city?: string;
  startDate?: string;
  segment?: string;
  genre?: string; 
}

// Combined Result for API response with pagination
export interface PaginatedTmEventResponse {
  events: TmEvent[];
  pagination: {
    page: number;
    size: number;
    total: number;
    totalPages: number;
  };
}
