/**
 * useCheckoutSession.ts
 * Hook for managing a checkout session
 * Handles reservation creation, coupon application, points application, timer, and status updates
 */
import { useState, useCallback, useEffect, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "@/apiAxios/axios";
import { toast } from "sonner";
import {
  CheckoutSession,
  CheckoutItem,
  CheckoutSessionStatus, // Import the enum
  CreateReservationResponse,
  ApplyCouponResponse,
  ApplyPointsResponse,
  RefreshSessionResponse,
  CreateCheckoutSessionPayload,
  // Assuming a type for the update response might exist, or use 'any'
  // UpdateSessionStatusResponse
} from "../types/checkout.types";

interface UseCheckoutSessionProps {
  sessionId?: string;
}

export const useCheckoutSession = ({
  sessionId: initialSessionId,
}: UseCheckoutSessionProps = {}) => {
  const queryClient = useQueryClient();
  const [sessionId, setSessionId] = useState<string | undefined>(
    initialSessionId
  );

  const sessionQueryKey = useMemo(
    () => ["checkoutSession", sessionId],
    [sessionId]
  );

  const {
    data: session,
    isLoading: isLoadingSession,
    error: sessionError,
    refetch: refetchSession,
  } = useQuery({
    queryKey: sessionQueryKey,
    queryFn: async (): Promise<CheckoutSession | null> => {
      if (!sessionId) return null;
      console.log(`➡️🔍 Attempting to fetch session: ${sessionId}`);
      const response = await api.get<{
        success: boolean;
        message: string;
        data: CheckoutSession;
      }>(`/api/v1/checkout/session/${sessionId}`);
      if (!response.data.success) {
        console.error(
          `➡️❌ Session fetch failed for ${sessionId}: ${response.data.message}`
        );
        throw new Error(
          response.data.message || "Failed to fetch checkout session"
        );
      }
      console.log(`➡️✅ Session fetched successfully for ${sessionId}`);
      return response.data.data;
    },
    enabled: !!sessionId,
    refetchInterval: 3 * 60 * 1000, // 3 minutes
    refetchIntervalInBackground: true,
  });

  // --- Existing Mutations (createReservation, applyCoupon, applyPoints, refreshSession) remain the same ---
  // ... (keep existing mutation definitions) ...
  const createReservation = useMutation({
    mutationFn: async (
      data: CreateCheckoutSessionPayload
    ): Promise<CreateReservationResponse> => {
      console.log(
        "➡️🛒 [useCheckoutSession] Calling createReservation mutation with:",
        data
      ); // Log input

      // *** LOG 1: Log the raw response from the API ***
      const response = await api.post<CreateReservationResponse>(
        "/api/v1/checkout/reserve",
        {
          eventId: data.eventId,
          items: data.items,
          billingAddressId: data.billingAddressId, // Pass it to the backend
        }
      );

      // *** Log the entire response object ***
      console.log("➡️📬 Raw API Response:", response);
      console.log("➡️📦 Raw API Response Data:", response.data);

      if (!response.data.success) {
        console.error(
          "➡️❌ Create reservation failed (API success=false):",
          response.data.message
        );
        throw new Error(
          response.data.message || "Failed to create reservation"
        );
      }

      // *** LOG 2: Log the specific data part before accessing nested properties ***
      console.log(
        "➡️✅ Reservation API success=true. Data object:",
        response.data.data
      );

      // Type assertion AFTER logging, or keep as 'any' if debugging structure
      return response.data as CreateReservationResponse;
    },
    onSuccess: (data) => {
      // *** LOG 3: Log the 'data' object received by onSuccess ***
      // This 'data' is the result returned from mutationFn
      console.log("➡️✨ createReservation onSuccess received data:", data);

      // *** LOG 4: Log the nested parts before accessing .id ***
      console.log("➡️🔍 Accessing data.data:", data.data);
      console.log("➡️🔍 Accessing data.data.session:", data?.data?.session); // Use optional chaining for safety

      // Check if the necessary parts exist before trying to access .id
      if (data && data.data && data.data.session && data.data.session.id) {
        const newSessionId = data.data.session.id;
        console.log(`➡️🆔 Setting new Session ID: ${newSessionId}`);
        setSessionId(newSessionId);

        console.log("➡️💾 Updating React Query cache for key:", [
          "checkoutSession",
          newSessionId,
        ]);
        queryClient.setQueryData(
          ["checkoutSession", newSessionId],
          data.data.session // Cache the full session data
        );

        // Show success toast
        toast.success("Tickets reserved", {
          description: `You have ${Math.ceil(
            (new Date(data.data.session.expiresAt).getTime() - Date.now()) /
              60000
          )} minutes to complete checkout`,
        });

        // If some items were unavailable, show warning
        if (
          data.data.unavailableItems &&
          data.data.unavailableItems.length > 0
        ) {
          toast.warning("Some items are no longer available", {
            description: "Your selection has been adjusted",
          });
        }
      } else {
        // *** LOG 5: Log an error if the expected structure is missing ***
        console.error(
          "➡️❌ ERROR: Missing expected data structure in onSuccess!",
          {
            hasData: !!data,
            hasDataData: !!data?.data,
            hasDataDataSession: !!data?.data?.session,
            hasDataDataSessionId: !!data?.data?.session?.id,
          }
        );
        toast.error("Failed to process reservation response", {
          description: "Could not extract session details. Please try again.",
        });
      }
    },
    onError: (error: Error) => {
      console.error("➡️💥 createReservation onError:", error); // Log errors
      toast.error("Failed to reserve tickets", {
        description: error.message,
      });
    },
  });

  // Apply a coupon code (keep existing code, consider adding logs)
  const applyCoupon = useMutation({
    mutationFn: async ({
      couponCode,
    }: {
      couponCode: string;
    }): Promise<ApplyCouponResponse> => {
      // ➡️ Debug log: Added log for apply coupon mutation call
      console.log("➡️🎁 Calling applyCoupon mutation...");
      if (!sessionId) {
        console.warn("➡️⚠️ Attempted to apply coupon without session ID");
        throw new Error("No active checkout session");
      }
      const response = await api.post<{
        success: boolean;
        message: string;
        data: ApplyCouponResponse["data"];
      }>("/api/v1/checkout/coupon", { sessionId, couponCode });

      if (!response.data.success) {
        console.error("➡️❌ Apply coupon failed:", response.data.message);
        throw new Error(response.data.message || "Failed to apply coupon");
      }
      console.log("➡️✅ Apply coupon success!");
      return response.data as ApplyCouponResponse;
    },
    onSuccess: (data) => {
      if (data.data.isValid && data.data.session) {
        queryClient.setQueryData(sessionQueryKey, data.data.session);

        toast.success("Coupon applied", {
          description: `Discount: ${data.data.session.couponDiscount?.discountAmount.toFixed(
            2
          )}`,
        });
      } else {
        toast.error("Invalid coupon code", {
          description: data.data.error || "This coupon cannot be applied",
        });
      }
    },
    onError: (error: Error) => {
      toast.error("Failed to apply coupon", {
        description: error.message,
      });
    },
  });

  // Apply credit points (keep existing code, consider adding logs)
  const applyPoints = useMutation({
    mutationFn: async ({
      pointsToApply,
    }: {
      pointsToApply: number;
    }): Promise<ApplyPointsResponse> => {
      // ➡️ Debug log: Added log for apply points mutation call
      console.log("➡️🪙 Calling applyPoints mutation with:", pointsToApply);

      if (!sessionId) {
        console.warn("➡️⚠️ Attempted to apply points without session ID");
        throw new Error("No active checkout session");
      }

      // Extra validation to prevent infinite loops or invalid calls
      if (typeof pointsToApply !== "number" || isNaN(pointsToApply)) {
        console.error("➡️❌ Invalid points value:", pointsToApply);
        throw new Error("Invalid points value");
      }

      // Ensure pointsToApply is not negative
      const validPointsToApply = Math.max(0, pointsToApply);

      // Remove the skipping logic - let the backend handle 0 if needed for removal
      // if (validPointsToApply === 0 && pointsToApply !== 0) { ... }

      console.log(`➡️🪙 Applying ${validPointsToApply} points...`);

      const response = await api.post<{
        success: boolean;
        message: string;
        data: ApplyPointsResponse["data"];
      }>("/api/v1/checkout/points", {
        sessionId,
        pointsToApply: validPointsToApply,
      });

      if (!response.data.success) {
        console.error("➡️❌ Apply points failed:", response.data.message);
        throw new Error(response.data.message || "Failed to apply points");
      }
      console.log("➡️✅ Apply points success!");
      return response.data as ApplyPointsResponse;
    },
    onSuccess: (data) => {
      if (data.data.success && data.data.session) {
        queryClient.setQueryData(sessionQueryKey, data.data.session);

        toast.success("Credit points applied", {
          description: `Discount: ${data.data.session.appliedPoints?.discountAmount.toFixed(
            2
          )}`,
        });
      } else {
        // If the backend indicates success=false even if the request succeeded
        toast.error("Failed to apply points", {
          description: data.data.error || "Points could not be applied",
        });
      }
    },
    onError: (error: Error) => {
      toast.error("Failed to apply points", {
        description: error.message,
      });
    },
  });

  // Refresh/extend session
  const refreshSession = useMutation({
    mutationFn: async (): Promise<RefreshSessionResponse> => {
      // ➡️ Debug log: Added log for refresh session mutation call
      console.log("➡️🔄 Calling refreshSession mutation...");
      if (!sessionId) {
        console.warn("➡️⚠️ Attempted to refresh session without session ID");
        throw new Error("No active checkout session");
      }

      const response = await api.post<{
        success: boolean;
        message: string;
        data: RefreshSessionResponse["data"];
      }>(`/api/v1/checkout/session/${sessionId}/refresh`); // Verify this path

      if (!response.data.success) {
        console.error("➡️❌ Refresh session failed:", response.data.message);
        throw new Error(response.data.message || "Failed to refresh session");
      }

      // ✅ Debug log: Added log for refresh session success
      console.log("➡️✅ Refresh session success!");
      return response.data as RefreshSessionResponse;
    },
    onSuccess: (data) => {
      if (data.data.session) {
        // Update cache with refreshed session
        queryClient.setQueryData(sessionQueryKey, data.data.session);

        toast.success("Session extended", {
          description: "You have more time to complete your purchase",
        });
      } else {
        toast.error("Could not extend session", {
          description: data.data.error || "Please try again",
        });
      }
    },
    onError: (error: Error) => {
      toast.error("Failed to extend session", {
        description: error.message,
      });
    },
  });

  // --- NEW: Mutation to update session status ---
  const updateSessionStatusMutation = useMutation({
    mutationFn: async ({
      status,
      currentSessionId, // Pass sessionId explicitly to avoid closure issues
    }: {
      status: CheckoutSessionStatus;
      currentSessionId: string;
    }): Promise<any> /* Use specific response type if available */ => {
      console.log(
        `🔄 Attempting to update session ${currentSessionId} status to: ${status}`
      );
      const response = await api.patch(
        `/api/v1/checkout/session/${currentSessionId}/status`,
        { status }
      );
      if (!response.data.success) {
        console.error(
          `❌ Failed to update session ${currentSessionId} status to ${status}:`,
          response.data.message
        );
        throw new Error(
          response.data.message || "Failed to update session status"
        );
      }
      console.log(
        `✅ Session ${currentSessionId} status updated to ${status} successfully.`
      );
      return response.data; // Return the response data
    },
    onSuccess: (data, variables) => {
      // Optionally update local cache or show toast based on status
      if (variables.status === CheckoutSessionStatus.EXPIRED) {
        // Already handled by the checkExpiration effect toast
      } else if (variables.status === CheckoutSessionStatus.CANCELLED) {
        toast.info("Reservation Canceled", {
          description: "Your items are no longer held.",
        });
      }
      // Invalidate or remove the query to reflect the change if needed,
      // though often clearing the local state is enough for EXPIRED/CANCELLED.
      // queryClient.invalidateQueries({ queryKey: sessionQueryKey });
    },
    onError: (error: Error, variables) => {
      // Log error and show generic failure toast
      console.error(
        `💥 Error updating session status to ${variables.status}:`,
        error
      );
      toast.error("Failed to update reservation status", {
        description: error.message,
      });
    },
  });
  // --- End NEW ---

  // --- NEW: Helper to calculate time remaining ---
  const calculateTimeRemaining = useCallback((): number | null => {
    if (
      !session?.expiresAt ||
      session.status !== CheckoutSessionStatus.RESERVED
    ) {
      return null;
    }
    const expiresAtTimestamp = new Date(session.expiresAt).getTime();
    const now = Date.now();
    return Math.max(0, expiresAtTimestamp - now);
  }, [session?.expiresAt, session?.status]);

  // --- REVISED: Effect for auto-refresh ---
  // This effect now runs periodically checks the calculated time remaining
  useEffect(() => {
    const checkAndRefresh = () => {
      const remaining = calculateTimeRemaining();

      if (
        remaining !== null &&
        remaining > 0 &&
        remaining < 30000 && // Threshold: Less than 30 seconds
        !refreshSession.isPending &&
        session?.status === CheckoutSessionStatus.RESERVED
      ) {
        console.log(
          `➡️🔄 Auto-refresh triggered! Calculated remaining: ${remaining}ms`
        );
        refreshSession.mutate();
      }
    };

    // Check immediately when session data changes
    checkAndRefresh();

    // Also check every few seconds (e.g., 5 seconds)
    const intervalId = setInterval(checkAndRefresh, 5000); // Check less frequently

    return () => clearInterval(intervalId);
  }, [
    session?.status, // Re-run if status changes
    session?.expiresAt, // Re-run if expiry changes (after refresh)
    calculateTimeRemaining,
    refreshSession, // The mutation object itself
    refreshSession.isPending, // Re-run when mutation status changes
  ]);

  // --- REVISED: Effect for handling expiration ---
  // Now calls the backend to update status
  useEffect(() => {
    const checkExpiration = () => {
      const remaining = calculateTimeRemaining();
      // Check if expired and ensure we have a valid sessionId to update
      if (
        remaining !== null &&
        remaining <= 0 &&
        sessionId &&
        session?.status === CheckoutSessionStatus.RESERVED
      ) {
        console.log(
          `⏱️❌ Session ${sessionId} detected as expired. Updating status and clearing state.`
        );

        // --- MODIFICATION START ---
        // Call the mutation to update backend status BEFORE clearing local state
        updateSessionStatusMutation.mutate(
          {
            currentSessionId: sessionId,
            status: CheckoutSessionStatus.EXPIRED,
          },
          {
            // Optional: onSuccess/onError specific to this call if needed,
            // but generally handled by the mutation's own handlers.
            // We proceed to clear local state regardless of backend update success/failure
            // to ensure the UI reflects expiration immediately.
            onSettled: () => {
              toast.error("Your reservation has expired", {
                description: "Please start over to select tickets again",
              });
              setSessionId(undefined);
              queryClient.removeQueries({ queryKey: sessionQueryKey });
            },
          }
        );
        // --- MODIFICATION END ---

        // **Important:** The state clearing (setSessionId, removeQueries) and toast
        // are moved into the onSettled callback of the mutation to ensure they
        // run after the API call attempt (success or failure).
      }
    };

    checkExpiration();
    const intervalId = setInterval(checkExpiration, 1000);
    return () => clearInterval(intervalId);
    // Add updateSessionStatusMutation to dependencies to ensure the latest mutation object is used
  }, [
    sessionId,
    session?.status,
    calculateTimeRemaining,
    queryClient,
    sessionQueryKey,
    updateSessionStatusMutation,
  ]);
  // --- End REVISED ---

  return {
    // Session data
    session,
    sessionId,
    isLoadingSession,
    sessionError,

    // --- NEW: Expose expiry directly ---
    sessionExpiresAt: session?.expiresAt,

    // Helper flags based on calculated time or session status
    isReservationExpired: calculateTimeRemaining() === 0,
    isReservationActive:
      !!session &&
      session.status === CheckoutSessionStatus.RESERVED &&
      calculateTimeRemaining() !== null &&
      calculateTimeRemaining()! > 0,

    // Session actions
    createReservation,
    applyCoupon,
    applyPoints,
    refreshSession,
    // --- NEW: Expose status update function ---
    updateSessionStatus: updateSessionStatusMutation.mutate,
    // --- End NEW ---

    // Mutation states
    isCreatingReservation: createReservation.isPending,
    isApplyingCoupon: applyCoupon.isPending,
    isApplyingPoints: applyPoints.isPending,
    isRefreshingSession: refreshSession.isPending,
    // --- NEW: Expose status update loading state ---
    isUpdatingStatus: updateSessionStatusMutation.isPending,
    // --- End NEW ---

    // Action to clear session state
    clearSession: () => {
      console.log("🔄 Manual session clear triggered.");
      setSessionId(undefined);
      queryClient.removeQueries({ queryKey: sessionQueryKey });
    },

    // Helper function to determine if checkout can proceed
    canProceedToCheckout:
      !!session &&
      session.status === CheckoutSessionStatus.RESERVED &&
      calculateTimeRemaining() !== null &&
      calculateTimeRemaining()! > 0,

    // Refetch session data
    refetchSession,
  };
};
