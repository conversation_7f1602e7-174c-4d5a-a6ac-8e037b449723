// client/src/features/sales/hooks/useUploadTicketTemplate.ts
// Hook for uploading ticket files (template for an event, or specific tickets for a session)

import { useMutation, useQueryClient } from "@tanstack/react-query";
import axiosInstance from "@/apiAxios/axios";
import { toast } from "sonner";

interface UploadParams {
  eventId: string;
  checkoutSessionId?: string; // Make this optional for event-level templates, required for session-specific
  file: File;
  type: "template" | "batch" | "session"; // 'session' for individual purchase tickets
  // 'template' could be for the whole event, 'batch' for multiple specific purchases via CSV
}

interface UploadResponse {
  success: boolean;
  message: string;
  data?: {
    // Data might vary based on upload type
    uploadId?: string;
    fileUrl?: string;
    // Add other relevant fields returned by backend
  };
}

export function useUploadTicketTemplate() {
  const queryClient = useQueryClient();

  const {
    mutateAsync: uploadTemplate, // Keep the name for now, or rename to uploadFile if more generic
    isPending: isUploading,
    error,
  } = useMutation<UploadResponse, Error, UploadParams>({
    mutationFn: async ({
      eventId,
      checkoutSessionId,
      file,
      type,
    }: UploadParams) => {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("eventId", eventId);
      formData.append("uploadType", type); // To inform backend about the nature of upload

      let endpoint = `/api/v1/manager-events/${eventId}/ticket-template`; // Default to event template

      if (type === "session" && checkoutSessionId) {
        formData.append("checkoutSessionId", checkoutSessionId);
        // Example: Backend endpoint for session-specific ticket upload
        endpoint = `/api/v1/sales/sessions/${checkoutSessionId}/tickets`;
        // OR it could be nested under events: `/api/v1/manager-events/${eventId}/sessions/${checkoutSessionId}/tickets`
        // This endpoint needs to be defined on the backend.
        console.log(
          `🚀 Uploading specific ticket for session: ${checkoutSessionId} (Event: ${eventId})`
        );
      } else if (type === "batch") {
        // Endpoint for batch upload (e.g., ZIP + CSV)
        endpoint = `/api/v1/manager-events/${eventId}/batch-tickets`;
        console.log(`🚀 Uploading batch tickets for event: ${eventId}`);
      } else {
        console.log(`🚀 Uploading template for event: ${eventId}`);
      }

      try {
        const response = await axiosInstance.post<UploadResponse>(
          endpoint,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

        console.log(`✅ Upload response for type '${type}':`, response.data);

        if (response.data.success) {
          return response.data;
        } else {
          throw new Error(response.data.message || "Upload failed");
        }
      } catch (err: any) {
        console.error(`❌ Error uploading ticket (type: ${type}):`, err);
        const errorMessage =
          err.response?.data?.message ||
          err.message ||
          `Failed to upload ticket`;
        toast.error(errorMessage);
        throw new Error(errorMessage);
      }
    },
    onSuccess: (data, variables) => {
      toast.success(data.message || "File uploaded successfully!");
      // Invalidate queries to refetch sales data if ticketUploadStatus changes
      if (variables.type === "session" || variables.type === "batch") {
        queryClient.invalidateQueries({
          queryKey: ["managerDetailedSalesOverview"],
        });
      }
    },
    onError: (error) => {
      // Error is already toasted in mutationFn, but you can add more specific handling here if needed
      console.error("Mutation-level upload error:", error.message);
    },
  });

  return {
    uploadTemplate,
    isUploading,
    error: error as Error | null,
  };
}
