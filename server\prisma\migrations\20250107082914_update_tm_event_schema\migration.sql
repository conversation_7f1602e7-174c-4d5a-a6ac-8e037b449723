-- CreateTable
CREATE TABLE "TmEvent" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "url" TEXT,
    "locale" TEXT,
    "primaryImage" TEXT,
    "images" JSONB,
    "startDateTime" TIMESTAMP(3),
    "endDateTime" TIMESTAMP(3),
    "timezone" TEXT,
    "status" JSONB,
    "classifications" JSONB,
    "segment" TEXT,
    "genre" TEXT,
    "subGenre" TEXT,
    "venue" JSONB,
    "venueName" TEXT,
    "venueCity" TEXT,
    "venueState" TEXT,
    "venueCountry" TEXT,
    "priceRanges" JSONB,
    "priceRangeMin" DOUBLE PRECISION,
    "priceRangeMax" DOUBLE PRECISION,
    "currency" TEXT,
    "sales" JSONB,
    "seatmap" JSONB,
    "ticketLimit" JSONB,
    "accessibility" JSONB,
    "links" JSONB,
    "promoter" JSONB,
    "promoters" JSONB,
    "products" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TmEvent_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "TmEvent_startDateTime_idx" ON "TmEvent"("startDateTime");

-- CreateIndex
CREATE INDEX "TmEvent_genre_idx" ON "TmEvent"("genre");

-- CreateIndex
CREATE INDEX "TmEvent_venueName_idx" ON "TmEvent"("venueName");
