import React from 'react';
import {
  Dialog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { ProfileEditForm } from './ProfileEditForm';
import { ProfileData } from '@/features/profile/types/profile.types';

interface ProfileEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  profile: ProfileData;
}

export const ProfileEditModal: React.FC<ProfileEditModalProps> = ({
  isOpen,
  onClose,
  profile,
}) => {
  const handleSuccess = () => {
    console.log('✨ Profile updated successfully, closing modal');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Your Profile</DialogTitle>
          <DialogDescription>
            Update your profile information and contact details
          </DialogDescription>
        </DialogHeader>
        
        <ProfileEditForm 
          profile={profile} 
          onSubmitSuccess={handleSuccess}
          onCancel={onClose}
        />
      </DialogContent>
    </Dialog>
  );
};
