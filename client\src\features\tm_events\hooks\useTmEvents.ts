// useTmEvents.ts: Custom hook for managing Ticketmaster events data
import { useState } from 'react';
import { TmEventQueryParams, TmEvent } from '../types/tm.types';
import { useQuery } from '@tanstack/react-query';
import { fetchTmEvents } from '../api/tm.api';

// Define the default query parameters
const defaultQueryParams: TmEventQueryParams = {
    page: "0",
    size: "10",
    keyword: "",
    city: "",
    startDate: "",
    genre: "",
};

export const useTmEvents = () => {
    // Local state to manage query parameters
    const [queryParams, setQueryParams] = useState<TmEventQueryParams>(defaultQueryParams);

    // Fetch the events using react query
    const { data, isLoading, error, refetch } = useQuery({
        queryKey: ['tmEvents', queryParams],
        queryFn: () => fetchTmEvents(queryParams),
        staleTime: 60 * 1000 // 60 seconds
    });

    // Function to update query parameters
    const updateQueryParams = (newParams: Partial<TmEventQueryParams>) => {
        console.log('Updating Query Params:', newParams)
        setQueryParams((prevParams) => ({ ...prevParams, ...newParams }));
    };

    // Function to update page number
    const updatePage = (page: string) => {
        updateQueryParams({page})
    };

    // Function to update size
    const updateSize = (size:string) => {
        updateQueryParams({size})
    }

    // Function to update the keyword
    const updateKeyword = (keyword: string) => {
       updateQueryParams({keyword});
    };

    // Function to update the city
    const updateCity = (city: string) => {
        updateQueryParams({city});
   };

    // Function to update the startDate
    const updateStartDate = (startDate: string) => {
       updateQueryParams({startDate});
    };

    // Function to update the genre
    const updateGenre = (genre: string) => {
        updateQueryParams({genre})
    };

    // Log when data is updated or fetched
    if (data) {
         console.log('Fetched TM Events:', data);
    }

    // Return the necessary values from the hook
    return {
         events: data?.events || [],
         pagination: data?.pagination || { page: 0, size: 10, total: 0, totalPages: 0},
        isLoading,
        error,
         refetch,
        queryParams,
        updateQueryParams,
        updatePage,
        updateSize,
        updateKeyword,
        updateCity,
        updateStartDate,
        updateGenre,
    };
};