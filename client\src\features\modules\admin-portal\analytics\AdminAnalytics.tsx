"use client"
import { useState } from 'react';
import { motion } from 'framer-motion';
import { RevenueChart } from './components/RevenueChart';
import '@/lib/chart-config';

import { AnalyticsFilters } from '@/features/modules/shared/types/analytics.types';
import { UserGrowthChart } from './components/UserGrowthChart';
import { EventPerformanceChart } from './components/EventPerformanceChart';

export const AdminAnalytics = () => {
  const [filters, setFilters] = useState<AnalyticsFilters>({
    dateRange: 'month',
    comparison: false
  });

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-8"
    >
      {/* Header Section */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
          <p className="text-muted-foreground mt-1">
            Comprehensive overview of platform performance
          </p>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="col-span-2">
          <RevenueChart />
        </div>
        <UserGrowthChart />
        <EventPerformanceChart />
      </div>
    </motion.div>
  );
};
