
import { useState } from 'react';

export type Language = 'en' | 'es' ;

const languages: Record<Language, string> = {
  en: 'English',
  es: 'Español',
  
};

export const useLanguage = () => {
  const [language, setLanguage] = useState<Language>('en');

  const changeLanguage = (newLanguage: Language) => {
    setLanguage(newLanguage);
    // Here you can add logic to change the app's language
  };

  return { language, changeLanguage, languages };
};
