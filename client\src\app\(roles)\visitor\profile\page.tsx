/**
 * Visitor Profile Page
 *
 * Renders the visitor-specific profile using the reusable ProfilePage component
 * and adds subscription management UI.
 */
'use client';

import React from 'react';
import { ProfilePage } from '@/features/profile/ProfilePage'; // Assuming generic profile display
import { useSubscription } from '@/features/subscriptions/hooks/useSubscription';
import { SubscriptionStatus } from '@/features/subscriptions/components/SubscriptionStatus';
import { CancelSubscriptionButton } from '@/features/subscriptions/components/CancelSubscriptionButton';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { Loader2 } from 'lucide-react';
import { format } from 'date-fns';


export default function VisitorProfilePage() {
  // --- Add Subscription Hook ---
  const {
    isSubscribed,
    currentPlan,
    cancelAtPeriodEnd,
    currentPeriodEnd,
    cancelSubscription,
    isCanceling,
    isLoading: isLoadingSubscription,
  } = useSubscription();
  // --- End New ---

  const formattedEndDate = currentPeriodEnd
    ? format(new Date(currentPeriodEnd), 'MMMM d, yyyy')
    : 'N/A';

  return (
    <div className="space-y-8"> {/* Added container div */}
      {/* Render the core profile component */}
      <ProfilePage expectedRole="VISITOR" />

      {/* --- Add Subscription Section Directly Here --- */}
      <Card>
        <CardHeader>
          <CardTitle>Membership Status</CardTitle>
           <CardDescription>Your current subscription plan.</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingSubscription ? (
            <div className="flex items-center justify-center h-10">
              <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            </div>
          ) : isSubscribed ? (
            <div className="flex items-center justify-between">
               <div>
                 <p className="font-medium">
                   Current Plan: {currentPlan === 'VIP' ? 'VIP Member' : 'Monthly Subscriber'}
                 </p>
                 <SubscriptionStatus isActive={!!isSubscribed} isCanceled={!!cancelAtPeriodEnd} className="mt-1" />
                  {currentPeriodEnd && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {cancelAtPeriodEnd ? `Ends on ${formattedEndDate}` : `Renews on ${formattedEndDate}`}
                    </p>
                  )}
               </div>
               {!cancelAtPeriodEnd && (
                 <CancelSubscriptionButton
                    onCancel={cancelSubscription}
                    isCanceling={isCanceling}
                    currentPlanName={currentPlan === 'VIP' ? 'VIP' : 'Monthly'}
                    endDate={formattedEndDate}
                 />
               )}
               {cancelAtPeriodEnd && (
                   <Badge variant="outline">Cancellation Scheduled</Badge>
               )}
            </div>
          ) : (
             <p className="text-muted-foreground">You do not have an active subscription.</p>
          )}
        </CardContent>
         <CardFooter>
            {!isSubscribed && (
               <Button variant="link" asChild><Link href="/visitor/subscriptions">View Plans</Link></Button>
            )}
         </CardFooter>
      </Card>
      {/* --- End Subscription Section --- */}
    </div>
  );
}
