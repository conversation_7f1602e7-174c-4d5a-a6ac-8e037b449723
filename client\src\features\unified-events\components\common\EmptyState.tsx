import React from "react";
import { Button } from "@/components/ui/button";

interface EmptyStateProps {
  onReset: () => void;
}

export const EmptyState: React.FC<EmptyStateProps> = ({ onReset }) => {
  return (
    <div className="flex justify-between items-center p-8 bg-gray-50 rounded-lg">
      <p className="text-gray-500">No events found matching your criteria.</p>
      <Button size="sm" onClick={onReset} variant="outline">
        Reset Filters
      </Button>
    </div>
  );
};
