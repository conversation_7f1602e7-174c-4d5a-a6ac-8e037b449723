/**
 * ProfileHeader Component
 *
 * Displays the main profile banner, avatar, name, and key stats.
 * Used across all role-based profile pages.
 */

import React from "react";
import { ProfileComponentProps } from "../../types/profile.types";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ProfileAvatar } from "./ProfileAvatar";
import { CheckCircle, Mail, Phone, Shield, XCircle } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export function ProfileHeader({
  profile,
  isEditable = false,
}: ProfileComponentProps) {
  if (!profile) {
    return (
      <Card className="w-full h-[240px] animate-pulse bg-muted">
        <CardContent className="p-0 h-full flex flex-col items-center justify-center">
          <div className="text-muted-foreground">Loading profile...</div>
        </CardContent>
      </Card>
    );
  }

  // Determine verification status
  const isEmailVerified = !!profile.emailVerified;
  const isMobileVerified = !!profile.mobileVerified;

  return (
    <Card className="w-full overflow-hidden">
      {/* Banner Image - Gradient Fallback */}
      <div
        className="h-40 bg-gradient-to-r from-blue-500 to-purple-600 relative"
        aria-label="Profile banner"
      >
        {isEditable && (
          <button
            className="absolute bottom-2 right-2 bg-background/80 text-xs p-1.5 rounded-md 
                      hover:bg-background transition-colors duration-200 text-foreground"
            aria-label="Edit banner"
          >
            Edit Banner
          </button>
        )}
      </div>

      <CardContent className="pt-0 relative">
        {/* Avatar - positioned to overlap the banner */}
        <div className="absolute -top-16 left-6 ring-8 ring-background rounded-full">
          <ProfileAvatar
            profile={profile}
            size="large"
            isEditable={isEditable}
          />
        </div>

        {/* Profile Info */}
        <div className="pt-20 pb-4 px-2">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold tracking-tight flex items-center">
                {profile.name || profile.fullName || "Anonymous User"}
                <Badge
                  variant="outline"
                  className="ml-2 capitalize hidden sm:inline-flex"
                >
                  {profile.role || "visitor"}
                </Badge>
              </h1>

              {/* Verification Status Cards - Modern UI */}
              <div className="flex flex-wrap gap-2 mt-2">
                {/* Email Verification */}
                <div
                  className={`flex items-center px-3 py-1.5 rounded-full text-sm ${
                    isEmailVerified
                      ? "bg-emerald-50 text-emerald-700 border border-emerald-200"
                      : "bg-amber-50 text-amber-700 border border-amber-200"
                  }`}
                >
                  <Mail className="h-3.5 w-3.5 mr-1.5" />
                  <span className="font-medium truncate max-w-[220px]">
                    {profile.email || "No email"}
                  </span>
                  {isEmailVerified ? (
                    <CheckCircle className="h-3.5 w-3.5 ml-1.5 text-emerald-600" />
                  ) : (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div>
                            <XCircle className="h-3.5 w-3.5 ml-1.5 text-amber-600 cursor-help" />
                          </div>
                        </TooltipTrigger>
                        <TooltipContent side="right" className="text-xs">
                          <p>Email not verified. Verify in Edit profile .</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </div>

                {/* Mobile Verification - Only show if mobile exists */}
                {profile.mobile && (
                  <div
                    className={`flex items-center px-3 py-1.5 rounded-full text-sm ${
                      isMobileVerified
                        ? "bg-emerald-50 text-emerald-700 border border-emerald-200"
                        : "bg-amber-50 text-amber-700 border border-amber-200"
                    }`}
                  >
                    <Phone className="h-3.5 w-3.5 mr-1.5" />
                    <span className="font-medium">{profile.mobile}</span>
                    {isMobileVerified ? (
                      <CheckCircle className="h-3.5 w-3.5 ml-1.5 text-emerald-600" />
                    ) : (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div>
                              <XCircle className="h-3.5 w-3.5 ml-1.5 text-amber-600 cursor-help" />
                            </div>
                          </TooltipTrigger>
                          <TooltipContent side="right" className="text-xs">
                            <p>Phone not verified. Verify in Edit profile .</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                )}

                {/* Mobile badge for Role */}
                <Badge variant="outline" className="capitalize sm:hidden">
                  {profile.role || "visitor"}
                </Badge>
              </div>
            </div>

            {/* For wider screens, we can add account status indicators */}
            <div className="sm:flex items-center gap-2 hidden">
              {(isEmailVerified || isMobileVerified) && (
                <div className="flex items-center text-xs gap-1 bg-blue-50 text-blue-800 px-2 py-1 rounded-full">
                  <Shield className="h-3 w-3" />
                  <span>Verified Account</span>
                </div>
              )}
            </div>
          </div>

          {/* Key Stats */}
          <div className="flex gap-4 mt-4 text-sm">
            <div>
              <span className="font-medium">
                {profile.stats?.eventsAttended || 0}
              </span>
              <span className="text-muted-foreground ml-1">Events</span>
            </div>
            <div>
              <span className="font-medium">
                {profile.stats?.followers || 0}
              </span>
              <span className="text-muted-foreground ml-1">Followers</span>
            </div>
            <div>
              <span className="font-medium">
                {profile.stats?.following || 0}
              </span>
              <span className="text-muted-foreground ml-1">Following</span>
            </div>
          </div>

          {/* Bio/Description */}
          {profile.bio && (
            <p className="mt-4 text-sm text-foreground/80">{profile.bio}</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

console.log(
  "🧩 Enhanced ProfileHeader loaded with modern verification indicators"
);
