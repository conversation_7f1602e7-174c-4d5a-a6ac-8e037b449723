{"name": "server", "version": "1.0.0", "description": "", "main": "dist/src/index.js", "scripts": {"prebuild": "prisma generate", "build": "<PERSON>raf dist && tsc && tsc-alias ", "start": "node dist/src/index.js", "dev": "nodemon --exec ts-node -r tsconfig-paths/register src/index.ts", "generate": "prisma generate", "migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "seed": "ts-node prisma/seed.ts", "start:prod": "pnpm run build && cross-env NODE_ENV=production node -r tsconfig-paths/register dist/index.js", "update-geoip": "cd node_modules/geoip-lite && pnpm run-script updatedb license_key=****************************************", "setup": "pnpm install && pnpm run update-geoip"}, "engines": {"node": "20.x", "pnpm": "^10.0.0"}, "pnpm": {"onlyBuiltDependencies": ["@prisma/client", "prisma", "bcrypt"]}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/body-parser": "^1.19.5", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/luxon": "^3.4.2", "@types/morgan": "^1.9.9", "@types/node": "^22.10.0", "@types/node-cron": "^3.0.11", "@types/redis": "^4.0.11", "@types/uuid": "^10.0.0", "concurrently": "^9.1.0", "cross-env": "^7.0.3", "nodemon": "^3.1.7", "prisma": "^6.4.0", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "tsc-alias": "^1.8.16", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.2"}, "dependencies": {"@ai-sdk/openai": "^1.1.2", "@prisma/client": "^6.4.0", "@types/cookie-parser": "^1.4.8", "@types/geoip-lite": "^1.4.4", "@types/jsonwebtoken": "^9.0.7", "ai": "^4.1.5", "axios": "^1.7.9", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "compression": "^1.7.5", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.5", "express": "^4.21.1", "geoip-lite": "^1.4.10", "jsonwebtoken": "^9.0.2", "luxon": "^3.5.0", "morgan": "^1.10.0", "node-cron": "^3.0.3", "rate-limiter-flexible": "^5.0.4", "redis": "^4.7.0", "resend": "^4.0.1", "stripe": "^18.0.0", "uuid": "^11.1.0", "winston": "^3.17.0", "zod": "^3.23.8"}}