// This file defines types for Priority Events and re-exports related enums

import { PriorityEvent, EventCategory } from "@prisma/client";

// Single Priority Event payload type
export interface PriorityEventPayload {
  eventId: string;
  name: string;
  category: EventCategory;
  source: string;
  date: Date | string;
  venue: string;
  city: string;
  country: string;
  image?: string | null;
  ticketUrl?: string | null;
  isPopular?: boolean;
  isActive?: boolean;
  rawEventData: Record<string, any>;
}

// Bulk Priority Event payload type
export interface BulkPriorityEventPayload {
  events: PriorityEventPayload[];
}

// Single Priority Event response type
export type PriorityEventResponse = PriorityEvent;

// Bulk Priority Event response type
export interface BulkPriorityEventResponse {
  created: PriorityEvent[];
  errors: { index: number; message: string }[];
}

// Update Priority Event payload type
export type UpdatePriorityEventPayload = Partial<PriorityEventPayload> & {
  id: string;
};

// Payload type for toggling popular status
export type TogglePopularPayload = {
  id: string;
};

// Payload type for deleting a Priority Event
export type DeletePriorityEventPayload = {
  id: string;
};

// Re-export the EventCategory enum from Prisma
export { EventCategory };
