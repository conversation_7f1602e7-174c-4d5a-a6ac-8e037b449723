import winston from 'winston';
import path from 'path';
import { NODE_ENV } from '@/constants';

// Define log levels
const LOG_LEVELS = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  verbose: 4,
  debug: 5,
  silly: 6
};

// Define colors for each log level
const LOG_COLORS = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  verbose: 'cyan',
  debug: 'blue',
  silly: 'grey'
};

winston.addColors(LOG_COLORS);

/**
 * Logger Configuration based on environment
 */
export const LOGGER_CONFIG = {
  level: NODE_ENV === 'production' ? 'info' : 'debug',
  levels: LOG_LEVELS,
  
  // File paths for different environments
  files: {
    error: path.join(process.cwd(), 'logs', 'error.log'),
    combined: path.join(process.cwd(), 'logs', 'combined.log'),
    access: path.join(process.cwd(), 'logs', 'access.log')
  },
  
  // Max file size and rotation
  maxFileSize: 5242880, // 5MB
  maxFiles: 5,
  
  // Security settings for production
  production: {
    // Sensitive fields to redact in production
    sensitiveFields: ['password', 'token', 'api<PERSON>ey', 'secret', 'authorization'],
    // <PERSON> log message length
    maxLogLength: 1000
  }
};