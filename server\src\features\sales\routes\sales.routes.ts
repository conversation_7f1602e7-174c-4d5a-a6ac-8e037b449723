// server/src/features/sales/routes/sales.routes.ts
// Defines API routes for manager sales overview with detailed checkout sessions

import { Router } from "express";
import { SalesController } from "../controllers/sales.controller";
import { authMiddleware } from "@/middleware/auth.middleware";

const router = Router();

// --- Manager Sales Routes ---

// GET /api/v1/sales/overview - Fetch detailed sales data for managed events
router.get(
    "/overview",
    authMiddleware, // Ensure user is logged in
    SalesController.getSalesOverview // Controller verifies the manager role internally
);

export const salesRoutes = router;