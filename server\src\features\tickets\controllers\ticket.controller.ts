// server/src/features/tickets/controllers/ticket.controller.ts
// Controller to handle HTTP requests for visitor tickets

import { Request, Response } from "express";
import { asyncHandler } from "@/utils/asyncHandler";
import { TicketService } from "../services/ticket.service";
import ApiError from "@/utils/ApiError";

export class TicketController {

    /**
     * @description Get the logged-in user's purchased tickets (completed checkouts)
     * @route GET /api/v1/tickets/my-tickets
     * @access Private (Visitor)
     */
    static getMyTickets = asyncHandler(async (req: Request, res: Response) => {
        const userId = req.user?.userId;
        if (!userId) {
            throw new ApiError(401, "Authentication required.");
        }

        // Extract pagination parameters from query string
        const page = parseInt(req.query.page as string) || 1;
        const limit = parseInt(req.query.limit as string) || 10;

        console.log(`📲 [TicketController] Request received for getMyTickets - User: ${userId}, Page: ${page}, Limit: ${limit}`);

        const result = await TicketService.getVisitorTickets(userId, page, limit);

        // Use standard JSON response structure
        return res.status(200).json({
            success: true,
            message: "Ticket history retrieved successfully.",
            data: result
        });
    });

    /**
     * @description Get information required to download a specific ticket/purchase confirmation.
     * @route GET /api/v1/tickets/:sessionId/download-info
     * @access Private (Ticket Owner)
     */
    static getTicketDownloadInfo = asyncHandler(async (req: Request, res: Response) => {
        const userId = req.user?.userId;
        if (!userId) {
            throw new ApiError(401, "Authentication required.");
        }

        const { sessionId } = req.params;
        if (!sessionId) {
             throw new ApiError(400, "Checkout Session ID is required.");
        }

         console.log(`📲 [TicketController] Request received for getTicketDownloadInfo - User: ${userId}, Session: ${sessionId}`);

        const downloadInfo = await TicketService.getTicketDownloadInfo(userId, sessionId);

        // Use standard JSON response structure
        // Note: downloadInfo itself contains { success, message, downloadUrl }
        return res.status(200).json({
            success: downloadInfo.success, // Use success status from service
            message: downloadInfo.message,
            data: downloadInfo // Return the whole downloadInfo object
        });
    });
}