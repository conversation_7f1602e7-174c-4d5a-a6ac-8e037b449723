// Password strength checker utility (your existing implementation)// Password strength checker utility
export const checkPasswordStrength = (password: string) => {
    const criteria = {
      hasUpperCase: /[A-Z]/.test(password),
      hasLowerCase: /[a-z]/.test(password),
      hasNumber: /[0-9]/.test(password),
      hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password),
      isLongEnough: password.length >= 8,
    };
  
    const strength = Object.values(criteria).filter(Boolean).length;
  
    return {
      score: strength,
      criteria,
      label: strength < 2 ? "Weak" : strength < 4 ? "Medium" : "Strong",
      color: strength < 2 ? "red" : strength < 4 ? "yellow" : "green",
    };
  };
  