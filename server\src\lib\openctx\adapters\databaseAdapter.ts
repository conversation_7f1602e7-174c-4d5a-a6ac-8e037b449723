// Database Adapter to interact with the database using Prisma
// This adapter translates the OpenCTX query to Prisma compatible queries

import { PrismaClient, Prisma } from '@prisma/client';
import { OpenCTXRequest, Context } from '../types';

const prisma = new PrismaClient();

// Function to fetch data from the database
async function fetchFromDatabase<T>(query: OpenCTXRequest): Promise<T[]> {
  const { entity, filters, sort, pagination, query: searchQuery } = query;
  let prismaQuery: any = { where: {} };

  if (searchQuery) {
      prismaQuery.where = {
        ...prismaQuery.where,
        OR: [
          { name: { contains: searchQuery, mode: 'insensitive' } },
          { venueName: { contains: searchQuery, mode: 'insensitive' } },
          { venueCity: { contains: searchQuery, mode: 'insensitive' } },
          { venueState: { contains: searchQuery, mode: 'insensitive' } },
        ]
      };
    }

    if (filters) {
      filters.forEach((filter) => {
          if ('value' in filter) {
              prismaQuery.where = { ...prismaQuery.where, [filter.field]: filter.value };
          } else if ('values' in filter) {
              prismaQuery.where = { ...prismaQuery.where, [filter.field]: { in: filter.values } };
          }
      });
  }

  if (sort) {
      prismaQuery.orderBy = { [sort.field]: sort.order || 'asc' };
  }

  if (pagination) {
      prismaQuery.skip = (pagination.page - 1) * pagination.size;
      prismaQuery.take = pagination.size;
  }

  try {
      const results = await (prisma[entity as keyof PrismaClient] as any).findMany(prismaQuery);
      return results;
  } catch (error) {
        console.error("Error querying database:", error);
          throw new Error('Failed to fetch data from database.');
      }
  finally {
      await prisma.$disconnect()
  }
}

// Function to transform data to MCP context
function transformToContext<T>(events: T[], entity: string): Context[] {
return events.map((event: any) => {
    let text = '';
    if(entity === 'tmEvent') {
        text = `Event: ${event.name}, Venue: ${event.venueName}, City: ${event.venueCity}, State: ${event.venueState}`;
    }
    return ({
        type: 'event',
        text: text,
        metadata: {
            source: 'database',
        },
    })
 });
}

// Define a generic method to query data from the database
export const databaseAdapter = {
fetch: async <T>(query: OpenCTXRequest): Promise<Context[]> => {
  const events =  await fetchFromDatabase<T>(query);
  return transformToContext(events, query.entity);
},
};



/**
 * Certainly! Let's clarify the distinction between "adapter" and "service" in the context of backend development:

### Service
- **Purpose:** Services contain the business logic of your application. They handle the core operations related to your business needs.
- **Functionality:** They coordinate tasks, make decisions, and call other classes or modules (like repositories or external APIs) to achieve a specific business goal.
- **Example Use:** If your app involves processing orders, a service might handle validation, apply business rules, calculate totals, and coordinate with payment systems.

### Adapter
- **Purpose:** Adapters act as intermediaries that facilitate communication between different systems or layers by adjusting or translating information.
- **Functionality:** They abstract the details of an interaction with an external system or a different layer (e.g., database, third-party API), so the rest of your application doesn't need to manage these specifics.
- **Example Use:** An adapter might convert data from an external API into a format that your application can understand and work with.

### Key Difference
- **Business Logic vs. Infrastructure Logic:** Services focus on "what" your application does (business logic), while adapters focus on "how" it connects with other systems (infrastructure logic).

In a nutshell, think of **services** as the "brains" that manage business processes, and **adapters** as the "translators" that ensure systems can effectively communicate with each other.
 */