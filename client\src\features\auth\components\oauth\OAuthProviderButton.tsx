import { signIn } from "next-auth/react";
import { useState } from "react";
import { OAuthButtonProps } from "../../types/oauth.types";
import { Apple, Chrome, Github } from "lucide-react";

export const OAuthProviderButton = ({
  provider,
  className = "",
  onSuccess,
  onError,
}: OAuthButtonProps) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleSignIn = async () => {
    try {
      setIsLoading(true);
      const result = await signIn(provider, { redirect: false });

      if (result?.error) {
        onError?.(new Error(result.error));
      } else if (result?.ok) {
        onSuccess?.();
      }
    } catch (error) {
      onError?.(error as Error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <button
      onClick={handleSignIn}
      disabled={isLoading}
      className={`flex items-center justify-center gap-2 w-full p-3 rounded-md border 
        ${
          provider === "google"
            ? "border-gray-300 hover:bg-gray-50"
            : "border-gray-800 hover:bg-gray-900"
        } 
        ${className}`}
    >
      {isLoading ? (
        <span>Loading...</span>
      ) : (
        <>
    
          {provider === "google" && <Chrome size={20} />}{" "} 
         
          {provider === "github" && <Github size={20} />}{" "}

          {provider === "apple" && <Apple size={20} />}{" "}
         
        </>
      )}
    </button>
  );
};
