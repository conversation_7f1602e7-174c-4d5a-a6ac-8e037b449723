// Custom hook for fetching and managing visitor tickets

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useState, useCallback } from "react";
import axiosInstance from "@/apiAxios/axios";
import {
  VisitorTicketsResponse,
  TicketFilterOptions,
  UseVisitorTicketsResult,
  TicketDownloadInfo,
} from "../types/ticket.types";
import { toast } from "sonner";

// Query keys for React Query
const VISITOR_TICKETS_QUERY_KEY = "visitorTickets";
const TICKET_DOWNLOAD_QUERY_KEY = "ticketDownload";

export function useVisitorTickets(): UseVisitorTicketsResult {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  // State for pagination and filtering
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [filterOptions, setFilterOptions] = useState<TicketFilterOptions>({
    sortBy: "date",
    sortOrder: "desc",
  });

  // Fetch tickets data
  const { data, isLoading, isError, error, refetch } = useQuery<
    VisitorTicketsResponse,
    Error
  >({
    queryKey: [VISITOR_TICKETS_QUERY_KEY, currentPage, filterOptions],
    queryFn: async () => {
      try {
        console.log("🚀 Fetching visitor tickets...");

        // Build query parameters
        const params = new URLSearchParams();
        params.append("page", currentPage.toString());
        params.append("limit", "10"); // Default limit

        // Add sorting parameters
        if (filterOptions.sortBy) {
          params.append("sortBy", filterOptions.sortBy);
          params.append("sortOrder", filterOptions.sortOrder);
        }

        // Add date range if present
        if (filterOptions.dateRange?.start) {
          params.append(
            "startDate",
            filterOptions.dateRange.start.toISOString()
          );
        }
        if (filterOptions.dateRange?.end) {
          params.append("endDate", filterOptions.dateRange.end.toISOString());
        }

        const response = await axiosInstance.get<{
          success: boolean;
          message: string;
          data: VisitorTicketsResponse;
        }>(`/api/v1/tickets/my-tickets?${params.toString()}`);

        console.log("🎟️ Visitor tickets received:", response.data);

        if (response.data.success && response.data.data) {
          return response.data.data;
        } else {
          throw new Error(response.data.message || "Failed to fetch tickets");
        }
      } catch (err: any) {
        console.error("❌ Error fetching visitor tickets:", err);
        const errorMessage =
          err.response?.data?.message ||
          err.message ||
          "Failed to fetch tickets";
        toast.error(errorMessage);
        throw new Error(errorMessage);
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes before refetching
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
  });

  // Mutation for requesting ticket download info
  const { mutateAsync: downloadTicket, isPending: isDownloading } = useMutation<
    TicketDownloadInfo,
    Error,
    string
  >({
    mutationKey: [TICKET_DOWNLOAD_QUERY_KEY],
    mutationFn: async (sessionId: string) => {
      try {
        console.log(`🚀 Requesting download info for session: ${sessionId}`);

        const response = await axiosInstance.get<{
          success: boolean;
          message: string;
          data: TicketDownloadInfo;
        }>(`/api/v1/tickets/${sessionId}/download-info`);

        console.log("📄 Ticket download info received:", response.data);

        if (response.data.success && response.data.data) {
          return response.data.data;
        } else {
          throw new Error(
            response.data.message || "Failed to get ticket download information"
          );
        }
      } catch (err: any) {
        console.error("❌ Error getting ticket download info:", err);
        const errorMessage =
          err.response?.data?.message ||
          err.message ||
          "Failed to get ticket download information";
        toast.error(errorMessage);
        throw new Error(errorMessage);
      }
    },
    onSuccess: (data) => {
      // Show success toast
      if (data.downloadUrl) {
        toast.success("Ticket download ready!");
        // If we have a URL, we could automatically trigger download or open in new tab
        window.open(data.downloadUrl, "_blank");
      } else {
        toast.info(data.message || "Ticket download not yet available");
      }
    },
    onError: (error) => {
      toast.error(`Download request failed: ${error.message}`);
    },
  });

  // Handler for changing page
  const goToPage = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // Handler for updating filter options
  const updateFilterOptions = useCallback(
    (options: Partial<TicketFilterOptions>) => {
      setFilterOptions((prev) => ({
        ...prev,
        ...options,
      }));
      // Reset to first page when filters change
      setCurrentPage(1);
    },
    []
  );

  // Handler for requesting ticket download
  const requestTicketDownload = useCallback(
    async (sessionId: string) => {
      return await downloadTicket(sessionId);
    },
    [downloadTicket]
  );

  // Return the hook result
  return {
    tickets: data?.data || [],
    pagination: data?.pagination || {
      currentPage,
      totalPages: 0,
      totalItems: 0,
      hasNextPage: false,
      hasPrevPage: false,
    },
    isLoading,
    isError,
    error: error as Error,
    refetch,
    goToPage,
    updateFilterOptions,
    requestTicketDownload,
    filterOptions,
    isDownloading,
  };
}
