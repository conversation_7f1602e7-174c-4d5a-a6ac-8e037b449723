// Import necessary dependencies and types
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "@/apiAxios/axios";
import { useSession } from "next-auth/react";
import { ApprovalStatus } from "../types/approval.types";

// Custom hook to fetch approvals with optional status filter
export const usePendingApprovals = (
  status: ApprovalStatus = "PENDING",
  page: number = 1,
  limit: number = 4
) => {
  return useQuery({
    queryKey: ["approvals", status, page, limit],
    queryFn: async () => {
      let endpoint = "";
      
      switch (status) {
        case "PENDING":
          endpoint = "/api/v1/manager-events/pending";
          break;
        case "APPROVED":
          endpoint = "/api/v1/manager-events/approved";
          break;
        case "REJECTED":
          endpoint = "/api/v1/manager-events/rejected";
          break;
        default:
          endpoint = "/api/v1/manager-events/pending";
      }

      const response = await axios.get(endpoint, {
        params: { page, limit }
      });

      return response.data;
    },
    // Optionally adjust these based on UX requirements
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
  });
};

// Custom mutation hook for approving an event
export const useApproveEvent = () => {
  const { data: session } = useSession();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      eventId,
      notes,
    }: {
      eventId: string;
      notes?: string;
    }) => {
      // Check for admin authentication
      if (!session?.user?.id) {
        throw new Error("Admin authentication required");
      }

      const response = await axios.patch(
        `/api/v1/manager-events/${eventId}/approval`,
        {
          approvalStatus: "APPROVED",
          approvalNotes: notes || "Approved by admin",
          approvedBy: session.user.id, // Send the admin's ID
        }
      );
      return response.data;
    },
    onSuccess: () => {
      // Invalidate multiple queries to ensure UI is updated
      queryClient.invalidateQueries({ queryKey: ["approvals"] });
      queryClient.invalidateQueries({ queryKey: ["approvalStats"] });
    },
  });
};

// Custom mutation hook for rejecting an event
export const useRejectEvent = () => {
  const { data: session } = useSession();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      eventId,
      notes,
    }: {
      eventId: string;
      notes?: string;
    }) => {
      // Check for admin authentication
      if (!session?.user?.id) {
        throw new Error("Admin authentication required");
      }

      const response = await axios.patch(
        `/api/v1/manager-events/${eventId}/approval`,
        {
          approvalStatus: "REJECTED",
          approvalNotes: notes || "Rejected by admin",
          approvedBy: session.user.id, // Send the admin's ID
        }
      );
      return response.data;
    },
    onSuccess: () => {
      // Invalidate multiple queries to ensure UI is updated
      queryClient.invalidateQueries({ queryKey: ["approvals"] });
      queryClient.invalidateQueries({ queryKey: ["approvalStats"] });
    },
  });
};
