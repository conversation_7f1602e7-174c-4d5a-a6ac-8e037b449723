"use client";
import { AdminEventManagement } from '@/features/modules/admin-portal/events/AdminEventManagement';
import { usePermissions } from '@/utils/permissions/hooks/usePermissions';

const AdminEventsPageClient = () => {
    const { getEventOperations } = usePermissions();
    const operations = getEventOperations()
    return <AdminEventManagement operations={operations} />
}

export default function AdminEventsPage() {
    return <AdminEventsPageClient />
}