/**
 * Types related to points functionality
 */

export interface ApplyPointsRequest {
  sessionId: string;
  pointsToApply: number;
}

export interface AppliedPoints {
  pointsUsed: number;
  pointsValuePerUnit: number; // How much each point is worth in currency
  discountAmount: number; // Calculated amount in currency
  refereeStatus?: {
    isReferee: boolean;
    orderCount: number;
    discountMultiplier: number; // 0.5 for first/second orders, 1.0 otherwise
  };
}

export interface ApplyPointsResponse {
  success: boolean;
  message: string;
  data: {
    success: boolean;
    session?: any; // Will contain updated session if successful
    userPointsBalance?: number; // Remaining points after application
    error?: string;
  };
}