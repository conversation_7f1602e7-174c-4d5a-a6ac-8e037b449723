/**
 * API functions for subscription operations
 */
import { useMutation, useQuery } from "@tanstack/react-query";
import { api } from "@/apiAxios/axios";
import { toast } from "sonner";
import {
  SubscriptionPlanType,
  SubscriptionResponse,
  CreateCheckoutResponse,
} from "../types/subscription.types";

// Query key for subscription status
const subscriptionKeys = {
  status: () => ["subscription", "status"],
};

/**
 * Hook to fetch current subscription status
 */
export function useSubscriptionStatus() {
  return useQuery({
    queryKey: subscriptionKeys.status(),
    queryFn: async (): Promise<SubscriptionResponse> => {
      const response = await api.get<SubscriptionResponse>(
        "/api/v1/payments/subscriptions/status"
      );
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to create a subscription checkout session
 */
export function useCreateSubscriptionCheckout() {
  return useMutation({
    mutationFn: async ({
      planType,
      successUrl = window.location.origin +
        "/account/membership?status=success",
      cancelUrl = window.location.origin + "/subscribe?canceled=true",
    }: {
      planType: SubscriptionPlanType;
      successUrl?: string;
      cancelUrl?: string;
    }): Promise<CreateCheckoutResponse> => {
      const response = await api.post<CreateCheckoutResponse>(
        "/api/v1/payments/subscriptions/create-checkout",
        { planType, successUrl, cancelUrl }
      );
      return response.data;
    },
    onSuccess: (data) => {
      if (data.success && data.url) {
        // Redirect to Stripe Checkout
        window.location.href = data.url;
      } else if (data.error) {
        toast.error("Failed to start subscription", {
          description: data.error,
        });
      }
    },
    onError: (error: any) => {
      toast.error("Failed to start subscription", {
        description: error.message || "Please try again later",
      });
    },
  });
}

/**
 * Hook to cancel a subscription
 */
export function useCancelSubscription() {
  return useMutation({
    mutationFn: async ({
      immediately = false,
    }: {
      immediately?: boolean;
    } = {}): Promise<SubscriptionResponse> => {
      const response = await api.post<SubscriptionResponse>(
        "/api/v1/payments/subscriptions/cancel",
        { immediately }
      );
      return response.data;
    },
    onSuccess: (data) => {
      if (data.success) {
        toast.success("Subscription canceled", {
          description: data.message || "Your subscription has been canceled",
        });
      } else if (data.error) {
        toast.error("Failed to cancel subscription", {
          description: data.error,
        });
      }
    },
    onError: (error: any) => {
      toast.error("Failed to cancel subscription", {
        description: error.message || "Please try again later",
      });
    },
  });
}
