import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { PlusCircle, Home, AlertCircle } from 'lucide-react';
import { useBillingAddresses } from '../hooks/useBillingAddresses';
import { BillingAddressForm } from './BillingAddressForm';
import { BillingAddressCard } from './BillingAddressCard';
import { BillingAddress, CreateBillingAddressRequest, UpdateBillingAddressRequest } from '../types/billing-address.types';
import { Alert, AlertDescription } from '@/components/ui/alert';

export function BillingAddressManager() {
  const { 
    addresses, 
    isLoading, 
    isError, 
    error,
    isCreating,
    isUpdating,
    createAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress,
    refetch
  } = useBillingAddresses();
  
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState<BillingAddress | null>(null);
  
  const handleOpenNewForm = () => {
    setSelectedAddress(null);
    setIsFormOpen(true);
  };
  
  const handleEditAddress = (address: BillingAddress) => {
    setSelectedAddress(address);
    setIsFormOpen(true);
  };
  
  const handleCloseForm = () => {
    setIsFormOpen(false);
    setSelectedAddress(null);
  };
  
  const handleSubmit = async (data: CreateBillingAddressRequest | UpdateBillingAddressRequest) => {
    if ('id' in data) {
      await updateAddress(data);
    } else {
      await createAddress(data);
    }
    handleCloseForm();
  };
  
  // Render loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-semibold flex items-center">
            <Home className="mr-2 h-5 w-5" /> Billing Addresses / Shipping Addresses
          </CardTitle>
          <CardDescription>
            Manage your Shipping/billing addresses for purchases
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-[180px] w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }
  
  // Render error state
  if (isError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-semibold flex items-center">
            <Home className="mr-2 h-5 w-5" /> Billing Addresses / Shipping Addresses
          </CardTitle>
          <CardDescription>
            Manage your billing/shipping addresses for purchases
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error?.message || "Failed to load billing addresses. Please try again."}
            </AlertDescription>
          </Alert>
          <div className="mt-4 flex justify-center">
            <Button onClick={() => refetch()}>Try Again</Button>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="text-xl font-semibold flex items-center">
            <Home className="mr-2 h-5 w-5" /> Billing Addresses / Shipping Addresses
          </CardTitle>
          <CardDescription>
            Manage your billing addresses for purchases
          </CardDescription>
        </div>
        <Button onClick={handleOpenNewForm}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Add Address
        </Button>
      </CardHeader>
      <CardContent>
        {addresses.length === 0 ? (
          <div className="bg-gray-50 p-6 rounded-lg text-center">
            <Home className="h-12 w-12 mx-auto text-gray-400 mb-2" />
            <h3 className="text-lg font-medium mb-2">No Billing Addresses</h3>
            <p className="text-gray-500 mb-4">
              You haven&apos;t added any billing/shipping addresses yet. Add one to streamline your checkout process.
            </p>
            <Button onClick={handleOpenNewForm}>
              Add Your First Address
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {addresses.map((address) => (
              <BillingAddressCard
                key={address.id}
                address={address}
                onEdit={handleEditAddress}
                onDelete={deleteAddress}
                onSetDefault={setDefaultAddress}
              />
            ))}
          </div>
        )}
      </CardContent>
      
      {/* Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="sm:max-w-[550px] max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>
              {selectedAddress ? 'Edit Billing Address' : 'Add Billing Address'}
            </DialogTitle>
            <DialogDescription>
              {selectedAddress 
                ? 'Update your billing address details below'
                : 'Enter your billing address details below'}
            </DialogDescription>
          </DialogHeader>
          <ScrollArea className="max-h-[60vh] pr-4">
            <div className="p-1">
              <BillingAddressForm
                onSubmit={handleSubmit}
                initialData={selectedAddress || undefined}
                isSubmitting={isCreating || isUpdating}
                onCancel={handleCloseForm}
              />
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </Card>
  );
}