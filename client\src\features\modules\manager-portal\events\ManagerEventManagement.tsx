// Import necessary components and types
// import { EventManagement } from '@/features/event-management/EventManagement'; // Commented out old import

// ✨ New Import for the inventory table ✨
import { ManagerInventoryTable } from '@/features/manager-inventory/components/ManagerInventoryTable';
import { EventOperations } from '@/utils/permissions/types';

// Define props interface for ManagerEventManagement
interface ManagerEventManagementProps {
  operations: EventOperations; // Keep operations prop for potential use (e.g., controlling Create button visibility)
}

// ManagerEventManagement component now renders the inventory table
export const ManagerEventManagement = ({ operations }: ManagerEventManagementProps) => {
  // Previous implementation (commented out):
  // return <EventManagement operations={operations} />;

  // ✨ New Implementation using ManagerInventoryTable ✨
  // The table component now fetches its own data via the useManagerInventoryQuery hook.
  // The 'operations' prop might be used later to conditionally render other elements
  // on this page based on permissions (e.g., a "Create Listing" button).
  return (
      <div>
          {/* We can add a title or other elements here if needed */}
          {/* Example: <h1 className="text-2xl font-bold mb-4">My Event Inventory</h1> */}
          <ManagerInventoryTable />
      </div>
  );
};
