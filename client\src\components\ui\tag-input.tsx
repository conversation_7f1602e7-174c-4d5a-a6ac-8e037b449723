"use client"

import * as React from "react"
import { X } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Command, CommandGroup, CommandItem } from "@/components/ui/command"
import { Command as CommandPrimitive } from "cmdk"

interface TagInputProps {
  placeholder?: string
  tags: string[]
  setTags: (tags: string[]) => void
  suggestions?: string[]
}

export function TagInput({ placeholder, tags, setTags, suggestions = [] }: TagInputProps) {
  const inputRef = React.useRef<HTMLInputElement>(null)
  const [inputValue, setInputValue] = React.useState("")
  const [open, setOpen] = React.useState(false)

  const handleAddTag = (tag: string) => {
    if (tag && !tags.includes(tag)) {
      setTags([...tags, tag])
      setInputValue("")
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  return (
    <div className="border border-input rounded-md">
      <div className="flex flex-wrap gap-1 p-1">
        {tags.map(tag => (
          <Badge key={tag} variant="secondary">
            {tag}
            <button
              title={`Remove ${tag}`}
              className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              onClick={() => handleRemoveTag(tag)}
            >
              <X className="h-3 w-3" />
            </button>
          </Badge>
        ))}
        <CommandPrimitive>
          <input
            ref={inputRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter" && inputValue) {
                e.preventDefault()
                handleAddTag(inputValue)
              }
            }}
            placeholder={placeholder}
            className="flex-1 bg-transparent px-1 py-0.5 outline-none placeholder:text-muted-foreground"
          />
        </CommandPrimitive>
      </div>
      {open && suggestions.length > 0 && (
        <Command>
          <CommandGroup>
            {suggestions
              .filter(s => s.toLowerCase().includes(inputValue.toLowerCase()))
              .map(suggestion => (
                <CommandItem
                  key={suggestion}
                  onSelect={() => handleAddTag(suggestion)}
                >
                  {suggestion}
                </CommandItem>
              ))}
          </CommandGroup>
        </Command>
      )}
    </div>
  )
}
