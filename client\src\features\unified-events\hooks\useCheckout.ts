import { useState, useCallback } from "react";
import { use<PERSON>out<PERSON> } from "next/navigation";
import { useMutation } from "@tanstack/react-query";
import { api } from "@/apiAxios/axios"; // Assuming you use axios instance from api/axios.ts
import { toast } from "sonner";

// Import necessary hooks and types
import { useProfileQuery } from "@/features/profile/hooks/useProfileQuery";
import { useWaitingRoom } from "./useWaitingRoom"; // We need queue status and join action
import {
  CheckoutItem as CheckoutItemRequest,
  CreateReservationResponse,
} from "@/features/checkout/types/checkout.types"; // Assuming types are here

// Define a type for selections for clarity
type Selections = Record<string, number>;

/**
 * @interface UseCheckoutProps
 * Props for the useCheckout hook.
 * @param eventId - Optional ID of the event being checked out.
 * @param onCheckoutSuccess - Optional callback function executed after successful reservation creation and *before* redirection. Receives the new session ID.
 * @param onCheckoutError - Optional callback function executed if reservation creation fails. Receives the error object.
 */
interface UseCheckoutProps {
  eventId?: string;
  onCheckoutSuccess?: (sessionId: string) => void;
  onCheckoutError?: (error: Error) => void;
}

// Define the type for data passed to createReservation mutation
interface CreateReservationMutationData {
  eventId: string;
  items: CheckoutItemRequest[];
  billingAddressId?: string; // Added billingAddressId here
}

/**
 * Custom hook for orchestrating the entire checkout initiation process for an event.
 *
 * This hook acts as the central controller for starting the checkout flow after ticket selection.
 * It performs the following steps:
 * 1. Validates the user's profile (login status, email/mobile verification).
 * 2. Checks the event's waiting room/queue status using `useWaitingRoom`.
 * 3. If the queue is active and the user needs to join, it can attempt to automatically join.
 * 4. If the user is in the queue but not admitted, it prevents proceeding.
 * 5. If profile and queue checks pass, it calls the backend to create a ticket reservation (`CheckoutSession`).
 * 6. On successful reservation, it redirects the user to the dedicated checkout page (`/checkout/[sessionId]`).
 * 7. Manages loading (`isCheckingOut`) and error (`checkoutError`) states for the initiation process.
 *
 * @param {UseCheckoutProps} props - Configuration options for the hook.
 * @returns An object containing checkout state, actions, and relevant status flags.
 */
export const useCheckout = ({
  eventId,
  onCheckoutSuccess,
  onCheckoutError,
}: UseCheckoutProps = {}) => {
  const router = useRouter();
  /** @state {boolean} isCheckingOut - True if any asynchronous checkout operation (validation, reservation) is in progress. */
  const [isCheckingOut, setIsCheckingOut] = useState(false);
  /** @state {Error | null} checkoutError - Stores any error that occurred during the checkout initiation process. */
  const [checkoutError, setCheckoutError] = useState<Error | null>(null);

  // Get user profile data using the profile query hook.
  const {
    profile,
    isLoading: isProfileLoading,
    isError: isProfileError,
    error: profileError,
  } = useProfileQuery();

  // Get relevant queue status and actions for the current event using the waiting room hook.
  const {
    isActive: isQueueActive,
    isAdmitted,
    needsToJoin, // True if queue is active and user is not yet in it.
    joinQueue, // Function provided by useWaitingRoom to join the queue.
  } = useWaitingRoom(eventId);

  /**
   * @private
   * Internal helper function to validate the user's profile state.
   * Checks login status, email verification, and mobile verification.
   * @returns {object} An object indicating validity, potential error, and optional action (e.g., navigate to profile).
   */
  const validateProfile = useCallback((): {
    isValid: boolean;
    error?: Error;
    action?: () => void;
  } => {
    if (isProfileLoading)
      return { isValid: false, error: new Error("Loading profile...") };
    if (isProfileError || !profile)
      return {
        isValid: false,
        error:
          profileError instanceof Error
            ? profileError
            : new Error("Please log in."),
      };
    // Ensure your profile type has these fields or adjust accordingly
    if (!profile.emailVerified)
      return {
        isValid: false,
        error: new Error("Verify your email."),
        action: () => router.push("/profile"),
      };
    if (!profile.mobileVerified)
      return {
        isValid: false,
        error: new Error("Verify your mobile."),
        action: () => router.push("/profile"),
      };
    return { isValid: true };
  }, [isProfileLoading, isProfileError, profile, profileError, router]);

  /**
   * @private
   * React Query mutation hook for creating the checkout reservation session on the backend.
   * Called only after all validations (profile, queue, inventory) pass.
   */
  const createReservation = useMutation({
    mutationFn: async (data: CreateReservationMutationData) => {
      // Use the new type here
      const response = await api.post<CreateReservationResponse>(
        "/api/v1/checkout/reserve",
        // Pass all data including billingAddressId
        {
          eventId: data.eventId,
          items: data.items,
          billingAddressId: data.billingAddressId, // Send to backend
        }
      );

      if (!response.data.success || !response.data.data?.session?.id) {
        throw new Error(
          response.data.message || "Failed to create reservation session."
        );
      }

      return response.data;
    },
    onSuccess: (data) => {
      const sessionId = data.data?.session?.id;
      if (!sessionId) {
        toast.error("Reservation Error", {
          description: "Session ID not found in response.",
        });
        setCheckoutError(new Error("Session ID not found in response."));
        return;
      }
      toast.success("Reservation created", {
        description: "Redirecting to checkout...",
      });

      // Call success callback if provided
      if (onCheckoutSuccess) onCheckoutSuccess(sessionId);

      // Redirect to checkout page with session ID
      router.push(`/checkout/${sessionId}`);
    },
    onError: (error: Error) => {
      setCheckoutError(error);
      toast.error("Reservation Failed", { description: error.message });

      // Call error callback if provided
      if (onCheckoutError) onCheckoutError(error);
    },
    onSettled: () => {
      // Reset loading state whether success or error
      setIsCheckingOut(false);
    },
  });

  /**
   * The primary function exposed by the hook to initiate the checkout process.
   * This function should be called when the user clicks the main "Checkout" button
   * or the "Continue to Checkout" button after being admitted from the queue.
   *
   * @param {Selections} selections - The current state of selected tickets (mapping inventoryId to quantity).
   * @param {boolean} canProceedInventory - Flag indicating if the current selections meet inventory-specific rules (min/max qty, etc.), typically derived from `useTicketSelection`.
   * @param {string} [billingAddressId] - Optional ID of the selected billing address.
   */
  const checkoutFromSelections = useCallback(
    async (
      selections: Selections,
      canProceedInventory: boolean,
      billingAddressId?: string
    ) => {
      console.log(
        "🛒 Checkout initiation started via useCheckout hook with billingAddressId:",
        billingAddressId
      );
      console.log(
        "🆔 [useCheckout] EventId being used for checkout API call:",
        eventId
      );

      // Set loading state immediately.
      setIsCheckingOut(true);
      setCheckoutError(null);

      // --- Step 1: Basic Input Validation ---
      if (!eventId) {
        toast.error("Checkout Error", { description: "Event ID is missing." });
        setIsCheckingOut(false);
        return;
      }

      // Convert selections map to array format needed by backend.
      const items = Object.entries(selections)
        .filter(([_, qty]) => qty > 0)
        .map(([inventoryId, quantity]) => ({
          inventoryId,
          quantity: Number(quantity),
        }));

      console.log("📦 [useCheckout] Items being sent to checkout API:", items);

      if (items.length === 0) {
        toast.warning("Empty Selection", {
          description: "Please select tickets first.",
        });
        setIsCheckingOut(false);
        return;
      }

      // --- Step 2: Ticket Selection Rule Validation ---
      // Uses the flag passed from useTicketSelection state.
      if (!canProceedInventory) {
        toast.error("Invalid Selection", {
          description:
            "Please review ticket quantities and seller requirements.",
        });
        setIsCheckingOut(false);
        return;
      }

      // --- Step 3: Profile Validation ---
      const profileValidation = validateProfile();
      if (!profileValidation.isValid) {
        toast.error("Profile Incomplete", {
          description: profileValidation.error?.message,
          action: profileValidation.action
            ? { label: "Go to Profile", onClick: profileValidation.action }
            : undefined,
        });
        setIsCheckingOut(false);
        return;
      }

      // --- Step 4: Queue Validation ---
      // Checks the queue status obtained from useWaitingRoom.
      if (isQueueActive && !isAdmitted) {
        // If queue active and user isn't in it yet...
        if (needsToJoin) {
          toast.info("Waiting Room Required", {
            description: "Joining the waiting room automatically...",
          });
          try {
            await joinQueue(); // Attempt to join via useWaitingRoom's function.
            // Stop the checkout flow here. User needs to wait in the queue.
            // The UI should update based on useWaitingRoom's polling.
            toast.info("Joined Queue", {
              description:
                "Please wait for your turn. Your status will update automatically.",
            });
          } catch (joinError) {
            toast.error("Queue Join Failed", {
              description:
                joinError instanceof Error
                  ? joinError.message
                  : "Could not join queue.",
            });
          }
        } else {
          // User is already in the queue but waiting for admission.
          toast.info("You're in the Queue", {
            description: "Please wait until you are admitted to proceed.",
          });
        }
        // Crucially, stop the checkout process if queue validation fails.
        setIsCheckingOut(false);
        return; // Exit function.
      }

      // --- Step 5: Initiate Reservation ---
      // If all previous checks passed, proceed to call the backend reservation endpoint.
      console.log("✅ All checks passed. Creating reservation...");
      // The createReservation mutation handles the rest (API call, redirect, error handling).
      // Pass billingAddressId to the mutation
      createReservation.mutate({
        eventId,
        items: items as CheckoutItemRequest[], // Type assertion if needed, ensure items match CheckoutItemRequest
        billingAddressId, // Pass it here
      });
    },
    [
      eventId,
      validateProfile,
      isQueueActive,
      isAdmitted,
      needsToJoin,
      joinQueue, // Dependency for automatic joining
      createReservation, // Dependency for the mutation object
    ]
  );

  // --- Return Value ---
  // Expose state and actions needed by the UI components.
  return {
    /** True if the checkout initiation process (validation, reservation API call) is ongoing. */
    isCheckingOut: isCheckingOut || createReservation.isPending,
    /** Any error encountered during the checkout initiation. */
    checkoutError,

    /** The main function to call to start the checkout process from selections. */
    checkoutFromSelections,

    // Pass through relevant queue state for UI logic (e.g., disabling buttons, showing messages).
    /** True if the waiting room queue is currently active for the event. */
    isQueueActive,
    /** True if the user has been admitted from the queue and can proceed. */
    isAdmitted,
    /** True if the queue is active and the user needs to join it. */
    needsToJoin,

    /** True if the user's profile data is still loading. */
    isProfileLoading,
  };
};
