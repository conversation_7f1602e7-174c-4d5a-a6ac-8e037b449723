/*
  Warnings:

  - You are about to drop the column `area` on the `user_metadata` table. All the data in the column will be lost.
  - You are about to drop the column `eu` on the `user_metadata` table. All the data in the column will be lost.
  - You are about to drop the column `metro` on the `user_metadata` table. All the data in the column will be lost.
  - You are about to drop the column `range` on the `user_metadata` table. All the data in the column will be lost.
  - You are about to drop the column `region` on the `user_metadata` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "user_metadata" DROP COLUMN "area",
DROP COLUMN "eu",
DROP COLUMN "metro",
DROP COLUMN "range",
DROP COLUMN "region";

-- AlterTable
ALTER TABLE "users" ADD COLUMN     "image" TEXT,
ADD COLUMN     "providerId" TEXT,
ADD COLUMN     "providerType" TEXT,
ALTER COLUMN "password" DROP NOT NULL,
ALTER COLUMN "mobile" DROP NOT NULL;

-- CreateTable
CREATE TABLE "accounts" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,

    CONSTRAINT "accounts_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "accounts_provider_providerAccountId_key" ON "accounts"("provider", "providerAccountId");

-- CreateIndex
CREATE INDEX "users_email_providerId_idx" ON "users"("email", "providerId");

-- AddForeignKey
ALTER TABLE "accounts" ADD CONSTRAINT "accounts_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
