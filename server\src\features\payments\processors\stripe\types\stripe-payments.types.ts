/**
 * Stripe-specific payment types
 */
import { BasePaymentIntentRequest, SupportedCurrency } from '../../../common/types/payment-common.types';

/**
 * Stripe payment intent creation request
 * Extends the base payment intent request with Stripe-specific options
 */
export interface StripePaymentIntentRequest extends BasePaymentIntentRequest {
  paymentMethodTypes?: string[]; // Default: ['card']
  setupFutureUsage?: 'on_session' | 'off_session';
  statementDescriptor?: string;
  receiptEmail?: string;
  shipping?: {
    name: string;
    address: {
      line1: string;
      line2?: string;
      city: string;
      state?: string;
      postal_code: string;
      country: string;
    };
  };
}

/**
 * Response when creating a payment intent
 */
export interface StripePaymentIntentResponse {
  success: boolean;
  clientSecret?: string;
  paymentIntentId?: string;
  amount?: number;
  currency?: SupportedCurrency;
  status?: string;
  error?: {
    message: string;
    code?: string;
  };
}

/**
 * Response when retrieving a payment
 */
export interface StripePaymentDetailsResponse {
  success: boolean;
  payment?: {
    id: string;
    amount: number;
    currency: string;
    status: string;
    createdAt: number;
    paymentMethod?: string;
    paymentMethodDetails?: any;
    receiptUrl?: string;
    refunded?: boolean;
    refundedAmount?: number;
  };
  error?: {
    message: string;
    code?: string;
  };
}

/**
 * Stripe webhook event validation response
 */
export interface StripeWebhookValidationResult {
  valid: boolean;
  event?: any; // Stripe.Event
  error?: string;
}

/**
 * Request for creating a payment intent from a checkout session
 * Can include optional payment intent configuration
 */
export interface CreatePaymentIntentFromCheckoutRequest {
  sessionId: string;
  // All fields from StripePaymentIntentRequest are optional when creating
  // from a checkout session, since most values come from the session itself
  setupFutureUsage?: 'on_session' | 'off_session';
  paymentMethodTypes?: string[];
  statementDescriptor?: string;
  receiptEmail?: string;
  shipping?: {
    name: string;
    address: {
      line1: string;
      line2?: string;
      city: string;
      state?: string;
      postal_code: string;
      country: string;
    };
  };
  metadata?: Record<string, string>;
}

/**
 * Request for creating a subscription checkout session
 */
export interface CreateSubscriptionCheckoutRequest {
  priceId: string;
  successUrl: string;
  cancelUrl: string;
}

/**
 * Response for creating a subscription checkout session
 */
export interface CreateSubscriptionCheckoutResponse {
  success: boolean;
  url?: string;
  error?: string;
}