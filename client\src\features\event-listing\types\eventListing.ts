import { Context } from "@/types/openctx.types";

// Interface for the data of a single step in the form
export interface EventListingStepProps {
  step: number; // The current step number
  children: React.ReactNode;  // Children to render within the step
  title: string;     // Add a title for each step
  description?: string; // Optional description
}

// Interface for inventory item data
export interface InventoryItem {
  id: string;
  quantity: number;
  section: string;
  row: number;
  lowSeatNumber?: number;
  seatingType: 'Consecutive' | 'Odd-even' | 'GA';
  ticketFormat: 'E-ticket (PDF)' | 'Mobile Transfer' | 'Hard (Printed Tickets)';
  listPrice: number;
  serviceFee?: number;
  publicNote?: string;
  internalNote?: string;
  termsAndConditions: boolean;
  sellingPreference: 'Any' | 'Pairs' | 'Full' | 'Avoid Leaving Single';
  disclosures: string[]; // Added disclosures
  attributes: string[]; // Added attributes
}

// Interface for the data of a single step in the form
export interface EventListingData {
  selectedEvent: Context | null;
  inventory: InventoryItem[];
  tempInventoryItem?: InventoryItem; // Added tempInventoryItem, optional
  purchaseOrder?: {
    exchange: string;
    market: string;
    price: number;
    quantity: number;
    generateDraft: boolean;
  };
}

// Interface for EventListingContext
export interface EventListingContextProps {
  currentStep: number;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  goToStep: (step: number) => void;
  eventListingData: EventListingData;
  setEventListingData: React.Dispatch<React.SetStateAction<EventListingData>>;
  totalSteps: number;
  handleEditInventory: (item: InventoryItem) => void;
  handleDeleteInventory: (itemId: string) => void;
}
