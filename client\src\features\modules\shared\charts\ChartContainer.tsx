import { cn } from "@/lib/utils";
import { ChartContainerType } from "@/features/modules/shared/types/dashboard.types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export const ChartContainer = ({
  title,
  children,
  className,
  height = 300,
}: ChartContainerType) => {
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div style={{ height }}>{children}</div>
      </CardContent>
    </Card>
  );
};
