import React, { useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { ProfileData } from '@/features/profile/types/profile.types';
import { profileFormSchema, ProfileFormValues } from './schema';
import { ProfileVerification } from './ProfileVerification';
import { useProfileQuery } from '@/features/profile/hooks/useProfileQuery';
import { BadgePlus, Briefcase, Globe, LinkIcon, Loader2, Mail, MapPin, Save, User } from 'lucide-react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  <PERSON><PERSON><PERSON>er, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>Title 
} from '@/components/ui/card';

interface ProfileEditFormProps {
  profile: ProfileData;
  onSubmitSuccess?: () => void;
  onCancel?: () => void;
}

export const ProfileEditForm: React.FC<ProfileEditFormProps> = ({
  profile,
  onSubmitSuccess,
  onCancel
}) => {
  // Get the profile query hooks for verification
  const { 
    updateProfile, 
    sendEmailVerification, 
    verifyEmail,
    sendMobileVerification,
    verifyMobile
  } = useProfileQuery();
  
  // Initialize form with profile data
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      fullName: profile.fullName || '',
      headline: profile.headline || '',
      bio: profile.bio || '',
      mobile: profile.mobile || '',
      location: profile.location || '',
      website: profile.website || '',
      company: profile.company || '',
      jobTitle: profile.jobTitle || '',
      industry: profile.industry || '',
      skills: profile.skills || [],
      socialLinks: {
        twitter: profile.socialLinks?.twitter || '',
        linkedin: profile.socialLinks?.linkedin || '',
        facebook: profile.socialLinks?.facebook || '',
        instagram: profile.socialLinks?.instagram || '',
        github: profile.socialLinks?.github || '',
      }
    }
  });
  
  // Update form values when profile changes
  useEffect(() => {
    console.log('🔄 ProfileEditForm: Updating form values with profile data');
    form.reset({
      fullName: profile.fullName || '',
      headline: profile.headline || '',
      bio: profile.bio || '',
      mobile: profile.mobile || '',
      location: profile.location || '',
      website: profile.website || '',
      company: profile.company || '',
      jobTitle: profile.jobTitle || '',
      industry: profile.industry || '',
      skills: profile.skills || [],
      socialLinks: {
        twitter: profile.socialLinks?.twitter || '',
        linkedin: profile.socialLinks?.linkedin || '',
        facebook: profile.socialLinks?.facebook || '',
        instagram: profile.socialLinks?.instagram || '',
        github: profile.socialLinks?.github || '',
      }
    });
  }, [profile, form]);
  
  const onSubmit = async (data: ProfileFormValues) => {
    console.log('📝 Submitting profile form data:', data);
    try {
      await updateProfile.mutate(data);
      console.log('✅ Profile updated successfully');
      if (onSubmitSuccess) onSubmitSuccess();
    } catch (error) {
      console.error('❌ Error updating profile:', error);
    }
  };
  
  const handleSendEmailVerification = (email: string) => {
    console.log('📧 Sending email verification to:', email);
    sendEmailVerification.mutate(email);
  };
  
  const handleVerifyEmail = (otp: string) => {
    console.log('🔑 Verifying email with OTP:', otp);
    verifyEmail.mutate(otp);
  };
  
  const handleSendMobileVerification = (mobile: string) => {
    console.log('📱 Sending mobile verification to:', mobile);
    sendMobileVerification.mutate(mobile);
  };
  
  const handleVerifyMobile = (otp: string) => {
    console.log('🔑 Verifying mobile with OTP:', otp);
    verifyMobile.mutate(otp);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Edit Profile</CardTitle>
        <CardDescription>Update your personal and professional information</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Personal Information Section */}
            <div>
              <h3 className="text-lg font-medium flex items-center">
                <User className="mr-2 h-5 w-5" />
                Personal Information
              </h3>
              <Separator className="my-3" />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="fullName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Your full name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="headline"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Professional Headline</FormLabel>
                      <FormControl>
                        <Input placeholder="Your professional headline" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="mt-4">
                <FormField
                  control={form.control}
                  name="bio"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bio</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Tell us about yourself" 
                          className="resize-none h-24" 
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            
            {/* Contact & Verification Section */}
            <div>
              <h3 className="text-lg font-medium flex items-center">
                <Mail className="mr-2 h-5 w-5" />
                Contact Information
              </h3>
              <Separator className="my-3" />
              
              <div className="space-y-4">
                {/* Email Verification */}
                <div className="bg-muted/30 p-3 rounded-md">
                  <ProfileVerification
                    type="email"
                    value={profile.email || ''}
                    isVerified={!!profile.emailVerified}
                    onSendVerification={handleSendEmailVerification}
                    onVerify={handleVerifyEmail}
                    isSending={sendEmailVerification.isPending}
                    isVerifying={verifyEmail.isPending}
                  />
                </div>
                
                {/* Mobile Verification */}
                <div className="bg-muted/30 p-3 rounded-md">
                  <div className="grid grid-cols-1 sm:grid-cols-[1fr,auto] gap-2 mb-2">
                    <FormField
                      control={form.control}
                      name="mobile"
                      render={({ field }) => (
                        <FormItem className="mb-0">
                          <FormLabel>Mobile Number</FormLabel>
                          <FormControl>
                            <Input placeholder="+****************" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <ProfileVerification
                    type="mobile"
                    value={form.watch('mobile') || ''}
                    isVerified={!!profile.mobileVerified}
                    onSendVerification={handleSendMobileVerification}
                    onVerify={handleVerifyMobile}
                    isSending={sendMobileVerification.isPending}
                    isVerifying={verifyMobile.isPending}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="location"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center">
                          <MapPin className="h-4 w-4 mr-1" />
                          Location
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="City, Country" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="website"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center">
                          <Globe className="h-4 w-4 mr-1" />
                          Website
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="https://yourwebsite.com" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>
            
            {/* Professional Information Section */}
            <div>
              <h3 className="text-lg font-medium flex items-center">
                <Briefcase className="mr-2 h-5 w-5" />
                Professional Information
              </h3>
              <Separator className="my-3" />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="company"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Company/Organization</FormLabel>
                      <FormControl>
                        <Input placeholder="Your company" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="jobTitle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Job Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Your role" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="industry"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Industry</FormLabel>
                      <FormControl>
                        <Input placeholder="Your industry" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            
            {/* Social Links Section */}
            <div>
              <h3 className="text-lg font-medium flex items-center">
                <LinkIcon className="mr-2 h-5 w-5" />
                Social Links
              </h3>
              <Separator className="my-3" />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Twitter */}
                <FormField
                  control={form.control}
                  name="socialLinks.twitter"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Twitter</FormLabel>
                      <FormControl>
                        <Input placeholder="https://twitter.com/username" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                {/* LinkedIn */}
                <FormField
                  control={form.control}
                  name="socialLinks.linkedin"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>LinkedIn</FormLabel>
                      <FormControl>
                        <Input placeholder="https://linkedin.com/in/username" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                {/* GitHub */}
                <FormField
                  control={form.control}
                  name="socialLinks.github"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>GitHub</FormLabel>
                      <FormControl>
                        <Input placeholder="https://github.com/username" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                {/* Instagram */}
                <FormField
                  control={form.control}
                  name="socialLinks.instagram"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Instagram</FormLabel>
                      <FormControl>
                        <Input placeholder="https://instagram.com/username" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            
            {/* Skills Section */}
            <div>
              <h3 className="text-lg font-medium flex items-center">
                <BadgePlus className="mr-2 h-5 w-5" />
                Skills
              </h3>
              <Separator className="my-3" />
              
              <div className="grid grid-cols-1 gap-4">
                {/* TODO: Implement skills input with tags */}
                <p className="text-sm text-muted-foreground">
                  Skills management will be implemented soon.
                </p>
              </div>
            </div>
            
            {/* Form Actions */}
            <CardFooter className="flex justify-end space-x-2 px-0">
              <Button 
                type="button" 
                variant="outline" 
                onClick={onCancel}
              >
                Cancel
              </Button>
              <Button 
                type="submit"
                disabled={updateProfile.isPending}
              >
                {updateProfile.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
