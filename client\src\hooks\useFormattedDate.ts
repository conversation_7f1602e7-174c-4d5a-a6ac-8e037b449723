import { DateTime } from 'luxon';

/**
 * Formats the event date based on the event's timezone and also shows the user's local time.
 * @param isoDate - The ISO 8601 date string.
 * @param timezone - The event's timezone identifier.
 * @returns An object containing both formatted event time and user's local time.
 */
export const useFormattedDate = (isoDate: string, timezone: string) => {
  const eventTime = DateTime.fromISO(isoDate, { zone: 'utc' }).setZone(timezone);
  const localTime = eventTime.setZone(DateTime.local().zoneName);
  
  return {
    eventTime: eventTime.toFormat('yyyy-MM-dd HH:mm:ss ZZZZ'),
    localTime: localTime.toFormat('yyyy-MM-dd HH:mm:ss ZZZZ')
  };
};


/**
 * pnpm add luxon
pnpm add -D @types/luxon
 */