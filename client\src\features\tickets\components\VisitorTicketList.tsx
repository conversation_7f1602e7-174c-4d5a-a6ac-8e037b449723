// Component for displaying a list of visitor tickets

import { useState } from 'react';
import { useVisitorTickets } from '../hooks/useVisitorTickets';
import { VisitorTicketCard } from './VisitorTicketCard';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { DateRange } from "react-day-picker";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, ChevronRight, CalendarIcon, SearchIcon, Ticket, TicketX } from "lucide-react";
import { format } from "date-fns";

export function VisitorTicketList() {
  const {
    tickets,
    pagination,
    isLoading,
    isError,
    error,
    refetch,
    goToPage,
    updateFilterOptions,
    requestTicketDownload,
    filterOptions,
    isDownloading
  } = useVisitorTickets();

  const [searchTerm, setSearchTerm] = useState("");
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: undefined,
    to: undefined,
  });

  // Filter tickets locally based on search term
  const filteredTickets = tickets.filter(ticket => 
    ticket.eventName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    ticket.eventVenue.toLowerCase().includes(searchTerm.toLowerCase()) ||
    ticket.eventCity.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Apply date filter
  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range);
    if (range?.from && range?.to) {
      updateFilterOptions({
        dateRange: {
          start: range.from,
          end: range.to,
        },
      });
    }
  };

  // Handle sort change
  const handleSortChange = (value: string) => {
    updateFilterOptions({
      sortBy: value as 'date' | 'eventName' | 'price',
    });
  };

  // Handle sort order change
  const toggleSortOrder = () => {
    updateFilterOptions({
      sortOrder: filterOptions.sortOrder === 'asc' ? 'desc' : 'asc',
    });
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm("");
    setDateRange(undefined);
    updateFilterOptions({
      sortBy: 'date',
      sortOrder: 'desc',
      dateRange: undefined,
    });
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
          <Skeleton className="h-10 w-full md:w-1/3" />
          <div className="flex gap-2 w-full md:w-auto">
            <Skeleton className="h-10 w-32" />
            <Skeleton className="h-10 w-32" />
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[1, 2, 3, 4, 5, 6].map(i => (
            <Skeleton key={i} className="h-64 w-full" />
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (isError) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-red-600">
            Error Loading Tickets
          </CardTitle>
          <CardDescription>
            We encountered a problem retrieving your tickets.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-red-50 p-4 rounded-md text-center">
            <p className="text-red-800 mb-4">
              {error?.message || "Failed to load your tickets. Please try again later."}
            </p>
            <Button onClick={() => refetch()} variant="outline">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Empty state
  if (tickets.length === 0) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="text-xl font-bold flex items-center">
            <Ticket className="mr-2 h-5 w-5" /> My Tickets
          </CardTitle>
          <CardDescription>
            View your purchased event tickets
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-50 p-8 rounded-lg text-center">
            <TicketX className="h-16 w-16 mx-auto mb-4 text-gray-400" />
            <h3 className="font-semibold text-lg mb-2">No Tickets Found</h3>
            <p className="text-gray-600 mb-4">
              You haven&apos;t purchased any tickets yet.
            </p>
            <p className="text-sm text-gray-500">
              Browse our events and book your tickets to see them here.
            </p>
            <Button className="mt-4" onClick={() => window.location.href = '/events'}>
              Browse Events
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Display tickets
  return (
    <div className="space-y-6">
      {/* Filters and search */}
      <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
        <div className="relative w-full md:w-auto flex-1">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search tickets by event, venue, or city..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="pl-9 w-full"
          />
        </div>
        
        <div className="flex flex-wrap justify-end gap-2 w-full md:w-auto">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="justify-start text-left font-normal"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange?.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, "LLL dd, y")} -{" "}
                      {format(dateRange.to, "LLL dd, y")}
                    </>
                  ) : (
                    format(dateRange.from, "LLL dd, y")
                  )
                ) : (
                  <span>Filter by date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange?.from}
                selected={dateRange}
                onSelect={handleDateRangeChange}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>

          <Select
            defaultValue={filterOptions.sortBy}
            onValueChange={handleSortChange}
          >
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="date">Event Date</SelectItem>
              <SelectItem value="eventName">Event Name</SelectItem>
              <SelectItem value="price">Price</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="ghost"
            size="icon"
            onClick={toggleSortOrder}
            title={filterOptions.sortOrder === 'asc' ? "Sort Descending" : "Sort Ascending"}
          >
            {filterOptions.sortOrder === 'asc' ? "↑" : "↓"}
          </Button>

          <Button 
            variant="ghost"
            onClick={clearFilters}
            className="text-sm"
            disabled={!searchTerm && !dateRange?.from && filterOptions.sortBy === 'date' && filterOptions.sortOrder === 'desc'}
          >
            Clear
          </Button>
        </div>
      </div>

      {/* Active filters */}
      {(searchTerm || dateRange?.from) && (
        <div className="flex flex-wrap gap-2 mb-4">
          {searchTerm && (
            <Badge variant="secondary" className="text-xs">
              Search: {searchTerm}
            </Badge>
          )}
          {dateRange?.from && dateRange.to && (
            <Badge variant="secondary" className="text-xs">
              Date: {format(dateRange.from, "MMM d, yyyy")} - {format(dateRange.to, "MMM d, yyyy")}
            </Badge>
          )}
        </div>
      )}

      {/* Tickets grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTickets.map(ticket => (
          <VisitorTicketCard
            key={ticket.checkoutSessionId}
            ticket={ticket}
            onDownloadRequest={requestTicketDownload}
            isDownloading={isDownloading}
          />
        ))}
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2 mt-8">
          <Button
            variant="outline"
            size="sm"
            onClick={() => goToPage(pagination.currentPage - 1)}
            disabled={!pagination.hasPrevPage}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <span className="text-sm">
            Page {pagination.currentPage} of {pagination.totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => goToPage(pagination.currentPage + 1)}
            disabled={!pagination.hasNextPage}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  );
}