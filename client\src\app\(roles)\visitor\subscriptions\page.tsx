/**
 * Visitor Subscription Page
 * 
 * Displays subscription plans and allows visitors to subscribe.
 */
'use client';

import { useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { ArrowRight, Check, X, Info } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { LoadingSpinner } from '@/components/shared/LoadingSpinner';

import { useSubscription } from '@/features/subscriptions/hooks/useSubscription';
import { SubscriptionPlanType } from '@/features/subscriptions/types/subscription.types';
import { BenefitsList } from '@/features/subscriptions/components/BenefitsList'; // Assuming you created this

// Define the subscription plans available to visitors
// You might fetch this from a config or backend in a real app
const visitorPlans = [
  {
    id: SubscriptionPlanType.MONTHLY,
    name: 'Monthly Subscriber',
    description: 'Get priority queue access and discounts.',
    price: 9.99,
    currency: 'USD',
    interval: 'month',
    benefits: [
      { title: 'Priority Queue Access', included: true, description: "Jump ahead in most event queues." },
      { title: '10% Discount on Service Fees', included: true },
      { title: 'Early Access Notifications', included: true },
      { title: 'Exclusive VIP Event Access', included: false },
      { title: 'Highest Queue Priority', included: false },
    ],
  },
  {
    id: SubscriptionPlanType.VIP,
    name: 'VIP Member',
    description: 'Ultimate access with exclusive benefits.',
    price: 29.99, // Example price
    currency: 'USD',
    interval: 'month', // Assuming VIP is also monthly for simplicity here
    benefits: [
      { title: 'Highest Priority Queue Access', included: true, description: "Top of the list for all events." },
      { title: '25% Discount on Service Fees', included: true },
      { title: 'Early Access to ALL Events', included: true },
      { title: 'Exclusive VIP Event Access & Pre-sales', included: true },
      { title: 'Dedicated Support Channel', included: true },
    ],
    featured: true,
  },
];

export default function VisitorSubscriptionPage() {
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlanType | null>(null);
  const searchParams = useSearchParams();
  const canceled = searchParams.get('canceled') === 'true';

  const {
    isSubscribed,
    currentPlan,
    subscribeToPlan,
    isCreatingCheckout,
    isLoading: isLoadingStatus // Loading status for checking current subscription
  } = useSubscription();

  const handleSubscribe = (planType: SubscriptionPlanType) => {
    setSelectedPlan(planType); // Keep track of which button is loading
    subscribeToPlan(planType);
  };

  // Determine the appropriate success/cancel URLs for Stripe Checkout
  // These should point back to a page confirming the action, maybe the profile/membership page.
  const successUrl = `${window.location.origin}/visitor/profile?subscription_status=success`; // Redirect to profile on success
  const cancelUrl = `${window.location.origin}/visitor/subscriptions?canceled=true`; // Return here if user cancels checkout

  return (
    <div className="container max-w-6xl py-10">
      {/* Page Header */}
      <div className="text-center mb-10">
        <h1 className="text-4xl font-bold mb-4">Unlock Premium Access</h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          Choose a plan to get priority access, discounts, and exclusive benefits on Fanseatmaster.
        </p>
      </div>

      {/* Show alert if user canceled the Stripe Checkout flow */}
      {canceled && (
        <Alert variant="destructive" className="mb-8 max-w-2xl mx-auto">
          <Info className="h-4 w-4" />
          <AlertTitle>Subscription Canceled</AlertTitle>
          <AlertDescription>
            You canceled the subscription process. Feel free to choose a plan when you&apos;re ready.
          </AlertDescription>
        </Alert>
      )}

      {/* Show notice if already subscribed */}
      {isSubscribed && currentPlan && (
        <Alert variant="default" className="mb-8 max-w-2xl mx-auto">
          <Check className="h-4 w-4" />
          <AlertTitle>You&apos;re already a {currentPlan === SubscriptionPlanType.VIP ? 'VIP Member' : 'Subscriber'}!</AlertTitle>
          <AlertDescription>
            You can manage your current subscription in your profile settings.
          </AlertDescription>
        </Alert>
      )}

      {/* Loading state for checking subscription status */}
      {isLoadingStatus && (
         <div className="flex justify-center items-center my-10">
           <LoadingSpinner /> <span className="ml-2">Loading membership status...</span>
         </div>
      )}

      {/* Pricing cards - only show if not loading status */}
      {!isLoadingStatus && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
          {visitorPlans.map((plan) => (
            <Card
              key={plan.id}
              className={`flex flex-col ${plan.featured ? 'border-primary shadow-lg ring-1 ring-primary' : ''}`}
            >
              {plan.featured && (
                <div className="bg-primary text-primary-foreground text-center py-1.5 text-xs font-semibold tracking-wide uppercase">
                  Best Value
                </div>
              )}
              <CardHeader className="pb-4">
                <CardTitle className="text-2xl">{plan.name}</CardTitle>
                <CardDescription>{plan.description}</CardDescription>
                <div className="mt-3">
                  <span className="text-4xl font-bold tracking-tight">
                    ${plan.price.toFixed(2)}
                  </span>
                  <span className="text-muted-foreground ml-1">
                    /month
                  </span>
                </div>
              </CardHeader>
              <CardContent className="flex-grow">
                <h4 className="font-semibold mb-3 text-sm uppercase tracking-wider text-muted-foreground">What&apos;s Included:</h4>
                <BenefitsList benefits={plan.benefits} />
              </CardContent>
              <CardFooter>
                <Button
                  className="w-full"
                  size="lg"
                  onClick={() => handleSubscribe(plan.id)}
                  disabled={isCreatingCheckout || (isSubscribed && currentPlan === plan.id)}
                  variant={plan.featured ? "default" : "secondary"}
                >
                  {isSubscribed && currentPlan === plan.id
                    ? 'Current Plan'
                    : isCreatingCheckout && selectedPlan === plan.id
                      ? <><LoadingSpinner /> Processing...</>
                      : `Get ${plan.name}`
                  }
                   {!isSubscribed && <ArrowRight className="ml-2 h-5 w-5" />}
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}