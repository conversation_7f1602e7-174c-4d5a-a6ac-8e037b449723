// Component for displaying a section of events using a carousel
import { useState, useCallback } from "react"; // Added useCallback
import { EventCarouselShadcn } from "@/components/custom-components/EventComponents/EventCarouselShadcn";
import { HomeEventsSectionProps } from "../../types/home.types";
import { EventModal } from "@/features/unified-events/components/event-details/EventModal";
import { UnifiedEvent } from "@/features/unified-events/adapters/eventAdapter";
import { PriorityEventData } from "@/features/settings/components/PriorityEvents/types/priority-events.types";

export const EventsSection = ({
  title,
  events,
  isLoading,
}: HomeEventsSectionProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<UnifiedEvent | null>(null);

  const handleEventClick = useCallback(
    (event: PriorityEventData | null | undefined) => {
      console.log(
        "🕵️ [EventsSection] handleEventClick triggered. Received event:",
        event
      );

      // 🆕 ADD THIS DEBUG BLOCK
      console.log("🗺️ [DEBUG] Priority Event Seatmap Data Check:");
      console.log("- Event Source:", event?.source);
      console.log("- Raw Event Data:", event?.rawEventData);
      console.log("- Raw Event Data Type:", typeof event?.rawEventData);

      // Check for seatmap in different locations
      if (event?.rawEventData) {
        console.log(
          "- Seatmap in rawEventData:",
          (event.rawEventData as any)?.seatmap
        );
        console.log(
          "- Images in rawEventData:",
          (event.rawEventData as any)?.images
        );
        console.log(
          "- Full rawEventData structure:",
          Object.keys(event.rawEventData)
        );
      }

      if (!event || !event.id) {
        console.error(
          "🚨 [EventsSection] Invalid or missing event data received in handleEventClick. Aborting.",
          event
        );
        return;
      }

      let unifiedEvent: UnifiedEvent | null = null;

      try {
        // Extract the correct ManagerEvent ID for checkout
        // For PriorityEvents, the ManagerEvent ID is in rawEventData.id
        const managerEventId = event.rawEventData?.id || event.id;
        console.log(
          `🔑 [EventsSection] Using managerEventId for checkout: ${managerEventId}`
        );

        unifiedEvent = {
          id: managerEventId, // Use the ManagerEvent ID here for checkout
          name: event.name || "Unnamed Event",
          date: new Date(event.date), // Ensure date is a Date object
          venue: event.venue || "Unknown Venue",
          city: event.city || "Unknown City",
          country: event.country || "Unknown Country",
          imageUrl: event.image || null,
          genre: event.genre || null,
          segment: event.category || event.segment || null,
          subGenre: event.subGenre || null,
          source: event.source === "ticketmaster" ? "ticketmaster" : "manager",
          originalEvent: {
            inventory:
              event.source === "manager" && event.rawEventData?.inventory
                ? event.rawEventData.inventory
                : event.inventory || [],
            rawEventData: {
              ...event,
              seatmapUrl: event.seatmapUrl || null, // 🆕 ADD: Include seatmapUrl from database
            },
            url: event.url || event.ticketUrl || undefined,
          },
        };

        // 🆕 ADD THIS DEBUG BLOCK
        console.log("✨ [DEBUG] UnifiedEvent Conversion Complete:");
        console.log("- UnifiedEvent Source:", unifiedEvent.source);
        console.log(
          "- UnifiedEvent originalEvent:",
          unifiedEvent.originalEvent
        );
        console.log(
          "- UnifiedEvent originalEvent.rawEventData:",
          unifiedEvent.originalEvent.rawEventData
        );

        // Check what seatmap data looks like in the converted event
        if (unifiedEvent.source === "ticketmaster") {
          console.log(
            "- TM Seatmap Check:",
            (unifiedEvent.originalEvent.rawEventData as any)?.rawEventData
              ?.seatmap
          );
        } else {
          console.log(
            "- Manager Seatmap Check:",
            (unifiedEvent.originalEvent.rawEventData as any)?.rawEventData
              ?.seatmap
          );
        }

        console.log(
          "✨ [EventsSection] Successfully Converted to UnifiedEvent:",
          unifiedEvent
        );
      } catch (error) {
        console.error(
          "🚨 [EventsSection] Error during UnifiedEvent conversion:",
          error,
          "Original event data:",
          event
        );
        return;
      }

      if (unifiedEvent) {
        setSelectedEvent(unifiedEvent);
        setIsModalOpen(true);
      } else {
        console.error(
          "🚨 [EventsSection] UnifiedEvent is null after conversion attempt. Modal will not open."
        );
      }
    },
    []
  );

  const handleCloseModal = useCallback(() => {
    setIsModalOpen(false);
    setSelectedEvent(null);
  }, []);

  // Filter events before passing to EventCarouselShadcn
  const validEvents = Array.isArray(events)
    ? events.filter((event) => event && event.id)
    : [];
  if (Array.isArray(events) && validEvents.length !== events.length) {
    console.warn(
      "🚨 [EventsSection] Some events were filtered out due to missing id or being falsy:",
      {
        title: title,
        originalCount: events.length,
        validCount: validEvents.length,
      }
    );
  }

  return (
    <section className="py-8">
      <EventCarouselShadcn
        title={title}
        showMultiple={true}
        events={validEvents} // Pass only valid events
        autoplay={true}
        isLoading={isLoading}
        onEventClick={handleEventClick}
      />

      {/* Event Modal */}
      {isModalOpen && selectedEvent && (
        <EventModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          event={selectedEvent}
        />
      )}
    </section>
  );
};
