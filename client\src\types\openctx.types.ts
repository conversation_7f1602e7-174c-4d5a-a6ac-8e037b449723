// Define the structure for context data
export interface Context {
  type: string; // Type of data context (e.g., event, user)
  text: string; // Text content for the data
  metadata: {
    source: string; // Source of the context data
    id?: string; // Optional unique identifier
    name?: string; // Optional name
    venue?: string; // Optional venue information
    date?: string; // Optional date
    image?: string; // Optional image URL or reference
    address?: string; // Optional address information
    [key: string]: any; // Allow additional properties
  };
}

// API Response type
export interface ApiResponse<T> {
  success: boolean; // Indicates if the API call was successful
  message: string; // Message describing the result of the API call
  data: T[]; // Array of data returned by the API
}



/*
OpenCtx is Server-Centric: openctx.types.ts 
defines types for the server-side OpenCtx logic and implementation using MCP which is in a different domain.

Tools are Client-Centric Abstraction: tools.types.ts 
defines type definitions to use the API safely, so that client developers have a clear understanding of what to expect when they make a specific API call. The client is not aware of what OpenCTX or the providers, and it only cares about the result from the API, which it expects to conform with the OpenCTXRequest type.

*/