// Import necessary types and modules
import {
  TmEvent,
  TmApiResponse,
  PaginatedTmEventResponse,
  TmEventQueryParams,
} from "../types/tm.types";
import { TmEventTransformer } from "../utils/tm.transformer";
import { TmCache } from "../cache/tm.cache";
import axios from "axios";
import { PrismaClient, Prisma } from "@prisma/client";
import { NODE_ENV } from "@/constants";
import { TmCacheTransformer } from "../utils/tm.cache.transformer";

const prisma = new PrismaClient();
const ticketMasterApiKey = process.env.TICKETMASTER_API_KEY;
const tmApiUrl = "https://app.ticketmaster.com/discovery/v2/events.json";

//todo: //! home page events only removed when settings page triggered and priorty events status changed
//! home page events modified automatically
export class TmEventService {
  static async fetchAndCacheEvents(
    queryParams: TmEventQueryParams
  ): Promise<PaginatedTmEventResponse> {
    const {
      page = "1",
      size = "10",
      keyword = "",
      city = "",
      startDate = "",
      segment = "",
    } = queryParams;

    //Build cache key
    const cacheKey = `${keyword}:${city}:${startDate}:${segment}`;
    // Try to get data from cache
    const cachedEvents = await TmCache.get(cacheKey, page, size);
    if (cachedEvents) {
      return {
        events: cachedEvents,
        pagination: {
          page: Number(page),
          size: Number(size),
          total: 0, // Total is not cached, so needs to be fetched from DB if needed
          totalPages: 0, // Total pages needs to be fetched from DB if needed
        },
      };
    }
    // Fetch data from Ticketmaster API or from DB
    const { events, pagination } = await this.fetchEventsFromDatabaseOrApi({
      ...queryParams,
    });

    // Cache the fetched data
    await TmCache.set(cacheKey, page, size, events, 300); // Cache for 5 minutes (300 seconds)

    return {
      events,
      pagination,
    };
  }

  private static async fetchEventsFromDatabaseOrApi(
    queryParams: TmEventQueryParams
  ): Promise<PaginatedTmEventResponse> {
    const {
      page = "1",
      size = "10",
      keyword = "",
      city = "",
      startDate = "",
      segment = "",
    } = queryParams;

    const pageNumber = Number(page);
    const pageSize = Number(size);
    const skip = pageNumber * pageSize;

    // Define the filters
    // const whereClause: Prisma.TmEventWhereInput = {
    //   AND: [
    //     keyword
    //       ? {
    //           name: {
    //             contains: keyword,
    //             mode: "insensitive" as Prisma.QueryMode,
    //           },
    //         }
    //       : {},
    //     city
    //       ? {
    //           venueCity: {
    //             contains: city,
    //             mode: "insensitive" as Prisma.QueryMode,
    //           },
    //         }
    //       : {},
    //     startDate ? { startDateTime: { gte: new Date(startDate) } } : {},
    //     genre
    //       ? {
    //           genre: {
    //             contains: genre,
    //             mode: "insensitive" as Prisma.QueryMode,
    //           },
    //         }
    //       : {},

    //     // Add these filters for future events at least 24h in the future
    //     { startDateTime: { gte: new Date() } }, // Only future events
    //     { startDateTime: { gte: new Date(Date.now() + 24 * 60 * 60 * 1000) } }, // At least 24h in future
    //   ],
    // };
    const whereClause: Prisma.TmEventWhereInput = {
      AND: [
        keyword ? { name: { contains: keyword, mode: 'insensitive' as Prisma.QueryMode } } : {},
        city ? { venueCity: { contains: city, mode: 'insensitive' as Prisma.QueryMode } } : {},
        startDate ? { startDateTime: { gte: new Date(startDate) } } : {},
        segment ? { segment: { contains: segment, mode: 'insensitive' as Prisma.QueryMode } } : {},        
        // Add these filters for future events at least 24h in the future
        { startDateTime: { gte: new Date() } }, // Only future events
        { startDateTime: { gte: new Date(Date.now() + 24 * 60 * 60 * 1000) } }, // At least 24h in future
      ]
    };

    // Attempt to retrieve the filtered events from the DB
    const [total, dbEvents] = await Promise.all([
      prisma.tmEvent.count({ where: whereClause }),
      prisma.tmEvent.findMany({
        where: whereClause,
        skip,
        take: pageSize,
        orderBy: {
          // geting events in ascending order of startDateTime
          startDateTime: "asc",
        },
      }),
    ]);

    const totalPages = Math.ceil(total / pageSize);

    if (dbEvents.length > 0) {
      return {
        events: dbEvents,
        pagination: {
          page: pageNumber,
          size: pageSize,
          total,
          totalPages,
        },
      };
    }

    // If no data is available in the database, fetch from the TM API
    return await this.fetchEventsFromTicketmasterApi(queryParams);
  }

  private static async fetchEventsFromTicketmasterApi(
    queryParams: TmEventQueryParams
  ): Promise<PaginatedTmEventResponse> {
    const {
      page = "1",
      size = "10",
      keyword = "",
      city = "",
      startDate = "",
      segment = "",
    } = queryParams;
    // *
    const url = `${tmApiUrl}?apikey=${ticketMasterApiKey}&locale=*&page=${page}&size=${size}&keyword=${keyword}&city=${city}&startDateTime=${startDate}&segment=${segment}`;

    try {
      const response = await axios.get<TmApiResponse>(url);

      // Transform and prepare events for database insertion
      const transformedEvents: Prisma.TmEventCreateManyInput[] =
        TmEventTransformer.transformBatch(response.data._embedded.events).map(
          (event) => ({
            ...event,
            createdAt: new Date(),
            updatedAt: new Date(),
          })
        ) as Prisma.TmEventCreateManyInput[];

      // Transform events for cache
      const transformedEventsForCache =
        TmCacheTransformer.transformBatchForCache(
          response.data._embedded.events
        );

      // Filter events by date - only keep future events at least 24h away
      const now = new Date();
      const future24h = new Date(now.getTime() + 24 * 60 * 60 * 1000);

      const validEvents = transformedEvents.filter((event) => {
        const eventDate = event.startDateTime as Date;
        return eventDate && eventDate >= future24h;
      });

      // Only cache/store valid events
      await prisma.tmEvent.createMany({
        data: validEvents,
        skipDuplicates: true,
      });
      // Also filter cache transformer events
      const validEventsForCache = transformedEventsForCache.filter((event) => {
        const eventDate = event.startDateTime as Date;
        return eventDate && eventDate >= future24h;
      });

      return {
        events: validEventsForCache as TmEvent[],
        pagination: {
          page: response.data.page.number,
          size: response.data.page.size,
          total: response.data.page.totalElements,
          // totalPages: response.data.page.totalPages,
          totalPages: Math.ceil(validEventsForCache.length / Number(size)),
        },
      };
    } catch (error: any) {
      console.error(
        "Error fetching events from Ticketmaster API:",
        error.message
      );
      throw error; // Propagate error to the controller
    }
  }

  static async invalidateCache(
    query: string,
    page: string,
    size: string
  ): Promise<void> {
    await TmCache.invalidate(query, page, size);
  }
}
