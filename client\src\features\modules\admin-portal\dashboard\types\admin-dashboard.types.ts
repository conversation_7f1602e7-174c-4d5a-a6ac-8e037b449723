export type AdminMetricType = {
  title: string;
  value: number | string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  category: 'revenue' | 'users' | 'events' | 'transactions';
};

export type UserActivityType = {
  id: string;
  userType: 'visitor' | 'manager';
  userName: string;
  action: string;
  timestamp: string;
  details: string;
};

export type ManagerPerformanceType = {
  managerId: string;
  name: string;
  totalEvents: number;
  totalSales: number;
  activeEvents: number;
  rating: number;
};

export type TransactionType = {
  id: string;
  eventName: string;
  managerId: string;
  visitorId: string;
  amount: number;
  status: 'completed' | 'pending' | 'failed';
  timestamp: string;
};
