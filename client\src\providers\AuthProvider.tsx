"use client";

import { SessionProvider as NextAuthSessionProvider } from "next-auth/react";
import { ReactNode } from "react";

interface SessionProviderProps {
  children: ReactNode;
}

const AuthProvider = ({ children }: SessionProviderProps) => {
  return (
    <NextAuthSessionProvider 
      basePath="/api/authlogic/auth"
    >
      {children}
    </NextAuthSessionProvider>
  );
};

export default AuthProvider;
