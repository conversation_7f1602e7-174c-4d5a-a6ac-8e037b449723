"use client"; // Enable Client Components

import React from 'react';
import { EventListingProvider } from './EventListingContext';
import { EventsStep } from './Steps/EventsStep';
import { AddInventoryStep } from './Steps/AddInventory/AddInventoryStep'; // Import AddInventoryStep
import { PublishStep } from './Steps/PublishStep';
import { useEventListing } from './EventListingContext';
import { EventListingStep } from './EventListingStep';
import { StepIndicator } from './StepIndicator'; // Import StepIndicator
import { Preview } from './Preview'; // Import the Preview component
// import { useFormStatus } from 'react-dom'; // for disable logic


// Main component for the Event Listing Form
const EventListingForm: React.FC = () => {
  return (
    <EventListingProvider>
      <FormContent />
    </EventListingProvider>
  );
};

// FormContent component for rendering the event listing form steps and navigation
const FormContent: React.FC = () => {
  const { currentStep, goToNextStep, goToPreviousStep, totalSteps, eventListingData } = useEventListing();
    const isAddInventoryStep = currentStep === 2;
  let isInventoryStepValid = true; // Default to true for other steps

  if (isAddInventoryStep) {
      isInventoryStepValid = eventListingData.inventory.length > 0;
  }


  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {/* Main Form Content (Steps and Navigation) */}
      <div className="md:col-span-2">
        <StepIndicator />
        <EventListingStep step={currentStep} title={
          currentStep === 1 ? "Select Event" :
          currentStep === 2 ? "Add Inventory" :
          "Publish"
        }>
          {currentStep === 1 && <EventsStep />}
          {currentStep === 2 && <AddInventoryStep />}
          {currentStep === 3 && <PublishStep />}
        </EventListingStep>

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-4">
          <button
            onClick={goToPreviousStep}
            disabled={currentStep === 1}
            className="px-4 py-2 bg-gray-300 rounded disabled:opacity-50"
          >
            Previous
          </button>
           <button
            onClick={goToNextStep}
            disabled={currentStep === totalSteps || (isAddInventoryStep && !isInventoryStepValid)}
            className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>
      </div>

      {/* Preview Section */}
      <div className="md:col-span-1">
        <Preview />
      </div>
    </div>
  );
};

export default EventListingForm;

