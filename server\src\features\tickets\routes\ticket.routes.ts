// server/src/features/tickets/routes/ticket.routes.ts
// Defines API routes for visitor ticket operations

import { Router } from "express";
import { TicketController } from "../controllers/ticket.controller";
import { authMiddleware } from "@/middleware/auth.middleware"; // Assuming this middleware attaches user info

const router = Router();

// --- Visitor Ticket Routes ---

// GET /api/v1/tickets/my-tickets - Fetch user's purchased tickets
router.get(
    "/my-tickets",
    authMiddleware, // Ensure user is logged in
    TicketController.getMyTickets
);

// GET /api/v1/tickets/:sessionId/download-info - Get info for downloading a ticket
router.get(
    "/:sessionId/download-info",
    authMiddleware, // Ensure user is logged in
    TicketController.getTicketDownloadInfo
);

// Future: Maybe a POST endpoint if PDF generation is triggered on demand
// router.post("/:sessionId/generate-pdf", authMiddleware, TicketController.generateTicketPdf);


export const ticketRoutes = router;