/**
 * Custom API Error class that extends the built-in Error class
 * Provides standardized error handling for HTTP responses with status codes
 * Includes utility methods for common HTTP error scenarios
 * Properties include status code, error details, success flag, and response data
 */
class ApiError extends Error {
    statusCode: number;
    errors: any[];
    success: boolean;
    data: null;
  
    //---------------Constructor Implementation-------------------
    constructor(
      statusCode: number,
      message: string = 'Something went wrong',
      errors: any[] = [],
      stack: string = ''
    ) {
      super(message);
      this.statusCode = statusCode;
      this.errors = errors;
      this.success = false;
      this.data = null;
  
      // Handle stack trace - either use provided stack or capture new one
      if (stack) {
        this.stack = stack;
      } else {
        Error.captureStackTrace(this, this.constructor);
      }
    }
  
    //---------------HTTP 4xx Client Error Methods-------------------
    /**
     * Creates a 400 Bad Request error
     * Used when the request is malformed or contains invalid parameters
     */
    static badRequest(message: string, errors: any[] = []) {
      return new ApiError(400, message, errors);
    }
  
    /**
     * Creates a 401 Unauthorized error
     * Used when authentication is required but has failed or not been provided
     */
    static unauthorized(message: string = 'Unauthorized access') {
      return new ApiError(401, message);
    }
  
    /**
     * Creates a 403 Forbidden error
     * Used when the client doesn't have permission to access the requested resource
     */
    static forbidden(message: string = 'Forbidden access') {
      return new ApiError(403, message);
    }
  
    /**
     * Creates a 404 Not Found error
     * Used when the requested resource doesn't exist
     */
    static notFound(message: string = 'Resource not found') {
      return new ApiError(404, message);
    }
  
    /**
     * Creates a 409 Conflict error
     * Used when the request conflicts with current state of the server
     */
    static conflict(message: string = 'Conflict') {
      return new ApiError(409, message);
    }
  
    /**
     * Creates a 429 Too Many Requests error
     * Used for rate limiting scenarios
     */
    static tooMany(message: string = 'Too many requests') {
      return new ApiError(429, message);
    }
  
    //---------------HTTP 5xx Server Error Methods-------------------
    /**
     * Creates a 500 Internal Server Error
     * Used when an unexpected condition was encountered on the server
     */
    static internal(message: string = 'Internal server error') {
      return new ApiError(500, message);
    }
  }
  
  export default ApiError;