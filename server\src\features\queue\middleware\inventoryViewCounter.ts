/**
 * Middleware to track inventory views and update demand counters
 * This should be attached to routes that display event inventory
 */
import { Request, Response, NextFunction } from 'express';
import { DemandDetectionService } from '../services/demandDetection.service';
import { asyncHandler } from '@/utils/asyncHandler';

const demandDetectionService = new DemandDetectionService();

export const inventoryViewCounter = asyncHandler(
  async (req: Request, res: Response, next: NextFunction) => {
    // Extract eventId from route parameters or query string
    const eventId = req.params.eventId || req.query.eventId as string;
    
    if (eventId) {
      try {
        // Increment the view counter - this is async but we don't need to wait
        demandDetectionService.incrementViewCounter(eventId)
          .then(() => {
            // Optional: Check if queue should be activated (also async)
            return demandDetectionService.checkAndTriggerQueue(eventId);
          })
          .catch(err => {
            // Log error but don't block the request
            console.error(`❌ [inventoryViewCounter] Error incrementing view counter:`, err);
          });
          
        // Log in development mode
        if (process.env.NODE_ENV === 'development') {
          console.log(`👁️ [inventoryViewCounter] View recorded for event ${eventId}`);
        }
      } catch (error) {
        // Don't block the request if counter fails
        console.error(`❌ [inventoryViewCounter] Error in middleware:`, error);
      }
    }
    
    // Always continue to the next middleware/controller
    next();
  }
);
