"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogTrigger,
  DialogTitle,
} from "@/components/ui/dialog";
import Image from "next/image";

interface SeatmapDisplayProps {
  seatmapUrl: string | null | undefined;
}

export const SeatmapDisplay: React.FC<SeatmapDisplayProps> = ({ seatmapUrl }) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  if (!seatmapUrl) {
    return (
      <div className="text-gray-500 text-center py-4">
        Seatmap not available for this event.
      </div>
    );
  }

  const handleImageError = () => {
    console.log('❌ Seatmap image failed to load:', seatmapUrl);
    setImageError(true);
    setIsLoading(false);
  };

  const handleImageLoad = () => {
    setIsLoading(false);
    setImageError(false);
  };

  // Fallback UI for failed images
  const FallbackUI = () => (
    <div className="w-full aspect-[16/9] bg-gray-50 flex items-center justify-center rounded-lg border-2 border-dashed border-gray-300">
      <div className="text-center">
        <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
        <p className="mt-2 text-sm text-gray-500">Seatmap temporarily unavailable</p>
        <p className="text-xs text-gray-400">Please try again later</p>
      </div>
    </div>
  );

  if (imageError) {
    return <FallbackUI />;
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <div className="relative w-full cursor-pointer group">
          <div className="relative w-full aspect-[16/9] rounded-lg overflow-hidden">
            {isLoading && (
              <div className="absolute inset-0 bg-gray-100 animate-pulse flex items-center justify-center z-10">
                <div className="text-center">
                  <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  <p className="mt-2 text-sm text-gray-500">Loading seatmap...</p>
                </div>
              </div>
            )}
            
            <Image
              src={seatmapUrl}
              alt="Event Seatmap Thumbnail"
              fill
              unoptimized={true} // 🔑 This bypasses Vercel's image optimization
              className="object-cover transition-transform duration-300 group-hover:scale-105"
              quality={75}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              priority
              onLoad={handleImageLoad}
              onError={handleImageError}
            />
            
            {!isLoading && !imageError && (
              <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                <span className="text-white font-medium">View Full Map</span>
              </div>
            )}
          </div>
        </div>
      </DialogTrigger>

      <DialogContent className="max-w-[95vw] max-h-[95vh] w-full h-full p-4">
        <DialogTitle className="sr-only">Venue Seatmap</DialogTitle>
        <div className="relative w-full h-[calc(95vh-2rem)] rounded-lg overflow-hidden">
          <Image
            src={seatmapUrl}
            alt="Event Seatmap Full View"
            fill
            unoptimized={true} // 🔑 This bypasses Vercel's image optimization
            quality={100}
            className="object-contain"
            sizes="95vw"
            priority
            onError={handleImageError}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};







// "use client";

// import React from "react";
// import {
//   Dialog,
//   DialogContent,
//   DialogTrigger,
//   DialogTitle,
// } from "@/components/ui/dialog";
// import Image from "next/image";

// interface SeatmapDisplayProps {
//   seatmapUrl: string | null | undefined;
// }

// export const SeatmapDisplay: React.FC<SeatmapDisplayProps> = ({ seatmapUrl }) => {
//   if (!seatmapUrl) {
//     return (
//       <div className="text-gray-500 text-center py-4">
//         Seatmap not available for this event.
//       </div>
//     );
//   }

//   return (
//     <Dialog>
//       <DialogTrigger asChild>
//         <div className="relative w-full cursor-pointer group">
//           <div className="relative w-full aspect-[16/9] rounded-lg overflow-hidden">
//             <Image
//               src={seatmapUrl}
//               alt="Event Seatmap Thumbnail"
//               fill
//               className="object-cover transition-transform duration-300 group-hover:scale-105"
//               quality={75}
//               sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
//               priority
//             />
//             <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
//               <span className="text-white font-medium">View Full Map</span>
//             </div>
//           </div>
//         </div>
//       </DialogTrigger>

//       <DialogContent className="max-w-[95vw] max-h-[95vh] w-full h-full p-4">
//         <DialogTitle className="sr-only">Venue Seatmap</DialogTitle>
//         <div className="relative w-full h-[calc(95vh-2rem)] rounded-lg overflow-hidden">
//           <Image
//             src={seatmapUrl}
//             alt="Event Seatmap Full View"
//             fill
//             quality={100}
//             className="object-contain"
//             sizes="95vw"
//             priority
//           />
//         </div>
//       </DialogContent>
//     </Dialog>
//   );
// };



















// // "use client";

// // import React from "react";
// // import {
// //   Dialog,
// //   DialogContent,
// //   DialogTrigger,
// //   DialogTitle,
// // } from "@/components/ui/dialog";
// // import { Button } from "@/components/ui/button";

// // interface SeatmapDisplayProps {
// //   seatmapUrl: string | null | undefined;
// // }

// // export const SeatmapDisplay: React.FC<SeatmapDisplayProps> = ({ seatmapUrl }) => {
// //   if (!seatmapUrl) {
// //     return (
// //       <div className="text-gray-500 text-center py-4">
// //         Seatmap not available for this event.
// //       </div>
// //     );
// //   }

// //   return (
// //     <Dialog>
// //       <DialogTrigger asChild>
// //         <div className="relative w-full cursor-pointer group">
// //           <div className="relative w-full aspect-[16/9] rounded-lg overflow-hidden">
// //             <img
// //               src={seatmapUrl}
// //               alt="Event Seatmap Thumbnail"
// //               className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
// //             />
// //             <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
// //               <span className="text-white font-medium">Click to expand</span>
// //             </div>
// //           </div>
// //         </div>
// //       </DialogTrigger>

// //       <DialogContent className="max-w-screen max-h-screen w-full h-full p-0 border-none bg-black/90">
// //         <DialogTitle className="sr-only">Venue Seatmap</DialogTitle>
// //         <div className="relative w-full h-full flex items-center justify-center p-4">
// //           <img
// //             src={seatmapUrl}
// //             alt="Event Seatmap Full"
// //             className="max-w-full max-h-[90vh] object-contain"
// //           />
// //           <Button 
// //             className="absolute top-4 right-4 p-2 rounded-full bg-white/10 hover:bg-white/20 text-white"
// //             onClick={() => document.dispatchEvent(new KeyboardEvent('keydown', {'key': 'Escape'}))}
// //           >
// //             <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
// //               <line x1="18" y1="6" x2="6" y2="18"></line>
// //               <line x1="6" y1="6" x2="18" y2="18"></line>
// //             </svg>
// //           </Button>
// //         </div>
// //       </DialogContent>
// //     </Dialog>
// //   );
// // };
