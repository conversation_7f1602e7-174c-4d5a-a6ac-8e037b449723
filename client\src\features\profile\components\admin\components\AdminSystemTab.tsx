/**
 * AdminSystemTab Component
 * 
 * Displays system configuration and status information.
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Server, Database, HardDrive, Cpu, 
  RefreshCw, AlertTriangle, CheckCircle, Settings
} from 'lucide-react';

interface AdminSystemTabProps {
  isCurrentUser: boolean;
}

export function AdminSystemTab({ isCurrentUser }: AdminSystemTabProps) {
  const systemResources = [
    { name: 'CPU Usage', value: 42, icon: Cpu, color: 'text-blue-600' },
    { name: 'Memory Usage', value: 68, icon: HardDrive, color: 'text-amber-600' },
    { name: 'Storage', value: 54, icon: Database, color: 'text-purple-600' },
    { name: 'Network', value: 25, icon: RefreshCw, color: 'text-green-600' }
  ];

  return (
    <div className="space-y-6">
      {/* System Status Card */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <CardTitle className="text-xl flex items-center">
              <Server className="h-5 w-5 mr-2" />
              System Status
            </CardTitle>
            <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50">
              <CheckCircle className="h-3.5 w-3.5 mr-1" />
              Operational
            </Badge>
          </div>
          <CardDescription>
            Overview of system performance and resources
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-6">
            {systemResources.map((resource, index) => (
              <div key={index} className="space-y-1.5">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <resource.icon className={`h-4 w-4 mr-2 ${resource.color}`} />
                    <span className="text-sm font-medium">{resource.name}</span>
                  </div>
                  <span className="text-sm">{resource.value}%</span>
                </div>
                <Progress 
                  value={resource.value} 
                  className={`h-2 ${
                    resource.value > 80 ? "bg-red-500" : 
                    resource.value > 60 ? "bg-amber-500" : ""
                  }`}
                />
              </div>
            ))}
          </div>
        </CardContent>
        
        {isCurrentUser && (
          <CardFooter className="border-t pt-6 flex justify-between">
            <Button variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Data
            </Button>
            <Button variant="default" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              System Settings
            </Button>
          </CardFooter>
        )}
      </Card>
      
      {/* System Alerts */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2" />
            System Alerts
          </CardTitle>
          <CardDescription>
            Recent system notifications and warnings
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-3">
            <div className="flex p-3 border-l-4 border-amber-500 bg-amber-50 rounded-r-md">
              <AlertTriangle className="h-5 w-5 text-amber-600 mr-3 mt-0.5" />
              <div>
                <h4 className="font-medium text-sm">Database Backup Warning</h4>
                <p className="text-xs text-muted-foreground">
                  Last automated backup was 3 days ago. Consider running a manual backup.
                </p>
                <div className="mt-2">
                  <Button variant="outline" size="sm" className="h-7 text-xs">
                    Run Backup Now
                  </Button>
                </div>
              </div>
            </div>
            
            <div className="flex p-3 border-l-4 border-green-500 bg-green-50 rounded-r-md">
              <CheckCircle className="h-5 w-5 text-green-600 mr-3 mt-0.5" />
              <div>
                <h4 className="font-medium text-sm">Security Scan Complete</h4>
                <p className="text-xs text-muted-foreground">
                  Weekly security scan completed with no issues found.
                </p>
                <div className="mt-2">
                  <Button variant="outline" size="sm" className="h-7 text-xs">
                    View Report
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
