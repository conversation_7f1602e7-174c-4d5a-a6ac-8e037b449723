import React from "react";
import { format } from "date-fns";
import { UnifiedEvent } from "../../adapters/eventAdapter";
import { DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Calendar, Building2, MapPin } from "lucide-react";

interface EventHeaderProps {
  event: UnifiedEvent;
}

export const EventHeader: React.FC<EventHeaderProps> = ({ event }) => {
  // Format date
  const formattedDate = event.date 
    ? format(new Date(event.date), "EEEE, MMMM d, yyyy 'at' h:mm a") 
    : "Date not available";
  
  // Event source styling - Change Ticketmaster to External Event
  const sourceLabel = event.source === 'manager' ? 'Verified Reseller' : 'External Event';
  const sourceColor = event.source === 'manager' 
    ? 'bg-emerald-100 text-emerald-800 border-emerald-200' 
    : 'bg-blue-100 text-blue-800 border-blue-200';

  return (
    <DialogHeader className="p-5 border-b bg-gradient-to-r from-background to-muted/30">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        {/* Event Name and Details */}
        <div>
          <DialogTitle className="text-xl lg:text-2xl font-bold mb-2">{event.name}</DialogTitle>
          <div className="flex flex-wrap items-center gap-x-4 gap-y-2 text-sm text-muted-foreground">
            <span className="flex items-center">
              <Calendar className="h-4 w-4 mr-1.5" /> {formattedDate}
            </span>
            <span className="flex items-center">
              <Building2 className="h-4 w-4 mr-1.5" /> {event.venue}
            </span>
            <span className="flex items-center">
              <MapPin className="h-4 w-4 mr-1.5" /> {event.city}, {event.country}
            </span>
          </div>
        </div>
        {/* Source Badge */}
        <Badge className={`${sourceColor} border whitespace-nowrap text-xs md:text-sm px-3 py-1 rounded-full shrink-0`}>
          {sourceLabel}
        </Badge>
      </div>
    </DialogHeader>
  );
};
