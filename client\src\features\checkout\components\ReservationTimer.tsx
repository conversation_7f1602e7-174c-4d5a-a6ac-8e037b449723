import { useEffect, useState, useMemo } from 'react';
import { cn } from '@/lib/utils';
import { AlertCircle, Clock } from 'lucide-react';

interface ReservationTimerProps {
  expiresAt: string | undefined | null;
  onRefresh?: () => void;
  isRefreshing?: boolean;
  className?: string;
  hideRefreshButton?: boolean; // New prop to control button visibility
}

// Helper function to format milliseconds into MM:SS
const formatTime = (ms: number | null): string | null => {
  if (ms === null || ms < 0) return "00:00"; // Show 00:00 if invalid or expired
  const totalSeconds = Math.floor(ms / 1000);
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

export const ReservationTimer: React.FC<ReservationTimerProps> = ({
  expiresAt,
  onRefresh,
  isRefreshing = false,
  className,
  hideRefreshButton = false // Default to showing the button
}) => {
  const [currentTime, setCurrentTime] = useState(Date.now());

  // --- Use effect to update current time every second ---
  useEffect(() => {
    const intervalId = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000);

    // Cleanup interval on component unmount
    return () => clearInterval(intervalId);
  }, []); // Empty dependency array: runs only once on mount

  // --- Calculate remaining time based on current time and expiry ---
  const timeRemaining = useMemo((): number | null => {
    if (!expiresAt) return null;
    const expiresAtTimestamp = new Date(expiresAt).getTime();
    if (isNaN(expiresAtTimestamp)) return null; // Handle invalid date string
    return Math.max(0, expiresAtTimestamp - currentTime);
  }, [expiresAt, currentTime]);

  // --- Format the remaining time ---
  const formattedTime = useMemo(() => formatTime(timeRemaining), [timeRemaining]);

  // Determine visual states
  const isWarning = timeRemaining !== null && timeRemaining < 120000; // Less than 2 minutes
  const isCritical = timeRemaining !== null && timeRemaining < 30000;  // Less than 30 seconds
  const animatePulse = isCritical; // Pulse when critical

  // Don't render if expiry is not set or time is invalid
  if (timeRemaining === null || !formattedTime) {
     return null;
  }

  return (
    <div 
      className={cn(
        "flex items-center justify-between px-4 py-3 rounded-md",
        !isWarning && "bg-blue-50 border border-blue-200 text-blue-800",
        isWarning && !isCritical && "bg-amber-50 border border-amber-200 text-amber-800",
        isCritical && "bg-red-50 border border-red-200 text-red-800",
        className
      )}
    >
      <div className="flex items-center">
        {isCritical ? (
          <AlertCircle 
            className={cn(
              "h-5 w-5 mr-2",
              animatePulse && "animate-pulse text-red-600"
            )} 
          />
        ) : (
          <Clock className="h-5 w-5 mr-2" />
        )}
        
        <div>
          <p className="text-sm font-medium">
            {timeRemaining <= 0 ? "Reservation Expired" :
             isCritical ? "Reservation expiring soon!" :
             isWarning ? "Reservation expiring" :
             "Reservation time remaining"}
          </p>
          <p 
            className={cn(
              "text-xl font-bold",
              animatePulse && timeRemaining > 0 && "animate-pulse" // Only pulse if not expired
            )}
          >
            {formattedTime}
          </p>
        </div>
      </div>
      
      {/* Only render the button if hideRefreshButton is false */}
      {onRefresh && timeRemaining > 0 && !hideRefreshButton && (
        <button 
          className={cn(
            "px-3 py-1 text-sm font-medium rounded-md transition-colors",
            isCritical 
              ? "bg-red-600 hover:bg-red-700 text-white" 
              : "bg-transparent border border-current hover:bg-blue-100",
            "disabled:opacity-50 disabled:cursor-not-allowed"
          )}
          disabled={isRefreshing}
          onClick={onRefresh}
        >
          {isRefreshing ? "Extending..." : "Extend Time"}
        </button>
      )}
    </div>
  );
};
