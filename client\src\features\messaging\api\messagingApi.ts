/**
 * Messaging API Functions
 * 
 * TanStack Query-based API functions for the ticket messaging system.
 * Handles all HTTP requests, caching, and state management.
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axiosInstance from '@/apiAxios/axios';
import { toast } from 'sonner';
import {
  SendMessageRequest,
  GetConversationRequest,
  AdminMessagesRequest,
  MessageDTO,
  ConversationDTO,
  ConversationSummaryDTO,
  SendMessageResponse,
  GetConversationResponse,
  GetConversationsResponse,
  AdminConversationsResponse,
  ApiResponse,
  MessageStatus,
  UseConversationResult,
  UseConversationsResult,
  UseAdminMessagingResult
} from '../types/messaging.types';

// ============================================================================
// QUERY KEYS - Centralized for consistency
// ============================================================================

export const messagingKeys = {
  all: ['messaging'] as const,
  conversations: () => [...messagingKeys.all, 'conversations'] as const,
  conversation: (checkoutSessionId: string) => [...messagingKeys.all, 'conversation', checkoutSessionId] as const,
  myConversations: (userId: string) => [...messagingKeys.all, 'my-conversations', userId] as const,
  adminConversations: () => [...messagingKeys.all, 'admin', 'conversations'] as const,
  adminStats: () => [...messagingKeys.all, 'admin', 'stats'] as const,
} as const;

// ============================================================================
// RAW API FUNCTIONS (For direct use in custom hooks)
// ============================================================================

/**
 * Send a new message
 */
const sendMessage = async (data: SendMessageRequest): Promise<MessageDTO> => {
  console.log('🌐 Calling API:', axiosInstance.defaults.baseURL + '/api/v1/messaging/send');
  console.log('📤 Sending data:', data);
  
  const response = await axiosInstance.post<SendMessageResponse>('/api/v1/messaging/send', data);
  return response.data.data;
};

/**
 * Get conversation messages
 */
const getConversation = async (
  checkoutSessionId: string, 
  options: GetConversationRequest = {}
): Promise<GetConversationResponse['data']> => {
  const params = new URLSearchParams();
  if (options.page) params.append('page', options.page.toString());
  if (options.limit) params.append('limit', options.limit.toString());
  if (options.includeResolved !== undefined) params.append('includeResolved', options.includeResolved.toString());
  if (options.since) params.append('since', options.since);

  const response = await axiosInstance.get<GetConversationResponse>(
    `/api/v1/messaging/conversation/${checkoutSessionId}?${params.toString()}`
  );
  return response.data.data;
};

/**
 * Get user's conversations summary
 */
const getMyConversations = async (
  page: number = 1,
  limit: number = 20,
  status?: string
): Promise<GetConversationsResponse['data']> => {
  const params = new URLSearchParams();
  params.append('page', page.toString());
  params.append('limit', limit.toString());
  if (status) params.append('status', status);

  const response = await axiosInstance.get<GetConversationsResponse>(
    `/api/v1/messaging/my-conversations?${params.toString()}`
  );
  return response.data.data;
};

/**
 * Mark message as read
 */
const markMessageAsRead = async (messageId: string): Promise<void> => {
  await axiosInstance.patch<ApiResponse<null>>(`/api/v1/messaging/message/${messageId}/read`);
};

/**
 * Mark conversation as resolved
 */
const resolveConversation = async (checkoutSessionId: string): Promise<void> => {
  await axiosInstance.patch<ApiResponse<null>>(`/api/v1/messaging/conversation/${checkoutSessionId}/resolve`);
};

/**
 * Get admin conversations
 */
const getAdminConversations = async (options: AdminMessagesRequest = {}): Promise<AdminConversationsResponse['data']> => {
  const params = new URLSearchParams();
  if (options.page) params.append('page', options.page.toString());
  if (options.limit) params.append('limit', options.limit.toString());
  if (options.status) params.append('status', options.status);
  if (options.senderRole) params.append('senderRole', options.senderRole);
  if (options.eventId) params.append('eventId', options.eventId);
  if (options.dateFrom) params.append('dateFrom', options.dateFrom);
  if (options.dateTo) params.append('dateTo', options.dateTo);
  if (options.isEscalated !== undefined) params.append('isEscalated', options.isEscalated.toString());

  const response = await axiosInstance.get<AdminConversationsResponse>(
    `/api/v1/messaging/admin/conversations?${params.toString()}`
  );
  return response.data.data;
};

/**
 * Send admin message
 */
const sendAdminMessage = async (data: SendMessageRequest): Promise<MessageDTO> => {
  const response = await axiosInstance.post<SendMessageResponse>('/api/v1/messaging/admin/send', data);
  return response.data.data;
};

/**
 * Escalate conversation
 */
const escalateConversation = async (checkoutSessionId: string): Promise<void> => {
  await axiosInstance.post<ApiResponse<null>>(`/api/v1/messaging/admin/escalate/${checkoutSessionId}`);
};

/**
 * Get messaging stats for admin
 */
const getMessagingStats = async (dateFrom?: string, dateTo?: string) => {
  const params = new URLSearchParams();
  if (dateFrom) params.append('dateFrom', dateFrom);
  if (dateTo) params.append('dateTo', dateTo);

  const response = await axiosInstance.get(`/api/v1/messaging/admin/stats?${params.toString()}`);
  return response.data.data;
};

/**
 * Test messaging API health
 */
export const testMessagingAPI = async () => {
  try {
    console.log('🧪 Testing messaging API health...');
    const response = await axiosInstance.get('/api/v1/messaging/health');
    console.log('✅ Messaging API is working:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Messaging API test failed:', error);
    return false;
  }
};

// ============================================================================
// 🆕 NEW: Export messagingApi object for custom hooks
// ============================================================================

export const messagingApi = {
  // Core messaging functions
  sendMessage,
  getConversation,
  getMyConversations,
  markMessageAsRead,
  resolveConversation,
  
  // Admin functions
  getAdminConversations,
  sendAdminMessage,
  escalateConversation,
  getMessagingStats,
  
  // Test functions
  testMessagingAPI,
  
  // Query keys
  keys: messagingKeys,
};

// ============================================================================
// REACT QUERY HOOKS - For Components
// ============================================================================

/**
 * Hook to get a single conversation with real-time capabilities
 */
export function useConversation(
  checkoutSessionId: string,
  options: GetConversationRequest = {}
): UseConversationResult {
  const queryClient = useQueryClient();

  // Query for conversation data
  const {
    data,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: messagingKeys.conversation(checkoutSessionId),
    queryFn: () => getConversation(checkoutSessionId, options),
    enabled: !!checkoutSessionId,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes (formerly cacheTime)
  });

  // Mutation for sending messages
  const sendMessageMutation = useMutation({
    mutationFn: sendMessage,
    onSuccess: (newMessage) => {
      // Update the conversation cache with new message
      queryClient.setQueryData(
        messagingKeys.conversation(checkoutSessionId),
        (oldData: GetConversationResponse['data'] | undefined) => {
          if (!oldData) return oldData;
          
          return {
            ...oldData,
            conversation: {
              ...oldData.conversation,
              messages: [...oldData.conversation.messages, newMessage],
              lastMessageDate: newMessage.createdAt,
            },
          };
        }
      );

      // Invalidate conversations list to update unread counts
      queryClient.invalidateQueries({ queryKey: messagingKeys.conversations() });
      
      toast.success('Message sent successfully');
    },
    onError: (error) => {
      console.error('Failed to send message:', error);
      toast.error('Failed to send message. Please try again.');
    },
  });

  // Mutation for marking messages as read
  const markAsReadMutation = useMutation({
    mutationFn: markMessageAsRead,
    onSuccess: (_, messageId) => {
      // Update the message status in cache
      queryClient.setQueryData(
        messagingKeys.conversation(checkoutSessionId),
        (oldData: GetConversationResponse['data'] | undefined) => {
          if (!oldData) return oldData;
          
          return {
            ...oldData,
            conversation: {
              ...oldData.conversation,
              messages: oldData.conversation.messages.map(msg =>
                msg.id === messageId 
                  ? { ...msg, status: MessageStatus.READ, readAt: new Date().toISOString() }
                  : msg
              ),
              unreadCount: Math.max(0, oldData.conversation.unreadCount - 1),
            },
          };
        }
      );

      // Invalidate conversations list to update unread counts
      queryClient.invalidateQueries({ queryKey: messagingKeys.conversations() });
    },
    onError: (error) => {
      console.error('Failed to mark message as read:', error);
      // Silent failure - not critical for UX
    },
  });

  // Mutation for resolving conversations
  const resolveConversationMutation = useMutation({
    mutationFn: resolveConversation,
    onSuccess: () => {
      // Update conversation status in cache
      queryClient.setQueryData(
        messagingKeys.conversation(checkoutSessionId),
        (oldData: GetConversationResponse['data'] | undefined) => {
          if (!oldData) return oldData;
          
          return {
            ...oldData,
            conversation: {
              ...oldData.conversation,
              status: 'RESOLVED',
              messages: oldData.conversation.messages.map(msg => ({
                ...msg,
                status: MessageStatus.RESOLVED,
              })),
            },
          };
        }
      );

      // Invalidate conversations list
      queryClient.invalidateQueries({ queryKey: messagingKeys.conversations() });
      
      toast.success('Conversation marked as resolved');
    },
    onError: (error) => {
      console.error('Failed to resolve conversation:', error);
      toast.error('Failed to resolve conversation. Please try again.');
    },
  });

  return {
    conversation: data?.conversation || null,
    messages: data?.conversation?.messages || [],
    isLoading,
    isError,
    error: error as Error | null,
    sendMessage: async (message: string) => {
      await sendMessageMutation.mutateAsync({
        checkoutSessionId,
        message: message.trim(),
      });
    },
    markAsRead: async (messageId: string) => {
      await markAsReadMutation.mutateAsync(messageId);
    },
    resolveConversation: async () => {
      await resolveConversationMutation.mutateAsync(checkoutSessionId);
    },
    refetch: async () => {
      await refetch();
    },
  };
}

/**
 * Hook to get user's conversations list
 */
export function useConversations(
  page: number = 1,
  limit: number = 20,
  status?: string
): UseConversationsResult {
  const queryClient = useQueryClient();

  const {
    data,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: [...messagingKeys.conversations(), page, limit, status],
    queryFn: () => getMyConversations(page, limit, status),
    staleTime: 1000 * 60 * 2, // 2 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
  });

  return {
    conversations: data?.items || [],
    isLoading,
    isError,
    error: error as Error | null,
    pagination: data?.pagination || {
      currentPage: 1,
      totalPages: 0,
      totalItems: 0,
      hasNextPage: false,
      hasPrevPage: false,
    },
    goToPage: (newPage: number) => {
      queryClient.invalidateQueries({ 
        queryKey: [...messagingKeys.conversations(), newPage, limit, status] 
      });
    },
    refetch: async () => {
      await refetch();
    },
  };
}

/**
 * Hook for admin messaging operations
 */
export function useAdminMessaging(
  page: number = 1,
  limit: number = 20,
  options: AdminMessagesRequest = {}
): UseAdminMessagingResult {
  const queryClient = useQueryClient();

  // Query for admin conversations
  const {
    data,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: [...messagingKeys.adminConversations(), page, limit, options],
    queryFn: () => getAdminConversations({ ...options, page, limit }),
    staleTime: 1000 * 60 * 1, // 1 minute for admin data
    gcTime: 1000 * 60 * 5, // 5 minutes
  });

  // Mutation for escalating conversations
  const escalateMutation = useMutation({
    mutationFn: escalateConversation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: messagingKeys.adminConversations() });
      toast.success('Conversation escalated successfully');
    },
    onError: (error) => {
      console.error('Failed to escalate conversation:', error);
      toast.error('Failed to escalate conversation. Please try again.');
    },
  });

  // Mutation for sending admin messages
  const sendAdminMessageMutation = useMutation({
    mutationFn: sendAdminMessage,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: messagingKeys.adminConversations() });
      queryClient.invalidateQueries({ queryKey: messagingKeys.all });
      toast.success('Admin message sent successfully');
    },
    onError: (error) => {
      console.error('Failed to send admin message:', error);
      toast.error('Failed to send admin message. Please try again.');
    },
  });

  return {
    conversations: data?.conversations || [],
    isLoading,
    isError,
    error: error as Error | null,
    summary: data?.summary || {
      totalConversations: 0,
      unreadMessages: 0,
      escalatedConversations: 0,
      activeConversations: 0,
    },
    pagination: data?.pagination || {
      currentPage: 1,
      totalPages: 0,
      totalItems: 0,
      hasNextPage: false,
      hasPrevPage: false,
    },
    escalateConversation: async (checkoutSessionId: string) => {
      await escalateMutation.mutateAsync(checkoutSessionId);
    },
    sendAdminMessage: async (checkoutSessionId: string, message: string) => {
      await sendAdminMessageMutation.mutateAsync({
        checkoutSessionId,
        message: message.trim(),
      });
    },
    goToPage: (newPage: number) => {
      queryClient.invalidateQueries({ 
        queryKey: [...messagingKeys.adminConversations(), newPage, limit, options] 
      });
    },
    refetch: async () => {
      await refetch();
    },
  };
}

// ============================================================================
// INDIVIDUAL MUTATION HOOKS (For specific operations)
// ============================================================================

/**
 * Hook for sending messages (standalone)
 */
export function useSendMessage() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: sendMessage,
    onSuccess: (newMessage) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ 
        queryKey: messagingKeys.conversation(newMessage.checkoutSessionId) 
      });
      queryClient.invalidateQueries({ queryKey: messagingKeys.conversations() });
      
      toast.success('Message sent successfully');
    },
    onError: (error) => {
      console.error('Failed to send message:', error);
      toast.error('Failed to send message. Please try again.');
    },
  });
}

/**
 * Hook for marking messages as read (standalone)
 */
export function useMarkAsRead() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: markMessageAsRead,
    onSuccess: () => {
      // Invalidate all conversation-related queries
      queryClient.invalidateQueries({ queryKey: messagingKeys.all });
    },
    onError: (error) => {
      console.error('Failed to mark message as read:', error);
      // Silent failure for read status
    },
  });
}

/**
 * Hook for resolving conversations (standalone)
 */
export function useResolveConversation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: resolveConversation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: messagingKeys.all });
      toast.success('Conversation resolved successfully');
    },
    onError: (error) => {
      console.error('Failed to resolve conversation:', error);
      toast.error('Failed to resolve conversation. Please try again.');
    },
  });
}