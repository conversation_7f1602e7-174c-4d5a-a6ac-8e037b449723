/**
 * Backend Profile Types
 * 
 * Type definitions for profile-related data, ensuring sensitive data is excluded.
 */
import { Profile, User } from '@prisma/client';

/**
 * Safe user data excluding sensitive fields
 */
export type SafeUserData = Omit<
  User, 
  'password' | 
  'refreshToken' | 
  'resetPasswordToken' | 
  'resetPasswordExpires'
>;

/**
 * Profile response DTO combining profile and user data
 */
export interface ProfileResponseDTO {
  // Profile fields
  id: string;
  userId: string;
  bio?: string | null;
  headline?: string | null;
  location?: string | null;
  website?: string | null;
  phoneVisible: boolean;
  company?: string | null;
  jobTitle?: string | null;
  industry?: string | null;
  skills: string[];
  displayEmail: boolean;
  theme?: string | null;
  createdAt: Date;
  updatedAt: Date;
  
  // Social media links
  socialLinks?: {
    twitter?: string;
    linkedin?: string;
    facebook?: string;
    instagram?: string;
    github?: string;
  };
  
  // User fields
  fullName?: string | null;
  email?: string | null;
  mobile?: string | null;
  role?: string;
  emailVerified?: Date | null;
  mobileVerified?: Date | null;
  isActive?: boolean;
  lastLogin?: Date | null;
  image?: string | null;
}

/**

 * Profile update DTO - MODIFIED to separate User and Profile fields
 */
export interface ProfileUpdateDTO {
  // Profile fields
  bio?: string;
  headline?: string;
  location?: string;
  website?: string;
  phoneVisible?: boolean;
  company?: string;
  jobTitle?: string;
  industry?: string;
  skills?: string[];
  displayEmail?: boolean;
  theme?: string;
  
  socialLinks?: {
    twitter?: string;
    linkedin?: string;
    facebook?: string;
    instagram?: string;
    github?: string;
  };
  
  // User fields - explicitly marked for separation
  fullName?: string;
  mobile?: string;
}
