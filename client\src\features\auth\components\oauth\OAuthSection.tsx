import { OAuthProviderButton } from './OAuthProviderButton';
import { Separator } from '@/components/ui/separator';

export const OAuthSection = () => {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <Separator />
        <span className="text-sm text-gray-500">or continue with</span>
        <Separator />
      </div>
      
      <div className="space-y-3">
        <OAuthProviderButton 
          provider="google"
          onError={(error) => console.error('Google auth error:', error)}
        />
        <OAuthProviderButton 
          provider="github"
          onError={(error) => console.error('Github auth error:', error)}
        />
      </div>
    </div>
  );
};
