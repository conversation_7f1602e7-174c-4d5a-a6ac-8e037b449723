"use client";
import { Calendar, Plus } from "lucide-react";
import { Bento<PERSON>ox } from "../../../shared/widgets/BentoBox";
import { But<PERSON> } from "@/components/ui/button";

export const EventManagementBento = () => {
  // Dummy event data
  const events = [
    {
      id: 1,
      name: "Summer Music Festival",
      date: "2024-06-15",
      status: "Active",
    },
    { id: 2, name: "Sports Championship", date: "2024-07-01", status: "Draft" },
    { id: 3, name: "Tech Conference", date: "2024-08-10", status: "Active" },
  ];

  return (
    <BentoBox
      title="Event Management"
      description="Manage your upcoming events"
      className="col-span-2 row-span-2"
      header={
        <div className="flex justify-between items-center">
          <Calendar className="h-5 w-5 text-primary" />
          <Button size="sm" variant="outline">
            <Plus className="h-4 w-4 mr-2" />
            New Event
          </Button>
        </div>
      }
    >
      <div className="space-y-4 mt-4">
        {events.map((event) => (
          <div
            key={event.id}
            className="flex items-center justify-between p-3 rounded-lg bg-accent/50 hover:bg-accent/70 transition-colors"
          >
            <div>
              <h4 className="font-medium">{event.name}</h4>
              <p className="text-sm text-muted-foreground">{event.date}</p>
            </div>
            <span
              className={`text-sm ${
                event.status === "Active" ? "text-green-600" : "text-orange-600"
              }`}
            >
              {event.status}
            </span>
          </div>
        ))}
      </div>
    </BentoBox>
  );
};
