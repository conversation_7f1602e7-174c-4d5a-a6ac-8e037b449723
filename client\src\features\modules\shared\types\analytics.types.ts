export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string;
    borderWidth?: number;
  }[];
}

export interface AnalyticMetric {
  label: string;
  value: number | string;
  change: {
    value: number;
    trend: 'up' | 'down' | 'neutral';
  };
  icon?: React.ReactNode;
}

export interface TimeSeriesData {
  timestamp: string;
  value: number;
}

export interface AnalyticsFilters {
  dateRange: 'today' | 'week' | 'month' | 'year';
  comparison?: boolean;
}
