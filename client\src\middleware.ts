import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

/**
 * Security Middleware for Next.js Application
 * 
 * IMPORTANCE:
 * 1. Security Benefits:
 *    - Prevents common web vulnerabilities
 *    - Adds essential security headers
 *    - Recommended for production applications
 * 
 * 2. Specific Protections:
 *    - x-content-type-options: Prevents MIME-type sniffing attacks
 *    - x-frame-options: Prevents clickjacking attacks via iframe embedding
 * 
 * 3. Performance Impact:
 *    - Minimal overhead
 *    - Lightweight implementation
 *    - No significant impact on application performance
 * 
 * Note: While the application can work without this middleware,
 * removing it will expose your application to potential security vulnerabilities.
 */

export function middleware(request: NextRequest) {
  console.log('⚡ Middleware processing:', request.url);
  
  // Optionally skip auth routes
  if (request.nextUrl.pathname.startsWith('/api/auth') || 
      request.nextUrl.pathname.startsWith('/api/authlogic')) {
    console.log('⏭️ Skipping middleware for auth route');
    return NextResponse.next();
  }
  
  const response = NextResponse.next();
  
  // Add security headers
  response.headers.set('x-content-type-options', 'nosniff');
  response.headers.set('x-frame-options', 'DENY');
  
  return response;
}

export const config = {
  matcher: [
    '/((?!api/auth|api/authlogic|_next/static|_next/image|favicon.ico).*)',
  ],
};
