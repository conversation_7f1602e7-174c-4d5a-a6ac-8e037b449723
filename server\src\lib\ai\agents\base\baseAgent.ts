// server/src/lib/ai/agents/base/baseAgent.ts
// Base class for AI agents
import { openai } from '@ai-sdk/openai';
import { generateText,  CoreToolCall } from 'ai';

// Define the base agent class
export class BaseAgent {
  static async generateTextFromPrompt(prompt: string, userQuery: string, tools?:any): Promise<{ response: any; toolCalls?: CoreToolCall<string, Record<string, unknown>>[] }> {
    try {
      const { response } = await generateText({
        model: openai('gpt-3.5-turbo'),
        prompt: prompt,
        tools: tools || {},
        messages: [{ role: 'user', content: userQuery }],
      });
       // Check if the AI responded
        if (!response) {
            throw new Error('No response from AI');
          }
            return {
              response,
              // Removed toolCalls from the return object
            };
      } catch (error:any) {
          console.error('Error in Base Agent:', error);
          throw new Error(`Error in Base Agent: ${error.message}`);
      }
    }
}