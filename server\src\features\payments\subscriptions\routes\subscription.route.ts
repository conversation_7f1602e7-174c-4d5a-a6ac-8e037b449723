/**
 * Subscription Routes
 * 
 * Defines API endpoints for subscription functionality.
 */

import express from 'express';
import { SubscriptionController } from '../controllers/subscription.controller';
import { authMiddleware } from '@/middleware/auth.middleware';

const router = express.Router();

// Get subscription status (requires authentication)
router.get(
  '/status',
  authMiddleware,
  SubscriptionController.getSubscriptionStatus
);

// Create a subscription checkout session (requires authentication)
router.post(
  '/create-checkout',
  authMiddleware,
  SubscriptionController.createSubscriptionCheckout
);

// Cancel a subscription (requires authentication)
router.post(
  '/cancel',
  authMiddleware,
  SubscriptionController.cancelSubscription
);

export default router;