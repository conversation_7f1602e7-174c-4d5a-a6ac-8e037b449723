/**
 * MessageBubble Component
 * 
 * Displays individual messages with role-based styling.
 * Used in conversation views for Visitors, Managers, and Admin.
 */

import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  User, 
  UserCheck, 
  Shield, 
  Clock,
  CheckCheck,
  Check
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useMessageValidation } from '../hooks/useMessaging';
import { MessageBubbleProps, MessageStatus, UserRole } from '../types/messaging.types';

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isOwn,
  showAvatar = true,
  onMarkAsRead,
}) => {
  const { formatDisplayName, formatMessageTime } = useMessageValidation();

  // Determine message alignment and styling based on sender
  const isVisitor = message.senderRole === 'VISITOR';
  const isManager = message.senderRole === 'MANAGER';
  const isAdmin = message.senderRole === 'ADMIN';

  // Get role-based styling
  const getRoleStyles = () => {
    if (isAdmin) {
      return {
        bubbleClass: "bg-purple-100 border-purple-200 text-purple-900 dark:bg-purple-900/30 dark:border-purple-700 dark:text-purple-100",
        badgeClass: "bg-purple-500 text-white",
        icon: <Shield className="h-3 w-3" />,
        roleLabel: "Support"
      };
    }
    
    if (isManager) {
      return {
        bubbleClass: isOwn 
          ? "bg-blue-500 text-white" 
          : "bg-green-100 border-green-200 text-green-900 dark:bg-green-900/30 dark:border-green-700 dark:text-green-100",
        badgeClass: "bg-green-500 text-white",
        icon: <UserCheck className="h-3 w-3" />,
        roleLabel: "Seller"
      };
    }
    
    // Visitor
    return {
      bubbleClass: isOwn 
        ? "bg-blue-500 text-white" 
        : "bg-gray-100 border-gray-200 text-gray-900 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-100",
      badgeClass: "bg-blue-500 text-white",
      icon: <User className="h-3 w-3" />,
      roleLabel: "Buyer"
    };
  };

  const roleStyles = getRoleStyles();
  const displayName = formatDisplayName(message.senderRole, message.senderId);
  const timeDisplay = formatMessageTime(message.createdAt);

  // Get status icon for message read status
  const getStatusIcon = () => {
    if (!isOwn) return null; // Only show status for own messages
    
    switch (message.status) {
      case MessageStatus.READ:
        return <CheckCheck className="h-3 w-3 text-blue-400" />;
      case MessageStatus.UNREAD:
        return <Check className="h-3 w-3 text-gray-400" />;
      case MessageStatus.RESOLVED:
        return <CheckCheck className="h-3 w-3 text-green-400" />;
      default:
        return <Clock className="h-3 w-3 text-gray-400" />;
    }
  };

  // Handle mark as read action
  const handleMarkAsRead = () => {
    if (onMarkAsRead && message.status === MessageStatus.UNREAD && !isOwn) {
      onMarkAsRead(message.id);
    }
  };

  return (
    <div 
      className={cn(
        "flex gap-3 group transition-all duration-200",
        isOwn ? "flex-row-reverse" : "flex-row",
        "hover:bg-gray-50/50 dark:hover:bg-gray-800/20 p-2 rounded-lg -m-2"
      )}
    >
      {/* Avatar */}
      {showAvatar && (
        <div className="flex-shrink-0">
          <Avatar className="h-8 w-8">
            <AvatarImage src={message.avatarUrl} />
            <AvatarFallback className={cn("text-xs", roleStyles.badgeClass)}>
              {roleStyles.icon}
            </AvatarFallback>
          </Avatar>
        </div>
      )}

      {/* Message Content */}
      <div className={cn("flex flex-col max-w-[80%]", isOwn ? "items-end" : "items-start")}>
        {/* Sender Info */}
        <div className={cn("flex items-center gap-2 mb-1", isOwn ? "flex-row-reverse" : "flex-row")}>
          <Badge 
            variant="outline" 
            className={cn("text-xs px-2 py-0.5", roleStyles.badgeClass)}
          >
            {roleStyles.icon}
            <span className="ml-1">{roleStyles.roleLabel}</span>
          </Badge>
          
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {timeDisplay}
          </span>
          
          {/* Admin Escalation Badge */}
          {message.isEscalated && (
            <Badge variant="outline" className="text-xs bg-orange-100 text-orange-700 border-orange-200">
              <Shield className="h-3 w-3 mr-1" />
              Escalated
            </Badge>
          )}
        </div>

        {/* Message Bubble */}
        <div 
          className={cn(
            "relative rounded-lg px-4 py-2 border shadow-sm",
            roleStyles.bubbleClass,
            "break-words whitespace-pre-wrap",
            // Unread message styling
            message.status === MessageStatus.UNREAD && !isOwn && "ring-2 ring-blue-200 ring-opacity-50",
            // Clickable if unread and not own
            message.status === MessageStatus.UNREAD && !isOwn && "cursor-pointer hover:shadow-md transition-shadow"
          )}
          onClick={handleMarkAsRead}
        >
          {/* Admin Prefix for Admin Messages */}
          {isAdmin && message.message.includes('[ADMIN INTERVENTION]') && (
            <div className="text-xs font-semibold mb-1 text-purple-600 dark:text-purple-300">
              🛡️ Admin Intervention
            </div>
          )}
          
          {/* Message Text */}
          <p className="text-sm leading-relaxed">
            {message.message.replace('[ADMIN INTERVENTION] ', '')}
          </p>
          
          {/* Message Status */}
          <div className={cn(
            "flex items-center justify-end gap-1 mt-1",
            isOwn ? "justify-end" : "justify-start"
          )}>
            {/* Read Status */}
            {getStatusIcon()}
            
            {/* Mark as Read Button for Unread Messages */}
            {message.status === MessageStatus.UNREAD && !isOwn && onMarkAsRead && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2 text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={(e) => {
                  e.stopPropagation();
                  handleMarkAsRead();
                }}
              >
                Mark Read
              </Button>
            )}
          </div>
        </div>

        {/* Metadata (IP, User Agent) for Admin View */}
        {isAdmin && message.metadata && (
          <div className="text-xs text-gray-400 mt-1 max-w-full">
            {message.metadata.ipAddress && (
              <div>IP: {message.metadata.ipAddress}</div>
            )}
            {message.metadata.userAgent && (
              <div className="truncate">UA: {message.metadata.userAgent}</div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// Default export for easier imports
export default MessageBubble;