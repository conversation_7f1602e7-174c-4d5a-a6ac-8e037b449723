
import { Prisma } from '@prisma/client';
import { Request } from 'express';

// Use Prisma's type generator for TmEvent model
export type TmEvent = Prisma.TmEventGetPayload<{}>;

// API Response Types (Ticketmaster API)
export interface TmApiResponse {
  _embedded: {
    events: TmApiEvent[];
  };
  page: {
    size: number;
    totalElements: number;
    totalPages: number;
    number: number;
  };
}

// Main Event Interface (simplified for data fetching/transformation)
export interface TmApiEvent {
    name: string;
    type: string;
    id: string;
    test: boolean;
    url: string;
    locale: string;
    images: TmImage[];
    sales: TmSales;
    dates: TmDates;
    classifications: TmClassification[];
    promoter?: {
      id: string;
      name: string;
      description: string;
    };
    promoters?: Array<{
      id: string;
      name: string;
      description: string;
    }>;
    priceRanges?: TmPriceRange[];
    products?: any[];
    seatmap?: {
      staticUrl: string;
    };
    accessibility?: {
      ticketLimit: number;
    };
    ticketLimit?: {
      info: string;
    };
    _embedded?: {
      venues?: TmVenue[];
    };
    _links: {
      self: {
        href: string;
      };
      attractions?: Array<{
        href: string;
      }>;
      venues?: Array<{
        href: string;
      }>;
    };
  }

// Event Image
export interface TmImage {
    ratio: string;
    url: string;
    width: number;
    height: number;
    fallback: boolean;
  }
  
  // Dates & Times
  export interface TmDates {
    start: {
      localDate: string;
      localTime: string;
      dateTime: string;
      dateTBD: boolean;
      dateTBA: boolean;
      timeTBA: boolean;
      noSpecificTime: boolean;
    };
    timezone: string;
    status: {
      code: string;
    };
    spanMultipleDays: boolean;
  }
  
  // Classifications
  export interface TmClassification {
    primary: boolean;
    segment: {
      id: string;
      name: string;
    };
    genre: {
      id: string;
      name: string;
    };
    subGenre: {
      id: string;
      name: string;
    };
    family: boolean;
  }
  
  // Pricing
  export interface TmPriceRange {
    type: string;
    currency: string;
    min: number;
    max: number;
  }
  
  // Venue
  export interface TmVenue {
    name: string;
    type: string;
    id: string;
    test: boolean;
    locale: string;
    postalCode: string;
    timezone: string;
    city: {
      name: string;
    };
    state: {
      name: string;
      stateCode: string;
    };
    country: {
      name: string;
      countryCode: string;
    };
    address: {
      line1: string;
    };
    location: {
      longitude: string;
      latitude: string;
    };
  }
  
  // Sales
  export interface TmSales {
    public: {
      startDateTime: string;
      endDateTime: string;
      startTBD: boolean;
      startTBA: boolean;
    };
    presales: Array<{
      startDateTime: string;
      endDateTime: string;
      name: string;
    }>;
  }

// Custom Request Interface for Filtering, Pagination & Searching
export interface TmEventQueryParams {
  page?: string;
  size?: string;
  keyword?: string;
  city?: string;
  startDate?: string;
  segment?: string;
}

// Combined Result for API response with pagination
export interface PaginatedTmEventResponse {
  events: TmEvent[];
  pagination: {
    page: number;
    size: number;
    total: number;
    totalPages: number;
  };
}

export interface CustomRequest extends Request {
  rateLimit?: {
      remaining: number;
      resetTime: number;
  };
}


























// --------------------older version push successful in local db----------------------

// import { Prisma } from '@prisma/client'

// // Use Prisma's type generator
// export type TmEvent = Prisma.TmEventGetPayload<{}>

// // API Response Types
// export interface TmApiResponse {
//   _embedded: {
//     events: TmApiEvent[]
//   }
//   page: {
//     size: number
//     totalElements: number
//     totalPages: number
//     number: number
//   }
// }

// // Main Event Interface (simplified)
// // export interface TmApiEvent {
// //   name: string
// //   type: string
// //   id: string
// //   url: string
// //   locale: string
// //   images: TmImage[]
// //   dates: TmDates
// //   classifications: TmClassification[]
// //   priceRanges?: TmPriceRange[]
// //   _embedded?: {
// //     venues?: TmVenue[]
// //   }
// // }

// // Event Image
// export interface TmImage {
//   ratio: string
//   url: string
//   width: number
//   height: number
//   fallback: boolean
// }

// // Dates & Times
// export interface TmDates {
//   start: {
//     localDate: string
//     localTime: string
//     dateTime: string
//     dateTBD: boolean
//     dateTBA: boolean
//     timeTBA: boolean
//     noSpecificTime: boolean
//   }
//   timezone: string
//   status: {
//     code: string
//   }
//   spanMultipleDays: boolean
// }

// // Classifications
// export interface TmClassification {
//   primary: boolean
//   segment: {
//     id: string
//     name: string
//   }
//   genre: {
//     id: string
//     name: string
//   }
//   subGenre: {
//     id: string
//     name: string
//   }
//   family: boolean
// }

// // Pricing
// export interface TmPriceRange {
//   type: string
//   currency: string
//   min: number
//   max: number
// }

// // Venue
// export interface TmVenue {
//   name: string
//   type: string
//   id: string
//   test: boolean
//   locale: string
//   postalCode: string
//   timezone: string
//   city: {
//     name: string
//   }
//   state: {
//     name: string
//     stateCode: string
//   }
//   country: {
//     name: string
//     countryCode: string
//   }
//   address: {
//     line1: string
//   }
//   location: {
//     longitude: string
//     latitude: string
//   }
// }

// // Sales
// export interface TmSales {
//   public: {
//     startDateTime: string
//     endDateTime: string
//     startTBD: boolean
//     startTBA: boolean
//   }
//   presales: Array<{
//     startDateTime: string
//     endDateTime: string
//     name: string
//   }>
// }

// // Main Event Interface
// export interface TmApiEvent {
//   name: string
//   type: string
//   id: string
//   test: boolean
//   url: string
//   locale: string
//   images: TmImage[]
//   sales: TmSales
//   dates: TmDates
//   classifications: TmClassification[]
//   promoter?: {
//     id: string
//     name: string
//     description: string
//   }
//   promoters?: Array<{
//     id: string
//     name: string
//     description: string
//   }>
//   priceRanges?: TmPriceRange[]
//   products?: any[]
//   seatmap?: {
//     staticUrl: string
//   }
//   accessibility?: {
//     ticketLimit: number
//   }
//   ticketLimit?: {
//     info: string
//   }
//   _embedded?: {
//     venues?: TmVenue[]
//   }
//   _links: {
//     self: {
//       href: string
//     }
//     attractions?: Array<{
//       href: string
//     }>
//     venues?: Array<{
//       href: string
//     }>
//   }
// }



