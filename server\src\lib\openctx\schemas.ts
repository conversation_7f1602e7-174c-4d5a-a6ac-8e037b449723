// Define schemas for validating OpenCTX requests
// These schemas specify the expected structure of OpenCTX queries

import { z } from 'zod';

// Schema for filtering by a single value (e.g., genre: 'Music')
export const singleValueFilterSchema = z.object({
    field: z.string(),          // Field to filter
    value: z.string(),          // Single value to filter by
});
// Schema for filtering by a list of values (e.g., venueCity: ['New York', 'Toronto'])
export const multiValueFilterSchema = z.object({
    field: z.string(),          // Field to filter
    values: z.string().array(),  // Array of values to filter by
});


// Schema for sorting events
export const sortSchema = z.object({
    field: z.string(),    // Field to sort by
    order: z.enum(['asc', 'desc']).optional(),    // Sorting order
});


// Schema for pagination
export const paginationSchema = z.object({
  page: z.number().int().positive().default(1),
  size: z.number().int().positive().default(10),
});

// Main OpenCTX request schema
export const openCTXSchema = z.object({
  protocol: z.literal('openctx/v1'),    // Protocol identifier
  entity: z.string(),         // Entity type (e.g., 'events')
  filters: z.union([singleValueFilterSchema, multiValueFilterSchema]).array().optional(), // Array of filters
  sort: sortSchema.optional(),        // Sorting criteria (optional)
  pagination: paginationSchema.optional(),     // Pagination details
  query: z.string().optional(),      // Optional free text query for keyword searches
});


export type OpenCTXSchema = z.infer<typeof openCTXSchema>;
