/**
 * Controller for points operations within the checkout feature
 */

import { Request, Response } from 'express';
import { asyncHandler } from '@/utils/asyncHandler';
import ApiError from '@/utils/ApiError';
import { PointsService } from '../services/points.service';
import { ApplyPointsRequest } from '../types/points.types';

export class PointsController {
  /**
   * Apply points to a checkout session
   * POST /api/v1/checkout/points
   */
  static applyPoints = asyncHandler(async (req: Request, res: Response) => {
    console.log("🪙 PointsController: Hit applyPoints handler");
    
    // Validate required fields in request body
    const { sessionId, pointsToApply } = req.body as ApplyPointsRequest;
    
    if (!sessionId || pointsToApply === undefined) {
      throw ApiError.badRequest('Session ID and points amount are required');
    }
    
    // Validate points amount is not negative
    if (pointsToApply < 0) {
      throw ApiError.badRequest('Points amount cannot be negative');
    }
    
    // Get user ID from authenticated request
    const userId = req.user?.userId;
    
    if (!userId) {
      throw ApiError.unauthorized('User must be authenticated');
    }
    
    console.log(`🔍 Processing points application: ${pointsToApply} points for session ${sessionId}`);
    
    // This service will throw ApiErrors for validation failures
    const result = await PointsService.applyPoints(sessionId, pointsToApply, userId);
    
    console.log("✅ Points applied successfully");
    
    // Return success with 200 OK status
    return res.status(200).json(result);
  });
}