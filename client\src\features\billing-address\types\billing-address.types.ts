/**
 * Types for billing address management feature
 */

export interface BillingAddress {
  id: string;
  name: string;
  email: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateBillingAddressRequest {
  name: string;
  email: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  isDefault?: boolean;
}

export interface UpdateBillingAddressRequest {
  id: string;
  name?: string;
  email?: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  isDefault?: boolean;
}

export interface BillingAddressResponse {
  success: boolean;
  message: string;
  data: BillingAddress[];
}

export interface BillingAddressSingleResponse {
  success: boolean;
  message: string;
  data: BillingAddress;
}

export interface BillingAddressState {
  addresses: BillingAddress[];
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  refetch: () => Promise<any>;
  createAddress: (address: CreateBillingAddressRequest) => Promise<BillingAddress>;
  updateAddress: (address: UpdateBillingAddressRequest) => Promise<BillingAddress>;
  deleteAddress: (id: string) => Promise<void>;
  setDefaultAddress: (id: string) => Promise<void>;
}