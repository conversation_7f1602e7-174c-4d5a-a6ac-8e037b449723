// Hook for managing settings state and updates
import { useState } from 'react'

interface Settings {
  defaultQuery?: string
  sortBy?: 'date' | 'popularity' | 'price'
  resultsPerPage?: number
  category?: string
  preferredCategory?: string
  dataSource?: 'database' | 'ticketmaster'  // Added dataSource option
}

export function useSettings() {
  const [settings, setSettings] = useState<Settings>({})
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updateSettings = async (newSettings: Settings) => {
    setIsLoading(true)
    setError(null)
    try {
      // Directly update settings without API call, merging with previous settings
      setSettings(prev => ({...prev, ...newSettings}))
    } catch (err) {
      setError('Failed to update settings')
    } finally {
      setIsLoading(false)
    }
  }

  return {
    settings,
    isLoading,
    error,
    updateSettings,
  }
}
