import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { OtpInput } from './OtpInput';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Loader2, Send } from 'lucide-react';

interface VerificationProps {
  type: 'email' | 'mobile';
  value: string;
  isVerified?: boolean;
  onSendVerification: (value: string) => void;
  onVerify: (otp: string) => void;
  isSending?: boolean;
  isVerifying?: boolean;
}

export const ProfileVerification: React.FC<VerificationProps> = ({
  type,
  value,
  isVerified = false,
  onSendVerification,
  onVerify,
  isSending = false,
  isVerifying = false,
}) => {
  const [showOtpInput, setShowOtpInput] = useState(false);
  
  const handleSendCode = () => {
    console.log(`📤 Sending verification code to ${type}: ${value}`);
    onSendVerification(value);
    setShowOtpInput(true);
  };
  
  const handleVerifyOtp = (otp: string) => {
    console.log(`🔐 Verifying OTP: ${otp} for ${type}`);
    onVerify(otp);
  };
  
  const label = type === 'email' ? 'Email' : 'Mobile';
  
  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <div className="flex-1">
          <div className="text-sm font-medium mb-1 flex items-center">
            {label}
            {isVerified && (
              <Badge variant="outline" className="ml-2 bg-emerald-50 text-emerald-700 border-emerald-200">
                <CheckCircle className="w-3 h-3 mr-1" />
                Verified
              </Badge>
            )}
          </div>
          <p className="text-sm text-muted-foreground">{value}</p>
        </div>
        
        {!isVerified && !showOtpInput && (
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleSendCode}
            disabled={isSending || !value}
          >
            {isSending ? (
              <Loader2 className="w-4 h-4 mr-1 animate-spin" />
            ) : (
              <Send className="w-4 h-4 mr-1" />
            )}
            Verify
          </Button>
        )}
      </div>
      
      {showOtpInput && !isVerified && (
        <div className="bg-muted/40 rounded-md p-3 space-y-3">
          <p className="text-xs text-center">
            Enter the verification code sent to your {type}
          </p>
          
          <OtpInput onComplete={handleVerifyOtp} />
          
          {isVerifying && (
            <div className="flex justify-center">
              <Loader2 className="w-4 h-4 animate-spin text-primary" />
            </div>
          )}
          
          <div className="flex justify-between text-xs">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleSendCode} 
              disabled={isSending}
              className="text-xs h-8"
            >
              Resend code
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setShowOtpInput(false)}
              className="text-xs h-8"
            >
              Cancel
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
