/**
 * AdminUsersTab Component
 * 
 * Displays user management tools and user listing with administrative actions.
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { 
  Search, UserPlus, Filter, MoreHorizontal,
  Shield, User, Mail, Check, X, Lock
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface AdminUsersTabProps {
  isCurrentUser: boolean;
}

export function AdminUsersTab({ isCurrentUser }: AdminUsersTabProps) {
  const [searchQuery, setSearchQuery] = useState('');
  
  // Sample user data - would be fetched from an API in a real application
  const users = [
    { 
      id: 1, 
      name: 'Jane Cooper', 
      email: '<EMAIL>', 
      role: 'admin', 
      status: 'active', 
      lastLogin: '2 hours ago',
      avatar: '/avatars/01.png'
    },
    { 
      id: 2, 
      name: 'Michael Scott', 
      email: '<EMAIL>', 
      role: 'manager', 
      status: 'active', 
      lastLogin: '1 day ago',
      avatar: '/avatars/02.png'
    },
    { 
      id: 3, 
      name: 'Sarah Johnson', 
      email: '<EMAIL>', 
      role: 'manager', 
      status: 'inactive', 
      lastLogin: '5 days ago',
      avatar: '/avatars/03.png'
    },
    { 
      id: 4, 
      name: 'Robert Chen', 
      email: '<EMAIL>', 
      role: 'visitor', 
      status: 'pending', 
      lastLogin: 'Never',
      avatar: '/avatars/04.png'
    },
  ];

  // Get initials from name for avatar fallback
  const getInitials = (name: string): string => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Get appropriate badge style based on status
  const getStatusBadge = (status: string) => {
    switch(status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100"><Check className="h-3 w-3 mr-1" />Active</Badge>;
      case 'inactive':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100"><X className="h-3 w-3 mr-1" />Inactive</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>;
      case 'suspended':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100"><Lock className="h-3 w-3 mr-1" />Suspended</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Get appropriate icon/color based on role
  const getRoleBadge = (role: string) => {
    switch(role) {
      case 'admin':
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 hover:bg-purple-50"><Shield className="h-3 w-3 mr-1" />Admin</Badge>;
      case 'manager':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50">Manager</Badge>;
      case 'visitor':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 hover:bg-gray-50"><User className="h-3 w-3 mr-1" />Visitor</Badge>;
      default:
        return <Badge variant="outline">{role}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
            <div>
              <CardTitle className="text-xl flex items-center">
                <User className="h-5 w-5 mr-2" />
                User Management
              </CardTitle>
              <CardDescription>
                View and manage system users
              </CardDescription>
            </div>
            
            {isCurrentUser && (
              <Button className="w-full sm:w-auto">
                <UserPlus className="h-4 w-4 mr-2" />
                Add New User
              </Button>
            )}
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-4">
            {/* Search and Filter */}
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input 
                  placeholder="Search users..." 
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              
              <div className="flex gap-2">
                <Select defaultValue="all-roles">
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Filter by role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all-roles">All Roles</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="manager">Manager</SelectItem>
                    <SelectItem value="visitor">Visitor</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select defaultValue="all-status">
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all-status">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="suspended">Suspended</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            {/* Users List */}
            <div className="rounded-md border">
              <div className="relative overflow-x-auto">
                <table className="w-full text-sm text-left">
                  <thead className="bg-muted/50 text-muted-foreground">
                    <tr>
                      <th scope="col" className="px-4 py-3 font-medium">User</th>
                      <th scope="col" className="px-4 py-3 font-medium">Role</th>
                      <th scope="col" className="px-4 py-3 font-medium">Status</th>
                      <th scope="col" className="px-4 py-3 font-medium">Last Login</th>
                      <th scope="col" className="px-4 py-3 font-medium text-right">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    {users.map((user) => (
                      <tr key={user.id} className="bg-card hover:bg-muted/50 transition-colors">
                        <td className="px-4 py-3">
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={user.avatar} alt={user.name} />
                              <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{user.name}</div>
                              <div className="text-xs text-muted-foreground flex items-center">
                                <Mail className="h-3 w-3 mr-1" />
                                {user.email}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3">{getRoleBadge(user.role)}</td>
                        <td className="px-4 py-3">{getStatusBadge(user.status)}</td>
                        <td className="px-4 py-3 text-muted-foreground">{user.lastLogin}</td>
                        <td className="px-4 py-3 text-right">
                          {isCurrentUser && (
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon" className="h-8 w-8">
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">Actions</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem>View Profile</DropdownMenuItem>
                                <DropdownMenuItem>Edit User</DropdownMenuItem>
                                <DropdownMenuItem>Reset Password</DropdownMenuItem>
                                <DropdownMenuSeparator />
                                {user.status === 'active' ? (
                                  <DropdownMenuItem className="text-amber-600">Deactivate User</DropdownMenuItem>
                                ) : (
                                  <DropdownMenuItem className="text-green-600">Activate User</DropdownMenuItem>
                                )}
                                <DropdownMenuItem className="text-destructive">Delete User</DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </CardContent>
        
        <CardFooter className="border-t pt-6 flex justify-between">
          <div className="text-sm text-muted-foreground">
            Showing <span className="font-medium">{users.length}</span> of <span className="font-medium">{users.length}</span> users
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" size="sm" disabled>Previous</Button>
            <Button variant="outline" size="sm" disabled>Next</Button>
          </div>
        </CardFooter>
      </Card>
      
      {/* User Statistics Card */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">User Statistics</CardTitle>
          <CardDescription>Overview of user accounts and activity</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-muted/20 p-4 rounded-lg">
              <div className="text-2xl font-bold">124</div>
              <div className="text-sm text-muted-foreground">Total Users</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-700">98</div>
              <div className="text-sm text-green-700/70">Active Users</div>
            </div>
            <div className="bg-yellow-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-yellow-700">12</div>
              <div className="text-sm text-yellow-700/70">Pending Approvals</div>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-700">14</div>
              <div className="text-sm text-blue-700/70">New This Month</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
