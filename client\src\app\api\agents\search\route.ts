// client/src/app/api/agents/search/route.ts
// API route for search agent - Client side API (thin wrapper)

import { NextResponse } from 'next/server';
import { OpenCTXRequest } from '@/lib/ai/types/tools.types';

// Set max duration to allow more time for search API request
export const maxDuration = 60;

// POST request for the route
export async function POST(req: Request): Promise<NextResponse<OpenCTXRequest>> {
    try {
        // Extract the user query and optional filters from the request body
        const { userQuery, openCTXFilters } = await req.json();

        // Make a call to the server side API route
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/ai/search`, {
          method: 'POST',
           headers: {
            'Content-Type': 'application/json',
            },
           body: JSON.stringify({userQuery, openCTXFilters})
        });

         // Throw error if the response is not ok
        if (!response.ok) {
          const error = await response.json()
            throw new Error(`Search request failed: ${error.message}`);
        }
         // Parse the response
        const data: OpenCTXRequest = await response.json();

        // Return the results in JSON format
        return NextResponse.json(data);
    } catch (error: any) {
        console.error('Error in search api route:', error);
        // Return a NextResponse with a consistent OpenCTXRequest structure even on error
         const errorResponse: OpenCTXRequest = {
            protocol: 'openctx/v1',
            entity: 'events', // Provide a default entity or derive from the request as needed
            query: "",
             // You can add other default or empty values if needed
        };
        return NextResponse.json({ ...errorResponse, message: error.message }, { status: 500 });
    }
}