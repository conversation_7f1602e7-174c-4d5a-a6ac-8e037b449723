import React from "react";
import { UnifiedEvent } from "../adapters/eventAdapter";
import { Enhanced3DCard } from "../components/cards/Enhanced3DCard";

interface EventGridProps {
  events: UnifiedEvent[];
  onEventClick: (event: UnifiedEvent) => void;
}

export const EventGrid: React.FC<EventGridProps> = ({
  events,
  onEventClick,
}) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8 p-4">
      {events.map((event) => (
        <div key={event.id} className="group/card relative">
          <Enhanced3DCard event={event} onEventClick={onEventClick} />
        </div>
      ))}
    </div>
  );
};
