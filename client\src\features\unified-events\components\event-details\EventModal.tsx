/**
 * @module EventModal
 * @description Represents the main modal component for displaying unified event details,
 * handling ticket selection, interacting with the waiting room queue, and initiating
 * the checkout process by creating a reservation and redirecting.
 *
 * @component
 * @param {object} props - Component props.
 * @param {boolean} props.isOpen - Controls whether the modal is visible.
 * @param {function} props.onClose - Callback function to close the modal.
 * @param {UnifiedEvent | null} props.event - The event data to display. Can be null if no event is selected.
 *
 * @example
 * <EventModal isOpen={isModalOpen} onClose={handleCloseModal} event={selectedEvent} />
 *
 * @uses useTicketSelection - Hook to manage the state of selected tickets (quantities, cost, validation).
 * @uses useCheckout - Hook to orchestrate the checkout initiation process, including profile validation, queue checks, reservation creation, and redirection.
 * @uses useWaitingRoom - Hook to get detailed queue status information needed for displaying the waiting room UI.
 * @uses EventHeader - Sub-component to display the event's title, date, venue, etc.
 * @uses EventSeatmap - Sub-component to display the seatmap image or placeholder.
 * @uses EventTickets - Sub-component to display available ticket inventory and handle quantity selection.
 * @uses WaitingRoomDisplay - Sub-component shown when the event queue is active and the user needs to wait or join.
 * @uses EventCheckoutFooter - Sub-component displaying the total cost and action buttons (Close, Checkout/Reserve).
 */
import React, { useState } from "react";
import { useRouter } from 'next/navigation';
import { toast } from "sonner";

import { Dialog, DialogContent } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { UnifiedEvent } from "../../adapters/eventAdapter"; // Assuming this type exists

// Import sub-components
import { EventHeader } from "./EventHeader";
import { EventSeatmap } from "./EventSeatmap";
import { EventTickets } from "./EventTickets";
import { EventCheckoutFooter } from "./EventCheckoutFooter";
import { WaitingRoomDisplay } from "../waiting-room/WaitingRoomDisplay"; // Adjust path if needed
import { BillingAddressSelection } from "@/features/billing-address/components/BillingAddressSelection"; // Import BillingAddressSelection

// Import the hooks that now contain the logic
import { useTicketSelection } from "../../hooks/useTicketSelection";
import { useCheckout } from "../../hooks/useCheckout"; // Use our refactored hook
import { useWaitingRoom } from "../../hooks/useWaitingRoom"; // For detailed queue status if needed

// Import UI Components if needed inside this file (e.g., Button, Loader2)
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { UserCheck, AlertTriangle } from 'lucide-react'; // Icons used in footer example might be needed here if footer logic moves
import { cn } from '@/lib/utils'; // Utility for class names


interface EventModalProps {
  isOpen: boolean;
  onClose: () => void;
  event: UnifiedEvent | null;
}

export const EventModal: React.FC<EventModalProps> = ({
  isOpen,
  onClose,
  event,
}) => {
  const router = useRouter();
  
  // 🆕 ADD THIS DEBUG BLOCK
  console.log('🔍 [EventModal] Modal opened with event data:');
  console.log('- Event ID:', event?.id);
  console.log('- Event Name:', event?.name);
  console.log('- Event Source:', event?.source);
  console.log('- Event originalEvent:', event?.originalEvent);
  console.log('- Full Event Object:', event);
  
  // Add detailed logging to see what event data we're receiving
  console.log('🔍 [EventModal] Received event object:', event);
  console.log('🆔 [EventModal] Event ID being used for checkout:', event?.id);
  console.log('📊 [EventModal] Event source:', event?.source);
  console.log('🧩 [EventModal] Original event data:', event?.originalEvent?.rawEventData);
  
  // Add state for selected billing address
  const [selectedBillingAddressId, setSelectedBillingAddressId] = useState<string | undefined>(undefined);

  // Hook for managing ticket selection state
  const {
    selections,
    handleQuantityChange,
    totalCost,
    totalTickets,
    animateCost,
    hasSelections,
    hasInvalidSelections,
    canProceedInventory,
    hasManagerInventory,
  } = useTicketSelection(event, isOpen);

  // Hook for managing the checkout process initiation
  const {
    checkoutFromSelections,
    isCheckingOut,
    isQueueActive,
    isAdmitted,
    isProfileLoading,
  } = useCheckout({ eventId: event?.id, onCheckoutSuccess: onClose });

  // Hook specifically for detailed waiting room state if needed by WaitingRoomDisplay
  const {
      // status, position, totalWaiting // Uncomment if WaitingRoomDisplay needs these props
  } = useWaitingRoom(event?.id);

  // Handler for billing address selection
  const handleBillingAddressSelect = (addressId: string | undefined) => {
    setSelectedBillingAddressId(addressId);
    console.log(`🛒 Selected billing address: ${addressId}`);
  };

  // Handler for adding a new address - this could navigate to settings or open a dialog
  const handleAddNewAddress = () => {
    // This could open a dialog or navigate to settings page
    // For now, let's just show a toast message
    toast.info("Please go to Account Settings to add a new billing address");
  };

  // Don't render if no event data
  if (!event) return null;

  // Determine if the waiting room UI should be displayed
  const showWaitingRoomDisplay = isQueueActive && !isAdmitted;

  // --- Primary Checkout Action ---
  // Passed to the footer button. Calls the central checkout logic from the hook.
  const handlePrimaryCheckoutAction = () => {
    // Check if billing address is selected for manager events
    if (event.source === 'manager' && hasManagerInventory && !selectedBillingAddressId) {
      toast.error("Please select a billing address to continue checkout.");
      return;
    }
    
    // Pass selectedBillingAddressId to checkoutFromSelections
    checkoutFromSelections(selections, canProceedInventory, selectedBillingAddressId);
  };

  // --- Continue Checkout After Queue ---
  // Passed to WaitingRoomDisplay. Also calls the central checkout logic.
  const handleContinueCheckoutAfterQueue = () => {
    // Check if billing address is selected for manager events
    if (event.source === 'manager' && hasManagerInventory && !selectedBillingAddressId) {
      toast.error("Please select a billing address to continue checkout.");
      return;
    }
    
    // Pass selectedBillingAddressId to checkoutFromSelections
    checkoutFromSelections(selections, canProceedInventory, selectedBillingAddressId);
  };


  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-5xl max-h-[90vh] flex flex-col p-0 rounded-lg overflow-hidden">
        {/* Renders event title, date, location etc. */}
        <EventHeader event={event} />

        {/* Scrollable area for main content */}
        <ScrollArea className="flex-grow overflow-y-auto px-1">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 p-5">
            {/* Left Column: Seatmap and conditional Waiting Room */}
            <div className="lg:col-span-1 space-y-5 order-2 lg:order-1 flex flex-col">
              {/* Renders seatmap image */}
              <EventSeatmap event={event} />
              
              {/* Add BillingAddressSelection component for manager events */}
              {event.source === 'manager' && hasManagerInventory && (
                <div className="p-4 border rounded-lg bg-slate-50/50 space-y-2">
                  <h3 className="text-sm font-medium mb-2">Billing Address</h3>
                  <BillingAddressSelection 
                    onAddressSelect={handleBillingAddressSelect}
                    onAddNewAddress={handleAddNewAddress}
                  />
                </div>
              )}
              
              {/* Conditionally renders the waiting room UI */}
              {showWaitingRoomDisplay && event.id && (
                 <WaitingRoomDisplay
                    eventId={event.id}
                    onContinueToCheckout={isAdmitted ? handleContinueCheckoutAfterQueue : undefined}
                    // Pass other props like status, position if needed by WaitingRoomDisplay
                 />
              )}
            </div>

            {/* Right Column: Ticket selection */}
            <div className="lg:col-span-2 space-y-5 order-1 lg:order-2">
              {/* Renders inventory items and handles quantity changes */}
              <EventTickets
                event={event}
                selections={selections}
                onQuantityChange={handleQuantityChange}
              />
            </div>
          </div>
        </ScrollArea>

        {/* Footer: Displays total cost and action buttons */}
        {/* Only show footer for manager events with inventory */}
        {event.source === 'manager' && hasManagerInventory && (
            <EventCheckoutFooter
                eventId={event.id}
                hasSelections={hasSelections}
                hasInvalidSelections={hasInvalidSelections}
                canProceedInventory={canProceedInventory}
                totalTickets={totalTickets}
                totalCost={totalCost}
                animateCost={animateCost}
                isProfileLoading={isProfileLoading}
                isCreatingReservation={isCheckingOut} // Pass loading state for reservation
                onCheckout={handlePrimaryCheckoutAction} // Pass primary checkout action
                onClose={onClose}
            />
        )}
         {/* Simple footer for non-manager events */}
         {event.source !== 'manager' && (
             <div className="p-4 border-t flex justify-end gap-2">
                 {/* Replace Ticketmaster link with join as manager button */}
                 {/* {event.source === 'ticketmaster' && (
                    <Button asChild className="bg-blue-600 hover:bg-blue-700 text-white">
                         <a href="/auth/register?role=manager">Join as Manager</a>
                    </Button>
                 )} */}
                 <Button variant="outline" onClick={onClose}>Close</Button>
             </div>
         )}
      </DialogContent>
    </Dialog>
  );
};
