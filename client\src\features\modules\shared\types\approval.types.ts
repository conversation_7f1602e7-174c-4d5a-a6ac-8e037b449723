import { ManagerTrustLevel } from "./manager.types";

// Define ApprovalStatus as a union type of possible approval statuses
export type ApprovalStatus = 'pending' | 'approved' | 'rejected';

// Define the structure for an ApprovalRequest
export interface ApprovalRequest {
  id: string;
  eventId: string;
  managerId: string;
  // Removed adminId field
  status: ApprovalStatus;  // Using the strict union type
  createdAt: Date;
  updatedAt: Date;
  event: {
    title: string;
    date: Date;
    location: string;
  };
  manager: {
    name: string;
    trustLevel: ManagerTrustLevel;  // Using enum instead of string
  };
}

export interface ApprovalStats {
  pending: number;
  approvedToday: number;
  approvalRate: number;
  total: number;
}

// Default values
export const DEFAULT_APPROVAL_STATS: ApprovalStats = {
  pending: 0,
  approvedToday: 0,
  approvalRate: 0,
  total: 0
};
