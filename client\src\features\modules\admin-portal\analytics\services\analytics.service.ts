import {
  ChartData,
  TimeSeriesData,
  AnalyticMetric,
} from "@/features/modules/shared/types/analytics.types";

export const analyticsService = {
  // Revenue Data
  async getRevenueData(): Promise<ChartData> {
    // Simulate API call
    return {
      labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
      datasets: [
        {
          label: "Revenue",
          data: [30000, 45000, 35000, 50000, 49000, 60000],
          borderColor: "rgb(75, 192, 192)",
          // tension: 0.1
        },
      ],
    };
  },

  // User Growth Data
  async getUserGrowthData(): Promise<ChartData> {
    return {
      labels: ["Q1", "Q2", "Q3", "Q4"],
      datasets: [
        {
          label: "New Users",
          data: [120, 150, 180, 220],
          backgroundColor: [
            "rgba(255, 99, 132, 0.5)",
            "rgba(54, 162, 235, 0.5)",
            "rgba(255, 206, 86, 0.5)",
            "rgba(75, 192, 192, 0.5)",
          ],
        },
      ],
    };
  },

  // Event Performance Data
  async getEventPerformanceData(): Promise<TimeSeriesData[]> {
    return [
      { timestamp: "2024-01", value: 45 },
      { timestamp: "2024-02", value: 52 },
      { timestamp: "2024-03", value: 48 },
      { timestamp: "2024-04", value: 65 },
    ];
  },
};
