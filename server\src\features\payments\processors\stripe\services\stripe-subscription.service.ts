// Handles Stripe subscription checkouts and customer portal sessions.
import <PERSON><PERSON> from "stripe";
import ApiError from "@/utils/ApiError";
import { prisma } from "@/lib/prisma";
import { StripeBaseService } from "./stripe-base.service";

export class StripeSubscriptionService {

    /**
     * Creates a Stripe Checkout session for starting a new subscription.
     */
    static async createSubscriptionCheckoutSession(
        userId: string,
        priceId: string,
        successUrl: string,
        cancelUrl: string
    ): Promise<{ success: boolean; url?: string; error?: { message: string; code?: string } }> {
        const stripe = StripeBaseService.getStripeClient();
        try {
            const user = await prisma.user.findUnique({ where: { id: userId }, select: { email: true } });
            if (!user || !user.email) throw new ApiError(404, "User not found or missing email");

            const stripeCustomerId = await StripeBaseService.getOrCreateStripeCustomer(userId, user.email);

            const session = await stripe.checkout.sessions.create({
                customer: stripeCustomerId,
                payment_method_types: ["card"],
                line_items: [{ price: priceId, quantity: 1 }],
                mode: "subscription",
                subscription_data: { metadata: { userId } }, // Include userId in subscription metadata
                success_url: successUrl,
                cancel_url: cancelUrl,
                metadata: { userId }, // Also include in session metadata
            });

            return { success: true, url: session.url ?? undefined }; // Return url if exists
        } catch (error) {
            console.error(`❌ Error creating subscription checkout for user ${userId}:`, error);
            if (error instanceof ApiError) throw error; // Re-throw known errors
            if (error instanceof Stripe.errors.StripeError) {
                 return { success: false, error: { message: error.message, code: error.code } };
            }
            return { success: false, error: { message: `Subscription checkout creation failed: ${error instanceof Error ? error.message : "Unknown server error"}`}};
        }
    }

    /**
     * Creates a Stripe Billing Portal session for managing existing subscriptions.
     */
    static async createCustomerPortalSession(
        userId: string,
        returnUrl: string
     ): Promise<{ success: boolean; url?: string; error?: { message: string; code?: string } }> {
        const stripe = StripeBaseService.getStripeClient();
        try {
            const user = await prisma.user.findUnique({
                where: { id: userId },
                select: { stripeCustomerId: true },
            });
            if (!user || !user.stripeCustomerId) {
                throw new ApiError(404, "User not found or no Stripe customer associated");
            }

            const session = await stripe.billingPortal.sessions.create({
                customer: user.stripeCustomerId,
                return_url: returnUrl,
            });

             return { success: true, url: session.url ?? undefined };
        } catch (error) {
            console.error(`❌ Error creating customer portal for user ${userId}:`, error);
             if (error instanceof ApiError) throw error;
             if (error instanceof Stripe.errors.StripeError) {
                 return { success: false, error: { message: error.message, code: error.code } };
             }
             return { success: false, error: { message: `Customer portal creation failed: ${error instanceof Error ? error.message : "Unknown server error"}`}};
        }
    }
}