// server/src/features/sales/types/sales.types.ts
// Defines the structure for manager sales overview responses with detailed checkout sessions

// Minimal buyer information - be mindful of privacy concerns
export interface BuyerInfoDTO {
  userId: string;
  // Only include these if they're genuinely needed and comply with privacy regulations
  // fullName?: string; 
  // email?: string;
}

// Details of a purchased item from the checkout session
export interface PurchasedItemDetailDTO {
  inventoryId: string;
  quantity: number;
  name: string;
  price: number; // Price paid by visitor (per ticket)
  subtotal: number; // price * quantity
  section?: string;
  row?: string | number;
  seat?: string;
  attributes?: string[];
  ticketFormat?: string;
}

// Billing address information stored with the checkout session
export interface BillingAddressDTO {
  id?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  name: string;
  email: string;
}

// Represents the details of a single checkout session (purchase)
export interface CheckoutSessionDetailDTO {
  sessionId: string;
  status: string; // From CheckoutSessionStatus enum
  purchaseDate: string; // ISO string of the completed date
  buyerInfo: BuyerInfoDTO;
  billingAddress?: BillingAddressDTO; // Optional as it might not be provided
  items: PurchasedItemDetailDTO[];
  totalAmountPaid: number; // What the visitor paid in total (including fees, tax)
  subtotal: number; // Sum of item prices * quantities
  serviceFee: number; // Service fee charged
  tax?: number; // Tax amount if applicable
  currency: string; // Currency code (e.g., "USD")
  managerPayout: number; // Calculated amount manager receives for this purchase
  ticketUploadStatus?: 'pending' | 'uploaded'; // For tracking ticket upload status
}

// Represents the aggregated sales information for a single event with detailed checkout sessions
export interface ManagerEventSalesDetailsDTO {
  eventId: string;
  eventName: string;
  eventDate: string; // ISO string format
  eventVenue: string;
  eventCity?: string;
  eventCountry?: string;
  totalTicketsSold: number; // Sum from all sessions
  totalRevenue: number; // Sum of managerPayout from all sessions
  currency: string; // Currency of the listPrice/payout
  eventImageUrl?: string | null;
  sessions: CheckoutSessionDetailDTO[]; // Detailed checkout sessions
}

// Structure for the API response containing detailed sales information with pagination
export interface ManagerSalesDetailedResponse {
  data: ManagerEventSalesDetailsDTO[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number; // Total number of *events* with sales for this manager
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}