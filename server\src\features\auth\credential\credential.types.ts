// able to import the User and UserRole type from the Prisma schema
/**
 * Server-Side Custom Types: Despite Prisma's type generation, you'll often need custom types for:
Data Transfer Objects (DTOs)
Request/Response shapes
Validation schemas
 */
import { Request } from "express";
import { User, UserRole} from "@prisma/client";
import { JwtPayload } from "jsonwebtoken";

export interface TokenPayload extends JwtPayload {
  userId: string;
  email: string;
  role: UserRole;
  iat: number;
  exp: number;
}

export interface AuthRequest extends Request {
  user?: User;
}


export interface GeoData {
  ip: string;
  city?: string;
  country?: string;
  timezone?: string;
  latitude?: number | null;
  longitude?: number | null;
}
export interface RegisterDTO {
  email: string;
  password: string;
  role?: UserRole;
  fullName: string;
  mobile: string;
  geoData?: GeoData;
}

export interface LoginDTO {
  email: string;
  password: string;
 
}

export interface AuthResponse {
  user: {
    id: string;
    email: string;
    role: UserRole;
    fullName: string;
    mobile: string;
  };
  token: string;
}



export interface MobileVerificationDTO {
  mobile: string;
  verificationCode: string;
}
// -------------
