import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";

export const EventCardSkeleton = (): JSX.Element => {
  return (
    <motion.div
      className="w-full h-[450px] p-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      <Card className="relative h-full group rounded-2xl overflow-hidden border-0 bg-white dark:bg-gray-900 shadow-xl">
        {/* Image Section - 60% height */}
        <div className="absolute top-0 left-0 right-0 h-[60%] overflow-hidden bg-gray-300 animate-pulse rounded-t-2xl" />

        {/* Content Section - Fixed height and alignment */}
        <div className="absolute bottom-0 left-0 right-0 h-[45%] bg-gray-200 dark:bg-gray-800 rounded-t-3xl animate-pulse">
          <div className="h-full flex flex-col p-6">
            {/* Main Content Area */}
            <div className="flex-1 space-y-3">
              {/* Date Badge */}
              <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded-full w-1/3 animate-pulse" />

              {/* Title - Fixed height with ellipsis */}
              <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded-full w-2/3 animate-pulse" />

              {/* Event Details */}
              <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded-full w-1/2 animate-pulse" />
            </div>

            {/* Fixed Position Button Container */}
            <div className="absolute left-0 right-0 bottom-0 p-6">
              <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded-full w-full animate-pulse" />
            </div>
          </div>
        </div>
      </Card>
    </motion.div>
  );
};
