import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';

export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  const requestId = uuidv4();
  
  // This will now work because requestId is declared in auth.middleware.ts
  req.requestId = requestId;
  
  console.log(`📝 [${requestId}] ${req.method} ${req.url}`);
  
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    console.log(`✅ [${requestId}] ${req.method} ${req.url} - ${res.statusCode} - ${duration}ms`);
  });

  next();
};