// Import necessary modules and dependencies
import { Request, Response } from 'express';
import { asyncHand<PERSON> } from '@utils/asyncHandler';
import { eventSchema } from './event.types';
import { createEvent } from './event.service';
import { logger } from '@utils/logger';

// Hand<PERSON> for creating events, wrapped with async<PERSON><PERSON><PERSON> for error handling
export const createEventHandler = asyncHandler(
    async (req: Request, res: Response) => {
        logger.debug('Received create event request', { body: req.body });

        // Validate request body
        const validatedData = eventSchema.parse(req.body);
        logger.info('Event data validated successfully');

        // Create event
        const event = await createEvent(validatedData);

        logger.info('Event creation completed', { eventId: event.id });
        return res.status(201).json({
            success: true,
            data: event
        });
    }
);
