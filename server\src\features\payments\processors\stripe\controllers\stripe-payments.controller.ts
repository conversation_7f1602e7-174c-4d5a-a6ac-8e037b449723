/**
 * Stripe Payments Controller
 *
 * Handles HTTP requests related to Stripe payments.
 * Assumes authMiddleware populates req.user for authenticated routes.
 * Uses asyncHandler for robust error handling.
 *
 * Refactored to delegate logic to specific service classes (Base, PaymentIntent, Subscription, Webhook).
 * Includes a DEVELOPMENT-ONLY webhook simulation bypass in createPaymentIntentFromCheckout.
 */

import { Request, Response } from 'express';
import { asyncHandler } from '@/utils/asyncHandler';
import ApiError from '@/utils/ApiError';
// Import the newly refactored services
import { StripePaymentIntentService } from '../services/stripe-payment-intent.service'; // Handles PI creation
import { StripeSubscriptionService } from '../services/stripe-subscription.service'; // Handles subscription/portal
import { StripeWebhookService } from '../services/stripe-webhook.service'; // Handles webhooks
import { StripeBaseService } from '../services/stripe-base.service'; // Base service (needed for dev simulation client access)

// Import request/response types (ensure paths are correct)
import {
  CreatePaymentIntentFromCheckoutRequest,
  CreateSubscriptionCheckoutRequest,
  // Also potentially import response types if needed for strict typing of return values
  StripePaymentIntentResponse,
  CreateSubscriptionCheckoutResponse,
} from '../types/stripe-payments.types';
import Stripe from 'stripe'; // Import Stripe as a value for instanceof checks and type for simulation object structure


export class StripePaymentsController {

  /**
   * Create a payment intent from a checkout session.
   * POST /api/v1/payments/stripe/create-intent-from-checkout
   * Requires authMiddleware.
   *
   * In DEVELOPMENT ONLY, this also simulates the 'payment_intent.succeeded'
   * webhook event processing immediately after successful PI creation.
   */
  static createPaymentIntentFromCheckout = asyncHandler(async (req: Request, res: Response) => {
    console.log('🌟 StripePaymentsController: Hit createPaymentIntentFromCheckout');

    // --- 1. Authentication & Validation ---
    const userId = req.user?.userId;
    if (!userId) {
      console.warn('⚠️ createPaymentIntentFromCheckout reached without userId despite authMiddleware.');
      throw ApiError.unauthorized('Authentication required to create payment.');
    }

    const { sessionId, ...options } = req.body as CreatePaymentIntentFromCheckoutRequest;
    if (!sessionId) {
      throw ApiError.badRequest('Checkout session ID is required.');
    }

    console.log(`💳 Processing payment intent for checkout session: ${sessionId} by user: ${userId}`);

    // --- 2. Call the dedicated Payment Intent Service to create the PI ---
    let result: StripePaymentIntentResponse;
    try {
        result = await StripePaymentIntentService.createPaymentIntentFromCheckoutSession(
          sessionId, userId, options
        );
    } catch (error) {
        // Re-throw ApiErrors directly from the service
        if (error instanceof ApiError) {
             throw error;
        }
        // Wrap any unexpected errors
        console.error(`❌ StripePaymentIntentService failed:`, error);
        throw new ApiError(500, `Payment initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }


    // Check if the PI creation was successful and we got an ID and clientSecret
    if (!result.success || !result.paymentIntentId || !result.clientSecret) {
         // This case should ideally be caught by the try/catch above if the service throws
         console.error(`❌ PI creation service returned success=false or missing data.`);
         // If the service didn't throw but returned an error object
         if (result.error) {
             const statusCode = result.error.code === '404' ? 404 : (result.error.code === '403' ? 403 : 500); // Basic status mapping
             throw new ApiError(statusCode, typeof result.error === 'string' ? result.error : result.error.message);
         }
         // Fallback for unexpected success:false without an explicit error
         throw new ApiError(500, "Failed to initialize payment intent due to unexpected service response.");
    }


    // --- 3. DEVELOPMENT WEBHOOK SIMULATION BEGIN ---
    // This block simulates the 'payment_intent.succeeded' webhook IN DEVELOPMENT ONLY.
    // In PRODUCTION, the real Stripe webhook endpoint will trigger StripeWebhookService.handleWebhookEvent.
    // if (process.env.NODE_ENV === 'development') {
    //   console.log(`\n🧪 DEV MODE (Controller): Simulating 'payment_intent.succeeded' webhook for ${result.paymentIntentId}...`);
    //   try {
    //      // Retrieve the full PaymentIntent object using the helper service
    //      // Need the object to pass to the webhook handler
    //      const paymentIntent = await StripePaymentIntentService.retrievePaymentIntent(result.paymentIntentId);

    //      // Construct a simulated event object mimicking Stripe's structure
    //      const simulatedEvent: Stripe.Event = {
    //          id: `evt_dev_sim_${Date.now()}`, // Simulated event ID
    //          object: 'event',
    //          api_version: '2025-03-31.basil', // Use your Stripe API version
    //          created: Math.floor(Date.now() / 1000),
    //          data: {
    //              object: paymentIntent, // The actual PaymentIntent object from Stripe
    //          },
    //          livemode: false, // Important for simulation
    //          pending_webhooks: 0,
    //          request: { id: `req_dev_sim_${Date.now()}`, idempotency_key: null }, // Simulate request context
    //          type: 'payment_intent.succeeded', // The event type we are simulating
    //      };

    //      // Call the central Webhook Service's handler directly with the simulated event
    //      // This bypasses the need for a local webhook listener in dev
    //      console.log(`➡️ Triggering StripeWebhookService.handleWebhookEvent from DEV simulation...`);
    //      await StripeWebhookService.handleWebhookEvent(simulatedEvent);

    //      console.log(`✅ DEV MODE (Controller): Successfully triggered webhook processing for ${result.paymentIntentId}. PaymentRecord and inventory update *should* be created.`);

    //   } catch (bypassError) {
    //     console.error(`❌ DEV MODE (Controller): Webhook simulation failed for ${result.paymentIntentId}:`, bypassError);
    //     // Log the error but proceed normally - DO NOT re-throw here,
    //     // the main API call succeeded, only the simulation failed.
    //   }
    //   console.log('🧪 DEV MODE (Controller): Webhook simulation attempt finished.\n');
    // }
    // ===== DEVELOPMENT WEBHOOK SIMULATION END =====


    // --- 4. Send Response to Client ---
    // This response contains the clientSecret needed for the frontend confirmation
    console.log(`✅ Payment intent creation API call complete for session: ${sessionId}`);
    return res.status(200).json(result); // Return the successful result from the service
  });

  /**
   * Create a subscription checkout session.
   * POST /api/v1/payments/stripe/create-subscription-checkout
   * Requires authMiddleware.
   * Delegates to StripeSubscriptionService.
   */

  //!-------------improve the subscription logic we have seperate folder for it 
  // static createSubscriptionCheckout = asyncHandler(async (req: Request, res: Response) => {
  //   console.log('🌟 StripePaymentsController: Hit createSubscriptionCheckout');

  //   // --- 1. Authentication & Validation ---
  //   const userId = req.user?.userId;
  //   if (!userId) throw ApiError.unauthorized('Authentication required to create subscription.');

  //   // Validate request body against the type
  //   const { priceId, successUrl, cancelUrl } = req.body as CreateSubscriptionCheckoutRequest;
  //   if (!priceId || !successUrl || !cancelUrl) {
  //     throw ApiError.badRequest('Price ID, success URL, and cancel URL are required.');
  //   }

  //   console.log(`📅 Creating subscription checkout for user: ${userId}, price: ${priceId}`);

  //   // --- 2. Call the dedicated Subscription Service ---
  //   let result: CreateSubscriptionCheckoutResponse;
  //   try {
  //       result = await StripeSubscriptionService.createSubscriptionCheckoutSession(
  //         userId, priceId, successUrl, cancelUrl
  //       );
  //   } catch (error) {
  //       // Re-throw ApiErrors directly from the service
  //       if (error instanceof ApiError) {
  //            throw error;
  //       }
  //       // Wrap any unexpected errors
  //       console.error(`❌ StripeSubscriptionService.createSubscriptionCheckoutSession failed:`, error);
  //       throw new ApiError(500, `Subscription checkout failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  //   }


  //   // Handle potential error response from the service return object structure
  //    if (!result.success && result.error) {
  //         // Use a 400 for client/Stripe errors usually returned by this service method
  //         const statusCode = result.error.code === '404' ? 404 : 400; // Simple mapping
  //         throw new ApiError(statusCode, result.error.message);
  //    }
  //     // Ensure URL is present on success
  //     if (!result.success || !result.url) {
  //         // This indicates a successful service call but missing URL somehow
  //         throw new ApiError(500, "Failed to obtain subscription checkout session URL.");
  //     }


  //   console.log(`✅ Subscription checkout URL created for user: ${userId}`);
  //   // Return only the success status and the redirect URL
  //   return res.status(200).json({ success: true, url: result.url });
  // });

  /**
   * Create a customer portal session URL.
   * POST /api/v1/payments/stripe/create-customer-portal
   * Requires authMiddleware.
   * Delegates to StripeSubscriptionService.
   */
  static createCustomerPortal = asyncHandler(async (req: Request, res: Response) => {
    console.log('🌟 StripePaymentsController: Hit createCustomerPortal');

    // --- 1. Authentication & Validation ---
    const userId = req.user?.userId;
    if (!userId) throw ApiError.unauthorized('Authentication required to access customer portal.');

    const { returnUrl } = req.body;
    if (!returnUrl) throw ApiError.badRequest('Return URL is required.');

    console.log(`👤 Creating customer portal for user: ${userId}`);

    // --- 2. Call the dedicated Subscription Service ---
    let result; // Adjust type if SubscriptionService returns a specific PortalSessionResponse type
    try {
        result = await StripeSubscriptionService.createCustomerPortalSession(userId, returnUrl);
    } catch (error) {
         // Re-throw ApiErrors directly (e.g., 404 if no Stripe customer)
         if (error instanceof ApiError) {
              throw error;
         }
         // Wrap any unexpected errors
         console.error(`❌ StripeSubscriptionService.createCustomerPortalSession failed:`, error);
         throw new ApiError(500, `Customer portal access failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }


     // Handle potential error response from the service return object structure
     if (!result.success && result.error) {
          // Use 404 if user has no Stripe customer ID, otherwise 400/500 based on error code
          const statusCode = result.error.code === '404' ? 404 : (result.error.code === '400' ? 400 : 500);
          throw new ApiError(statusCode, result.error.message);
     }
      // Ensure URL is present on success
      if (!result.success || !result.url) {
          // This indicates a successful service call but missing URL somehow
          throw new ApiError(500, "Failed to obtain customer portal session URL.");
      }


    console.log(`✅ Customer portal URL created for user: ${userId}`);
    // Return only the success status and the redirect URL
    return res.status(200).json({ success: true, url: result.url });
  });

  /**
   * Handle Stripe webhooks.
   * POST /api/v1/payments/stripe/webhook
   * This endpoint is PUBLIC and does NOT use authMiddleware.
   * Delegates validation and processing to StripeWebhookService.
   */
  static handleWebhook = asyncHandler(async (req: Request, res: Response) => {
    console.log(`📣 [Controller|Webhook] Received webhook request`);

    // --- 1. Extract raw body and signature ---
    // The express.raw({ type: 'application/json' }) middleware is CRUCIAL here
    // and must be applied specifically to this route BEFORE express.json().
    const signature = req.headers['stripe-signature'] as string;
    const rawBody = req.body; // This should be a Buffer or string due to express.raw()

    console.log(`➡️ [Controller|Webhook] Signature Header Present: ${!!signature}`); // Log signature presence
    // Basic validation that raw body and signature exist
     if (!signature) {
         console.error('❌ [Controller|Webhook] Webhook Error: Missing Stripe signature header.');
         return res.status(400).send('Webhook Error: Missing stripe-signature header.');
     }
      // Check if express.raw() worked correctly
     if (rawBody === undefined || rawBody === null || typeof rawBody === 'object' && !Buffer.isBuffer(rawBody) && typeof rawBody !== 'string') {
         console.error('❌ [Controller|Webhook] Webhook Error: Raw body not available or wrong type.');
         return res.status(500).send('Webhook Error: Server configuration error regarding raw body parsing.');
     }


    console.log(`🔑 Validating webhook via ${StripeWebhookService.name}`);
    // --- 2. Call the dedicated Webhook Service for validation ---
    const { valid, event, error } = StripeWebhookService.validateWebhookEvent(rawBody, signature);

    // If validation fails, return 400
    if (!valid || !event) {
      console.error(`❌ Invalid webhook event received: ${error}`);
      return res.status(400).json({ received: false, error: error || 'Webhook validation failed.' });
    }

    console.log(`⚙️ Processing webhook event ${event.id} (Type: ${event.type}) via ${StripeWebhookService.name}`);
    // --- 3. Call the dedicated Webhook Service for event processing ---
    // Errors during processing should ideally be handled/logged INSIDE the service
    // We just need to know if the processing attempt itself finished (success: true)
    // or encountered a fundamental issue (success: false).
    let processingResult;
    try {
        processingResult = await StripeWebhookService.handleWebhookEvent(event);
    } catch (processingError) {
        // Catch errors thrown by the webhook service handlers
         console.error(`💥 Error thrown during webhook processing for event ${event.id} (Type: ${event.type}):`, processingError);
         // Return 500 to indicate the server failed to process the valid event
         return res.status(500).json({
             received: true,
             processed: false,
             error: processingError instanceof Error ? processingError.message : 'Unknown error during processing',
         });
    }


    // --- 4. Respond based on processing outcome ---
    // Webhook processing should return { success: boolean }
    if (processingResult?.success) {
        console.log(`✅ Webhook processed successfully: ${event.id}`);
        return res.status(200).json({ received: true });
    } else {
         // This case means the WebhookService.handleWebhookEvent ran without *throwing*,
         // but returned success: false. This might indicate a specific event handler
         // logged a non-critical issue but didn't throw. Still return 500 for caution.
         console.error(`⚠️ Webhook processing completed, but reported non-success for event: ${event.id}`);
         return res.status(500).json({ received: true, processed: false, error: "Webhook processing reported non-success." });
    }
  });


  /**
   * Retrieve payment details by paymentIntentId.
   * GET /api/v1/payments/stripe/payment/:paymentIntentId
   * Requires authMiddleware.
   * TODO: Implement this route using StripeBaseService.getStripeClient() or a new service method.
   */
   static getPaymentDetails = asyncHandler(async (req: Request, res: Response) => {
     console.log('🔍 StripePaymentsController: Hit getPaymentDetails');

     // --- 1. Authentication ---
     const userId = req.user?.userId;
     if (!userId) throw ApiError.unauthorized('Authentication required.');

     // --- 2. Validation ---
     const { paymentIntentId } = req.params;
     if (!paymentIntentId) throw ApiError.badRequest('Payment Intent ID is required.');

     // --- 3. Call Service (Placeholder - Implement Retrieval and Auth Check) ---
     // TODO: Create a method in StripePaymentIntentService or a new service
     // e.g., `StripePaymentService.getPaymentDetails(paymentIntentId, userId)`
     // This method would:
     //  a) Retrieve the PaymentIntent from Stripe.
     //  b) Verify the PaymentIntent's metadata (like userId or checkoutSessionId)
     //     matches the authenticated user's ID to ensure they can view it.
     //  c) Format and return relevant details.

     console.warn(`❗ getPaymentDetails route is a placeholder. Retrieving directly from Stripe for now WITHOUT user validation.`);
     const stripe = StripeBaseService.getStripeClient();
     try {
        const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

         // TODO: Add proper authorization check here!
         // Check paymentIntent.metadata.userId === userId or paymentIntent.metadata.checkoutSessionId linked to user

         // Return some basic details from the retrieved object
         return res.status(200).json({
             success: true,
             message: "Payment details retrieved (Authorization check placeholder)",
             data: {
                 id: paymentIntent.id,
                 amount: paymentIntent.amount,
                 currency: paymentIntent.currency,
                 status: paymentIntent.status,
                 created: paymentIntent.created,
                 receiptUrl: typeof paymentIntent.latest_charge === 'string' ? undefined : paymentIntent.latest_charge?.receipt_url,
                 // Add more details as needed, potentially joining with your PaymentRecord if it exists
             }
         });

     } catch (error) {
         console.error(`❌ Error retrieving payment details for PI ${paymentIntentId}:`, error);
         if (error instanceof Stripe.errors.StripeError) {
              // Specific Stripe error, likely 404 if not found
             const statusCode = error.statusCode || 400;
             throw new ApiError(statusCode, `Stripe API Error: ${error.message} (Code: ${error.code})`);
         }
         throw new ApiError(500, `Failed to retrieve payment details: ${error instanceof Error ? error.message : 'Unknown error'}`);
     }

     // return res.status(501).json({ success: false, message: "Not Implemented" }); // Alternative: return Not Implemented until fully done
   });

} // End StripePaymentsController class
























// /**
//  * Stripe Payments Controller
//  * 
//  * Handles HTTP requests related to Stripe payments.
//  * Assumes authMiddleware populates req.user for authenticated routes.
//  * Uses asyncHandler for robust error handling.
//  */

// import { Request, Response } from 'express';
// import { asyncHandler } from '@/utils/asyncHandler';
// import ApiError from '@/utils/ApiError';
// import { StripePaymentsService } from '../services/stripe-payments.service';
// import { CreatePaymentIntentFromCheckoutRequest, CreateSubscriptionCheckoutRequest, StripePaymentIntentRequest } from '../types/stripe-payments.types';
// import type Stripe from 'stripe';

// export class StripePaymentsController {
//   /**
//    * Create a payment intent from a checkout session
//    * POST /api/v1/payments/stripe/create-intent-from-checkout
//    * Requires authMiddleware to populate req.user
//    *! Includes a development-only webhook simulation bypass.
//    */
//   static createPaymentIntentFromCheckout = asyncHandler(async (req: Request, res: Response) => {
//     console.log('🌟 StripePaymentsController: Hit createPaymentIntentFromCheckout handler');

//     // --- 1. Authentication & Validation ---
//     const userId = req.user?.userId;
//     if (!userId) {
//       console.warn('⚠️ createPaymentIntentFromCheckout reached without userId despite authMiddleware.');
//       throw ApiError.unauthorized('Authentication required to create payment.');
//     }

//     const { sessionId, ...options } = req.body as CreatePaymentIntentFromCheckoutRequest & Partial<StripePaymentIntentRequest>;
//     if (!sessionId) {
//       throw ApiError.badRequest('Checkout session ID is required.');
//     }

//     console.log(`💳 Processing payment intent for checkout session: ${sessionId} by user: ${userId}`);

//     // --- 2. Create Payment Intent via Service ---
//     const result = await StripePaymentsService.createPaymentIntentFromCheckoutSession(
//       sessionId,
//       userId,
//       options
//     );

//     // ===== DEVELOPMENT WEBHOOK SIMULATION BEGIN =====
//     // This block simulates the 'payment_intent.succeeded' webhook in development.
//     // It allows PaymentRecord creation without a configured webhook endpoint.
//     // FOR PRODUCTION:
//     //   - Option A: Set NODE_ENV=production (this block will be skipped automatically).
//     //   - Option B: Comment out this entire 'if' block.
//     if (process.env.NODE_ENV === 'development' && result.success && result.paymentIntentId) {
//       console.log(`\n🧪 DEV MODE: Simulating 'payment_intent.succeeded' webhook for ${result.paymentIntentId}...`);
//       try {
//         // Retrieve the full PaymentIntent object from Stripe.
//         // We need the static stripe instance from the service.
//         // Accessing static members directly might require adjusting visibility
//         // or using a helper method if strictly private. Assuming it's accessible for dev bypass.
//         const stripeInstance = (StripePaymentsService as any).stripe;
//         if (!stripeInstance) {
//             // Ensure Stripe is initialized within the service if it hasn't been already
//              (StripePaymentsService as any).initialize();
//              const reinitializedStripeInstance = (StripePaymentsService as any).stripe;
//              if(!reinitializedStripeInstance) {
//                  throw new Error("Stripe client could not be initialized in service.");
//              }
//              console.log("🛠️ DEV MODE: Re-initialized Stripe service client for simulation.");
//              const paymentIntent = await reinitializedStripeInstance.paymentIntents.retrieve(result.paymentIntentId);

//              // Create a fake webhook event object mimicking Stripe's structure
//              const simulatedEvent: Stripe.Event = {
//                id: `evt_dev_sim_${Date.now()}`, // Simulated event ID
//                object: 'event',
//                api_version: '2025-03-31.basil', // Use your Stripe API version
//                created: Math.floor(Date.now() / 1000),
//                data: {
//                  object: paymentIntent, // The actual PaymentIntent object
//                },
//                livemode: false,
//                pending_webhooks: 0,
//                request: {
//                  id: `req_dev_sim_${Date.now()}`,
//                  idempotency_key: null,
//                },
//                type: 'payment_intent.succeeded', // The event type we are simulating
//              };

//              // Call the service's *public* static webhook handler with the simulated event
//              // This ensures all logic within the service (including calling private handlers) runs
//              await StripePaymentsService.handleWebhookEvent(simulatedEvent);

//              console.log(`✅ DEV MODE: Successfully simulated webhook processing for ${result.paymentIntentId}. PaymentRecord should be created.`);


//          } else {
//             const paymentIntent = await stripeInstance.paymentIntents.retrieve(result.paymentIntentId);

//             // Create a fake webhook event object mimicking Stripe's structure
//             const simulatedEvent: Stripe.Event = {
//               id: `evt_dev_sim_${Date.now()}`, // Simulated event ID
//               object: 'event',
//               api_version: '2025-03-31.basil', // Use your Stripe API version
//               created: Math.floor(Date.now() / 1000),
//               data: {
//                 object: paymentIntent, // The actual PaymentIntent object
//               },
//               livemode: false,
//               pending_webhooks: 0,
//               request: {
//                 id: `req_dev_sim_${Date.now()}`,
//                 idempotency_key: null,
//               },
//               type: 'payment_intent.succeeded', // The event type we are simulating
//             };

//             // Call the service's *public* static webhook handler with the simulated event
//             // This ensures all logic within the service (including calling private handlers) runs
//             await StripePaymentsService.handleWebhookEvent(simulatedEvent);

//             console.log(`✅ DEV MODE: Successfully simulated webhook processing for ${result.paymentIntentId}. PaymentRecord should be created.`);
//          }


//       } catch (bypassError) {
//         console.error(`❌ DEV MODE: Webhook simulation failed for ${result.paymentIntentId}:`, bypassError);
//         // Log the error but proceed normally - don't block the API response
//       }
//        console.log('🧪 DEV MODE: Webhook simulation attempt finished.\n');
//     }
//     // ===== DEVELOPMENT WEBHOOK SIMULATION END =====

//     // --- 3. Send Response to Client ---
//     // This response contains the clientSecret needed for the frontend confirmation
//     console.log(`✅ Payment intent creation API call complete for session: ${sessionId}`);
//     return res.status(200).json(result);
//   });

//   /**
//    * Create a subscription checkout session
//    * POST /api/v1/payments/stripe/create-subscription-checkout
//    * Requires authMiddleware to populate req.user
//    */
//   static createSubscriptionCheckout = asyncHandler(async (req: Request, res: Response) => {
//     console.log('🌟 StripePaymentsController: Hit createSubscriptionCheckout handler');

//     // Get authenticated user from req.user populated by authMiddleware
//     const userId = req.user?.userId;

//     if (!userId) {
//       console.warn('⚠️ createSubscriptionCheckout reached without userId despite authMiddleware.');
//       throw ApiError.unauthorized('Authentication required to create subscription.');
//     }

//     // Validate request body
//     const { priceId, successUrl, cancelUrl } = req.body as CreateSubscriptionCheckoutRequest;
//     if (!priceId || !successUrl || !cancelUrl) {
//       throw ApiError.badRequest('Price ID, success URL, and cancel URL are required.');
//     }

//     console.log(`📅 Creating subscription checkout for user: ${userId}, price: ${priceId}`);

//     // Call the service
//     const result = await StripePaymentsService.createSubscriptionCheckoutSession(
//       userId, 
//       priceId, 
//       successUrl,
//       cancelUrl
//     );

//     console.log(`✅ Subscription checkout created for user: ${userId}`);
//     return res.status(200).json(result);
//   });

//   /**
//    * Create a customer portal session
//    * POST /api/v1/payments/stripe/create-customer-portal
//    * Requires authMiddleware to populate req.user
//    */
//   static createCustomerPortal = asyncHandler(async (req: Request, res: Response) => {
//     console.log('🌟 StripePaymentsController: Hit createCustomerPortal handler');

//     // Get authenticated user from req.user populated by authMiddleware
//     const userId = req.user?.userId;

//     if (!userId) {
//       console.warn('⚠️ createCustomerPortal reached without userId despite authMiddleware.');
//       throw ApiError.unauthorized('Authentication required to access customer portal.');
//     }

//     // Validate request body
//     const { returnUrl } = req.body;
//     if (!returnUrl) {
//       throw ApiError.badRequest('Return URL is required.');
//     }

//     console.log(`👤 Creating customer portal for user: ${userId}`);

//     // Call the service
//     const result = await StripePaymentsService.createCustomerPortalSession(userId, returnUrl);

//     console.log(`✅ Customer portal created for user: ${userId}`);
//     return res.status(200).json(result);
//   });

//   /**
//    * Handle Stripe webhooks
//    * POST /api/v1/payments/stripe/webhook
//    * This endpoint is public and does not use authMiddleware.
//    */
//   static handleWebhook = asyncHandler(async (req: Request, res: Response) => {
//     console.log('📣 StripePaymentsController: Received webhook request');

//     const signature = req.headers['stripe-signature'] as string;
//     if (!signature) {
//       console.error('❌ Missing Stripe signature in webhook request headers.');
//       return res.status(400).send('Webhook Error: Missing stripe-signature header.');
//     }
    
//     const rawBody = req.body; 
//     if (!rawBody || typeof rawBody === 'object') { 
//         console.error('❌ Webhook Error: Raw body not available. Ensure express.raw() middleware is used correctly BEFORE express.json().');
//         return res.status(500).send('Webhook Error: Server configuration error regarding raw body parsing.');
//     }

//     const { valid, event, error } = StripePaymentsService.validateWebhookEvent(
//       rawBody,
//       signature
//     );

//     if (!valid || !event) {
//       console.error(`❌ Invalid webhook event: ${error}`);
//       return res.status(400).json({ received: false, error: error || 'Webhook validation failed.' });
//     }

//     console.log(`✅ Processing valid webhook: ${event.type} [${event.id}]`);

//     try {
//       await StripePaymentsService.handleWebhookEvent(event);
//       return res.status(200).json({ received: true });
//     } catch (processingError) {
//       console.error(`💥 Error processing webhook event ${event.id} (Type: ${event.type}):`, processingError);
//       return res.status(500).json({ 
//         received: true, 
//         processed: false, 
//         error: processingError instanceof Error ? processingError.message : 'Unknown error' 
//       });
//     }
//   });

//   /**
//    * Retrieve payment details
//    * GET /api/v1/payments/stripe/payment/:paymentIntentId
//    * Requires authMiddleware to populate req.user
//    */
//   static getPaymentDetails = asyncHandler(async (req: Request, res: Response) => {
//     console.log('🔍 StripePaymentsController: Hit getPaymentDetails handler');

//     const userId = req.user?.userId;
//     if (!userId) {
//       console.warn('⚠️ getPaymentDetails reached without userId despite authMiddleware.');
//       throw ApiError.unauthorized('Authentication required to retrieve payment details.');
//     }

//     const { paymentIntentId } = req.params;
//     if (!paymentIntentId) {
//       throw ApiError.badRequest('Payment Intent ID is required in the URL path.');
//     }

//     console.log(`🔍 Retrieving payment details for ID: ${paymentIntentId} by User: ${userId}`);

//     // Implement this method in the service if needed
//     // const result = await StripePaymentsService.retrievePaymentIntent(paymentIntentId);

//     // For now, returning a placeholder
//     console.log(`✅ Payment details retrieved successfully: ${paymentIntentId}`);
//     return res.status(200).json({ 
//       success: true,
//       message: "Payment details retrieved successfully",
//       data: { paymentIntentId }
//     });
//   });
// }
