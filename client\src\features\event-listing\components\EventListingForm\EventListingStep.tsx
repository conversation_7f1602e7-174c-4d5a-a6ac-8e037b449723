// Client-side component for rendering a step in the event listing form
"use client" // Add this directive to indicate client-side rendering
import React from "react";
import { EventListingStepProps } from "../../types/eventListing";

export const EventListingStep: React.FC<EventListingStepProps> = ({
  step,
  children,
}) => {
  return (
    <div
      className={`step-container ${step === step ? "active" : ""}`}
    >
      {children}
    </div>
  );
};
