// Displays a badge indicating the approval status of a manager's event listing.
import React from 'react';
import { Badge } from "@/components/ui/badge";
// ✨ Use the specific string literal type for safety ✨
import { EventApprovalStatusString, ApprovalStatusBadgeProps } from '../../types/inventory.types';
import { cn } from "@/lib/utils"; // For conditional classes

// Define valid statuses and their styles/text
const validStatuses: EventApprovalStatusString[] = ["PENDING", "APPROVED", "REJECTED"];

const statusStyles: Record<EventApprovalStatusString, string> = {
  PENDING: "bg-yellow-100 text-yellow-800 border-yellow-300 hover:bg-yellow-200",
  APPROVED: "bg-green-100 text-green-800 border-green-300 hover:bg-green-200",
  REJECTED: "bg-red-100 text-red-800 border-red-300 hover:bg-red-200",
};

const statusText: Record<EventApprovalStatusString, string> = {
  PENDING: "Pending",
  APPROVED: "Approved",
  REJECTED: "Rejected",
};

// Fallback style/text for unknown statuses
const fallbackStyle = "bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200";
const fallbackText = "Unknown";

export const ApprovalStatusBadge: React.FC<ApprovalStatusBadgeProps> = ({ status }) => {
  // ✨ Check if the received status is valid ✨
  const isValidStatus = status && validStatuses.includes(status);

  // ✨ Use fallback if status is invalid or missing ✨
  const currentStyle = isValidStatus ? statusStyles[status] : fallbackStyle;
  const currentText = isValidStatus ? statusText[status] : fallbackText;

  // Optional: Log a warning in development if status is invalid
  if (!isValidStatus && process.env.NODE_ENV === 'development') {
    console.warn(`ApprovalStatusBadge received invalid status: ${status}`);
  }

  return (
    <Badge variant="outline" className={cn("font-medium", currentStyle)}>
      {currentText}
    </Badge>
  );
};
