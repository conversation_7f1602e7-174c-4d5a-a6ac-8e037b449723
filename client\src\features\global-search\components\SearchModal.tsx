// SearchModal component for global event search functionality with category filtering
import { useState, useEffect } from 'react';
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogClose
} from "@/components/ui/dialog";
import { Input } from '@/components/ui/input';
import { Search as SearchIcon, X } from 'lucide-react';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { SearchSuggestions } from "./SearchSuggestions"
import { useAppDispatch } from '@/app/redux';
import { resetSearchQuery , setSearchQuery } from '@/state';
import { Loader2 } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Brain } from 'lucide-react'; // Import Brain icon
import { useSearch } from '@/lib/ai/hooks/useSearch';
import { useGlobalSearch } from '../hooks/useGlobalSearch';
import { OpenCTXRequest } from '@/lib/ai/types/tools.types';

interface SearchModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSearch: (query: string) => void;
}

export const SearchModal = ({ isOpen, onClose }: SearchModalProps) => {
    // Added useGlobalSearch hook
    const { handleSearch:handleGlobalSearch, results: globalResults, isLoading:globalIsLoading, toggleNlpSearch, isNlpSearchActive } = useGlobalSearch();
    const { performSearch, results, isLoading, error } = useSearch();
    const [searchValue, setSearchValue] = useState('');
    const [selectedSegment, setSelectedSegment] = useState<string | null>(null);
    const dispatch = useAppDispatch();

    const handleSegmentChange = (value: string) => {
        setSelectedSegment(value === "All" ? null : value);
    }

    // Updated handleSearch function to use global search when NLP is not active
    const handleSearch = (query: string) => {
      const filters = selectedSegment ? [{field: 'segment', value: selectedSegment}] : undefined;

        if(isNlpSearchActive) {
             performSearch(query, filters);
        } else {
           handleGlobalSearch(query)
           dispatch(setSearchQuery(query));
        }
    }

    // Reset selectedSegment and search value when modal is closed
    useEffect(() => {
        if (!isOpen) {
            setSelectedSegment(null);
            setSearchValue('');
            dispatch(resetSearchQuery())
        //    setIsNlpSearchActive(false);
        }
    }, [isOpen, dispatch]);

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            {/* Updated DialogContent with new styling and positioning */}
            <DialogContent className="flex flex-col w-full p-0 fixed top-0 left-0 right-0 bottom-0 sm:top-[10%] sm:inset-auto sm:left-1/2 sm:top-1/2 sm:-translate-x-1/2 sm:-translate-y-1/2 sm:max-w-xl rounded-md shadow-lg bg-white text-gray-900">
                <DialogHeader className="px-4 py-3 border-b mt-6">
                    <DialogTitle className="sr-only">Search Events</DialogTitle>
                    <div className="flex items-center gap-3">
                        <SearchIcon className="w-5 h-5 text-gray-500" />
                        <Input
                            type="text"
                            placeholder="Search events..."
                            value={searchValue}
                            onChange={(e) => {
                                setSearchValue(e.target.value);
                                handleSearch(e.target.value);
                            }}
                            className="border-none bg-gray-100 focus-visible:ring-0 flex-1"
                            autoFocus
                        />
                        <Select onValueChange={handleSegmentChange}>
                            <SelectTrigger className="w-[100px] ">
                                <SelectValue placeholder=" Category" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="All">All</SelectItem>
                                <SelectItem value="Music">Music</SelectItem>
                                <SelectItem value="Sports">Sports</SelectItem>
                                <SelectItem value="Arts">Arts</SelectItem>
                            </SelectContent>
                        </Select>

                         {/*//!not working  Updated NLP Toggle switch with Brain icon */}
                       {/* <div className="flex items-center">
                            <Switch
                                id="nlp-search-toggle"
                                checked={isNlpSearchActive}
                                onCheckedChange={toggleNlpSearch}
                                className="ml-1"
                            />
                            <Brain
                                className={`w-8 h-8 pl-2 transition-colors duration-200 ${
                                    isNlpSearchActive ? 'text-green-500' : 'text-gray-500'
                                }`}
                            />
                        </div> */}

                        {/* Updated close button to use DialogClose */}
                        {/* <DialogClose asChild>
                            <Button
                                variant="ghost"
                                size="icon"
                            >
                                <X className="w-5 h-5" />
                            </Button>
                        </DialogClose> */}
                    </div>
                </DialogHeader>

                {/* Conditionally render the loader or the search suggestions based on NLP status */}
                {isNlpSearchActive ? (
                   isLoading ? (
                        <div className="flex justify-center items-center p-4">
                            <Loader2 className="animate-spin h-6 w-6 text-gray-500" />
                        </div>
                    ) : (
                        error ?
                        <div className="flex justify-center items-center p-4 text-red-500">
                            {error}
                        </div>
                          :
                        // Updated to pass results directly without filtering
                        <SearchSuggestions
                            results={results as any}
                            selectedSegment={selectedSegment}
                            onClose={onClose}
                        />
                      )
                )
                :
                   (globalIsLoading ? (
                        <div className="flex justify-center items-center p-4">
                            <Loader2 className="animate-spin h-6 w-6 text-gray-500" />
                        </div>
                    ) : (
                        // Updated to pass globalResults directly without filtering
                        <SearchSuggestions
                           results={globalResults as any}
                             selectedSegment={selectedSegment}
                             onClose={onClose}
                       />
                  )
                )}
            </DialogContent>
        </Dialog>
    );
};