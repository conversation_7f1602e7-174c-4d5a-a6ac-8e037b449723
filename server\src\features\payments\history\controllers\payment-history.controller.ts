/**
 * Payment History Controller
 * 
 * Handles HTTP requests related to payment history.
 * Assumes authMiddleware populates req.user for authenticated routes.
 * Uses asyncHandler for robust error handling.
 */

import { Request, Response } from 'express';
import { asyncHandler } from '@/utils/asyncHandler';
import ApiError from '@/utils/ApiError';
import { PaymentHistoryService } from '../services/payment-history.service';
import { ListPaymentHistoryRequest } from '../types/payment-history.types';

export class PaymentHistoryController {
  /**
   * Get user's payment history
   * GET /api/v1/payments/history
   * Requires authMiddleware to populate req.user
   */
  static getUserPaymentHistory = asyncHandler(async (req: Request, res: Response) => {
    console.log('🌟 PaymentHistoryController: Hit getUserPaymentHistory handler');

    // Get authenticated user from req.user populated by authMiddleware
    const userId = req.user?.userId;

    if (!userId) {
      console.warn('⚠️ getUserPaymentHistory reached without userId despite authMiddleware.');
      throw ApiError.unauthorized('Authentication required to access payment history.');
    }

    // Extract query parameters for pagination, sorting, filtering
    const { page, limit, sortBy, sortDirection, status } = req.query;
    
    // Type conversion for query parameters
    const options: ListPaymentHistoryRequest = {
      page: page ? parseInt(page as string, 10) : undefined,
      limit: limit ? parseInt(limit as string, 10) : undefined,
      sortBy: (sortBy as any) || undefined,
      sortDirection: (sortDirection as any) || undefined,
      status: (status as any) || undefined,
    };

    console.log(`📋 Fetching payment history for user: ${userId}`);

    // Call the service
    const result = await PaymentHistoryService.getUserPaymentHistory(userId, options);

    console.log(`✅ Retrieved ${result.payments.length} payment records for user: ${userId}`);
    return res.status(200).json({
      success: true,
      data: result
    });
  });

  /**
   * Get details of a specific payment
   * GET /api/v1/payments/history/:paymentId
   * Requires authMiddleware to populate req.user
   */
  static getPaymentDetails = asyncHandler(async (req: Request, res: Response) => {
    console.log('🌟 PaymentHistoryController: Hit getPaymentDetails handler');

    // Get authenticated user from req.user populated by authMiddleware
    const userId = req.user?.userId;

    if (!userId) {
      console.warn('⚠️ getPaymentDetails reached without userId despite authMiddleware.');
      throw ApiError.unauthorized('Authentication required to access payment details.');
    }

    const { paymentId } = req.params;
    if (!paymentId) {
      throw ApiError.badRequest('Payment ID is required in the URL path.');
    }

    console.log(`🔍 Fetching payment details for ID: ${paymentId}, User: ${userId}`);

    // Call the service
    const payment = await PaymentHistoryService.getPaymentDetails(paymentId, userId);

    console.log(`✅ Retrieved payment details for ID: ${paymentId}`);
    return res.status(200).json({
      success: true,
      data: { payment }
    });
  });
}