// server/src/features/tickets/types/ticket.types.ts
// Defines the structure for visitor ticket data responses

import { CheckoutSessionStatus } from "@prisma/client";

// Describes a single ticket item within a completed purchase
export interface PurchasedTicketItem {
  inventoryId: string; // Reference from CheckoutSession items
  quantity: number;
  name: string; // e.g., "Section 101, Row A" or "General Admission"
  price: number; // Price per ticket at time of purchase
  section?: string;
  row?: string | number;
  // Add other relevant details parsed from CheckoutSession.items if needed
}

// Represents a single completed purchase (Checkout Session) shown to the visitor
export interface VisitorTicketDTO {
  checkoutSessionId: string;
  eventId: string; // Corresponds to ManagerEvent.id or PriorityEvent.id
  eventName: string;
  eventDate: string; // ISO string format
  eventVenue: string;
  eventCity: string;
  eventCountry: string;
  eventImageUrl?: string | null;
  purchaseDate: string; // ISO string format (CheckoutSession completedAt)
  tickets: PurchasedTicketItem[]; // Parsed from CheckoutSession.items
  totalAmount: number; // Total amount paid for this session
  currency: string;
  status: CheckoutSessionStatus; // Should always be COMPLETED
  receiptUrl?: string | null; // From CheckoutSession (if available)
  downloadUrl?: string | null; // Placeholder for future S3 link
}

// Structure for the API response containing a list of tickets and pagination info
export interface VisitorTicketsResponse {
  data: VisitorTicketDTO[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

// Structure for requesting download info (placeholder for now)
export interface TicketDownloadInfo {
    success: boolean;
    message: string;
    downloadUrl?: string | null; // The future S3 link or relevant info
    // Include other details needed if generating client-side?
    // eventName?: string;
    // tickets?: PurchasedTicketItem[];
}