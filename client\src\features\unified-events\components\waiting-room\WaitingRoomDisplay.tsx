import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>ader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>riangle, <PERSON> } from 'lucide-react';
import { cn } from '@/lib/utils';
import { QueueUserStatus } from '@/features/unified-events/types/queue.types';
import { getQueueStatusMessage } from '@/features/unified-events/helpers/queueHelpers';
import { useWaitingRoom } from '../../hooks/useWaitingRoom';

type WaitingRoomDisplayProps = {
  eventId: string;
  onContinueToCheckout?: () => void;
  className?: string;
};

export const WaitingRoomDisplay: React.FC<WaitingRoomDisplayProps> = ({
  eventId,
  onContinueToCheckout,
  className = '',
}) => {
  // Use our custom hook
  const {
    queueStatus,
    isLoading,
    isActive,
    isInQueue,
    isAdmitted,
    needsToJoin,
    position,
    status,
    totalWaiting,
    joinQueue,
    refetchStatus,
    isJoining
  } = useWaitingRoom(eventId);
  
  // If queue is not active, don't show anything
  if (!isActive) {
    return null;
  }
  
  // If user needs to join queue
  if (needsToJoin) {
    return (
      <div className={cn("bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm", className)}>
        <h3 className="font-semibold text-lg text-amber-800 mb-2 flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Waiting Room Required
        </h3>
        <p className="text-amber-700 mb-4">
          This event is in high demand. Please join the waiting room to secure your place in line for checkout.
        </p>
        <Button 
          onClick={joinQueue} 
          className="bg-amber-600 hover:bg-amber-700 text-white w-full sm:w-auto"
          disabled={isJoining}
        >
          {isJoining ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Joining...
            </>
          ) : (
            'Join Waiting Room'
          )}
        </Button>
      </div>
    );
  }
  
  // If user is in queue but not yet admitted
  if (isInQueue && !isAdmitted) {
    return (
      <div className={cn("bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm", className)}>
        <h3 className="font-semibold text-lg text-blue-800 mb-2 flex items-center gap-2">
          <Clock className="h-5 w-5" />
          You&apos;re in the Waiting Room
        </h3>
        
        <div className="flex items-center justify-between mb-4">
          <span className="text-blue-700">
            {getQueueStatusMessage(status, position)}
          </span>
          {isLoading && <Loader2 className="h-4 w-4 animate-spin text-blue-600" />}
        </div>
        
        {position && (
          <div className="w-full mb-4">
            <div className="flex justify-between mb-1 text-sm">
              <span className="text-blue-700">Position {position}</span>
              {totalWaiting && (
                <span className="text-blue-500">Total waiting: {totalWaiting}</span>
              )}
            </div>
            
            <div className="w-full bg-blue-100 rounded-full h-2.5 mb-2">
              <div
                className="bg-blue-600 h-2.5 rounded-full transition-all duration-500 ease-in-out"
                style={{ width: `${Math.max(5, Math.min(95, 100 - ((position / (totalWaiting || 10)) * 100)))}%` }}
              />
            </div>
            
            <div className="flex justify-between text-xs text-blue-600">
              <span>You are here</span>
              <span>Checkout</span>
            </div>
          </div>
        )}
        
        <p className="text-sm text-blue-600 mb-4">
          Please keep this page open. We&apos;ll automatically update your status.
        </p>
        

        <Button
          onClick={refetchStatus}
          variant="outline"
          className="text-blue-600 border-blue-300 hover:bg-blue-50 w-full sm:w-auto"
          disabled={isLoading}
        >
          {isLoading ? 'Updating...' : 'Update Status'}
        </Button>
      </div>
    );
  }
  
  // If user is admitted
  if (isAdmitted) {
    return (
      <div className={cn("bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm", className)}>
        <h3 className="font-semibold text-lg text-green-800 mb-2 flex items-center gap-2">
          <UserCheck className="h-5 w-5" />
          It&apos;s Your Turn!
        </h3>
        <p className="text-green-700 mb-4">
          You&apos;ve been admitted from the waiting room. You may now proceed to checkout.
        </p>

        
        {onContinueToCheckout && (
          <Button 
            onClick={onContinueToCheckout}
            className="bg-green-600 hover:bg-green-700 text-white w-full sm:w-auto"
          >
            Continue to Checkout
          </Button>
        )}
        <p className="text-sm text-green-600 mt-2">
          Your access will expire if you don&apos;t complete checkout soon.
        </p>
      </div>
    );
  }
  
  // Fallback case (loading initial state)
  return (
    <div className={cn("bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm flex items-center justify-center", className)}>
      <Loader2 className="h-5 w-5 animate-spin text-gray-500 mr-2" />
      <span className="text-gray-600">Checking waiting room status...</span>
    </div>
  );
};