"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { authApi } from "@/features/auth/api/credentialApi";
import { ResetPasswordForm } from "@/features/auth/components/credential/ResetPasswordForm";

// Skip the type altogether and just extract the param directly
export default function ResetPasswordPage({ params }: any) {
  const token = params.token;
  const router = useRouter();

  const [isValidToken, setIsValidToken] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Verify token on page load
  useEffect(() => {
    const verifyToken = async () => {
      try {
        // Verify token validity
        const isValid = await authApi.verifyResetToken(token);
        setIsValidToken(isValid);
      } catch (error) {
        toast.error("Invalid or expired reset link");
        setIsValidToken(false);
      } finally {
        setIsLoading(false);
      }
    };

    verifyToken();
  }, [token]);

  // Rest of component unchanged
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!isValidToken) {
    return (
      <div className="flex flex-col gap-4 justify-center items-center min-h-screen">
        <h1 className="text-2xl font-bold text-destructive">
          Invalid Reset Link
        </h1>
        <p className="text-muted-foreground">
          This password reset link is invalid or has expired.
        </p>
        <button
          onClick={() => router.push("/")}
          className="text-primary hover:underline"
        >
          Return to Home
        </button>
      </div>
    );
  }

  return (
    <div className="container flex flex-col justify-center items-center px-4 mx-auto max-w-md min-h-screen">
      <div className="space-y-6 w-full">
        <div className="space-y-2 text-center">
          <h1 className="text-2xl font-bold tracking-tight">Reset Password</h1>
          <p className="text-sm text-muted-foreground">
            Enter your new password below
          </p>
        </div>
        <ResetPasswordForm token={token} />
      </div>
    </div>
  );
}
