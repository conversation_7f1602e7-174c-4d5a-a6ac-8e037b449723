import { PendingEventApproval } from "../../types/approval.types";
import { format } from "date-fns";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface ApprovalPreviewCardProps {
  event: PendingEventApproval;
}

export const ApprovalPreviewCard = ({ event }: ApprovalPreviewCardProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{event.name}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <p>
            <strong>Date:</strong> {format(new Date(event.date), "PPP")}
          </p>
          <p>
            <strong>Venue:</strong> {event.venue}
          </p>
          <p>
            <strong>Location:</strong> {event.city}, {event.country}
          </p>
          <p>
            <strong>Category:</strong> {event.category}
          </p>
          <p>
            <strong>Source:</strong> {event.source}
          </p>
          <p>
            <strong>Manager ID:</strong> {event.managerId}
          </p>
          <div className="mt-4">
            <h4 className="font-medium text-md mb-2">Inventory Summary:</h4>
            <ScrollArea className="h-32">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Section</TableHead>
                    <TableHead>Row</TableHead>
                    <TableHead>List Price</TableHead>
                    <TableHead>Service Fee</TableHead>
                    <TableHead>Selling Pref</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {event.inventory.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.quantity}</TableCell>
                      <TableCell>{item.section}</TableCell>
                      <TableCell>{item.row}</TableCell>
                      <TableCell>${item.listPrice}</TableCell>
                      <TableCell>
                        ${item.serviceFee ? item.serviceFee : 0}
                      </TableCell>
                      <TableCell>{item.sellingPreference}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ScrollArea>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
