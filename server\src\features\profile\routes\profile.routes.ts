/**
 * Profile Routes
 * 
 * Defines all routes related to user profiles
 */
import express from 'express';
import { ProfileController } from '../controllers/profile.controller';
import { authMiddleware } from '@/middleware/auth.middleware';

const router = express.Router();

// All profile routes require authentication
router.use(authMiddleware);

// Profile routes
router.get('/', ProfileController.getProfile);
router.put('/', ProfileController.updateProfile);
router.put('/social-links', ProfileController.updateSocialLinks);

// Email verification routes
router.post('/verify-email/send', ProfileController.initiateEmailVerification);
router.post('/verify-email/confirm', ProfileController.verifyEmail);

// Mobile verification routes
router.post('/verify-mobile/send', ProfileController.initiateMobileVerification);
router.post('/verify-mobile/confirm', ProfileController.verifyMobile);

export default router;
