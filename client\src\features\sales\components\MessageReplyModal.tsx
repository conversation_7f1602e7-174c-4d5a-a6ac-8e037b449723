/**
 * MessageReplyModal Component - COMPLETELY UPGRADED
 * 
 * Modern messaging interface for managers to communicate with buyers.
 * Uses the new messaging system with real API integration.
 */

import React, { useState, useEffect, useRef } from 'react';
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  Di<PERSON>Header, 
  DialogTitle, 
  DialogDescription 
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { 
  MessageCircle, 
  User, 
  UserCheck, 
  Ticket,
  Clock,
  CheckCircle,
  AlertTriangle,
  X,
  Minimize2,
  Maximize2
} from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

// 🆕 NEW: Import messaging components and types
import MessageBubble from '@/features/messaging/components/MessageBubble';
import MessageInput from '@/features/messaging/components/MessageInput';
import { ConversationDTO, MessageDTO } from '@/features/messaging/types/messaging.types';

// 🆕 UPDATED: New interface with real messaging data
interface MessageReplyModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  ticketId: string;
  buyerName: string;
  eventName: string;
  // 🆕 NEW: Real messaging data
  conversation: ConversationDTO | null;
  messages: MessageDTO[];
  onSendMessage: (message: string) => Promise<void>;
  isLoading?: boolean;
}

export function MessageReplyModal({
  open,
  onOpenChange,
  ticketId,
  buyerName,
  eventName,
  conversation,
  messages,
  onSendMessage,
  isLoading = false,
}: MessageReplyModalProps) {
  const [isMinimized, setIsMinimized] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Auto-scroll to bottom when modal opens
  useEffect(() => {
    if (open && messagesEndRef.current) {
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    }
  }, [open]);

  // Handle message sending
  const handleSendMessage = async (message: string) => {
    if (!message.trim() || isSending) return;

    setIsSending(true);
    try {
      await onSendMessage(message);
      toast.success('Message sent successfully');
    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error('Failed to send message. Please try again.');
    } finally {
      setIsSending(false);
    }
  };

  // Get conversation status styling
  const getStatusStyling = () => {
    if (!conversation) return { color: 'gray', label: 'No conversation' };

    switch (conversation.status) {
      case 'ACTIVE':
        return { 
          color: 'blue', 
          label: 'Active conversation',
          icon: <MessageCircle className="h-4 w-4" />
        };
      case 'RESOLVED':
        return { 
          color: 'green', 
          label: 'Resolved',
          icon: <CheckCircle className="h-4 w-4" />
        };
      case 'ESCALATED':
        return { 
          color: 'red', 
          label: 'Escalated to admin',
          icon: <AlertTriangle className="h-4 w-4" />
        };
      default:
        return { 
          color: 'gray', 
          label: 'Unknown status',
          icon: <Clock className="h-4 w-4" />
        };
    }
  };

  const statusStyling = getStatusStyling();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent 
        className={cn(
          "transition-all duration-300 ease-in-out",
          isMinimized 
            ? "sm:max-w-sm h-16" 
            : "sm:max-w-4xl max-h-[90vh] h-[80vh]",
          "p-0 overflow-hidden"
        )}
      >
        {/* Header */}
        <DialogHeader className="px-6 py-4 border-b bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/50 rounded-full">
                <Ticket className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              
              <div className="flex-1 min-w-0">
                <DialogTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Conversation with {buyerName}
                </DialogTitle>
                <DialogDescription className="text-sm text-gray-600 dark:text-gray-400">
                  Ticket #{ticketId.substring(0, 8)}... • {eventName}
                </DialogDescription>
              </div>
            </div>

            {/* Header Actions */}
            <div className="flex items-center space-x-2">
              {/* Status Badge */}
              <Badge 
                variant="outline" 
                className={cn(
                  "flex items-center space-x-1",
                  statusStyling.color === 'blue' && "border-blue-200 text-blue-700 bg-blue-50",
                  statusStyling.color === 'green' && "border-green-200 text-green-700 bg-green-50",
                  statusStyling.color === 'red' && "border-red-200 text-red-700 bg-red-50"
                )}
              >
                {statusStyling.icon}
                <span>{statusStyling.label}</span>
              </Badge>

              {/* Minimize/Maximize Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMinimized(!isMinimized)}
                className="h-8 w-8 p-0"
              >
                {isMinimized ? (
                  <Maximize2 className="h-4 w-4" />
                ) : (
                  <Minimize2 className="h-4 w-4" />
                )}
              </Button>

              {/* Close Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onOpenChange(false)}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Conversation Stats (when expanded) */}
          {!isMinimized && conversation && (
            <div className="flex items-center justify-between pt-3 text-xs text-gray-500">
              <div className="flex items-center space-x-4">
                <span>{messages.length} messages</span>
                <span>{conversation.unreadCount} unread</span>
                <span>Last activity: {new Date(conversation.lastMessageDate).toLocaleString()}</span>
              </div>
              
              {/* Participants */}
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-1">
                  <User className="h-3 w-3" />
                  <span>{conversation.participants.visitor.name}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <UserCheck className="h-3 w-3" />
                  <span>{conversation.participants.manager?.name || 'You'}</span>
                </div>
              </div>
            </div>
          )}
        </DialogHeader>

        {/* Content (only shown when not minimized) */}
        {!isMinimized && (
          <div className="flex flex-col h-full">
            {/* Messages Area */}
            <div className="flex-1 min-h-0">
              {isLoading ? (
                // Loading State
                <div className="flex items-center justify-center h-full">
                  <div className="text-center space-y-3">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                    <p className="text-sm text-gray-500">Loading conversation...</p>
                  </div>
                </div>
              ) : messages.length === 0 ? (
                // Empty State
                <div className="flex items-center justify-center h-full">
                  <div className="text-center space-y-3 p-8">
                    <MessageCircle className="h-12 w-12 mx-auto text-gray-400" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                      Start the conversation
                    </h3>
                    <p className="text-sm text-gray-500 max-w-sm">
                      Send a message to {buyerName} about their ticket purchase. 
                      They&apos;ll be notified and can respond directly.
                    </p>
                  </div>
                </div>
              ) : (
                // Messages List
                <ScrollArea className="h-full px-6 py-4" ref={scrollAreaRef}>
                  <div className="space-y-4">
                    {messages.map((message, index) => {
                      const isOwn = message.senderRole === 'MANAGER';
                      const showAvatar = index === 0 || 
                        messages[index - 1]?.senderId !== message.senderId;

                      return (
                        <MessageBubble
                          key={message.id}
                          message={message}
                          isOwn={isOwn}
                          showAvatar={showAvatar}
                        />
                      );
                    })}
                    {/* Scroll anchor */}
                    <div ref={messagesEndRef} />
                  </div>
                </ScrollArea>
              )}
            </div>

            {/* Separator */}
            <Separator />

            {/* Message Input Area */}
            <div className="p-6 bg-gray-50 dark:bg-gray-900/50">
              <MessageInput
                onSendMessage={handleSendMessage}
                placeholder={`Reply to ${buyerName}...`}
                disabled={isSending || isLoading}
                isLoading={isSending}
                maxLength={1000}
                minLength={5}
              />

              {/* Footer Info */}
              <div className="flex items-center justify-between mt-3 text-xs text-gray-500">
                <div className="flex items-center space-x-3">
                  <span>💡 Tip: Be helpful and professional</span>
                  {conversation?.status === 'RESOLVED' && (
                    <Badge variant="outline" className="text-green-600 border-green-200">
                      Conversation resolved
                    </Badge>
                  )}
                </div>
                
                <div className="text-right">
                  <span>Conversation ID: {ticketId.substring(0, 8)}</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
