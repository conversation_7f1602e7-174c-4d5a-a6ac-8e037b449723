// client/src/features/sales/components/SalesDataTable.columns.tsx
"use client"

import { ColumnDef } from "@tanstack/react-table"
import { ManagerEventSalesDetailsDTO } from "../types/sales.types"
import { Button } from "@/components/ui/button"
import { ArrowUpDown, ChevronDown, ChevronRight, Eye, CalendarDays, MapPin, Users, DollarSign } from "lucide-react"
import { formatCurrency, formatDate } from "@/utils/format"
import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// This type will be used for the accessorKey to get the expander
export type EventSalesColumn = ManagerEventSalesDetailsDTO & {
  subRows?: ManagerEventSalesDetailsDTO["sessions"] // Not directly used by table, but for typing sub-component
}

export const columns: ColumnDef<EventSalesColumn>[] = [
  {
    id: "expander",
    header: () => null,
    cell: ({ row }) => {
      return row.getCanExpand() ? (
        <Button
          variant="ghost"
          size="sm"
          onClick={row.getToggleExpandedHandler()}
          className="cursor-pointer h-8 w-8 p-0"
        >
          {row.getIsExpanded() ? 
            <ChevronDown className="h-4 w-4 text-primary" /> : 
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
          }
        </Button>
      ) : null
    },
  },
  {
    accessorKey: "eventName",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="hover:bg-transparent"
        >
          Event
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const event = row.original
      
      // Calculate the total manager revenue based on session payouts for mobile display
      const totalManagerRevenue = event.sessions?.reduce(
        (total, session) => total + (session.managerPayout || 0), 
        0
      );
      
      return (
        <div className="flex flex-col min-w-0 max-w-full py-1">
          <div className="flex items-center space-x-3 min-w-0 max-w-full">
            {event.eventImageUrl && (
              <div className="relative h-10 w-16 rounded overflow-hidden shrink-0 hidden sm:block">
                <Image
                  src={event.eventImageUrl}
                  alt={event.eventName}
                  layout="fill"
                  objectFit="cover"
                  className="rounded-sm"
                />
              </div>
            )}
            <div className="space-y-1 min-w-0">
              <div className="font-medium truncate max-w-[180px] md:max-w-[250px] lg:max-w-[400px]" title={event.eventName}>
                {event.eventName}
              </div>
              {/* Mobile-only date display */}
              <div className="text-xs text-muted-foreground flex items-center sm:hidden">
                <CalendarDays className="h-3 w-3 mr-1 flex-shrink-0" />
                {formatDate(row.getValue("eventDate"))}
              </div>
            </div>
          </div>
          
          {/* Mobile-only payout display */}
          <div className="sm:hidden mt-2 ml-1">
            <div className="flex items-center text-green-700 dark:text-green-400 font-medium">
              <DollarSign className="h-3 w-3 mr-1 flex-shrink-0 text-green-600 dark:text-green-400" />
              <span className="text-sm">Your Payout: {formatCurrency(totalManagerRevenue, event.currency)}</span>
            </div>
          </div>
        </div>
      )
    },
  },
  {
    accessorKey: "eventDate",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="hover:bg-transparent hidden sm:flex"
        >
          Date
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => (
      <div className="hidden sm:flex items-center">
        <CalendarDays className="h-4 w-4 mr-2 text-muted-foreground" />
        <span>{formatDate(row.getValue("eventDate"))}</span>
      </div>
    ),
  },
  {
    accessorKey: "eventVenue",
    header: "Venue",
    enableSorting: false,
    cell: ({ row }) => (
      <div className="hidden md:flex items-start max-w-[180px] lg:max-w-[250px]">
        <MapPin className="h-4 w-4 mr-2 text-muted-foreground mt-0.5 flex-shrink-0" />
        <div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="truncate" title={row.original.eventVenue}>
                  {row.original.eventVenue}
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>{row.original.eventVenue}</p>
                {row.original.eventCity && <p className="text-xs text-muted-foreground">{row.original.eventCity}</p>}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          {row.original.eventCity && (
            <div className="text-xs text-muted-foreground truncate">
              {row.original.eventCity}
            </div>
          )}
        </div>
      </div>
    )
  },
  {
    accessorKey: "totalTicketsSoldThisEvent",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className={cn(
            "hover:bg-transparent",
            "text-right w-full justify-end hidden sm:flex"
          )}
        >
          Tickets Sold
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const event = row.original;
      
      // Calculate total tickets sold by summing quantities from all sessions and their items
      const totalTicketsSold = event.sessions?.reduce((sessionTotal, session) => {
        // For each session, sum up item quantities
        const sessionQuantity = session.items?.reduce(
          (itemTotal, item) => itemTotal + (item.quantity || 0), 
          0
        ) || 0;
        
        return sessionTotal + sessionQuantity;
      }, 0) || 0;
      
      return (
        <div className="hidden sm:flex items-center justify-end text-right font-medium">
          <Users className="h-4 w-4 mr-2 text-muted-foreground hidden lg:block" />
          {totalTicketsSold}
        </div>
      );
    },
  },
  {
    accessorKey: "totalManagerRevenueThisEvent",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="text-right w-full justify-end hover:bg-transparent hidden sm:flex"
        >
          Your Payout
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const event = row.original;
      // Calculate the total manager revenue based on session payouts
      const totalManagerRevenue = event.sessions?.reduce(
        (total, session) => total + (session.managerPayout || 0), 
        0
      );
      
      return (
        <div className="text-right font-semibold hidden sm:flex items-center justify-end">
          <DollarSign className="h-4 w-4 mr-1 flex-shrink-0 text-green-600 dark:text-green-400" />
          <span className="text-green-700 dark:text-green-400">
            {formatCurrency(totalManagerRevenue, event.currency)}
          </span>
        </div>
      );
    },
  },
  {
    id: "sessionsCount",
    header: "Purchases",
    enableSorting: false,
    cell: ({ row }) => {
      const count = row.original.sessions?.length || 0;
      return (
        <div className="text-center hidden md:block">
          <Badge variant={count > 0 ? "default" : "outline"} className="px-3 py-1">
            {count}
          </Badge>
        </div>
      );
    }
  }
]