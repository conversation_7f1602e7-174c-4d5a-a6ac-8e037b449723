'use client';
import React from 'react';
import { UnifiedEvent } from '../adapters/eventAdapter';
import { Enhanced3DCard } from '../components/cards/Enhanced3DCard';

interface UnifiedEventListProps {
  events: UnifiedEvent[];
  onEventClick: (event: UnifiedEvent) => void;
}

// UnifiedEventList component to display a grid of enhanced 3D event cards
export const UnifiedEventList: React.FC<UnifiedEventListProps> = ({ 
  events, 
  onEventClick 
}) => {
  return (
    // Keep the same grid layout from the original EventList
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8 p-4">
      {events.map((event) => (
        <div key={event.id} className="group/card relative">
          <Enhanced3DCard event={event} onEventClick={onEventClick} />
        </div>
      ))}
    </div>
  );
};
