/**
 * Frontend Messaging Types
 * 
 * Client-side TypeScript interfaces for the ticket messaging system.
 * These types match the backend messaging API structure.
 */

/**
 * User roles in the messaging system
 */
export enum UserRole {
  VISITOR = 'VISITOR',
  MANAGER = 'MANAGER',
  ADMIN = 'ADMIN'
}
// ============================================================================
// ENUMS (Match backend enums)
// ============================================================================

export enum MessageStatus {
  UNREAD = 'UNREAD',
  READ = 'READ',
  RESOLVED = 'RESOLVED',
  ARCHIVED = 'ARCHIVED'
}

// ============================================================================
// REQUEST TYPES (For API calls)
// ============================================================================

/**
 * Request payload for sending a new message
 */
export interface SendMessageRequest {
  checkoutSessionId: string;
  message: string;
  conversationId?: string;
  parentMessageId?: string;
}

/**
 * Request payload for getting conversation messages
 */
export interface GetConversationRequest {
  page?: number;
  limit?: number;
  includeResolved?: boolean;
  since?: string; // ISO date string
}

/**
 * Request payload for admin message queries
 */
export interface AdminMessagesRequest {
  page?: number;
  limit?: number;
  status?: MessageStatus;
  senderRole?: UserRole;
  eventId?: string;
  dateFrom?: string;
  dateTo?: string;
  isEscalated?: boolean;
}

// ============================================================================
// RESPONSE DATA TRANSFER OBJECTS (DTOs)
// ============================================================================

/**
 * Individual message data from API
 */
export interface MessageDTO {
  id: string;
  checkoutSessionId: string;
  senderId: string;
  senderRole: UserRole;
  senderName: string; // Display name like "Buyer #abc123"
  message: string;
  status: MessageStatus;
  conversationId?: string;
  parentMessageId?: string;
  isEscalated?: boolean;
  escalatedBy?: string;
  escalatedAt?: string;
  createdAt: string; // ISO string
  updatedAt: string; // ISO string
  readAt?: string; // ISO string
  avatarUrl?: string;
  metadata?: {
    ipAddress?: string;
    userAgent?: string;
    [key: string]: any;
  };
}

/**
 * Conversation summary with messages
 */
export interface ConversationDTO {
  checkoutSessionId: string;
  ticketId: string; // Shortened display ID
  eventName?: string;
  buyerName: string;
  managerName?: string;
  messages: MessageDTO[];
  unreadCount: number;
  lastMessageDate: string;
  status: 'ACTIVE' | 'RESOLVED' | 'ESCALATED';
  participants: {
    visitor: { id: string; name: string };
    manager?: { id: string; name: string };
    admin?: { id: string; name: string };
  };
}

/**
 * Conversation summary for listings
 */
export interface ConversationSummaryDTO {
  checkoutSessionId: string;
  ticketId: string;
  lastMessageDate: string;
  messageCount: number;
  unreadCount: number;
  latestMessage: {
    message: string;
    senderRole: UserRole;
    createdAt: string;
  } | null;
  eventName?: string;
  buyerName?: string;
  managerName?: string;
}

/**
 * Admin conversation overview
 */
export interface AdminConversationOverviewDTO {
  id: string;
  ticketId: string;
  eventName?: string;
  buyerDisplayName: string;
  managerDisplayName?: string;
  lastMessageDate: string;
  status: 'ACTIVE' | 'RESOLVED' | 'ESCALATED';
  messageCount: number;
  unreadCount: number;
  escalated: boolean;
}

// ============================================================================
// API RESPONSE WRAPPERS
// ============================================================================

/**
 * Standard API response structure
 */
export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

/**
 * Paginated response structure
 */
export interface PaginatedResponse<T> {
  success: boolean;
  message: string;
  data: {
    items: T[];
    pagination: {
      currentPage: number;
      totalPages: number;
      totalItems: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
    };
  };
}

/**
 * Response for sending a message
 */
export interface SendMessageResponse extends ApiResponse<MessageDTO> {}

/**
 * Response for getting a conversation
 */
export interface GetConversationResponse extends ApiResponse<{
  conversation: ConversationDTO;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}> {}

/**
 * Response for getting conversation summaries
 */
export interface GetConversationsResponse extends PaginatedResponse<ConversationSummaryDTO> {}

/**
 * Response for admin conversations
 */
export interface AdminConversationsResponse extends ApiResponse<{
  conversations: ConversationDTO[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  summary: {
    totalConversations: number;
    unreadMessages: number;
    escalatedConversations: number;
    activeConversations: number;
  };
}> {}

// ============================================================================
// COMPONENT PROP TYPES
// ============================================================================

/**
 * Props for MessageBubble component
 */
export interface MessageBubbleProps {
  message: MessageDTO;
  isOwn: boolean; // Is this message from the current user?
  showAvatar?: boolean;
  onMarkAsRead?: (messageId: string) => void;
}

/**
 * Props for ConversationList component
 */
export interface ConversationListProps {
  conversations: ConversationSummaryDTO[];
  onSelectConversation: (checkoutSessionId: string) => void;
  selectedConversationId?: string;
  isLoading?: boolean;
}

/**
 * Props for MessageInput component
 */
export interface MessageInputProps {
  onSendMessage: (message: string) => void;
  placeholder?: string;
  disabled?: boolean;
  isLoading?: boolean;
  maxLength?: number;
  minLength?: number;
}

// ============================================================================
// HOOK RETURN TYPES
// ============================================================================

/**
 * Return type for useConversation hook
 */
export interface UseConversationResult {
  conversation: ConversationDTO | null;
  messages: MessageDTO[];
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  sendMessage: (message: string) => Promise<void>;
  markAsRead: (messageId: string) => Promise<void>;
  resolveConversation: () => Promise<void>;
  refetch: () => Promise<void>;
}

/**
 * Return type for useConversations hook (for listings)
 */
export interface UseConversationsResult {
  conversations: ConversationSummaryDTO[];
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  goToPage: (page: number) => void;
  refetch: () => Promise<void>;
}

/**
 * Return type for useAdminMessaging hook
 */
export interface UseAdminMessagingResult {
  conversations: ConversationDTO[];
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  summary: {
    totalConversations: number;
    unreadMessages: number;
    escalatedConversations: number;
    activeConversations: number;
  };
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  escalateConversation: (checkoutSessionId: string) => Promise<void>;
  sendAdminMessage: (checkoutSessionId: string, message: string) => Promise<void>;
  goToPage: (page: number) => void;
  refetch: () => Promise<void>;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Message filtering options
 */
export interface MessageFilters {
  status?: MessageStatus;
  senderRole?: UserRole;
  dateRange?: {
    start: Date;
    end: Date;
  };
  searchTerm?: string;
}

/**
 * Display helper for message sender
 */
export interface MessageSender {
  id: string;
  role: UserRole;
  displayName: string;
  avatarUrl?: string;
}

/**
 * Real-time message event types (for future WebSocket integration)
 */
export interface MessageEvent {
  type: 'NEW_MESSAGE' | 'MESSAGE_READ' | 'CONVERSATION_RESOLVED';
  data: MessageDTO | ConversationDTO;
  timestamp: string;
}