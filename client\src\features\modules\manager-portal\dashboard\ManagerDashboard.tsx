"use client";

import { motion } from "framer-motion";
import { SalesMetricsGrid } from "./components/SalesMetricsGrid";
import { EventManagementBento } from "./components/EventManagementBento";
import { InventoryBento } from "./components/InventoryBento";
import { RevenueChartBento } from "./components/RevenueChartBento";
import { GridLayout } from "../../shared/layout/GridLayout";

export const ManagerDashboard = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-8"
    >
      {/* Dashboard Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold tracking-tight">Manager Dashboard</h1>
      </div>

      {/* Sales Metrics */}
      <SalesMetricsGrid />

      {/* Main Content Grid */}
      <GridLayout className="grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
        <EventManagementBento />
        <InventoryBento />
        <RevenueChartBento />
      </GridLayout>
    </motion.div>
  );
};
