/**
 * This module defines the global state slice for the application using Redux Toolkit.
 * It manages the state of the sidebar collapse, dark mode settings, selected search event, search query, and event filters.
 * The slice includes actions to update these settings and the corresponding reducer.
 */

import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Context } from "@/types/openctx.types";

// --------------- State Interface ---------------
export interface InitialStateTypes {
  isSidebarCollapsed: boolean;
  isDarkMode: boolean;
  selectedSearchEvent: Context | null;
  searchQuery: string;
  // Add event filters
  eventFilters: {
    segment: string;
    city: string;
    startDate: string | null;
    keyword: string;
  };
}

// --------------- Initial State ---------------
const initialState: InitialStateTypes = {
  isSidebarCollapsed: false,
  isDarkMode: false,
  selectedSearchEvent: null,
  searchQuery: '',
  // Initialize event filters
  eventFilters: {
    segment: 'all',
    city: '',
    startDate: null,
    keyword: ''
  }
};

// --------------- Global Slice ---------------
export const globalSlice = createSlice({
  name: "global",
  initialState,
  reducers: {
    // Action to update the sidebar collapsed state
    setIsSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.isSidebarCollapsed = action.payload;
    },
    // Action to update the dark mode state
    setIsDarkMode: (state, action: PayloadAction<boolean>) => {
      state.isDarkMode = action.payload;
    },
    // Action to update the selected search event
    setSelectedSearchEvent: (state, action: PayloadAction<Context | null>) => {
      state.selectedSearchEvent = action.payload;
    },
    // Action to update the search query
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    // Action to reset the search query
    resetSearchQuery: (state) => {
      state.searchQuery = '';
    },
    
    // Add new reducers for filters
    setEventFilter: (
      state, 
      action: PayloadAction<{ key: string; value: string | null }>
    ) => {
      const { key, value } = action.payload;
      // Ensure eventFilters is defined; if not, initialize it with default values.
      if (!state.eventFilters) {
        state.eventFilters = {
          segment: 'all',
          city: '',
          startDate: null,
          keyword: ''
        };
      }
      // @ts-ignore - Handle dynamic key assignment
      state.eventFilters[key] = value;
    },
    setAllEventFilters: (
      state,
      action: PayloadAction<Partial<InitialStateTypes['eventFilters']>>
    ) => {
      state.eventFilters = {
        ...state.eventFilters,
        ...action.payload
      };
    },
    resetEventFilters: (state) => {
      state.eventFilters = initialState.eventFilters;
    }
  },
});

// Export individual action creators for use in components
export const { 
  setIsSidebarCollapsed, 
  setIsDarkMode, 
  setSelectedSearchEvent,
  setSearchQuery,
  resetSearchQuery,
  // New filter actions
  setEventFilter,
  setAllEventFilters,
  resetEventFilters
} = globalSlice.actions;

// Export the reducer to be included in the store
export default globalSlice.reducer;