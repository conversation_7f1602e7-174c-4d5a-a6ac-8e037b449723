/**
 * Messaging Hooks - FIXED: Create conversation on first message
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { toast } from 'sonner';
import { messagingApi } from '../api/messagingApi';
import { 
  ConversationDTO, 
  MessageDTO, 
  SendMessageRequest 
} from '../types/messaging.types';

// ============================================================================
// VISITOR SIDE - Ticket Conversation Hook (FIXED)
// ============================================================================

export function useVisitorTicketConversation(checkoutSessionId: string) {
  const queryClient = useQueryClient();
  const [hasAttemptedSend, setHasAttemptedSend] = useState(false);
  
  // 🔧 FIX: Only fetch conversation AFTER we know it might exist
  const {
    data: conversation,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery({
    queryKey: ['conversation', checkoutSessionId],
    queryFn: () => messagingApi.getConversation(checkoutSessionId),
    enabled: !!checkoutSessionId && hasAttemptedSend, // 🔧 Only fetch after first send attempt
    retry: 1,
    staleTime: 30000,
  });

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: (messageData: SendMessageRequest) => 
      messagingApi.sendMessage(messageData),
    onSuccess: () => {
      // Mark that we've attempted to send (so we can start fetching)
      setHasAttemptedSend(true);
      
      // Invalidate and refetch conversation
      queryClient.invalidateQueries({ queryKey: ['conversation', checkoutSessionId] });
      toast.success('Message sent successfully');
    },
    onError: (error: any) => {
      console.error('Failed to send message:', error);
      toast.error('Failed to send message. Please try again.');
    },
  });

  // Send message function
  const sendMessage = async (message: string): Promise<void> => {
    const messageData: SendMessageRequest = {
      checkoutSessionId,
      message: message.trim(),
    };
    
    console.log('📤 Sending first message, will create conversation:', messageData);
    await sendMessageMutation.mutateAsync(messageData);
  };

  return {
    conversation: conversation?.conversation || null,
    messages: conversation?.conversation.messages || [],
    isLoading: isLoading && hasAttemptedSend, // Only show loading if we're actually fetching
    isError: isError && hasAttemptedSend, // Only show error if we've tried fetching
    error,
    sendMessage,
    isSending: sendMessageMutation.isPending,
    unreadCount: conversation?.conversation.unreadCount || 0,
    hasMessages: (conversation?.conversation.messages || []).length > 0,
    refetch,
    // New state to track if we should fetch
    shouldFetch: hasAttemptedSend,
  };
}

// ============================================================================
// MANAGER SIDE - Buyer Conversation Hook (UPDATED)
// ============================================================================

export function useManagerBuyerConversation(checkoutSessionId: string) {
  const queryClient = useQueryClient();
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  // Manager side should fetch immediately (they respond to existing conversations)
  const {
    data: conversation,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery({
    queryKey: ['manager-conversation', checkoutSessionId],
    queryFn: () => messagingApi.getConversation(checkoutSessionId),
    enabled: !!checkoutSessionId,
    retry: 1,
    staleTime: 30000,
  });

  // Send reply mutation
  const sendReplyMutation = useMutation({
    mutationFn: (messageData: SendMessageRequest) => 
      messagingApi.sendMessage(messageData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['manager-conversation', checkoutSessionId] });
      toast.success('Reply sent successfully');
    },
    onError: (error: any) => {
      console.error('Failed to send reply:', error);
      toast.error('Failed to send reply. Please try again.');
    },
  });

  const sendReply = async (message: string): Promise<void> => {
    const messageData: SendMessageRequest = {
      checkoutSessionId,
      message: message.trim(),
    };
    
    await sendReplyMutation.mutateAsync(messageData);
  };

  return {
    conversation: conversation?.conversation || null,
    messages: conversation?.conversation.messages || [],
    isLoading,
    isError, 
    error,
    sendReply,
    isSending: sendReplyMutation.isPending,
    unreadCount: conversation?.conversation.unreadCount || 0,
    hasMessages: (conversation?.conversation.messages || []).length > 0,
    needsAttention: (conversation?.conversation.unreadCount || 0) > 0,
    isModalOpen,
    setIsModalOpen,
    refetch,
  };
}

// ============================================================================
// 🆕 NEW: Missing hooks that PurchaseAccordion expects
// ============================================================================

/**
 * Hook to get unread message count for a checkout session
 */
export function useUnreadMessageCount(checkoutSessionId: string) {
  const { data, isLoading } = useQuery({
    queryKey: ['unread-count', checkoutSessionId],
    queryFn: () => messagingApi.getConversation(checkoutSessionId),
    enabled: !!checkoutSessionId,
    staleTime: 10000, // 10 seconds
    select: (data) => data?.conversation.unreadCount || 0,
  });

  return {
    unreadCount: data || 0,
    isLoading,
  };
}

// ============================================================================
// MESSAGE VALIDATION HOOK
// ============================================================================

export function useMessageValidation() {
  const validateMessage = (message: string, minLength = 10, maxLength = 1000) => {
    const trimmed = message.trim();
    
    if (trimmed.length === 0) {
      return { isValid: false, error: 'Message cannot be empty' };
    }
    
    if (trimmed.length < minLength) {
      return { 
        isValid: false, 
        error: `Message must be at least ${minLength} characters` 
      };
    }
    
    if (trimmed.length > maxLength) {
      return { 
        isValid: false, 
        error: `Message cannot exceed ${maxLength} characters` 
      };
    }
    
    return { isValid: true, error: null };
  };

  const formatDisplayName = (role: string, userId: string) => {
    const shortId = userId.substring(0, 8);
    switch (role) {
      case 'VISITOR':
        return `Buyer #${shortId}`;
      case 'MANAGER':
        return `Seller #${shortId}`;
      case 'ADMIN':
        return `Support #${shortId}`;
      default:
        return `User #${shortId}`;
    }
  };

  const formatMessageTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
      
      if (diffInHours < 1) {
        return 'Just now';
      } else if (diffInHours < 24) {
        return `${Math.floor(diffInHours)}h ago`;
      } else {
        return date.toLocaleDateString();
      }
    } catch (error) {
      return 'Unknown time';
    }
  };

  return {
    validateMessage,
    formatDisplayName,
    formatMessageTime,
  };
}