/**
 * Common payment types shared across different payment processors
 * and payment-related features.
 */

/**
 * Standard currency codes supported by the application
 */
export type SupportedCurrency = 'USD' | 'EUR' | 'GBP' | 'INR' | 'CAD' | 'AUD';

/**
 * Payment status enumeration
 */
export enum PaymentStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  SUCCEEDED = 'SUCCEEDED',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED',
  PARTIALLY_REFUNDED = 'PARTIALLY_REFUNDED',
  CANCELED = 'CANCELED',
}

/**
 * Base payment item interface
 * Represents an item in a payment (e.g., ticket, merchandise)
 */
export interface BasePaymentItem {
  id: string;
  name: string;
  description?: string;
  quantity: number;
  unitAmount: number; // Amount in cents/smallest currency unit
  currency: SupportedCurrency;
}

/**
 * Base customer information for payments
 */
export interface BasePaymentCustomer {
  id?: string; // Customer ID if known
  email: string;
  name?: string;
  metadata?: Record<string, string>;
}

/**
 * Base payment intent request
 * Common properties for creating a payment intent across providers
 */
export interface BasePaymentIntentRequest {
  amount: number; // Total amount in cents/smallest currency unit
  currency: SupportedCurrency;
  description?: string;
  customerId?: string; // Optional if creating a one-time payment
  customerEmail?: string; // Optional email for guest checkout
  metadata?: Record<string, string>; // Additional data to attach to the payment
  items?: BasePaymentItem[]; // Optional line items
  receiptEmail?: string;
}