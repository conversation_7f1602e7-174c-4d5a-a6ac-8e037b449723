import { Request, Response, NextFunction } from 'express';
import { AnyZodObject, ZodError } from 'zod';  // Update import

import geoip from 'geoip-lite';


export const validateSchema = (schema: AnyZodObject) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      await schema.parseAsync(req.body);
      console.log("schemaValidation success")
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        console.log("schemaValidaion Error")
        res.status(400).json({ errors: error.errors });
        return;
      }
      next(error);
    }
  };
};


export const enrichRequestWithGeoData = (
  req: Request,
  res: Response, 
  next: NextFunction
): void => {
  // Enhanced IP detection with proxy handling
  const ip = (
    req.headers['cf-connecting-ip'] ||  // Cloudfare
    req.headers['x-real-ip'] ||        // Nginx
    req.headers['x-client-ip'] ||      // Apache
    (typeof req.headers['x-forwarded-for'] === 'string' 
      ? req.headers['x-forwarded-for'].split(',')[0]
      : Array.isArray(req.headers['x-forwarded-for'])
        ? req.headers['x-forwarded-for'][0]
        : null) ||
    req.socket.remoteAddress ||
    '127.0.0.1'
  ) as string;

  // Clean IPv6 localhost to IPv4
  const cleanIp = ip.replace('::ffff:', '');
//! tracking with local host not possible
 // Add this line for development testing
 const testIP = '*******'; // Google's DNS IP for testing
  
 // Use testIP instead of cleanIp during development
 const geoData = geoip.lookup(testIP); // Change this to cleanIp when deploying



  //-----------------production-----------------------
  console.log('Raw IP:', ip);
  console.log('Cleaned IP:', cleanIp);

  // const geoData = geoip.lookup(cleanIp);
  // console.log('GeoIP Lookup Result:', geoData)
  
  // Lookup with error handling
  try {
  
    req.body.geoData = {
      ip: cleanIp,
      city: geoData?.city || null,
      country: geoData?.country || null,
      timezone: geoData?.timezone || null,
      latitude: geoData?.ll?.[0] || null,
      longitude: geoData?.ll?.[1] || null,
      region: geoData?.region || null,
      range: geoData?.range || null,
      eu: geoData?.eu || null,
      metro: geoData?.metro || null,
      area: geoData?.area || null
    };

    // console.log(`GeoIP Data for ${cleanIp}:`, req.body.geoData);
    console.log(`GeoIP Data for ${testIP}:`, req.body.geoData);
  } catch (error) {
    console.error('GeoIP Lookup failed:', error);
    // Proceed with null values rather than failing the request
    // req.body.geoData = { ip: cleanIp };

    //----------------------testing------------------
    console.error('GeoIP Lookup failed:', error);
    req.body.geoData = { ip: testIP }; // Change back to cleanIp when deploying
  }

  next();
};



/*
PS C:\Users\<USER>\Desktop\Fanseatmaster\server> ls -l node_modules/geoip-lite/data


    Directory: C:\Users\<USER>\Desktop\Fanseatmaster\server\node_modules\geoip-lite\data


Mode                 LastWriteTime         Length Name
----                 -------------         ------ ----
-a----        26-11-2024  04:10 PM             97 city.checksum
-a----        26-11-2024  04:06 PM            100 country.checksum
-a----        26-11-2024  04:07 PM        6896120 geoip-city-names.dat
-a----        26-11-2024  04:08 PM       77943336 geoip-city.dat
-a----        26-11-2024  04:10 PM       80508864 geoip-city6.dat
-a----        26-11-2024  04:06 PM        5023930 geoip-country.dat
-a----        26-11-2024  04:06 PM       16649086 geoip-country6.dat


*/