// Component for filtering the manager inventory table.
import React from 'react';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';
import { ManagerInventoryFilterProps } from '../types/inventory.types'; // Assuming props are defined here

export const ManagerInventoryFilter: React.FC<ManagerInventoryFilterProps> = ({ filterText, onFilterChange }) => {
  return (
    <div className="relative w-full md:w-1/3">
      <Search className="absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
      <Input
        type="search"
        placeholder="Filter by name, venue, city..."
        value={filterText}
        onChange={(e) => onFilterChange(e.target.value)}
        className="pl-8 py-2" // Add padding for the icon
      />
    </div>
  );
};
