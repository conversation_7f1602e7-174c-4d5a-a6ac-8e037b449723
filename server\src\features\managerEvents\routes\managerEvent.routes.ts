import { Router } from "express";
import { ManagerEventController } from "../controllers/managerEvent.controller";
// ✨ Import the new inventory controller ✨
import { ManagerInventoryController } from "../controllers/managerInventory.controller";

// ✨ Assuming authMiddleware is correctly defined and exported ✨
import { authMiddleware } from '@/middleware/auth.middleware'; // Adjust path if necessary
import { inventoryViewCounter } from "@/features/queue/middleware/inventoryViewCounter";

const router = Router();

// POST endpoint for creating a manager event inventory record (Existing)
// This might also need authMiddleware depending on requirements
router.post("/",  ManagerEventController.create);

// ✨ GET endpoint for fetching the authenticated manager's inventory ✨
// Protected by authentication middleware
router.get(
    "/inventory",
    authMiddleware, // Ensure this middleware attaches req.user.id
    ManagerInventoryController.getManagerInventory
);

//?-------- Route to get current inventory with real-time availability
router.get(
    '/:eventId/inventory',
    // Apply the inventory view counter middleware here
    inventoryView<PERSON>ounter, 
    ManagerInventoryController.getEventInventory
  );

// ✨ New Route: PATCH endpoint for toggling isActive status ✨
router.patch(
    "/inventory/:id/toggle-active", // Route takes event ID as parameter
    authMiddleware,
    ManagerInventoryController.toggleActive
);

// ✨ New Route: DELETE endpoint for deleting an event listing ✨
router.delete(
  "/inventory/:id", // Route takes event ID as parameter
  authMiddleware,
  ManagerInventoryController.deleteEvent
);

// ✨ New Route: PATCH endpoint for updating the inventory list ✨
router.patch(
    "/inventory/:id", // Route takes event ID as parameter
    authMiddleware,
    ManagerInventoryController.updateEventInventory // Map to the new controller method
);

export default router;
