/**
 * Demand Detection Types Module
 * 
 * Defines types related to event demand calculation and threshold detection
 * for automatic queue activation/deactivation.
 */

/**
 * Configuration for demand detection thresholds
 */
export interface DemandDetectionConfig {
  // Demand score that triggers queue activation
  ACTIVATION_THRESHOLD: number;
  
  // Lower demand score that triggers queue deactivation (hysteresis)
  DEACTIVATION_THRESHOLD: number;
  
  // Weight multiplier for checkout attempts in the demand formula
  CHECKOUT_WEIGHT: number;
  
  // Redis key expiry time in seconds (for rate calculation window)
  COUNTER_EXPIRY: number;
  
  // Minimum time a queue must remain active once activated (in seconds)
  MIN_ACTIVE_DURATION: number;
}

/**
 * Counter data from Redis
 */
export interface EventCounters {
  viewCount: number;
  checkoutCount: number;
}

/**
 * Result of demand score calculation
 */
export interface DemandCalculationResult {
  eventId: string;
  score: number;
  isHighDemand: boolean;
  counters: EventCounters;
  availableInventory: number;
}

/**
 * Demand detection service interface
 */
export interface DemandDetectionServiceInterface {
  incrementViewCounter(eventId: string): Promise<void>;
  incrementCheckoutCounter(eventId: string): Promise<void>;
  getEventCounters(eventId: string): Promise<EventCounters>;
  calculateDemandScore(eventId: string): Promise<DemandCalculationResult>;
  checkAndTriggerQueue(eventId: string): Promise<boolean>;
  checkAndDeactivateQueue(eventId: string): Promise<boolean>;
}
