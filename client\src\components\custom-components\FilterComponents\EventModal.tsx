import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog"
import { TmEvent } from '@/features/tm_events/types/tm.types';
import { format } from 'date-fns';
import Image from 'next/image';
import { X } from 'lucide-react';

interface EventModalProps {
    isOpen: boolean;
    onClose: () => void;
    event: TmEvent | null;
}

export const EventModal: React.FC<EventModalProps> = ({isOpen, onClose, event}) => {
    if (!event) return null;

    const formattedDate = event.startDateTime ? format(new Date(event.startDateTime), 'MMM d, yyyy h:mm a') : 'Not Available';
    const availableSeats = 150;
    
    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white w-[95%] max-w-5xl transition-all duration-300 max-h-[90vh] overflow-y-auto rounded-2xl shadow-2xl border border-gray-700">
                <DialogHeader className="px-8 py-6 border-b border-gray-700/50 relative flex items-center justify-between">
                    <DialogTitle className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
                        {event?.name}
                    </DialogTitle>
                    {/* <DialogClose className="absolute right-8 top-6">
                        <X className="h-6 w-6 text-gray-400 hover:text-white transition-colors" />
                    </DialogClose> */}
                </DialogHeader>

                <div className="p-8 space-y-8">
                    {event?.primaryImage && (
                        <div className="relative w-full h-[400px] overflow-hidden rounded-2xl group">
                            <Image
                                src={event.primaryImage}
                                alt={event.name}
                                className="object-cover transition-transform duration-500 group-hover:scale-110"
                                fill
                                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
                                priority
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-gray-900/60 to-transparent" />
                        </div>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <section className="p-6 rounded-2xl bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 transition-all duration-300 hover:bg-gray-700/50">
                            <h3 className="text-xl font-semibold mb-6 pb-2 border-b border-gray-700/50">Event Details</h3>
                            
                            <div className="space-y-4">
                                {event?.venueName && (
                                    <div className="flex flex-col gap-1">
                                        <span className="text-sm text-gray-400">Venue</span>
                                        <span className="text-white font-medium">{event.venueName}</span>
                                    </div>
                                )}
                                
                                {formattedDate && (
                                    <div className="flex flex-col gap-1">
                                        <span className="text-sm text-gray-400">Date & Time</span>
                                        <span className="text-white font-medium">{formattedDate}</span>
                                    </div>
                                )}

                                {(event?.genre || event?.subGenre) && (
                                    <div className="flex flex-col gap-1">
                                        <span className="text-sm text-gray-400">Category</span>
                                        <div className="flex flex-wrap gap-2">
                                            {event.genre && (
                                                <span className="px-3 py-1 rounded-full text-sm font-medium bg-gray-700/50 border border-gray-600 text-gray-200">
                                                    {event.genre}
                                                </span>
                                            )}
                                            {event.subGenre && (
                                                <span className="px-3 py-1 rounded-full text-sm font-medium bg-gray-700/50 border border-gray-600 text-gray-200">
                                                    {event.subGenre}
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </section>

                        <section className="p-6 rounded-2xl bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 transition-all duration-300 hover:bg-gray-700/50">
                            <h3 className="text-xl font-semibold mb-6 pb-2 border-b border-gray-700/50">Location & Price</h3>
                            
                            <div className="space-y-4">
                                {event?.venueCity && event?.venueCountry && (
                                    <div className="flex flex-col gap-1">
                                        <span className="text-sm text-gray-400">Location</span>
                                        <span className="text-white font-medium">
                                            {event.venueCity}, {event.venueCountry}
                                        </span>
                                    </div>
                                )}

                                {event?.priceRangeMin && event?.priceRangeMax && (
                                    <div className="flex flex-col gap-1">
                                        <span className="text-sm text-gray-400">Price Range</span>
                                        <span className="text-white font-medium">
                                            {event.currency} {event.priceRangeMin} - {event.currency} {event.priceRangeMax}
                                        </span>
                                    </div>
                                )}
                            </div>
                        </section>

                        <section className="flex flex-col justify-between p-6 rounded-2xl bg-gradient-to-br from-green-600/20 via-green-500/20 to-green-600/20 backdrop-blur-sm border border-green-500/30 transition-all duration-300 hover:bg-green-500/30">
                            <div>
                                <h3 className="text-xl font-semibold mb-6 pb-2 border-b border-green-500/30">Available Seats</h3>
                                <div className="flex items-center justify-center h-full">
                                    <span className="text-5xl font-bold text-green-400">{availableSeats}</span>
                                </div>
                            </div>
                            <button className="mt-6 w-full px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors duration-300">
                                Book Now
                            </button>
                        </section>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};
