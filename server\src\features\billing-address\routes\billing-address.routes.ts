// server/src/features/billing-address/routes/billing-address.routes.ts
import { Router } from 'express';
import { BillingAddressController } from '../controllers/billing-address.controller';
import { authMiddleware } from '@/middleware/auth.middleware';

const router = Router();

router.use(authMiddleware); // All routes require authentication

router.get('/', BillingAddressController.getAll);
router.post('/', BillingAddressController.create);
router.get('/:addressId', BillingAddressController.getById);
router.put('/:addressId', BillingAddressController.update);
router.delete('/:addressId', BillingAddressController.delete);
router.patch('/:addressId/set-default', BillingAddressController.setDefault); // Using PATCH for partial update like setting default

export const billingAddressRoutes = router;