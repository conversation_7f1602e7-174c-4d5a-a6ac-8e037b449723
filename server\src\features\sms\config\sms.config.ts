import { NODE_ENV } from '@/constants';

/**
 * SMS Configuration
 * 
 * Centralized configuration for SMS services
 */
export const SMS_CONFIG = {
  // Provider can be 'twilio', 'aws-sns', 'messagebird', etc.
  PROVIDER: process.env.SMS_PROVIDER || (NODE_ENV === 'development' ? 'development' : 'twilio'),
  
  
  // Test phone number for development
  TEST_PHONE: process.env.TEST_PHONE_NUMBER || '+**********',
  
  // Message templates
  TEMPLATES: {
    VERIFICATION: (otp: string) => `Your Fanseatmaster verification code is: ${otp}`,
    WELCOME: (name: string) => `Welcome to Fanseatmaster, ${name}! Thank you for joining us.`
  },
  
  // OTP settings
  OTP: {
    EXPIRY_MINUTES: 15,
    LENGTH: 6
  },
  
  // Provider-specific settings
  PROVIDERS: {
    // Twilio specific settings
    TWILIO: {
      ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID,
      AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN,
      PHONE_NUMBER: process.env.TWILIO_PHONE_NUMBER
    },
    
    // AWS SNS specific settings
    AWS_SNS: {
      REGION: process.env.AWS_REGION || 'us-east-1',
      ACCESS_KEY: process.env.AWS_ACCESS_KEY,
      SECRET_KEY: process.env.AWS_SECRET_KEY
    }
  }
};

// Log configuration in development mode
if (NODE_ENV === 'development') {
  console.log("📱 SMS Configuration:", {
    provider: SMS_CONFIG.PROVIDER,
    testPhone: SMS_CONFIG.TEST_PHONE,
    twilioConfigured: !!SMS_CONFIG.PROVIDERS.TWILIO.ACCOUNT_SID
  });
}

