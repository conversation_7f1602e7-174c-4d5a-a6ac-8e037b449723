/**
 * Checkout Controller
 * 
 * Handles the initiation and processing of checkout requests,
 * integrating with the waiting room queue system.
 */

import { Request, Response } from 'express';
import { CheckoutService } from '../services/checkout.service';
import { CreateCheckoutSessionRequest, CreateReservationRequest, UpdateSessionStatusRequest } from '../types/checkout.types';
import { asyncHandler } from '@/utils/asyncHandler';
import ApiError from '@/utils/ApiError';
import { CheckoutSessionStatus } from '@prisma/client';

export class CheckoutController {
  /**
   * Create a reservation
   * POST /api/v1/checkout/reserve
   */
  static createReservation = asyncHandler(async (req: Request, res: Response) => {
    console.log("➡️🛒 [CheckoutController] Hit createReservation handler.");
    
    // Log the entire request body to see what's coming from the client
    console.log("📬 [CheckoutController] Received request body:", JSON.stringify(req.body, null, 2));

    try {
      console.log("📥 Incoming reservation request");
      
      // Extract data from request - now also extracting billingAddressId
      const { eventId, items, billingAddressId } = req.body as CreateReservationRequest;

      // Log the extracted billingAddressId
      console.log(`🏠 [CheckoutController] Extracted billingAddressId: ${billingAddressId}`);
      
      // Validate required fields
      if (!eventId) {
        console.log("❌ Missing eventId in request");
        return res.status(400).json({
          success: false,
          message: 'Event ID is required'
        });
      }
      
      if (!Array.isArray(items) || items.length === 0) {
        console.log("❌ Invalid or empty items array");
        return res.status(400).json({
          success: false,
          message: 'Items array is required and cannot cannot empty'
        });
      }
      
      // Validate all items have inventoryId and quantity
      const invalidItems = items.filter(item => 
        !item.inventoryId || 
        typeof item.quantity !== 'number' || 
        item.quantity <= 0
      );
      
      if (invalidItems.length > 0) {
        console.log("❌ One or more items are invalid:", invalidItems);
        return res.status(400).json({
          success: false,
          message: 'All items must have a valid inventoryId and positive quantity'
        });
      }
      
      // Get user ID from authenticated request
      const userId = req.user?.userId;
      
      if (!userId) {
        console.log("🔒 [CheckoutController] Authentication required - userId missing.");
        return res.status(401).json({
          success: false,
          message: 'User must be authenticated'
        });
      }
      
      console.log(`⚙️ [CheckoutController] Processing reservation for user: ${userId}, event: ${eventId}, billingAddressId: ${billingAddressId}`);
      // Log if billing address was provided
      if (billingAddressId) {
        console.log("🏠 Billing address ID provided:", billingAddressId);
      }
      
      // Get client info for security logging
      const clientInfo = {
        ipAddress: req.ip,
        userAgent: req.headers['user-agent']
      };
      
      // Create the reservation 
      // IMPORTANT: We're keeping the original method signature to avoid breaking changes
      const result = await CheckoutService.createReservation(
        userId,
        eventId, 
        items,
        clientInfo,
        billingAddressId
      );
      
      if (!result.success) {
        // Log appropriate error message based on error type
        console.log(`❌ Reservation failed: ${result.error}`);
        
        // If the failure is due to no available tickets, return 409 Conflict
        if (result.error === 'NO_AVAILABLE_TICKETS') {
          return res.status(409).json(result);
        }
        
        // If the failure is due to event not found, return 404 Not Found
        if (result.error === 'EVENT_NOT_FOUND') {
          return res.status(404).json(result);
        }
        // If the failure is due to invalid inventory
        if (result.error === 'INVALID_INVENTORY') {
          return res.status(500).json(result); // Or 400, depending on how you categorize this
        }
        
        // For any other errors, return 400 Bad Request (or 500 if internal)
        return res.status(400).json(result);
      }
      
      console.log("✅ [CheckoutController] Reservation processing complete.");
      // Return success with 201 Created status
      return res.status(201).json(result);
    } catch (error) {
      console.error('💥 Unhandled error in createReservation:', error);
      // Ensure ApiErrors propagate correctly or return a generic 500
      if (error instanceof ApiError) {
        throw error; // Re-throw ApiError to be caught by asyncHandler if configured
      }
      throw new ApiError(500, 'Internal server error while creating reservation');
    }
  });

  static createCheckoutSessionHandler = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.userId;
    if (!userId) throw new ApiError(401, 'Authentication required');

    const payload = req.body as CreateCheckoutSessionRequest; // Ensure this type includes billingAddressId

    // Log the entire payload received by this specific handler
    console.log('🛒 [CheckoutController] Received createCheckoutSessionHandler payload:', JSON.stringify(payload, null, 2));
    console.log(`🏠 [CheckoutController] billingAddressId in createCheckoutSessionHandler payload: ${payload.billingAddressId}`);


    // The service method here MUST also accept and process billingAddressId
    const session = await CheckoutService.createCheckoutSession(userId, payload);

    res.status(201).json({
        success: true,
        message: 'Checkout session created successfully.',
        data: session,
    });
  });

  /**
   * Get a checkout session
   * GET /api/v1/checkout/session/:sessionId
   */
  static getSession = asyncHandler(async (req: Request, res: Response) => {
    try {
      const { sessionId } = req.params;
      
      console.log(`📥 Fetching session: ${sessionId}`);
      
      if (!sessionId) {
        console.log("❌ Missing sessionId in request");
        return res.status(400).json({
          success: false,
          message: 'Session ID is required'
        });
      }
      
      const result = await CheckoutService.getSession(sessionId);
      
      if (!result.success) {
        console.log(`❌ Session fetch failed: ${result.error}`);
        
        if (result.error === 'SESSION_NOT_FOUND') {
          return res.status(404).json(result);
        }
        
        return res.status(400).json(result);
      }
      
      console.log("✅ Session fetched successfully");
      return res.status(200).json(result);
    } catch (error) {
      console.error('💥 Unhandled error in getSession:', error);
      throw new ApiError(500, 'Internal server error while fetching session');
    }
  });

  /**
   * Refresh a checkout session (extend expiration time)
   * POST /api/v1/checkout/session/:sessionId/refresh
   */
  static refreshSession = asyncHandler(async (req: Request, res: Response) => {
    try {
      const { sessionId } = req.params;
      
      console.log(`📥 Refreshing session: ${sessionId}`);
      
      if (!sessionId) {
        console.log("❌ Missing sessionId in request");
        return res.status(400).json({
          success: false,
          message: 'Session ID is required'
        });
      }
      
      const result = await CheckoutService.refreshSession(sessionId);
      
      if (!result.success) {
        console.log(`❌ Session refresh failed: ${result.error}`);
        
        if (result.error === 'SESSION_NOT_FOUND') {
          return res.status(404).json(result);
        }
        
        return res.status(400).json(result);
      }
      
      console.log("✅ Session refreshed successfully");
      return res.status(200).json(result);
    } catch (error) {
      console.error('💥 Unhandled error in refreshSession:', error);
      throw new ApiError(500, 'Internal server error while refreshing session');
    }
  });

  /**
   * Update a checkout session status
   * PATCH /api/v1/checkout/session/:sessionId/status
   */
  static updateSessionStatus = asyncHandler(async (req: Request, res: Response) => {
    console.log("🔄 Checkout Controller: Hit updateSessionStatus handler");
    
    try {
      const { sessionId } = req.params;
      const { status } = req.body as UpdateSessionStatusRequest;
      
      if (!sessionId) {
        console.log("❌ Missing sessionId in request");
        return res.status(400).json({
          success: false,
          message: 'Session ID is required'
        });
      }
      
      if (
        !status ||
        !Object.values(CheckoutSessionStatus).includes(status as CheckoutSessionStatus)
      ) {
        console.log("❌ Invalid status in request:", status);
        return res.status(400).json({
          success: false,
          message: 'Valid status is required'
        });
      }
      
      // Get user ID from authenticated request
      const userId = req.user?.userId;
      
      if (!userId) {
        console.log("🔒 Authentication required - userId missing");
        return res.status(401).json({
          success: false,
          message: 'User must be authenticated'
        });
      }
      
      console.log(`⚙️ Processing status update for session ${sessionId} to ${status}`);
      
      // Update the session status
      const result = await CheckoutService.updateSessionStatus(sessionId, status as CheckoutSessionStatus, userId);
      
      if (!result.success) {
        console.log(`❌ Session update failed: ${result.error}`);
        
        if (result.error === 'SESSION_NOT_FOUND') {
          return res.status(404).json(result);
        }
        
        if (result.error === 'UNAUTHORIZED') {
          return res.status(403).json(result);
        }
        
        if (result.error === 'INVALID_STATUS_TRANSITION') {
          return res.status(400).json(result);
        }
        
        return res.status(400).json(result);
      }
      
      console.log("✅ Session status updated successfully");
      return res.status(200).json(result);
    } catch (error) {
      console.error('💥 Unhandled error in updateSessionStatus:', error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(500, 'Internal server error while updating session status');
    }
  });
}