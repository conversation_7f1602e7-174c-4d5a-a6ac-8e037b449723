/**
 * Payment History Routes
 *
 * Defines API endpoints for accessing payment history.
 */

import express from "express";
import { PaymentHistoryController } from "../controllers/payment-history.controller";
import { authMiddleware } from "@/middleware/auth.middleware"; // Adjust path as needed

const router = express.Router();

// Get user's payment history (requires authentication)
router.get("/", authMiddleware, PaymentHistoryController.getUserPaymentHistory);

// Get details of a specific payment (requires authentication)
router.get(
  "/:paymentId",
  authMiddleware,
  PaymentHistoryController.getPaymentDetails
);

export default router;
