"use client";
import { motion } from "framer-motion";

import { GridLayout } from "../../shared/layout/GridLayout";
import { GlobalMetricsGrid } from "./components/GlobalMetricsGrid";
import { UserActivityBento } from "./components/UserActivityBento";
import { ManagerPerformanceBento } from "./components/ManagerPerformanceBento";
import { EventMonitoringBento } from "./components/EventMonitoringBento";
import { TransactionHistoryBento } from "./components/TransactionHistoryBento";

export const AdminDashboard = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.1 },
    },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-8"
    >
      {/* Admin Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold tracking-tight">
          Admin Control Panel
        </h1>
      </div>

      {/* Global Metrics */}
      <GlobalMetricsGrid />

      {/* Main Content */}
      <GridLayout className="grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
        <UserActivityBento />
        <ManagerPerformanceBento />
        <EventMonitoringBento />
        <TransactionHistoryBento />
      </GridLayout>
    </motion.div>
  );
};
