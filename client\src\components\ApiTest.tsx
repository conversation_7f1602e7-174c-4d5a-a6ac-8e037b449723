"use client";

import { useState } from "react";
import { api, apiV2 } from "@/apiAxios/axios";

export default function ApiTest() {
  const [v1Response, setV1Response] = useState("");
  const [v2Response, setV2Response] = useState("");
  const [v2EventsResponse, setV2EventsResponse] = useState("");

  const testV1 = async () => {
    try {
      // manually mentioning the api version
      const response = await api.get("api/v1/test");
      setV1Response(JSON.stringify(response.data, null, 2));
    } catch (error) {
      console.error("V1 Error:", error);
      setV1Response("Error fetching V1");
    }
  };

  const testV2 = async () => {
    try {
      // using the apiV2 instance which has the base URL set to /api/v2
      const response = await apiV2.get("/test");
      setV2Response(JSON.stringify(response.data, null, 2));
    } catch (error) {
      console.error("V2 Error:", error);
      setV2Response("Error fetching V2");
    }
  };

  const testV2Events = async () => {
    try {
      const response = await apiV2.get("/events");
      setV2EventsResponse(JSON.stringify(response.data, null, 2));
    } catch (error) {
      console.error("V2 Events Error:", error);
      setV2EventsResponse("Error fetching V2 Events");
    }
  };

  return (
    <div className="p-4">
      <div className="mb-4">
        <button
          onClick={testV1}
          className="bg-blue-500 text-white px-4 py-2 rounded mr-2"
        >
          Test V1 API
        </button>
        <pre className="mt-2 bg-gray-100 p-2 rounded">{v1Response}</pre>
      </div>

      <div className="mb-4">
        <button
          onClick={testV2}
          className="bg-green-500 text-white px-4 py-2 rounded mr-2"
        >
          Test V2 API
        </button>
        <pre className="mt-2 bg-gray-100 p-2 rounded">{v2Response}</pre>
      </div>

      <div>
        <button
          onClick={testV2Events}
          className="bg-purple-500 text-white px-4 py-2 rounded mr-2"
        >
          Test V2 Events
        </button>
        <pre className="mt-2 bg-gray-100 p-2 rounded">{v2EventsResponse}</pre>
      </div>
    </div>
  );
}
