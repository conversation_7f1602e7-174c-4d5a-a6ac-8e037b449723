"use client"
import { Line } from 'react-chartjs-2';
import { ChartContainer } from '@/features/modules/shared/charts/ChartContainer';
import { useEffect, useState } from 'react';
import { analyticsService } from '../services/analytics.service';
import type { TimeSeriesData } from '@/features/modules/shared/types/analytics.types';

export const EventPerformanceChart = () => {
  const [data, setData] = useState<TimeSeriesData[] | null>(null);

  useEffect(() => {
    const loadData = async () => {
      const performanceData = await analyticsService.getEventPerformanceData();
      setData(performanceData);
    };
    loadData();
  }, []);

  if (!data) return null;

  const chartData = {
    labels: data.map(item => item.timestamp),
    datasets: [{
      label: 'Events Performance',
      data: data.map(item => item.value),
      fill: false,
      borderColor: 'rgb(54, 162, 235)',
      tension: 0.1
    }]
  };

  return (
    <ChartContainer title="Event Performance Trends" height={300}>
      <Line 
        data={chartData}
        options={{
          responsive: true,
          plugins: {
            tooltip: {
              callbacks: {
                label: (context) => `Events: ${context.raw}`
              }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: 'Number of Events'
              }
            }
          }
        }}
      />
    </ChartContainer>
  );
};
