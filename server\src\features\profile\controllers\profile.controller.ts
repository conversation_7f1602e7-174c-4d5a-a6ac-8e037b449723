/**
 * Profile Controller
 *
 * Handles HTTP requests and responses for profile operations
 * using consistent error handling patterns.
 */
import { Request, Response } from "express";
import { ProfileService } from "../services/profile.service";
import { asyncHandler } from "@/utils/asyncHandler";
import { VerificationService } from "../services/verification.service";
import { ProfileResponseDTO, ProfileUpdateDTO } from "../types/profile.types";

export class ProfileController {
  /**
   * Get the current user's profile
   */
  static getProfile = asyncHandler(async (req: Request, res: Response) => {
    console.log("check request 🐢", req.user);
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "Authentication required",
      });
    }

    console.log(`📋 Fetching profile for user: ${userId}`);
    const profile: ProfileResponseDTO = await ProfileService.getUserProfile(
      userId
    );

    // const profile = await ProfileService.getUserProfile(userId);
    // const profile = await ProfileRepository.findProfileByUserId(userId);

    return res.status(200).json({
      success: true,
      message: "Profile retrieved successfully",
      data: profile,
    });
  });

  /**
   * Update the current user's profile
   */
  static updateProfile = asyncHandler(async (req: Request, res: Response) => {
    console.log("✏️ Update profile request received:", req.body);
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "Authentication required",
      });
    }

    // Expecting the request body to follow ProfileUpdateDTO structure
    const profileData: ProfileUpdateDTO = req.body;
    
    // Call the service method that performs a nested update for user fields
    const updatedProfile = await ProfileService.updateProfile(userId, profileData);
    
    console.log(`✅ Profile updated for user: ${userId}`);
    return res.status(200).json({
      success: true,
      message: "Profile updated successfully",
      data: updatedProfile,
    });
  });

  /**
   * Update user's social links
   */
  static updateSocialLinks = asyncHandler(
    async (req: Request, res: Response) => {
      const userId = req.user?.userId;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "Authentication required",
        });
      }

      const socialLinks = req.body;
      console.log(`🔗 Updating social links for user: ${userId}`);

      const updatedProfile = await ProfileService.updateSocialLinks(
        userId,
        socialLinks
      );

      return res.status(200).json({
        success: true,
        message: "Social links updated successfully",
        data: updatedProfile,
      });
    }
  );

  // Add these methods to the existing ProfileController class

  /**
   * Initiate email verification
   */
  static initiateEmailVerification = asyncHandler(
    async (req: Request, res: Response) => {
      const userId = req.user?.userId;
      const { email } = req.body;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "Authentication required",
        });
      }

      if (!email) {
        return res.status(400).json({
          success: false,
          message: "Email is required",
        });
      }

      console.log(`📧 Initiating email verification for user: ${userId}`);
      const result = await VerificationService.initiateEmailVerification(
        userId,
        email
      );

      return res.status(200).json({
        success: true,
        message: "Verification code sent successfully",
        data: result,
      });
    }
  );

  /**
   * Verify email with OTP
   */
  static verifyEmail = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.userId;
    const { otp } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "Authentication required",
      });
    }

    if (!otp) {
      return res.status(400).json({
        success: false,
        message: "Verification code is required",
      });
    }

    console.log(`✅ Verifying email for user: ${userId}`);
    const result = await VerificationService.verifyEmail(userId, otp);

    return res.status(200).json({
      success: true,
      message: "Email verified successfully",
      data: result,
    });
  });

  /**
   * Initiate mobile verification
   */
  static initiateMobileVerification = asyncHandler(
    async (req: Request, res: Response) => {
      const userId = req.user?.userId;
      const { mobile } = req.body;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "Authentication required",
        });
      }

      if (!mobile) {
        return res.status(400).json({
          success: false,
          message: "Mobile number is required",
        });
      }

      console.log(`📱 Initiating mobile verification for user: ${userId}`);
      const result = await VerificationService.initiateMobileVerification(
        userId,
        mobile
      );

      return res.status(200).json({
        success: true,
        message: "Verification code sent successfully",
        data: result,
      });
    }
  );

  /**
   * Verify mobile with OTP
   */
  static verifyMobile = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.userId;
    const { otp } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "Authentication required",
      });
    }

    if (!otp) {
      return res.status(400).json({
        success: false,
        message: "Verification code is required",
      });
    }

    console.log(`✅ Verifying mobile for user: ${userId}`);
    const result = await VerificationService.verifyMobile(userId, otp);

    return res.status(200).json({
      success: true,
      message: "Mobile number verified successfully",
      data: result,
    });
  });
}
