/**
 * TicketMessageModal Component - FIXED: No 404 errors + Scroll bars
 */

import React, { useEffect, useRef } from 'react';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { 
  Ticket, 
  MessageCircle, 
  User, 
  UserCheck,
  Clock,
  CheckCircle,
  AlertTriangle,
  X,
  HelpCircle,
  Loader2,
  RefreshCw,
  Send
} from "lucide-react";
import { cn } from "@/lib/utils";

import MessageBubble from '@/features/messaging/components/MessageBubble';
import MessageInput from '@/features/messaging/components/MessageInput';
import { useVisitorTicketConversation } from '@/features/messaging/hooks/useMessaging';

interface TicketMessageModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  ticketId: string;
  eventName: string;
}

export const TicketMessageModal: React.FC<TicketMessageModalProps> = ({
  open,
  onOpenChange,
  ticketId,
  eventName,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // 🔧 FIXED: New hook that doesn't fetch until first message sent
  const {
    conversation,
    messages,
    isLoading,
    isError,
    error,
    sendMessage,
    isSending,
    unreadCount,
    hasMessages,
    refetch,
    shouldFetch, // New: tells us if we should be fetching
  } = useVisitorTicketConversation(ticketId);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current && messages.length > 0) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // 🔧 FIXED: Only refetch if we should be fetching
  useEffect(() => {
    if (open && shouldFetch) {
      console.log('🔄 [TicketMessageModal] Modal opened, refetching existing conversation...');
      refetch();
    }
  }, [open, shouldFetch, refetch]);

  // Handle message sending
  const handleSendMessage = async (message: string) => {
    console.log('📤 [TicketMessageModal] Sending message:', message);
    try {
      await sendMessage(message);
      console.log('✅ [TicketMessageModal] Message sent successfully');
    } catch (error) {
      console.error('❌ [TicketMessageModal] Failed to send message:', error);
    }
  };

  // Get conversation status styling
  const getStatusInfo = () => {
    // 🔧 FIXED: Only show error if we're actually fetching and got an error
    if (isError && shouldFetch) {
      const errorMessage = error?.message || 'Unknown error';
      console.error('❌ [TicketMessageModal] Connection error details:', errorMessage);
      
      return {
        color: 'red',
        label: 'Connection error',
        icon: <AlertTriangle className="h-4 w-4" />,
        description: `Unable to load conversation: ${errorMessage}`
      };
    }

    if (!hasMessages && !shouldFetch) {
      return {
        color: 'gray',
        label: 'New conversation',
        icon: <MessageCircle className="h-4 w-4" />,
        description: 'Start a conversation with the event manager'
      };
    }

    if (conversation?.status === 'RESOLVED') {
      return {
        color: 'green',
        label: 'Resolved',
        icon: <CheckCircle className="h-4 w-4" />,
        description: 'This conversation has been resolved'
      };
    }

    return {
      color: 'blue',
      label: 'Active',
      icon: <MessageCircle className="h-4 w-4" />,
      description: 'Active conversation with event manager'
    };
  };

  const statusInfo = getStatusInfo();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent 
        className="sm:max-w-4xl max-h-[90vh] h-[80vh] p-0 overflow-hidden bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white border border-gray-700"
      >
        {/* Header */}
        <DialogHeader className="px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-blue-900/30 via-gray-800/50 to-indigo-900/30 flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-600/20 border border-blue-500/30 rounded-full">
                <Ticket className="h-5 w-5 text-blue-400" />
              </div>
              
              <div className="flex-1 min-w-0">
                <DialogTitle className="text-lg font-semibold text-white">
                  Contact Event Manager
                </DialogTitle>
                <DialogDescription className="text-gray-300">
                  <span className="font-medium border border-gray-600 rounded px-2 py-0.5 mr-2">
                    {eventName}
                  </span>
                  Ticket #{ticketId.substring(0, 8)}...
                </DialogDescription>
              </div>
            </div>

            {/* Header Actions */}
            <div className="flex items-center space-x-2">
              {/* Status Badge */}
              <Badge 
                variant="outline" 
                className={cn(
                  "flex items-center space-x-1 border-gray-600 text-gray-300",
                  statusInfo.color === 'blue' && "border-blue-400 text-blue-300 bg-blue-900/30",
                  statusInfo.color === 'green' && "border-green-400 text-green-300 bg-green-900/30",
                  statusInfo.color === 'red' && "border-red-400 text-red-300 bg-red-900/30",
                )}
              >
                {statusInfo.icon}
                <span>{statusInfo.label}</span>
              </Badge>

              {/* Loading indicator */}
              {isLoading && shouldFetch && (
                <Loader2 className="h-4 w-4 animate-spin text-blue-400" />
              )}

              {/* Retry button for errors */}
              {isError && shouldFetch && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => refetch()}
                  className="h-8 w-8 p-0 text-orange-400 hover:text-orange-300 hover:bg-gray-700"
                  title="Retry connection"
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              )}

              {/* Close Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onOpenChange(false)}
                className="h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-gray-700"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Status Description */}
          <div className="pt-3 text-xs text-gray-400">
            <p>{statusInfo.description}</p>
            
            {conversation && (
              <div className="flex items-center justify-between mt-2">
                <div className="flex items-center space-x-4">
                  <span>{messages.length} messages</span>
                  {conversation.lastMessageDate && (
                    <span>Last: {new Date(conversation.lastMessageDate).toLocaleString()}</span>
                  )}
                </div>
                
                <div className="flex items-center space-x-3 text-xs">
                  <div className="flex items-center space-x-1">
                    <User className="h-3 w-3" />
                    <span>You</span>
                  </div>
                  {conversation.participants?.manager && (
                    <div className="flex items-center space-x-1">
                      <UserCheck className="h-3 w-3" />
                      <span>{conversation.participants.manager.name}</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </DialogHeader>

        {/* 🔧 FIXED: Content with proper flex layout and scroll */}
        <div className="flex flex-col h-full min-h-0">
          {/* Messages Area with ScrollArea */}
          <div className="flex-1 min-h-0 overflow-hidden">
            {(isLoading && shouldFetch) ? (
              // Loading State (only when fetching existing conversation)
              <div className="flex items-center justify-center h-full">
                <div className="text-center space-y-3">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-500 mx-auto" />
                  <p className="text-sm text-gray-400">Loading conversation...</p>
                </div>
              </div>
            ) : (isError && shouldFetch) ? (
              // Error State (only when we tried to fetch and failed)
              <div className="flex items-center justify-center h-full">
                <div className="text-center space-y-3 p-8">
                  <AlertTriangle className="h-12 w-12 mx-auto text-red-400" />
                  <h3 className="text-lg font-medium text-white">
                    Connection Error
                  </h3>
                  <p className="text-sm text-gray-400 max-w-sm">
                    Unable to load existing messages. You can still send new messages.
                  </p>
                  
                  <Button 
                    variant="outline" 
                    onClick={() => refetch()}
                    className="mt-4 border-gray-600 text-gray-300 hover:bg-gray-700"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Retry Loading Messages
                  </Button>
                </div>
              </div>
            ) : messages.length === 0 ? (
              // Empty State - Ready to send first message
              <div className="flex items-center justify-center h-full">
                <div className="text-center space-y-4 p-8 max-w-md">
                  <div className="p-4 bg-blue-600/20 border border-blue-500/30 rounded-full w-16 h-16 mx-auto flex items-center justify-center">
                    <MessageCircle className="h-8 w-8 text-blue-400" />
                  </div>
                  <h3 className="text-lg font-medium text-white">
                    Need help with your ticket?
                  </h3>
                  <p className="text-sm text-gray-400">
                    Having issues with your ticket for <span className="font-medium text-white">{eventName}</span>? 
                    Describe your problem below and the event manager will assist you.
                  </p>
                  
                  {/* Help Topics */}
                  <div className="grid grid-cols-2 gap-2 mt-4 text-xs">
                    <Badge variant="outline" className="border-gray-600 text-gray-400 justify-center py-2">
                      <HelpCircle className="h-3 w-3 mr-1" />
                      Ticket Issues
                    </Badge>
                    <Badge variant="outline" className="border-gray-600 text-gray-400 justify-center py-2">
                      <Clock className="h-3 w-3 mr-1" />
                      Event Changes
                    </Badge>
                    <Badge variant="outline" className="border-gray-600 text-gray-400 justify-center py-2">
                      <User className="h-3 w-3 mr-1" />
                      Transfer Help
                    </Badge>
                    <Badge variant="outline" className="border-gray-600 text-gray-400 justify-center py-2">
                      <Send className="h-3 w-3 mr-1" />
                      Other Issues
                    </Badge>
                  </div>
                  
                  <p className="text-xs text-blue-300 mt-4">
                    💡 Your first message will start the conversation
                  </p>
                </div>
              </div>
            ) : (
              // 🔧 FIXED: Messages List with proper vertical scrolling
              <ScrollArea className="h-full w-full">
                <div className="px-6 py-4 space-y-4 min-h-full">
                  {messages.map((message, index) => {
                    const isOwn = message.senderRole === 'VISITOR';
                    const showAvatar = index === 0 || 
                      messages[index - 1]?.senderId !== message.senderId;

                    return (
                      <MessageBubble
                        key={message.id}
                        message={message}
                        isOwn={isOwn}
                        showAvatar={showAvatar}
                      />
                    );
                  })}
                  {/* Scroll anchor */}
                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>
            )}
          </div>

          {/* Separator */}
          <Separator className="bg-gray-700 flex-shrink-0" />

          {/* Message Input Area - Fixed at bottom */}
          <div className="p-6 bg-gray-800/50 flex-shrink-0">
            <MessageInput
              onSendMessage={handleSendMessage}
              placeholder={
                hasMessages 
                  ? "Type your reply..." 
                  : "Describe your issue in detail to start the conversation..."
              }
              disabled={isSending}
              isLoading={isSending}
              maxLength={1000}
              minLength={10}
            />

            {/* Footer Info */}
            <div className="flex items-center justify-between mt-3 text-xs text-gray-500">
              <div className="flex items-center space-x-3">
                <span>
                  {hasMessages 
                    ? "💬 Continue the conversation" 
                    : "💡 Be specific about your issue for faster help"
                  }
                </span>
                {/* {conversation?.status === 'RESOLVED' && (
                  <Badge variant="outline" className="text-green-400 border-green-600">
                    Issue resolved
                  </Badge>
                )} */}
              </div>
              
              <div className="text-right">
                <span>Ticket: {ticketId.substring(0, 8)}...</span>
              </div>
            </div>

            {/* Help Text for First Message */}
            {messages.length === 0 && !isLoading && (
              <div className="mt-4 p-3 bg-blue-900/20 border border-blue-700/50 rounded-lg">
                <h4 className="text-sm font-medium text-blue-300 mb-1">
                  Getting the best help:
                </h4>
                <ul className="text-xs text-blue-200 space-y-1">
                  <li>• Describe your specific issue clearly</li>
                  <li>• Include any error messages you&apos;ve seen</li>
                  <li>• Mention if you&apos;ve tried any solutions already</li>
                  <li>• Be patient - managers typically respond within a few hours</li>
                </ul>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
