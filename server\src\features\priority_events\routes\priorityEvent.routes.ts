// Priority Event Routes
import { Router } from 'express';
import { PriorityEventController } from '../controllers/priorityEvent.controller';

const router = Router();

router.post('/', PriorityEventController.createPriorityEvents);
router.post('/bulk', PriorityEventController.createBulkPriorityEvents); // New route for bulk creation
router.get('/', PriorityEventController.getPriorityEvents);
router.delete('/:id', PriorityEventController.deletePriorityEvent);
router.get('/:id', PriorityEventController.getPriorityEventById);
router.put('/:id', PriorityEventController.updatePriorityEvent);
router.put('/:id/toggle', PriorityEventController.togglePopular)

export default router;
