import { Context } from "@/types/openctx.types";
import { Calendar, ExternalLink, MapPin, Plus, Tag, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { formatEventDate } from "@/utils/dateUtils"; // Import existing date utility

// Interface for EventDetailCard component props
interface EventDetailCardProps {
  event: Context;
  onAddToPriority?: () => void;
  onRemove?: () => void;  // New prop for remove functionality
  showActions?: boolean;
}

// EventDetailCard component to display event details and actions
export const EventDetailCard = ({ 
  event, 
  onAddToPriority,
  onRemove,
  showActions = true 
}: EventDetailCardProps) => {
  // Safely access nested properties to prevent undefined errors
  const metadata = event.metadata || {};
  const rawEvent = metadata.rawEvent || {};
  const isTicketmaster = metadata.source === 'ticketmaster';
  
  // Get date from appropriate source
  const eventDate = isTicketmaster && rawEvent.dates?.start?.dateTime 
    ? rawEvent.dates.start.dateTime 
    : metadata.date;
  
  // Get venue and city information
  const venue = metadata.venue || 'Unknown Venue';
  const city = isTicketmaster && rawEvent._embedded?.venues?.[0]?.city?.name
    ? rawEvent._embedded.venues[0].city.name
    : metadata.city || '';

  return (
    <div className="p-6 border rounded-lg bg-white shadow-sm">
      {/* Image and Basic Info Section */}
      <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 mb-6">
        <div className="w-full sm:w-48 h-48 overflow-hidden rounded-md">
          <Image
            src={metadata.image || "/placeholder.jpg"}
            alt={metadata.name || "Event Image"}
            width={192}
            height={192}
            className="w-full h-full object-cover"
          />
        </div>

        <div className="flex-1">
          <h3 className="text-2xl font-bold mb-2">
            {metadata.name || "Untitled Event"}
          </h3>
          <div className="space-y-2 text-gray-600">
            <p className="flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              {venue} {city ? `• ${city}` : ''}
            </p>
            <p className="flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              {eventDate ? formatEventDate(eventDate) : "Date not available"}
            </p>
            {isTicketmaster && rawEvent.classifications && (
              <div className="flex items-center gap-2">
                <Tag className="w-4 h-4" />
                <div className="flex gap-2">
                  {rawEvent.classifications.map(
                    (classification: any, index: number) =>
                      classification.primary && (
                        <span key={index} className="px-2 py-1 bg-gray-100 rounded text-sm">
                          {classification.segment?.name} • {classification.genre?.name}
                        </span>
                      )
                  )}
                </div>
              </div>
            )}
            {!isTicketmaster && metadata.category && (
              <div className="flex items-center gap-2">
                <Tag className="w-4 h-4" />
                <span className="px-2 py-1 bg-gray-100 rounded text-sm">
                  {metadata.category}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Additional Details Grid - Only show for Ticketmaster events */}
      {isTicketmaster && rawEvent.priceRanges && (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4 p-4 bg-gray-50 rounded-md">
          <div className="space-y-1">
            <h4 className="font-medium text-gray-700">Price Range</h4>
            <p className="text-sm">
              {rawEvent.priceRanges?.[0]?.min} - {rawEvent.priceRanges?.[0]?.max} {' '}
              {rawEvent.priceRanges?.[0]?.currency}
            </p>
          </div>

          {rawEvent.sales?.public && (
            <div className="space-y-1">
              <h4 className="font-medium text-gray-700">Sale Period</h4>
              <p className="text-sm">
                {rawEvent.sales.public.startDateTime ? new Date(rawEvent.sales.public.startDateTime).toLocaleDateString() : 'N/A'} - {' '}
                {rawEvent.sales.public.endDateTime ? new Date(rawEvent.sales.public.endDateTime).toLocaleDateString() : 'N/A'}
              </p>
            </div>
          )}

          {rawEvent._embedded?.venues?.[0]?.address && (
            <div className="space-y-1">
              <h4 className="font-medium text-gray-700">Venue Address</h4>
              <p className="text-sm">
                {rawEvent._embedded.venues[0].address?.line1}, {' '}
                {rawEvent._embedded.venues[0].postalCode}
              </p>
            </div>
          )}

          {rawEvent.dates?.status && (
            <div className="space-y-1">
              <h4 className="font-medium text-gray-700">Event Status</h4>
              <span className={`px-2 py-1 rounded text-sm ${
                rawEvent.dates.status.code === "onsale"
                  ? "bg-green-100 text-green-800"
                  : "bg-yellow-100 text-yellow-800"
              }`}>
                {rawEvent.dates.status.code}
              </span>
            </div>
          )}
        </div>
      )}

      {/* Manager Event Details - Only show for manager events */}
      {!isTicketmaster && (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4 p-4 bg-gray-50 rounded-md">
          {rawEvent.inventory && (
            <div className="space-y-1">
              <h4 className="font-medium text-gray-700">Inventory</h4>
              <p className="text-sm">
                {typeof rawEvent.inventory === 'object' 
                  ? `${Object.keys(rawEvent.inventory).length} items available` 
                  : 'Inventory available'}
              </p>
            </div>
          )}
          
          {rawEvent.status && (
            <div className="space-y-1">
              <h4 className="font-medium text-gray-700">Status</h4>
              <span className={`px-2 py-1 rounded text-sm bg-green-100 text-green-800`}>
                {rawEvent.status}
              </span>
            </div>
          )}
          
          {rawEvent.country && (
            <div className="space-y-1">
              <h4 className="font-medium text-gray-700">Country</h4>
              <p className="text-sm">{rawEvent.country}</p>
            </div>
          )}
        </div>
      )}

      {/* Action Buttons */}
      {showActions && (
        <div className="mt-6 flex justify-end gap-4">
          {/* New Remove Event button */}
          {onRemove && (
            <Button
              variant="destructive"
              onClick={onRemove}
              className="flex items-center gap-2"
            >
              <Trash2 className="h-4 w-4" />
              Remove Event
            </Button>
          )}
          
          {isTicketmaster && rawEvent.url && (
            <Button
              variant="outline"
              onClick={() => window.open(rawEvent.url, '_blank')}
              className="flex items-center gap-2"
            >
              <ExternalLink className="h-4 w-4" />
              View on Ticketmaster
            </Button>
          )}
          
          {onAddToPriority && (
            <Button
              variant="default"
              onClick={onAddToPriority}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add to Priority Events
            </Button>
          )}
        </div>
      )}
    </div>
  );
};
