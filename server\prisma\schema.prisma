// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

//! when model is not created get the error due to command - pnpm exec prisma generate or pnpm run prisma:generate(package.json) run on every time when new model is created
enum UserRole {
  ADMIN
  MANAGER
  VISITOR
}

// Updated User model with OAuth support
model User {
  id                   String    @id @default(uuid())
  email                String    @unique
  password             String? // Made nullable for OAuth-only users
  role                 UserRole  @default(VISITOR)
  fullName             String?
  mobile               String? // Made nullable for OAuth users
  isActive             Boolean   @default(true)
  emailVerified        DateTime?
  mobileVerified       DateTime?
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt
  lastLogin            DateTime?
  refreshToken         String?
  resetPasswordToken   String?
  resetPasswordExpires DateTime?

  // Added OAuth specific fields
  image        String? // For OAuth profile pictures
  providerType String? // The OAuth provider type
  providerId   String? // The provider's user ID

  // Added relation to OAuth accounts
  accounts     Account[] // OAuth accounts
  userMetadata UserMetadata?

  managerEvents ManagerEvent[] // Relation to events created by this manager
  // priorityEvents  PriorityEvent[] // Relation to priority events managed by this user

  profile Profile? // Added relation to Profile model

  // User's membership level - determines benefits and queue priority
  membershipTier MembershipTier @default(STANDARD)

  // Relation to QueueUser
  queueEntries QueueUser[]

  // Relation to CheckoutSession
  checkoutSessions CheckoutSession[]
  // Add this new field
  stripeCustomerId String?           @unique

  // Add this new relation (keep all your existing relations)
  paymentRecords PaymentRecord[]

  // Relation to BillingAddress
  billingAddresses BillingAddress[] // New relation

  // 🆕 NEW: Add message relations
  sentMessages      TicketMessage[] @relation("SentMessages") // Messages sent by this user
  escalatedMessages TicketMessage[] @relation("EscalatedMessages") // Messages escalated by this admin

  @@index([email])
  @@index([email, providerId]) // Added new index for OAuth support
  @@map("users")
}

// Added new OAuth accounts model

// New Profile model for extended user information
model Profile {
  id           String  @id @default(cuid())
  userId       String  @unique
  bio          String? @db.Text
  headline     String? @db.VarChar(100)
  location     String?
  website      String?
  phoneVisible Boolean @default(false)

  // Professional information
  company  String?
  jobTitle String?
  industry String?

  // Social media links
  twitter   String?
  linkedin  String?
  facebook  String?
  instagram String?
  github    String?

  // Skills as a string array
  skills String[]

  // Preferences
  displayEmail Boolean @default(false)
  theme        String? @default("system") // light, dark, system

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relation to User model
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("profiles")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

// Updated UserMetadata model (removed some fields)
model UserMetadata {
  id               String    @id @default(cuid())
  user             User      @relation(fields: [userId], references: [id])
  userId           String    @unique
  ip               String
  city             String?
  country          String?
  timezone         String?
  latitude         Float?
  longitude        Float?
  registrationDate DateTime  @default(now())
  lastLoginDate    DateTime?

  @@map("user_metadata")
}

model Event {
  id          String   @id @default(uuid())
  title       String
  description String? // Optional description
  date        DateTime
  location    String
  timezone    String
  imageUrl    String?
  features    String[]
  category    String[] // Allow multiple categories per event
  isPopular   Boolean  @default(false)
  createdAt   DateTime @default(now())

  @@index([date]) // Index for better query performance
}

//       "date": "2023-10-05T00:00:00Z"----> ISO 8601 format, which is perfect for storing dates in UTC.

// TmEvent model representing events from Ticketmaster API
// TmEvent model representing events from Ticketmaster API
model TmEvent {
  id     String  @id
  name   String
  type   String
  url    String?
  locale String?

  // Images
  primaryImage String?
  images       Json? // Will store array of image objects

  // Dates and Status
  startDateTime DateTime?
  endDateTime   DateTime?
  timezone      String?
  status        Json? // Will store full status object

  // Classifications
  classifications Json? // Store full classification array
  segment         String? // Primary classification segment
  genre           String? // Primary classification genre
  subGenre        String? // Primary classification subGenre

  // Venue
  venue        Json? // Store full venue object
  venueName    String?
  venueCity    String?
  venueState   String?
  venueCountry String?

  // Pricing
  priceRanges   Json? // Store full price ranges array
  priceRangeMin Float?
  priceRangeMax Float?
  currency      String?

  // Sales
  sales Json? // Store full sales object

  // Additional Info
  seatmap       Json? // Store seatmap object
  ticketLimit   Json? // Store ticket limit object
  accessibility Json? // Store accessibility info

  // Metadata
  links     Json? // Store _links object
  promoter  Json? // Store promoter info
  promoters Json? // Store promoters array
  products  Json? // Store products array

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([startDateTime])
  @@index([genre])
  @@index([venueName])
}

// Enum for event categories (used by both PriorityEvent and ManagerEvent)
enum EventCategory {
  SPORTS
  MUSIC
  ARTS
  THEATER
}

// Enum for inventory seating types
enum SeatingType {
  CONSECUTIVE
  ODD_EVEN
  GA
}

// Enum for ticket formats
enum TicketFormat {
  E_TICKET
  MOBILE_TRANSFER
  HARD_TICKETS
}

// Enum for selling preferences
enum SellingPreference {
  ANY
  PAIRS
  FULL
  AVOID_SINGLE
}

enum EventApprovalStatus {
  PENDING
  APPROVED
  REJECTED
}

model ManagerEvent {
  id              String  @id @default(cuid())
  managerId       String
  eventId         String
  priorityEventId String?

  name     String
  category EventCategory
  source   String
  date     DateTime
  venue    String
  city     String
  country  String
  image    String?

  inventory     Json
  purchaseOrder Json?

  isActive Boolean @default(true)
  status   String  @default("LISTED")

  addedAt   DateTime @default(now())
  updatedAt DateTime @updatedAt

  ticketUrl    String?
  rawEventData Json?

  // Approval fields
  approvalStatus EventApprovalStatus @default(PENDING) // Use the enum
  approvedBy     String? // Admin's User ID
  approvedAt     DateTime? // Timestamp of approval
  approvalNotes  String? // Optional notes

  manager       User           @relation(fields: [managerId], references: [id])
  priorityEvent PriorityEvent? @relation(fields: [priorityEventId], references: [id])

  @@index([managerId])
  @@index([eventId])
  @@index([priorityEventId])
  @@index([date])
  @@index([category])
}

model PriorityEvent {
  id      String  @id @default(cuid())
  eventId String? // Reference to external event (e.g., Ticketmaster)
  batchId String? // For bulk operations

  // Event Details
  name     String
  category EventCategory
  source   String
  date     DateTime
  venue    String
  city     String
  country  String
  image    String?

  // Status and Metrics
  isPopular  Boolean @default(false)
  isActive   Boolean @default(true)
  viewCount  Int     @default(0)
  clickCount Int     @default(0)

  // Timestamps
  addedAt   DateTime @default(now())
  updatedAt DateTime @updatedAt

  // URLs
  ticketUrl  String?
  seatmapUrl String? // 🆕 ADD: Seatmap URL for venue layout

  // Raw Data
  rawEventData Json?

  // Relationships
  managerEvents ManagerEvent[] // One-to-many relationship with ManagerEvent

  @@unique([eventId, source])
  @@index([eventId])
  @@index([date])
  @@index([category])
}

// Membership tiers for determining user benefits and queue priorities
enum MembershipTier {
  STANDARD // Regular users
  SUBSCRIBER // Monthly subscription users
  VIP // Premium members with highest priority
}

// Status options for users in a queue
enum QueueUserStatus {
  WAITING // User is in the queue waiting for admission
  ACTIVE // User has been admitted and can proceed to checkout
  EXPIRED // User's active session has expired without completing checkout
  COMPLETED // User has completed their checkout process
}

// New model for tracking queue state per event
model Queue {
  id String @id @default(uuid())

  // Reference to the event this queue is for
  // Assuming managerEventId is the correct field - adjust if needed
  eventId String

  // Whether this queue is currently active (users must wait)
  isActive Boolean @default(false)

  // Timestamps for monitoring and analytics
  activatedAt    DateTime? // When the queue was last activated
  deactivatedAt  DateTime? // When the queue was last deactivated
  lastAdmittedAt DateTime? // When the last user was admitted

  // Queue configuration (optional)
  batchSize Int @default(10) // How many users to admit at once

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  users QueueUser[] // Users in this queue

  // Optional: If you have a ManagerEvent model, add a relation
  // Uncomment and adjust if applicable
  // event ManagerEvent @relation(fields: [eventId], references: [id])

  // Indexes for performance
  @@index([eventId])
  @@index([isActive])
}

// New model for tracking individual users in queues
model QueueUser {
  id String @id @default(uuid())

  // Foreign keys
  userId  String
  queueId String

  // Queue positioning and status
  entryTime DateTime        @default(now()) // When user joined the queue
  status    QueueUserStatus @default(WAITING)
  priority  Int             @default(1) // Higher number = higher priority

  // Timestamps for status changes
  admittedAt DateTime? // When user was admitted (status -> ACTIVE)
  expiresAt  DateTime? // When current ACTIVE status expires

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user  User  @relation(fields: [userId], references: [id])
  queue Queue @relation(fields: [queueId], references: [id])

  // Prevent duplicate entries for same user in same queue
  @@unique([userId, queueId])
  // Indexes for queue operations and lookups
  @@index([queueId, status, priority, entryTime]) // For finding next users to admit
  @@index([userId, status]) // For checking if user is already in queue
}

// Checkout Session Status Enum
enum CheckoutSessionStatus {
  PENDING // Initial state when session is created
  COMPLETED // Payment successful and checkout complete
  CANCELLED // User cancelled the checkout
  EXPIRED // Session timed out without completion
  FAILED // Payment or other processing error
  ACTIVE // Reserved for later use
  RESERVED // Reserved for later use
}

// Add these new payment-related enums
enum PaymentProcessor {
  STRIPE
  PAYPAL // For future expansion
}

enum PaymentRecordStatus {
  SUCCEEDED
  FAILED
  REFUNDED
  PARTIALLY_REFUNDED
  CANCELED
}

// Add new PaymentRecord model
model PaymentRecord {
  id                   String              @id @default(uuid())
  userId               String // User who made the payment
  checkoutSessionId    String? // Optional link to checkout session
  processor            PaymentProcessor // Which payment processor
  processorPaymentId   String // Payment ID from processor (e.g. Stripe's paymentIntentId)
  status               PaymentRecordStatus // Payment status
  amount               Int // Amount in smallest currency unit (cents)
  currency             String // 3-letter currency code (e.g., "USD")
  paymentMethodDetails String? // Description of payment method (e.g., "Visa **** 4242")
  description          String? // Optional payment description
  processedAt          DateTime // When payment was processed
  refundedAmount       Int? // Amount refunded (if applicable)
  refundedAt           DateTime? // When refund occurred (if applicable)
  metadata             Json? // Additional payment details

  // Relations
  user            User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  checkoutSession CheckoutSession? @relation(fields: [checkoutSessionId], references: [id], onDelete: SetNull)

  @@index([userId, processedAt])
  @@index([checkoutSessionId])
  @@index([processor, processorPaymentId])
  @@map("payment_records")
}

// CheckoutSession Model
model CheckoutSession {
  id String @id @default(uuid())

  // Relations
  userId  String // User who initiated checkout
  eventId String // Event being purchased from

  // Add opposite relation to PaymentRecord
  paymentRecords PaymentRecord[] // All payment records for this session

  // Status tracking
  status        CheckoutSessionStatus @default(PENDING)
  statusMessage String? // Optional message about status (e.g., error details)

  // Items and pricing
  items      Json // Array of {inventoryId, quantity, price, etc.}
  subtotal   Float // Sum of item prices before fees
  serviceFee Float // Service fee amount
  tax        Float? // Tax if applicable
  total      Float // Final total amount
  currency   String @default("USD")

  // Added fields for discounts
  couponDiscount Json? // Coupon discount details
  appliedPoints  Json? // Applied points details

  // Timing
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  expiresAt   DateTime // When session becomes invalid if not completed
  completedAt DateTime? // When checkout was completed (if status is COMPLETED)

  // Payment details
  paymentMethod   String? // Payment method identifier
  paymentIntentId String? // External payment processor reference
  paymentStatus   String? // Status from payment processor

  // Transaction reference
  transactionId String? // Final transaction ID after successful payment
  receiptUrl    String? // URL to receipt/confirmation

  // IP and device info for security
  ipAddress String?
  userAgent String?

  // Field to store selected billing address snapshot
  billingAddress Json? // New field

  // 🆕 NEW: Add the opposite relation for TicketMessage
  ticketMessages TicketMessage[] // Messages related to this checkout session

  // Relations with models via fields
  user User @relation(fields: [userId], references: [id])

  // Indexes for query performance
  @@index([userId])
  @@index([eventId])
  @@index([status])
  @@index([createdAt])
}

// New BillingAddress model
model BillingAddress {
  id           String   @id @default(cuid())
  userId       String
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  name         String? // New: Name for the billing address (e.g., cardholder name)
  email        String? // New: Email associated with this billing address (optional)
  addressLine1 String
  addressLine2 String?
  city         String
  state        String // Or province
  postalCode   String
  country      String // Consider using an enum or a standardized country code (e.g., ISO 3166-1 alpha-2)
  isDefault    Boolean  @default(false)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@index([userId])
  @@map("billing_addresses")
}

// Ticket Communication model - connects Visitor ↔ Manager via CheckoutSession
model TicketMessage {
  id                String        @id @default(uuid())
  checkoutSessionId String
  senderId          String
  senderRole        UserRole
  message           String        @db.Text
  status            MessageStatus @default(UNREAD) // 🔄 Updated to use the enum
  conversationId    String? // Optional grouping identifier
  parentMessageId   String? // For threading/replies
  isEscalated       Boolean       @default(false)
  escalatedBy       String? // Admin who escalated
  escalatedAt       DateTime?
  metadata          Json? // Additional data (IP, user agent, etc.)
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  // Relations
  checkoutSession CheckoutSession @relation(fields: [checkoutSessionId], references: [id], onDelete: Cascade)
  sender          User            @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)
  escalatedByUser User?           @relation("EscalatedMessages", fields: [escalatedBy], references: [id], onDelete: SetNull)

  // Self-referencing relation for threading
  parentMessage TicketMessage?  @relation("MessageThread", fields: [parentMessageId], references: [id])
  replies       TicketMessage[] @relation("MessageThread")

  // Indexes
  @@index([checkoutSessionId])
  @@index([senderId])
  @@index([status])
  @@index([createdAt])
  @@index([conversationId])
  @@map("ticket_messages")
}

// Message status enum
enum MessageStatus {
  UNREAD
  READ
  ARCHIVED
  ESCALATED
  RESOLVED
  CLOSED
}

// pnpm exec prisma migrate dev --name update_for_billing_address_feature
// pnpm exec prisma generate
