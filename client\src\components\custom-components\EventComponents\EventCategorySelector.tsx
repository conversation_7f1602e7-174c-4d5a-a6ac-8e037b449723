import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";

interface SportCategory {
  name: string;
  icon: string;
  color: string;
}

const sportsCategories: SportCategory[] = [
  { name: "Horse Racing", icon: "🏇", color: "from-blue-300 to-blue-500" },
  { name: "Formula One", icon: "🏎️", color: "from-red-500 to-red-700" },
  { name: "Boxing", icon: "🥊", color: "from-red-300 to-red-500" },
  { name: "MMA", icon: "🥋", color: "from-gray-500 to-gray-700" },
  { name: "Olympics", icon: "🏅", color: "from-purple-400 to-indigo-600" },
  { name: "Trending", icon: "🔥", color: "from-orange-400 to-pink-600" },
  { name: "Football", icon: "⚽", color: "from-green-400 to-emerald-600" },
  { name: "Basketball", icon: "🏀", color: "from-orange-400 to-red-600" },
  { name: "<PERSON>", icon: "🎾", color: "from-yellow-400 to-yellow-600" },
  { name: "Baseball", icon: "⚾", color: "from-blue-400 to-indigo-600" },
  { name: "Hockey", icon: "🏒", color: "from-blue-300 to-blue-500" },
  { name: "Golf", icon: "⛳", color: "from-green-300 to-green-500" },
];

export const EventCategorySelector: React.FC = () => {
  const [showMore, setShowMore] = useState(false);
  const [visibleCategories, setVisibleCategories] = useState<SportCategory[]>(
    []
  );
  const [windowWidth, setWindowWidth] = useState(0);

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    handleResize();
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    if (windowWidth < 640) {
      // sm breakpoint
      setVisibleCategories(
        showMore ? sportsCategories : sportsCategories.slice(0, 4)
      );
    } else if (windowWidth < 768) {
      // md breakpoint
      setVisibleCategories(
        showMore ? sportsCategories : sportsCategories.slice(0, 6)
      );
    } else {
      setVisibleCategories(sportsCategories);
    }
  }, [windowWidth, showMore]);

  const showMoreButton = visibleCategories.length < sportsCategories.length;

  return (
    <div className="w-full  mx-auto px-4 py-8">
      <div className="bg-gradient-to-br from-blue-100 to-purple-100 rounded-3xl shadow-xl p-8">
        <h2 className="text-3xl font-bold mb-6 text-gray-800 text-center">
          Explore Events
        </h2>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {visibleCategories.map((category) => (
            <Button
              key={category.name}
              variant="ghost"
              className={`w-full text-white bg-gradient-to-r ${category.color} 
                          hover:scale-105 transition-all duration-200 ease-out
                          rounded-full py-8 px-4 shadow-lg backdrop-blur-md 
                          backdrop-filter bg-opacity-30 hover:bg-opacity-50
                          text-xs sm:text-sm md:text-base flex items-center justify-center`}
            >
              <span className="mr-2 text-2xl">{category.icon}</span>
              <span className="truncate">{category.name}</span>
            </Button>
          ))}
          {showMoreButton && (
            <Button
              variant="ghost"
              onClick={() => setShowMore(true)}
              className="w-full text-gray-600 bg-gradient-to-r from-gray-200 to-gray-300
                         hover:scale-105 transition-all duration-200 ease-out
                         rounded-full py-2 px-4 shadow-lg backdrop-blur-md 
                         backdrop-filter bg-opacity-30 hover:bg-opacity-50
                         text-xs sm:text-sm md:text-base flex items-center justify-center"
            >
              <span className="mr-2 text-xl">➕</span>
              More
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
