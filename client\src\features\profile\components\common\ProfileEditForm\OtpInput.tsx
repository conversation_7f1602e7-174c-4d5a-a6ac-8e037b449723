import React, { useState, useRef, useEffect } from 'react';
import { Input } from '@/components/ui/input';

interface OtpInputProps {
  length?: number;
  onComplete: (otp: string) => void;
}

export const OtpInput: React.FC<OtpInputProps> = ({ 
  length = 6,
  onComplete 
}) => {
  const [otp, setOtp] = useState<string[]>(Array(length).fill(''));
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Pre-create refs array with the correct length
  useEffect(() => {
    inputRefs.current = Array(length).fill(null);
  }, [length]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const value = e.target.value;
    
    // Only accept digits
    if (!/^\d*$/.test(value)) return;
    
    // Update the OTP array
    const newOtp = [...otp];
    // Take only the last char if multiple are pasted or entered
    newOtp[index] = value.substring(value.length - 1);
    setOtp(newOtp);
    
    // Auto-focus next input after entry
    if (value && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }
    
    // If all fields are filled, call onComplete
    if (newOtp.every(digit => digit !== '') || (value && index === length - 1)) {
      onComplete(newOtp.join(''));
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, index: number) => {
    // Handle backspace
    if (e.key === 'Backspace') {
      if (otp[index] === '' && index > 0) {
        // If current input is empty, focus previous input
        inputRefs.current[index - 1]?.focus();
      } else {
        // Clear current input
        const newOtp = [...otp];
        newOtp[index] = '';
        setOtp(newOtp);
      }
    }
    
    // Handle left arrow key
    if (e.key === 'ArrowLeft' && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
    
    // Handle right arrow key
    if (e.key === 'ArrowRight' && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text/plain').trim();
    
    // Check if pasted data is all digits
    if (!/^\d+$/.test(pastedData)) return;
    
    // Fill the OTP inputs with pasted data
    const newOtp = [...otp];
    for (let i = 0; i < Math.min(pastedData.length, length); i++) {
      newOtp[i] = pastedData[i];
    }
    setOtp(newOtp);
    
    // Focus the next empty input or the last input
    for (let i = 0; i < length; i++) {
      if (i >= pastedData.length) {
        inputRefs.current[i]?.focus();
        break;
      }
      if (i === length - 1) {
        inputRefs.current[i]?.focus();
      }
    }
    
    // If we've filled all inputs, call onComplete
    if (pastedData.length >= length) {
      onComplete(newOtp.join(''));
    }
  };

  console.log('🔢 OTP input rendered with', length, 'digits');

  return (
    <div className="flex justify-center gap-2">
      {Array.from({ length }).map((_, index) => (
        <Input
          key={index}
          type="text"
          inputMode="numeric"
          ref={el => { inputRefs.current[index] = el }}
          value={otp[index]}
          onChange={(e) => handleChange(e, index)}
          onKeyDown={(e) => handleKeyDown(e, index)}
          onPaste={handlePaste}
          className="w-10 h-12 text-center text-lg"
          maxLength={1}
          autoComplete="off"
        />
      ))}
    </div>
  );
};
