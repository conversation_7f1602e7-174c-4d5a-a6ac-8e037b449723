import { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ShoppingBag, AlertTriangle, CheckCircle, ArrowRight, CreditCard } from 'lucide-react';
import { cn } from '@/lib/utils';
import { CheckoutSession, CheckoutSessionStatus } from '../types/checkout.types';
import { ReservationTimer } from './ReservationTimer';
import { CouponInput } from './CouponInput';
import { PointsSelector } from './PointsSelector';
import { format } from "date-fns";
import { Calendar, Building2, MapPin } from "lucide-react";
import Image from "next/image";


interface CheckoutSummaryProps {
  session: CheckoutSession | null;
  isLoading: boolean;
  expiresAt: string | undefined | null;
  isExpired: boolean;
  onApplyCoupon: (code: string) => void;
  onApplyPoints: (points: number) => void;
  onRefreshSession: () => void;
  onProceedToPayment: () => void;
  onReturnToEvent: () => void;
  isApplyingCoupon: boolean;
  isApplyingPoints: boolean;
  isRefreshingSession: boolean;
  isProceedingToPayment: boolean;
  userPointsBalance?: number;
  className?: string;
}

/**
 * Displays a summary of the checkout session with items, pricing, discounts,
 * and allows applying coupons/points and proceeding to payment
 */
export const CheckoutSummary: React.FC<CheckoutSummaryProps> = ({
  session,
  isLoading,
  expiresAt,
  isExpired,
  onApplyCoupon,
  onApplyPoints,
  onRefreshSession,
  onProceedToPayment,
  onReturnToEvent,
  isApplyingCoupon,
  isApplyingPoints,
  isRefreshingSession,
  isProceedingToPayment,
  userPointsBalance = 0,
  className
}) => {
  const [activeTab, setActiveTab] = useState<'items' | 'discounts'>('items');
    // Add console log here to see the raw session data when the component renders or updates
    console.log("CheckoutSummary session details:", session);

  // Compute all the summary values from the session
  const summary = useMemo(() => {
    if (!session) return null;
     console.log("Calculating summary for session:", session); // Debug log inside useMemo


    // These values come directly from the session object provided by the backend
    const subtotal = session.subtotal || 0;
    const serviceFee = session.serviceFee || 0; // *** CHANGED: Use session.serviceFee (singular) ***
    const tax = session.tax || 0;               // Get tax from the session object
    const couponDiscount = session.couponDiscount?.discountAmount || 0;
    const pointsDiscount = session.appliedPoints?.discountAmount || 0;

    // Primarily rely on the total calculated by the backend
    const total = session.total || 0;

    return {
      subtotal,
      serviceFee, // *** CHANGED: Return serviceFee (singular) ***
      tax,        // Include tax
      couponDiscount,
      pointsDiscount,
      total,
      totalItems: session.items.reduce((sum, item) => sum + item.quantity, 0)
    };
  }, [session]);

  // Check if session is active
  const isSessionActive = !!session && session.status === CheckoutSessionStatus.RESERVED && !isExpired;

  // Determine if checkout can proceed
  const canProceedToPayment = isSessionActive && !isExpired;

  // If loading or no session, show loading state
  if (isLoading || (!session && !isExpired)) {
    return (
      <Card className={cn("w-full shadow-md rounded-xl overflow-hidden", className)}>
        <CardHeader className="bg-gradient-to-r from-primary/5 to-primary/10 border-b">
          <CardTitle className="flex items-center">
            <ShoppingBag className="mr-2 h-5 w-5 text-primary" />
            <span>Checkout Summary</span>
          </CardTitle>
          <CardDescription>Loading your checkout information...</CardDescription>
        </CardHeader>
        <CardContent className="h-64 flex items-center justify-center">
          <div className="animate-pulse flex flex-col items-center">
            <div className="h-10 w-10 bg-slate-200 rounded-full mb-4"></div>
            <div className="h-4 w-48 bg-slate-200 rounded mb-2"></div>
            <div className="h-4 w-36 bg-slate-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // If session expired, show expired state
  if (isExpired) {
    return (
      <Card className={cn("w-full border-red-300 shadow-md rounded-xl overflow-hidden", className)}>
        <CardHeader className="bg-red-50 border-b border-red-200">
          <CardTitle className="flex items-center text-red-700">
            <AlertTriangle className="mr-2 h-5 w-5" />
            <span>Reservation Expired</span>
          </CardTitle>
          <CardDescription className="text-red-600">
            Your ticket reservation has expired. Please return to the event to start over.
          </CardDescription>
        </CardHeader>
        <CardContent className="py-8">
          <div className="flex flex-col items-center text-center p-4">
            <AlertTriangle className="h-16 w-16 text-red-500 mb-6" />
            <h3 className="text-xl font-semibold mb-3">Session Timed Out</h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              The tickets you selected are no longer reserved and may not be available.
            </p>
            <Button 
              onClick={onReturnToEvent}
              className="mt-2 bg-primary hover:bg-primary/90 px-6 py-2 h-11"
              size="lg"
            >
              Return to Event Selection
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Render active session
  if (!session) return null;

  return (
    <Card className={cn("w-full shadow-md rounded-xl overflow-hidden", className)}>
      {session && session.eventName && (
        <div className="border-b bg-gradient-to-r from-primary/5 to-primary/10 p-4">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-3">
            {/* Event Name and Details */}
            <div>
              <h2 className="text-lg lg:text-xl font-bold mb-1">{session.eventName}</h2>
              <div className="flex flex-wrap items-center gap-x-4 gap-y-2 text-sm text-muted-foreground">
                {session.eventDate && (
                  <span className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1.5" /> 
                    {format(new Date(session.eventDate), "EEEE, MMMM d, yyyy 'at' h:mm a")}
                  </span>
                )}
                {session.eventVenue && (
                  <span className="flex items-center">
                    <Building2 className="h-4 w-4 mr-1.5" /> {session.eventVenue}
                  </span>
                )}
                {(session.eventCity || session.eventCountry) && (
                  <span className="flex items-center">
                    <MapPin className="h-4 w-4 mr-1.5" /> 
                    {[session.eventCity, session.eventCountry].filter(Boolean).join(", ")}
                  </span>
                )}
              </div>
            </div>
            
            {/* Small event image - optional */}
            {session.eventImage && (
              <div className="hidden md:block h-16 w-24 rounded-md overflow-hidden relative shrink-0">
                <Image
                  src={session.eventImage} 
                  alt={session.eventName} 
                  className="object-cover w-full h-full"
                  fill
                />
              </div>
            )}
          </div>
        </div>
      )}
      <CardHeader className="bg-gradient-to-r from-primary/5 to-primary/10 border-b pb-5">
        <CardTitle className="flex items-center justify-between mb-1">
          <div className="flex items-center">
            <ShoppingBag className="mr-2 h-5 w-5 text-primary" />
            <span>Checkout Summary</span>
          </div>
          {summary && (
            <span className="text-sm font-normal text-muted-foreground px-2 py-1 bg-background/80 rounded-full">
              {summary.totalItems} {summary.totalItems === 1 ? 'item' : 'items'}
            </span>
          )}
        </CardTitle>
        <CardDescription className="text-sm">
          Review your order and apply any discounts before proceeding to payment.
        </CardDescription>

        {/* Reservation Timer Display - No Extend Time button */}
        {isSessionActive && (
          <ReservationTimer
            expiresAt={expiresAt}
            onRefresh={onRefreshSession} // We'll keep the function prop but hide the button in ReservationTimer
            isRefreshing={isRefreshingSession}
            className="mt-3"
            hideRefreshButton={true} // Add this prop to hide the button
          />
        )}
      </CardHeader>

      <CardContent className="p-0">
        {/* Tab Selection */}
        <div className="flex border-b">
          <button 
            className={cn(
              "py-3 px-6 font-medium text-sm flex-1 transition-colors",
              activeTab === 'items' 
                ? "border-b-2 border-primary text-primary bg-primary/5" 
                : "text-muted-foreground hover:bg-muted/50"
            )}
            onClick={() => setActiveTab('items')}
          >
            Your Tickets
          </button>
          <button 
            className={cn(
              "py-3 px-6 font-medium text-sm flex-1 transition-colors",
              activeTab === 'discounts' 
                ? "border-b-2 border-primary text-primary bg-primary/5" 
                : "text-muted-foreground hover:bg-muted/50"
            )}
            onClick={() => setActiveTab('discounts')}
          >
            Apply Discounts
          </button>
        </div>

        <div className="p-5">
          {/* Items Tab Content */}
          {activeTab === 'items' && (
            <div className="space-y-4">
              {session.items.map((item, index) => (
                <div key={item.inventoryId || index} className="flex flex-col sm:flex-row justify-between p-3 border rounded-lg bg-card hover:bg-muted/20 transition-colors">
                  {/* Left side details */}
                  <div className='mb-2 sm:mb-0'>
                    {/* Use the constructed name */}
                    <p className="font-semibold text-base">{item.name || 'Ticket'}</p>
                    <p className="text-sm text-muted-foreground mt-1">
                      {item.quantity} {item.quantity === 1 ? 'ticket' : 'tickets'}
                    </p>
                    {/* Display Ticket Format */}
                    {item.ticketFormat && (
                      <p className="text-xs text-muted-foreground mt-1">Format: {item.ticketFormat}</p>
                    )}
                     {/* Display Attributes */}
                    {item.attributes && item.attributes.length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {item.attributes.map((attr, idx) => (
                           <span key={idx} className="text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">
                             {attr}
                           </span>
                        ))}
                      </div>
                    )}
                  </div>
                  {/* Right side pricing */}
                  <div className="text-left sm:text-right mt-2 sm:mt-0">
                    {/* Display the SUBtotal for this line item (price * quantity) */}
                    <p className="font-medium text-base">${item.subtotal?.toFixed(2) ?? 'N/A'}</p>
                    {/* Display base price per ticket if quantity > 1 */}
                    {item.price && item.quantity > 1 && (
                      <p className="text-xs text-muted-foreground mt-1">(${item.price.toFixed(2)} each)</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Discounts Tab Content */}
          {activeTab === 'discounts' && (
            <div className="space-y-6 py-2">
              <CouponInput 
                onApply={onApplyCoupon}
                isApplying={isApplyingCoupon}
                appliedCoupon={session.couponDiscount}
              />
              
              <Separator className="my-6" />
              
              <PointsSelector
                onApply={onApplyPoints}
                isApplying={isApplyingPoints}
                appliedPoints={session.appliedPoints}
                userPointsBalance={userPointsBalance}
                orderTotal={summary?.subtotal || 0}
              />
            </div>
          )}

          {/* Price Summary - Always Visible */}
          {summary && (
            <div className="mt-8 pt-5 border-t space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Subtotal</span>
                <span>${summary.subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Service Fee</span>
                <span>${summary.serviceFee.toFixed(2)}</span>
              </div>
              
              {summary.tax > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Tax</span>
                  <span>${summary.tax.toFixed(2)}</span>
                </div>
              )}
              
              {summary.couponDiscount > 0 && (
                <div className="flex justify-between text-sm text-green-600">
                  <span className="flex items-center">
                    <CheckCircle className="h-3.5 w-3.5 mr-1.5" />
                    Coupon ({session.couponDiscount?.couponCode || 'Discount'})
                  </span>
                  <span>-${summary.couponDiscount.toFixed(2)}</span>
                </div>
              )}
              
              {summary.pointsDiscount > 0 && (
                <div className="flex justify-between text-sm text-green-600">
                  <span className="flex items-center">
                    <CheckCircle className="h-3.5 w-3.5 mr-1.5" />
                    Credit Points ({session.appliedPoints?.pointsUsed.toLocaleString() || ''})
                  </span>
                  <span>-${summary.pointsDiscount.toFixed(2)}</span>
                </div>
              )}
              
              <div className="flex justify-between font-medium pt-3 mt-1 border-t text-base">
                <span>Total</span>
                <span className="text-lg font-bold">${summary.total.toFixed(2)}</span>
              </div>
            </div>
          )}
        </div>
      </CardContent>
      
      <CardFooter className="flex justify-center py-6 px-5 border-t bg-gradient-to-b from-background to-muted/20">
        {/* Only show the proceed button, no Back button */}
        <Button 
          onClick={onProceedToPayment}
          disabled={!canProceedToPayment || isProceedingToPayment}
          className={cn(
            "w-full max-w-md py-6 text-base shadow-md",
            isSessionActive 
              ? "bg-green-600 hover:bg-green-700" 
              : "bg-muted text-muted-foreground",
            "text-white"
          )}
        >
          <CreditCard className="mr-2 h-5 w-5" />
          {isProceedingToPayment ? "Processing..." : "Proceed to Payment"}
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  );
};
