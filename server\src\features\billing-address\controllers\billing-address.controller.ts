// server/src/features/billing-address/controllers/billing-address.controller.ts
import { Request, Response } from 'express';
import { asyncHand<PERSON> } from '@/utils/asyncHandler';
import { BillingAddressService } from '../services/billing-address.service';
import ApiError from '@/utils/ApiError';
import { CreateBillingAddressPayload, UpdateBillingAddressPayload } from '../types/billing-address.types';

export class BillingAddressController {
  static getAll = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.userId;
    if (!userId) throw new ApiError(401, 'Authentication required.');

    const addresses = await BillingAddressService.getAllByUserId(userId);
    res.status(200).json({ success: true, message: 'Billing addresses retrieved.', data: addresses });
  });

  static getById = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.userId;
    if (!userId) throw new ApiError(401, 'Authentication required.');
    const { addressId } = req.params;

    const address = await BillingAddressService.getByIdAndUserId(addressId, userId);
    if (!address) throw new ApiError(404, 'Billing address not found.');
    res.status(200).json({ success: true, message: 'Billing address retrieved.', data: address });
  });

  static create = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.userId;
    if (!userId) throw new ApiError(401, 'Authentication required.');
    const payload = req.body as CreateBillingAddressPayload; // Add validation here

    const newAddress = await BillingAddressService.create(userId, payload);
    res.status(201).json({ success: true, message: 'Billing address created.', data: newAddress });
  });

  static update = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.userId;
    if (!userId) throw new ApiError(401, 'Authentication required.');
    const { addressId } = req.params;
    const payload = req.body as UpdateBillingAddressPayload; // Add validation here

    const updatedAddress = await BillingAddressService.update(addressId, userId, payload);
    res.status(200).json({ success: true, message: 'Billing address updated.', data: updatedAddress });
  });

  static delete = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.userId;
    if (!userId) throw new ApiError(401, 'Authentication required.');
    const { addressId } = req.params;

    await BillingAddressService.delete(addressId, userId);
    res.status(200).json({ success: true, message: 'Billing address deleted.' });
  });

  static setDefault = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.userId;
    if (!userId) throw new ApiError(401, 'Authentication required.');
    const { addressId } = req.params;

    const updatedAddress = await BillingAddressService.setDefault(addressId, userId);
    res.status(200).json({ success: true, message: 'Default billing address set.', data: updatedAddress });
  });
}