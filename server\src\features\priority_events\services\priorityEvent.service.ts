import { PrismaClient, PriorityEvent } from '@prisma/client';
import {
    BulkPriorityEventPayload,
    BulkPriorityEventResponse,
    PriorityEventPayload,
    UpdatePriorityEventPayload,
    TogglePopularPayload,
    DeletePriorityEventPayload
} from '../types/priorityEvent.types';
import { parseDate } from '@/utils/dateUtils';

const prisma = new PrismaClient();

// Service class for handling Priority Event operations
export class PriorityEventService {
    // Create a single priority event or update if it already exists
    static async createPriorityEvent(data: PriorityEventPayload): Promise<PriorityEvent> {
        // console.log('Creating priority event services:', data);
        console.log('Creating priority event services:');

        try {
            // First check if the event already exists
            const existingEvent = await prisma.priorityEvent.findUnique({
                where: {
                    eventId_source: {
                        eventId: data.eventId,
                        source: data.source
                    }
                }
            });

            if (existingEvent) {
                // If event exists, update it instead
                return prisma.priorityEvent.update({
                    where: {
                        id: existingEvent.id  // Use the id instead of compound key
                    },
                    data: {
                        ...data,
                        date: parseDate(data.date),
                        rawEventData: data.rawEventData as any
                    }
                });
            }

            // If event doesn't exist, create new
            return prisma.priorityEvent.create({
                data: {
                    ...data,
                    date: parseDate(data.date),
                    rawEventData: data.rawEventData as any
                }
            });
        } catch (error) {
            console.error('Error in createPriorityEvent:', error);
            throw error;
        }
    }
    // Create multiple priority events in bulk
    static async createBulkPriorityEvents(data: BulkPriorityEventPayload): Promise<BulkPriorityEventResponse> {
        // console.log('Creating bulk priority events:', data);
           console.log('Creating bulk priority events:');
         const { events } = data;

        const results: BulkPriorityEventResponse = {
            created: [],
            errors: []
         };

        try {
           await prisma.$transaction(async (prisma) => {
                for (const [index, eventData] of events.entries()) {
                 try {
                    const createdEvent = await prisma.priorityEvent.create({
                        data: eventData,
                    });
                    results.created.push(createdEvent);
                } catch (error) {
                    console.error(`Error creating event at index ${index}:`, error);
                    results.errors.push({
                        index,
                        message: (error as Error).message || 'Failed to create event',
                    });
                  }
              }
          });
          return results;

        } catch (error) {
            console.error('Transaction error when creating bulk events:', error);
            throw new Error(`Failed to create bulk priority events: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    // Get all priority events
    static async getPriorityEvents(): Promise<PriorityEvent[]> {
        return prisma.priorityEvent.findMany(
            {
                where: {
                    isActive: true,
                    date: {
                        gte: new Date()
                    }
                }
            }
        );
    }

    // Get a single priority event by ID
    static async getPriorityEventById(id: string): Promise<PriorityEvent | null> {
        return prisma.priorityEvent.findUnique({
            where: {
                id,
                isActive: true,
                date: {
                    gte: new Date()
                }
            }
        });
    }

    // Update a priority event
     static async updatePriorityEvent(data: UpdatePriorityEventPayload): Promise<PriorityEvent | null> {
        return prisma.priorityEvent.update({
          where: {
            id: data.id,
          },
          data,
        });
      }
      // Delete a priority event
      static async deletePriorityEvent(payload: DeletePriorityEventPayload): Promise<void> {
          console.log('🔍 Delete request received:', {
              id: payload.id,
              timestamp: new Date().toISOString()
          });

          try {
              // Use transaction to ensure atomicity
              await prisma.$transaction(async (tx) => {
                  const existingEvent = await tx.priorityEvent.findUnique({
                      where: { id: payload.id },
                      select: { id: true, name: true }
                  });

                  if (!existingEvent) {
                      console.log('⚠️ Event not found before deletion:', payload.id);
                      return; // Exit gracefully if event doesn't exist
                  }

                  console.log('🎯 Found event, proceeding with deletion:', existingEvent);

                  await tx.priorityEvent.delete({
                      where: { id: payload.id }
                  });

                  console.log('✅ Event deletion successful:', payload.id);
              });
          } catch (error: any) {
              if (error.code === 'P2025') {
                  console.log('🔄 Event already deleted or not found:', payload.id);
                  return; // Handle gracefully
              }
              throw error;
          }
      }

    // Toggle the popular status of a priority event
     static async togglePopularStatus(payload: TogglePopularPayload): Promise<PriorityEvent | null> {
       const event = await prisma.priorityEvent.findUnique({
           where: { id: payload.id },
       });

       if (!event) {
           return null;
       }

        return prisma.priorityEvent.update({
             where: { id: payload.id },
             data: { isPopular: !event.isPopular },
         });
     }
}