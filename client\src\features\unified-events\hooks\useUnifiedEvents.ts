import { useQuery, useQueryClient } from "@tanstack/react-query";
import axiosInstance from "@/apiAxios/axios";
import { useState, useEffect } from "react";
import {
  UnifiedEvent,
  tmEventToUnified,
  managerEventToUnified,
} from "../adapters/eventAdapter";
import { fetchTmEvents } from "@/features/tm_events/api/tm.api";
import { TmEventQueryParams } from "@/features/tm_events/types/tm.types";
import axios from "axios";

interface UnifiedEventsResponse {
  events: UnifiedEvent[];
  isLoading: boolean;
  error: any;
  pagination: {
    page: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
  };
  setPage: (page: number) => void;
  setPageSize: (size: number) => void;
  filters: any;
  setFilters: (filters: any) => void;
  allManagerEvents: UnifiedEvent[]; // New property for client-side filtering
}

/**
 * Custom hook to fetch both manager events and TM events and combine them
 * Prioritizes approved manager events and supplements with TM events when needed
 */
export const useUnifiedEvents = (
  initialFilters = {},
  loadAllForFiltering = false
): UnifiedEventsResponse => {
  const queryClient = useQueryClient();
  // Fetch more events (30) but display fewer (10) per page
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(30); // Fetch 30 events total
  const [displaySize, setDisplaySize] = useState<number>(10); // But only show 10 per page
  const [filters, setFilters] = useState<any>(initialFilters);
  const [combinedEvents, setCombinedEvents] = useState<UnifiedEvent[]>([]);
  const [allManagerEvents, setAllManagerEvents] = useState<UnifiedEvent[]>([]);

  // Track total counts for pagination calculation
  const [totalManagerEvents, setTotalManagerEvents] = useState<number>(0);
  const [totalTmEvents, setTotalTmEvents] = useState<number>(0);

  // Calculate which manager page to fetch based on current page
  const managerPage = page;

  // Query to fetch approved manager events
  const {
    data: managerData,
    isLoading: isLoadingManager,
    error: managerError,
  } = useQuery({
    queryKey: ["approvedManagerEvents", managerPage, pageSize, filters],
    queryFn: async () => {
      // Construct query params
      const params = new URLSearchParams();
      params.append("page", managerPage.toString());
      params.append("limit", pageSize.toString());

      // Add filters
      if (filters.segment && filters.segment !== "all")
        params.append("segment", filters.segment);
      if (filters.startDate) params.append("startDate", filters.startDate);
      if (filters.city) params.append("city", filters.city);
      if (filters.keyword) params.append("keyword", filters.keyword);

      try {
        // Use axios directly from the library
        const response = await axios.get(
          `/api/v1/manager-events/approved?${params.toString()}`
        );
        const managerEvents = response.data.data || [];
        const managerPagination = response.data.pagination || {
          page: 1,
          pageSize,
          totalItems: 0,
          totalPages: 1,
        };

        console.log(
          `Manager events for page ${managerPage}:`,
          managerEvents.length,
          "total:",
          managerPagination.totalItems
        );
        setTotalManagerEvents(managerPagination.totalItems || 0);

        return {
          events: managerEvents,
          pagination: managerPagination,
        };
      } catch (error) {
        console.error("Error fetching approved manager events:", error);
        return {
          events: [],
          pagination: {
            page: 1,
            pageSize,
            totalItems: 0,
            totalPages: 1,
          },
        };
      }
    },
    staleTime: 60000, // 1 minute
  });

  // New query to fetch ALL manager events for client-side filtering
  const { data: allManagerData, isLoading: isLoadingAllManager } = useQuery({
    queryKey: ["allApprovedManagerEvents"],
    queryFn: async () => {
      if (!loadAllForFiltering) return { events: [] };

      try {
        // Use a large limit to get all events in one request
        const params = new URLSearchParams();
        params.append("page", "1");
        params.append("limit", "1000"); // Large limit to get all events

        const response = await axios.get(
          `/api/v1/manager-events/approved?${params.toString()}`
        );

        const allEvents = response.data.data || [];
        console.log(
          `Loaded all ${allEvents.length} manager events for filtering`
        );

        return { events: allEvents };
      } catch (error) {
        console.error("Error fetching all manager events:", error);
        return { events: [] };
      }
    },
    staleTime: 60000, // 1 minute
    enabled: loadAllForFiltering, // Only run if we need all events for filtering
  });

  // Calculate which TM page we need to fetch
  const calculateTmPage = () => {
    const managerTotalItems = totalManagerEvents;
    const managerPages = Math.ceil(managerTotalItems / pageSize);

    // If we're beyond manager pages, calculate which TM page to fetch
    if (page > managerPages) {
      // If we're on page 4 and manager has 2 pages (20 items with pageSize 10),
      // we need TM page 2 (0-based would be 1)
      return page - managerPages;
    }

    // If we're still within manager pages but need some TM items
    // (e.g., page 2, but page 2 only has 5 manager items),
    // fetch the first page of TM events
    return 0; // 0-based indexing for TM API
  };

  // Ensure TM events are always fetched
  const needTmEvents = () => {
    return true; // Always fetch TM events
  };

  // Query to fetch TM events using the existing fetchTmEvents function
  const {
    data: tmData,
    isLoading: isLoadingTm,
    error: tmError,
  } = useQuery({
    queryKey: ["tmEvents", calculateTmPage(), pageSize, filters],
    queryFn: async () => {
      const tmPage = calculateTmPage();

      // Create the query params for TM API
      //todo: // make the filter workabe hybrid appoarch
      const tmQueryParams: TmEventQueryParams = {
        page: tmPage.toString(),
        size: pageSize.toString(),
        keyword: filters.keyword || "",
        city: filters.city || "",
        startDate: filters.startDate || "",
        segment: filters.segment !== "all" ? filters.segment : "",
      };

      try {
        console.log(`Fetching TM events for TM page ${tmPage}`);
        const tmResponse = await fetchTmEvents(tmQueryParams);
        console.log(`TM events for page ${tmPage}:`, tmResponse.events.length);
        setTotalTmEvents(tmResponse.pagination.total || 0);

        return tmResponse;
      } catch (error) {
        console.error("Error fetching TM events:", error);
        return {
          events: [],
          pagination: {
            page: 0,
            size: pageSize,
            total: 0,
            totalPages: 0,
          },
        };
      }
    },
    staleTime: 60000, // 1 minute
    enabled: true, // Always enable TM events fetching
  });
  // Combine both event types whenever either data changes
  useEffect(() => {
    if (!isLoadingManager) {
      // Manager events for this page
      const managerEvents = (managerData?.events || []).map(
        managerEventToUnified
      );

      // TM events
      const tmEvents = (tmData?.events || []).map(tmEventToUnified);

      // Combine both types of events - ensure we're getting enough events
      const eventsForCurrentPage = [...managerEvents, ...tmEvents].slice(0, pageSize);

      setCombinedEvents(eventsForCurrentPage);
    }
  }, [managerData, tmData, page, pageSize, isLoadingManager]);

  // Calculate combined pagination
  const calculatePagination = () => {
    // Total items is manager events + TM events
    const totalItems = totalManagerEvents + totalTmEvents;

    // Calculate total pages based on display size, not fetch size
    const totalPages = Math.max(3, Math.ceil(totalItems / displaySize));

    return {
      page,
      pageSize: displaySize, // Use display size for pagination
      totalItems,
      totalPages,
    };
  };

  // Store all manager events when they're loaded
  useEffect(() => {
    if (loadAllForFiltering && allManagerData?.events) {
      const mappedEvents = allManagerData.events.map(managerEventToUnified);
      setAllManagerEvents(mappedEvents);
    }
  }, [loadAllForFiltering, allManagerData]);

  return {
    events: combinedEvents,
    allManagerEvents, // New property for client-side filtering
    isLoading: isLoadingManager || (loadAllForFiltering && isLoadingAllManager),
    error: managerError,
    pagination: calculatePagination(),
    setPage,
    setPageSize: setDisplaySize, // Use setDisplaySize for the public API
    filters,
    setFilters,
  };
};
