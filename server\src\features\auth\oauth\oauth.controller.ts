import { Request, Response } from "express";
import { OAuthService } from "./oauth.service";
import { asyncHandler } from "../../../utils/asyncHandler";
import ApiError from "../../../utils/ApiError";

const oauthService = new OAuthService();

export const syncOAuthUser = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { user, account, profile } = req.body;

      const syncedUser = await oauthService.syncUser({
        profile: {
          id: profile.sub || profile.id,
          email: profile.email,
          name: profile.name,
          image: profile.picture || profile.avatar_url,
          provider: account.provider,
        },
        account: {
          provider: account.provider,
          type: account.type,
          providerAccountId: account.providerAccountId,
          access_token: account.access_token,
          token_type: account.token_type,
          expires_at: account.expires_at,
        },
      });

      res.status(200).json({
        success: true,
        user: {
          id: syncedUser.id,
          email: syncedUser.email,
          fullName: syncedUser.fullName,
        },
      });
    } catch (error) {
      throw ApiError.internal("Failed to sync OAuth user");
    }
  }
);
