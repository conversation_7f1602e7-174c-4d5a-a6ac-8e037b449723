import React, { useState, useEffect } from "react";
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerClose,
} from "@/components/ui/drawer";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Calendar } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { format } from "date-fns";
import { UnifiedEvent } from "@/features/unified-events/adapters/eventAdapter";

interface ClientSideFilterDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  allEvents: UnifiedEvent[];
  onFilteredEventsChange: (filteredEvents: UnifiedEvent[]) => void;
}

export const ClientSideFilterDrawer: React.FC<ClientSideFilterDrawerProps> = ({
  isOpen,
  onClose,
  allEvents,
  onFilteredEventsChange,
}) => {
  // Filter state
  const [searchTerm, setSearchTerm] = useState("");
  const [cityFilter, setCityFilter] = useState("");
  const [venueFilter, setVenueFilter] = useState("");
  const [countryFilter, setCountryFilter] = useState("");
  const [dateFilter, setDateFilter] = useState<Date | undefined>(undefined);

  // Apply filters whenever filter state changes
  useEffect(() => {
    // Only filter manager events
    const managerEvents = allEvents.filter(event => event.source === "manager");
    
    // Apply all active filters
    const filtered = managerEvents.filter(event => {
      // Search term filter (checks name, city, venue, country)
      const searchTermMatch = !searchTerm || 
        event.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.city?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.venue?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.country?.toLowerCase().includes(searchTerm.toLowerCase());
      
      // Specific field filters
      const cityMatch = !cityFilter || 
        event.city?.toLowerCase().includes(cityFilter.toLowerCase());
      
      const venueMatch = !venueFilter || 
        event.venue?.toLowerCase().includes(venueFilter.toLowerCase());
      
      const countryMatch = !countryFilter || 
        event.country?.toLowerCase().includes(countryFilter.toLowerCase());
      
      // Date filter
      const dateMatch = !dateFilter || 
        (event.date && isSameDay(event.date, dateFilter));
      
      return searchTermMatch && cityMatch && venueMatch && countryMatch && dateMatch;
    });
    
    // Update parent component with filtered events
    onFilteredEventsChange(filtered);
  }, [searchTerm, cityFilter, venueFilter, countryFilter, dateFilter, allEvents, onFilteredEventsChange]);

  // Helper function to check if two dates are the same day
  const isSameDay = (date1: Date, date2: Date): boolean => {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  };

  // Reset all filters
  const handleReset = () => {
    setSearchTerm("");
    setCityFilter("");
    setVenueFilter("");
    setCountryFilter("");
    setDateFilter(undefined);
  };

  return (
    <Drawer open={isOpen} onOpenChange={onClose}>
      <DrawerContent className="bg-white w-screen">
        <DrawerHeader>
          <DrawerTitle>Filter Manager Events</DrawerTitle>
          <DrawerClose />
        </DrawerHeader>
        <div className="p-4 space-y-4">
          {/* General search */}
          <div className="space-y-2">
            <Label htmlFor="search">Search Events</Label>
            <Input
              id="search"
              type="text"
              placeholder="Search by name, city, venue..."
              className="w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          {/* City filter */}
          <div className="space-y-2">
            <Label htmlFor="city">City</Label>
            <Input
              id="city"
              type="text"
              placeholder="Filter by city"
              className="w-full"
              value={cityFilter}
              onChange={(e) => setCityFilter(e.target.value)}
            />
          </div>
          
          {/* Venue filter */}
          <div className="space-y-2">
            <Label htmlFor="venue">Venue</Label>
            <Input
              id="venue"
              type="text"
              placeholder="Filter by venue"
              className="w-full"
              value={venueFilter}
              onChange={(e) => setVenueFilter(e.target.value)}
            />
          </div>
          
          {/* Country filter */}
          <div className="space-y-2">
            <Label htmlFor="country">Country</Label>
            <Input
              id="country"
              type="text"
              placeholder="Filter by country"
              className="w-full"
              value={countryFilter}
              onChange={(e) => setCountryFilter(e.target.value)}
            />
          </div>
          
          {/* Date filter */}
          <div className="space-y-2">
            <Label>Event Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-start text-left">
                  <Calendar className="mr-2 h-4 w-4" />
                  {dateFilter ? format(dateFilter, "PPP") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <CalendarComponent
                  mode="single"
                  selected={dateFilter}
                  onSelect={setDateFilter}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          
          {/* Reset button */}
          <Button 
            variant="outline" 
            className="w-full"
            onClick={handleReset}
          >
            Reset Filters
          </Button>
        </div>
      </DrawerContent>
    </Drawer>
  );
};
