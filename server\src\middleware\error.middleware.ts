import { Request, Response, NextFunction } from 'express';
import ApiError from '../utils/ApiError';

export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
    //! testing
    console.error('Error occurred:', {
        path: req.path,
        method: req.method,
        error: err.message
      });

  if (err instanceof ApiError) {
    return res.status(err.statusCode).json({
      success: false,
      message: err.message,
      errors: err.errors
    });
  }

  return res.status(500).json({
    success: false,
    message: 'Internal Server Error',
    errors: process.env.NODE_ENV === 'development' ? err.stack : undefined
  });
};
