// Define a basic structure for inventory items as observed in rawEventData for manager events
// This should ideally align with or be imported from your main InventoryItem type if available
// (e.g., from "@/features/event-listing/types/eventListing")
export interface BasicInventoryItem {
  id: string;
  section?: string | null;
  row?: string | null;
  price?: number | null; // Price from seller
  listPrice?: number | null; // Price displayed to public (might include initial markups but not service fees)
  serviceFee?: number | null;
  quantity?: number | null;
  attributes?: string[] | null;
  disclosures?: string[] | null;
  internalNote?: string | null;
  publicNote?: string | null;
  seatingType?: string | null;
  sellingPreference?: string | null;
  termsAndConditions?: boolean | null;
  ticketFormat?: string | null;
  [key: string]: any; // Allow other dynamic properties if necessary
}

// Define a more specific structure for rawEventData when source is 'manager'
export interface RawManagerEventData {
  id?: string; // This is typically the original source's event ID (like eventId from CreatePriorityEventPayload)
  name?: string;
  category?: string; // Category from the raw source, could differ from the curated PriorityEventData.category
  date?: string; // ISO date string
  venue?: string;
  city?: string;
  country?: string;
  inventory?: BasicInventoryItem[] | null;
  status?: string; // e.g., "LISTED"
  // Include any other known properties within rawEventData for manager events
  [key: string]: any;
}

// Define the payload type for creating a priority event
export interface CreatePriorityEventPayload {
  eventId: string; // ID from the original source system (e.g., manager's internal ID or TM event ID)
  name: string;

  category: "SPORTS" | "MUSIC" | "ARTS"; // Curated category for display and filtering
  source: "manager" | "ticketmaster" | string; // Known sources, or other strings (mapping code defaults to 'manager')
  date: string; // ISO date string for the event
  venue: string;
  city: string;
  country: string;

  image?: string | null;
  seatmapUrl?: string | null; // 🆕 ADD: Seatmap URL for venue layout
  // rawEventData's structure can vary, but we provide a more specific type for manager data
  // For Ticketmaster, this might be the raw TM API response.
  rawEventData: RawManagerEventData | any;
}

// Extend CreatePriorityEventPayload to define PriorityEventData
// This is the shape of the event data primarily used in the Redux store and home page carousels
export interface PriorityEventData extends CreatePriorityEventPayload {
  id: string; // Unique database ID for this PriorityEvent record itself
  batchId: string | null;
  isPopular: boolean;
  isActive: boolean;
  viewCount: number;
  clickCount: number;

  addedAt: string; // ISO date string
  updatedAt: string; // ISO date string

  // Optional fields used/checked in mapping logic when converting to UnifiedEvent
  // These might or might not be directly on PriorityEventData,
  // but mapping functions check for them.
  ticketUrl?: string | null; // Primarily for Ticketmaster redirect
  url?: string | null; // Alternative field for Ticketmaster redirect or other external links

  genre?: string | null; // e.g., Rock, Pop, Classical (more specific than category)
  segment?: string | null; // e.g., Music, Sports, Arts & Theatre (often aligns with category)
  subGenre?: string | null; // e.g., Alternative Rock, Indie Pop

  priceRange?: {
    // Based on UnifiedEvent.priceRange structure
    min: number | null;
    max: number | null;
    currency: string | null;
  } | null;

  // Top-level inventory: mapping logic has a fallback to event.inventory.
  // Though for manager events, logs show inventory is nested in rawEventData.inventory.
  // This caters to potential alternative structures or direct inventory data.
  inventory?: BasicInventoryItem[] | null;
}

// For bulk operations if needed
export interface BulkPriorityEventPayload {
  events: CreatePriorityEventPayload[];
}

export interface PriorityEventsApiResponse {
  data: PriorityEventData[];
}
