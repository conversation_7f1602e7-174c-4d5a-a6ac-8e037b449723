/**
 * ApprovalList component for managing event approvals with advanced filtering and pagination
 * 
 * @component
 * @param {Object} props - Component properties
 * @param {ApprovalStatus} [props.status='PENDING'] - Filter events by approval status
 * @param {EventFilters} [props.filters] - Optional filters for searching and categorizing events
 * 
 * @returns {React.ReactElement} Rendered list of event approvals with interactive features
 * 
 * @description Provides a comprehensive UI for displaying pending, approved, or rejected events
 * with features including:
 * - Dynamic filtering by search and category
 * - Server and client-side pagination
 * - Refresh functionality
 * - Event preview modal
 * - Responsive loading and error states
 */
// ApprovalList component for managing event approvals
import { useState, useMemo } from 'react';
import { ApprovalCard } from './ApprovalCard';
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { PendingEventApproval, ApprovalStatus } from '../../types/approval.types';
import { ApprovalPreviewCard } from './ApprovalPreviewCard'; 
import { usePendingApprovals } from '../../hooks/useApprovalHooks';
import { Skeleton } from '@/components/ui/skeleton';
// Add pagination imports
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
// Add these imports for refresh functionality
import { RefreshCw, Clock, Search, Filter } from "lucide-react";
import { toast } from "sonner";
import React from 'react';

// ✨ NEW: Filter interface
interface EventFilters {
  search: string;
  category: string;
}

// Added interface for ApprovalList props
interface ApprovalListProps {
  status?: ApprovalStatus; // Optional status filter
  filters?: EventFilters; // ✨ NEW: Accept filters prop
}

//todo : // adding pagination 
// Updated component to accept status prop with default value
export const ApprovalList = ({ status = "PENDING", filters }: ApprovalListProps) => {
  const [selectedEvent, setSelectedEvent] = useState<PendingEventApproval | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 4;

  // ✨ FIXED: Determine if we need to fetch all data for filtering
  const hasActiveFilters = filters?.search || (filters?.category && filters.category !== "all");
  
  // ✨ FIXED: Fetch all data when filters are active, use pagination when no filters
  const { data, isLoading, error, refetch, isFetching } = usePendingApprovals(
    status, 
    hasActiveFilters ? 1 : currentPage, // Always page 1 when filtering
    hasActiveFilters ? 1000 : itemsPerPage // Large number to get all data when filtering
  );

  // ✨ FIXED: Better filtering and pagination logic
  const processedData = useMemo(() => {
    if (!data?.data) return null;

    let filteredEvents = data.data;

    // Apply filters if they exist
    if (hasActiveFilters) {
      filteredEvents = data.data.filter((event: PendingEventApproval) => {
        // Search filter
        if (filters?.search) {
          const searchTerm = filters.search.toLowerCase();
          const matchesSearch = 
            event.name.toLowerCase().includes(searchTerm) ||
            event.venue.toLowerCase().includes(searchTerm) ||
            event.city.toLowerCase().includes(searchTerm);
          
          if (!matchesSearch) return false;
        }

        // Category filter
        if (filters?.category) {
          if (event.category !== filters.category) return false;
        }

        return true;
      });

      // Client-side pagination for filtered data
      const totalFiltered = filteredEvents.length;
      const totalPages = Math.ceil(totalFiltered / itemsPerPage);
      const startIndex = (currentPage - 1) * itemsPerPage;
      const paginatedEvents = filteredEvents.slice(startIndex, startIndex + itemsPerPage);

      return {
        data: paginatedEvents,
        pagination: {
          page: currentPage,
          pageSize: itemsPerPage,
          totalItems: totalFiltered,
          totalPages: totalPages,
        }
      };
    } else {
      // No filters - use server pagination
      return {
        data: filteredEvents,
        pagination: data.pagination
      };
    }
  }, [data, filters, currentPage, itemsPerPage, hasActiveFilters]);

  // Reset pagination when filters change or status changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [filters?.search, filters?.category, status]);

  const openModal = (event: PendingEventApproval) => {
    setSelectedEvent(event);
  };

  // Function to close the modal
  const closeModal = () => {
    setSelectedEvent(null);
  };

  // Handle page changes
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Add refresh handler
  const handleRefresh = async () => {
    try {
      await refetch();
      toast.success(`${status.toLowerCase()} events refreshed successfully!`);
    } catch (error) {
      toast.error("Failed to refresh data. Please try again.");
    }
  };

  // Helper function to get status display info
  const getStatusInfo = () => {
    switch (status) {
      case "PENDING":
        return { icon: Clock, color: "text-yellow-600", bgColor: "bg-yellow-50" };
      case "APPROVED":
        return { icon: RefreshCw, color: "text-green-600", bgColor: "bg-green-50" };
      case "REJECTED":
        return { icon: RefreshCw, color: "text-red-600", bgColor: "bg-red-50" };
      default:
        return { icon: RefreshCw, color: "text-gray-600", bgColor: "bg-gray-50" };
    }
  };

  const statusInfo = getStatusInfo();
  const StatusIcon = statusInfo.icon;

  // Added loading state with skeleton for better UX
  if (isLoading) {
    return (
      <div className="space-y-4">
        {/* Header with refresh button skeleton */}
        <div className="flex justify-between items-center p-4 border border-gray-200 rounded-lg bg-gray-50">
          <div className="flex items-center gap-2">
            <Skeleton className="h-5 w-5 rounded-full" />
            <Skeleton className="h-6 w-32" />
          </div>
          <Skeleton className="h-10 w-24" />
        </div>
        
        {/* Event cards skeleton */}
        <div className="space-y-4 border border-gray-200 rounded-lg p-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="p-4 border rounded-lg">
              <div className="flex justify-between items-start mb-4">
                <div className="space-y-2">
                  <Skeleton className="h-6 w-48" />
                  <Skeleton className="h-4 w-32" />
                </div>
                <Skeleton className="h-10 w-24" />
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                <div className="space-y-1">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-5 w-24" />
                </div>
                <div className="space-y-1">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-5 w-32" />
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Skeleton className="h-10 w-24" />
                <Skeleton className="h-10 w-24" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Updated error state with improved UI
  if (error) {
    return (
      <div className="space-y-4">
        {/* Header with refresh button */}
        <div className="flex justify-between items-center p-4 border border-gray-200 rounded-lg bg-red-50">
          <div className="flex items-center gap-2">
            <StatusIcon className={`h-5 w-5 ${statusInfo.color}`} />
            <h3 className="font-medium text-gray-900">
              {status.charAt(0) + status.slice(1).toLowerCase()} Events
            </h3>
          </div>
          <Button 
            onClick={handleRefresh}
            variant="outline" 
            size="sm"
            disabled={isFetching}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isFetching ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
        
        <div className="border border-gray-200 rounded-lg p-6 text-center">
          <p className="text-red-500 mb-2">Error loading approvals: {error.message}</p>
          <Button onClick={handleRefresh} disabled={isFetching}>
            {isFetching ? "Refreshing..." : "Try Again"}
          </Button>
        </div>
      </div>
    );
  }

  // Updated empty state with improved UI and dynamic status message
  if (!processedData?.data || processedData.data.length === 0) {
    return (
      <div className="space-y-4">
        {/* Header with refresh button */}
        <div className="flex justify-between items-center p-4 border border-gray-200 rounded-lg bg-gray-50">
          <div className="flex items-center gap-2">
            <StatusIcon className={`h-5 w-5 ${statusInfo.color}`} />
            <h3 className="font-medium text-gray-900">
              {status.charAt(0) + status.slice(1).toLowerCase()} Events
            </h3>
            {hasActiveFilters && (
              <div className="flex items-center gap-1 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                <Filter className="h-3 w-3" />
                <span>Filtered</span>
              </div>
            )}
          </div>
          <Button 
            onClick={handleRefresh}
            variant="outline" 
            size="sm"
            disabled={isFetching}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isFetching ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
        
        <div className="border border-gray-200 rounded-lg p-8 text-center">
          <p className="text-gray-500 mb-4">
            {hasActiveFilters 
              ? `No ${status.toLowerCase()} events match your current filters.`
              : `No ${status.toLowerCase()} events found.`
            }
          </p>
          <Button size="sm" onClick={handleRefresh} disabled={isFetching}>
            {isFetching ? "Refreshing..." : "Refresh"}
          </Button>
        </div>
      </div>
    );
  }

  const { data: events, pagination } = processedData;

  return (
    <>
      {/* Header with filter indicators */}
      <div className={`flex justify-between items-center p-4 border border-gray-200 rounded-lg ${statusInfo.bgColor} mb-4`}>
        <div className="flex items-center gap-2">
          <StatusIcon className={`h-5 w-5 ${statusInfo.color}`} />
          <h3 className="font-medium text-gray-900">
            {status.charAt(0) + status.slice(1).toLowerCase()} Events
          </h3>
          {pagination && (
            <span className="text-sm text-gray-500">
              ({pagination.totalItems} 
              {hasActiveFilters ? " filtered" : " total"})
            </span>
          )}
          {hasActiveFilters && (
            <div className="flex items-center gap-1 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
              <Filter className="h-3 w-3" />
              <span>
                {filters?.search && filters?.category 
                  ? "Search + Category" 
                  : filters?.search 
                    ? "Search Active"
                    : "Category Filter"}
              </span>
            </div>
          )}
          {/* Show fetching indicator */}
          {isFetching && (
            <div className="flex items-center gap-1 text-sm text-blue-600">
              <RefreshCw className="h-3 w-3 animate-spin" />
              <span>Updating...</span>
            </div>
          )}
        </div>
        <Button 
          onClick={handleRefresh}
          variant="outline" 
          size="sm"
          disabled={isFetching}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isFetching ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      <div className="space-y-4 border border-gray-200 rounded-lg p-4">
        {events.map((approval: PendingEventApproval) => (
          <ApprovalCard
            key={approval.id}
            approval={approval}
            status={status}
            onApprove={() => {}} // Placeholder, handled in ApprovalCard
            onReject={() => {}}  // Placeholder, handled in ApprovalCard
            openModal={openModal}  // Pass the openModal function
          />
        ))}
      </div>
      
      {/* ✨ FIXED: Pagination that actually works */}
      {pagination && pagination.totalPages > 1 && (
        <div className="mt-6">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious 
                  onClick={(e) => {
                    e.preventDefault();
                    if (currentPage > 1) {
                      handlePageChange(currentPage - 1);
                    }
                  }}
                  className={currentPage <= 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>
              
              {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((page) => (
                <PaginationItem key={page}>
                  <PaginationLink
                    onClick={(e) => {
                      e.preventDefault();
                      handlePageChange(page);
                    }}
                    isActive={currentPage === page}
                    className="cursor-pointer"
                  >
                    {page}
                  </PaginationLink>
                </PaginationItem>
              ))}
              
              <PaginationItem>
                <PaginationNext 
                  onClick={(e) => {
                    e.preventDefault();
                    if (currentPage < pagination.totalPages) {
                      handlePageChange(currentPage + 1);
                    }
                  }}
                  className={currentPage >= pagination.totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
          
          {/* Debug info */}
          {/* <div className="text-center text-sm text-gray-500 mt-2">
            Page {pagination.page} of {pagination.totalPages} 
            {hasActiveFilters ? " (filtered)" : ""} 
            - {pagination.totalItems} total events
          </div> */}
        </div>
      )}
      
      {/* Event Details Modal */}
      <Dialog open={!!selectedEvent} onOpenChange={closeModal}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Event Details</DialogTitle>
            <DialogDescription>
              Preview of the selected event.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {selectedEvent && (
              <ApprovalPreviewCard event={selectedEvent} />
            )}
          </div>
          <DialogFooter>
            <Button type="button" onClick={closeModal}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
