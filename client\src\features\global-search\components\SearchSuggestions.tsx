import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { SearchResult } from '../types/search.types';
import { SearchResultCard } from './SearchResultCard';
import { Context } from '@/types/openctx.types';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp } from 'lucide-react';

interface SearchSuggestionsProps {
  results: SearchResult[];
  selectedSegment: string | null;
  onClose: () => void;
}

// Component to display search suggestions based on results and selected segment
export const SearchSuggestions: React.FC<SearchSuggestionsProps> = ({
  results,
  selectedSegment,
  onClose,
}) => {
  // State to track which categories have expanded to show all results
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});

  // Toggle expanded state for a category
  const toggleCategoryExpansion = (category: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  // Early return with empty state if no results
  if (!results || results.length === 0) {
    return (
      <motion.div
        className="p-4 text-center text-gray-500"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        No results found
      </motion.div>
    );
  }

  return (
    <motion.div
      className="p-4 max-h-[400px] overflow-y-auto"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      {results
        // Filter out results with no events
        .filter(result => result.events && result.events.length > 0)
        .filter(({ events }) =>
          selectedSegment
            ? events.some((event: Context) => event.metadata?.rawEvent?.classifications?.[0]?.segment?.name === selectedSegment)
            : true
        )
        .map(({ category, events }) => {
          // Determine if this category is expanded
          const isExpanded = expandedCategories[category] || false;
          // Get events to display based on expansion state
          const displayEvents = isExpanded ? events : events.slice(0, 4);
          
          return (
            <div key={`category-${category}`} className="mb-6">
              <h3 className="text-sm font-semibold text-gray-500 mb-2">
                {category.toUpperCase()}
              </h3>
              {displayEvents.map((event: Context, index: number) => (
                <div key={`${category}-${event.metadata.source}-${event.metadata.id || ''}-${index}`} 
                     className="mb-2">
                  <SearchResultCard 
                    key={`card-${event.metadata.source}-${event.metadata.id || ''}-${index}`} 
                    event={event} 
                    onClose={onClose} 
                  />
                </div>
              ))}
              
              {/* Show More/Less button if there are more than 4 events */}
              {events.length > 4 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleCategoryExpansion(category)}
                  className="mt-2 text-gray-600 hover:text-gray-900 flex items-center gap-1 w-full justify-center"
                >
                  {isExpanded ? (
                    <>
                      Show Less <ChevronUp className="h-4 w-4" />
                    </>
                  ) : (
                    <>
                      Show More ({events.length - 4} more) <ChevronDown className="h-4 w-4" />
                    </>
                  )}
                </Button>
              )}
            </div>
          );
        })}
    </motion.div>
  );
};
