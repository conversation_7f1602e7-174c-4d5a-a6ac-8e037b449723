/**
 * Stripe initialization and utilities
 */
import { loadStripe, Stripe } from '@stripe/stripe-js';

// Singleton pattern to avoid multiple Stripe instances
let stripePromise: Promise<Stripe | null>;

/**
 * Initialize and get the Stripe instance
 * Uses singleton pattern to avoid multiple instances
 */
export const getStripe = (): Promise<Stripe | null> => {
  if (!stripePromise) {
    const key = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
    
    if (!key) {
      console.error('❌ Stripe publishable key missing. Please add NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY to .env.local');
      return Promise.resolve(null);
    }
    
    stripePromise = loadStripe(key);
  }
  
  return stripePromise;
};



/**
 * 
 * PS C:\Users\<USER>\Desktop\Fanseatmaster> stripe listen --forward-to localhost:8002/api/v1/payments/stripe/webhook

> Ready! You are using Stripe API Version [2025-03-31.basil]. Your webhook signing secret is whsec_55560491503955e16940c81a8d34570da84cf431d44401b12f6a9bb8cf724822 (^C to quit)


 */