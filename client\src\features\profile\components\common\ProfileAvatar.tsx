/**
 * ProfileAvatar Component
 * 
 * Displays the user's avatar with optional editing capabilities.
 * Supports different sizes and fallback options.
 */

import React from 'react';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Pencil } from 'lucide-react';
import { ProfileComponentProps } from '../../types/profile.types';

interface ProfileAvatarProps extends Omit<ProfileComponentProps, 'isLoading'> {
  size?: 'small' | 'medium' | 'large';
  showEditButton?: boolean;
}

export function ProfileAvatar({ 
  profile, 
  isEditable = false, 
  size = 'medium',
  showEditButton = true
}: ProfileAvatarProps) {
  // Size mapping for avatar dimensions
  const sizeMap = {
    small: 'h-10 w-10',
    medium: 'h-16 w-16',
    large: 'h-24 w-24'
  };
  
  // Function to generate initials from name
  const getInitials = (name: string | null | undefined): string => {
    if (!name) return '?';
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <div className="relative group">
      <Avatar className={`${sizeMap[size]} border-2 border-background`}>
        <AvatarImage 
          src={profile?.image || ''} 
          alt={`${profile?.name || 'User'}'s avatar`} 
          className="object-cover"
        />
        <AvatarFallback className="bg-primary/10 text-primary">
          {getInitials(profile?.name)}
        </AvatarFallback>
      </Avatar>
      
      {isEditable && showEditButton && (
        <div className="absolute -bottom-1 -right-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button 
            variant="secondary" 
            size="icon" 
            className="h-7 w-7 rounded-full shadow-sm"
            aria-label="Edit profile picture"
          >
            <Pencil className="h-3.5 w-3.5" />
          </Button>
        </div>
      )}
    </div>
  );
}

// Add debugging if needed 
console.log('📷 ProfileAvatar loaded');
