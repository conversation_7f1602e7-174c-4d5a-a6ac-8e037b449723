import { Router } from 'express';
import { QueueController } from '../controllers/queue.controller';
import { QueueTestController } from '../controllers/queueTest.controller'; // Import test controller
import { authMiddleware } from '@/middleware/auth.middleware';

const router = Router();

// ---- ORIGINAL ROUTES (UNCHANGED) ----
// Route to get queue status for an event (requires user to be logged in)
// GET /api/v1/queue/status/:eventId
router.get(
    '/status/:eventId',
    authMiddleware, // Ensure user is authenticated
    QueueController.getQueueStatus
);

// Route to join the queue for an event (requires user to be logged in)
// POST /api/v1/queue/join
router.post(
    '/join',
    authMiddleware, // Ensure user is authenticated
    QueueController.joinQueue
);

// ---- TESTING ROUTES ----
// Test route to get queue status with configurable mock responses
// GET /api/v1/queue/test/status/:eventId?status=WAITING|ACTIVE|EXPIRED|TOGGLE|RESET|SIMULATE
router.get(
    '/test/status/:eventId',
    authMiddleware, // Keep auth for consistency
    QueueTestController.getQueueStatus
);

// Test route to join the queue with configurable responses
// POST /api/v1/queue/test/join?simulate=true
router.post(
    '/test/join',
    authMiddleware, // Keep auth for consistency
    QueueTestController.joinQueue
);

export default router;
