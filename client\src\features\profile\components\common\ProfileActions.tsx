/**
 * ProfileActions Component
 * 
 * Provides action buttons for interacting with a profile, such as
 * follow, message, share, and report functionalities.
 */

import React, { useState } from 'react'; // Added useState
import { 
  UserPlus, MessageCircle, Share2, Flag, 
  MoreHorizontal, UserCheck, Settings
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { ProfileComponentProps, ProfileAction } from '../../types/profile.types';
import { toast } from 'sonner';
import { ProfileEditModal } from '../common/ProfileEditForm'; // Added import for ProfileEditModal

interface ProfileActionsProps extends ProfileComponentProps {
  isCurrentUser?: boolean;
  isFollowing?: boolean;
  onActionClick?: (action: ProfileAction) => void;
}

export function ProfileActions({ 
  profile, 
  isCurrentUser = false,
  isFollowing = false,
  onActionClick 
}: ProfileActionsProps) {
  // Added state for edit modal
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const handleAction = (action: ProfileAction) => {
    if (action === 'edit') {
      // Open the edit modal instead of the default action
      console.log('🖊️ Opening profile edit modal');
      setIsEditModalOpen(true);
      return;
    }
    
    if (onActionClick) {
      onActionClick(action);
    } else {
      // Default actions if no handler provided
      const actionMessages = {
        edit: `Editing profile for ${profile.name}`,
        follow: isFollowing 
          ? `Unfollowed ${profile.name}` 
          : `Followed ${profile.name}`,
        message: `Messaging ${profile.name}`,
        share: `Sharing ${profile.name}'s profile`,
        report: `Reporting ${profile.name}'s profile`
      };
      
      toast(actionMessages[action]);
      
      console.log(`📣 Profile action triggered: ${action}`, profile);
    }
  };

  return (
    <>
      <div className="flex gap-2">
        {isCurrentUser ? (
          // Actions for viewing your own profile
          <Button 
            variant="default" 
            className="flex-1"
            onClick={() => handleAction('edit')}
          >
            <Settings className="h-4 w-4 mr-2" />
            Edit Profile
          </Button>
        ) : (
          // Actions for viewing someone else's profile
          <>
            <Button 
              variant={isFollowing ? "outline" : "default"}
              className="flex-1 sm:flex-none"
              onClick={() => handleAction('follow')}
            >
              {isFollowing ? (
                <>
                  <UserCheck className="h-4 w-4 mr-2" />
                  Following
                </>
              ) : (
                <>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Follow
                </>
              )}
            </Button>
            
            <Button 
              variant="outline"
              onClick={() => handleAction('message')}
            >
              <MessageCircle className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Message</span>
            </Button>
          </>
        )}
        
        {/* Additional actions in dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon">
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">More actions</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => handleAction('share')}>
              <Share2 className="h-4 w-4 mr-2" />
              <span>Share Profile</span>
            </DropdownMenuItem>
            
            <DropdownMenuSeparator />
            
            <DropdownMenuItem 
              onClick={() => handleAction('report')}
              className="text-destructive focus:text-destructive"
            >
              <Flag className="h-4 w-4 mr-2" />
              <span>Report User</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Profile Edit Modal */}
      <ProfileEditModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        profile={profile}
      />
    </>
  );
}

// Add debugging if needed
console.log('🔘 ProfileActions loaded');
