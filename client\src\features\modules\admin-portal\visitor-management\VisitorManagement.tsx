"use client"
import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { VisitorList } from "./components/VisitorList";
import { VisitorFilters } from "./components/VisitorFilters";
import { visitorService } from "./services/visitor-management.service";
import { Loader2 } from "lucide-react";
import type { Visitor, VisitorFilters as FilterType } from "./types/visitor-management.types";

export const VisitorManagement = () => {
  // State management
  const [visitors, setVisitors] = useState<Visitor[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<FilterType>({
    search: "",
    status: "all"
  });
  const [selectedVisitor, setSelectedVisitor] = useState<Visitor | null>(null);

  // Data fetching
  useEffect(() => {
    const loadVisitors = async () => {
      try {
        setLoading(true);
        const data = await visitorService.getVisitors(filters);
        setVisitors(data);
      } catch (error) {
        console.error("Failed to load visitors:", error);
        // Here you could add toast notification for error
      } finally {
        setLoading(false);
      }
    };

    loadVisitors();
  }, [filters]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    }
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-8"
    >
      {/* Header Section */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Visitor Management</h1>
          <p className="text-muted-foreground mt-2">
            Manage and monitor visitor activities across the platform
          </p>
        </div>
      </div>

      {/* Filters Section */}
      <VisitorFilters
        filters={filters}
        onFilterChange={setFilters}
      />

      {/* Content Section */}
      <div className="rounded-lg border bg-card">
        <div className="p-6">
          {loading ? (
            <div className="flex justify-center items-center min-h-[400px]">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : visitors.length === 0 ? (
            <div className="text-center py-12">
              <h3 className="text-lg font-semibold">No visitors found</h3>
              <p className="text-muted-foreground mt-2">
                Try adjusting your filters or search terms
              </p>
            </div>
          ) : (
            <VisitorList
              visitors={visitors}
              onVisitorSelect={setSelectedVisitor}
            />
          )}
        </div>
      </div>

      {/* Statistics Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="rounded-lg border bg-card p-6">
          <h3 className="font-semibold mb-2">Total Visitors</h3>
          <p className="text-2xl font-bold">{visitors.length}</p>
        </div>
        <div className="rounded-lg border bg-card p-6">
          <h3 className="font-semibold mb-2">Active Visitors</h3>
          <p className="text-2xl font-bold">
            {visitors.filter(v => v.status === 'active').length}
          </p>
        </div>
        <div className="rounded-lg border bg-card p-6">
          <h3 className="font-semibold mb-2">Total Bookings</h3>
          <p className="text-2xl font-bold">
            {visitors.reduce((acc, v) => acc + v.totalBookings, 0)}
          </p>
        </div>
      </div>
    </motion.div>
  );
};