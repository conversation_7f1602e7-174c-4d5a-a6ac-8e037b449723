import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON>er<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>erT<PERSON>le,
  DrawerClose,
} from "@/components/ui/drawer";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { useTmEvents } from "@/features/tm_events/hooks/useTmEvents";

interface FilterSideDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

export const FilterSideDrawer: React.FC<FilterSideDrawerProps> = ({
  isOpen,
  onClose,
}) => {
  const { updateGenre, updateCity, queryParams, updateStartDate } =
    useTmEvents();
  //Handle the change of genre select and call updateGenre to update the UI
  const handleGenreChange = (genre: string) => {
    updateGenre(genre);
  };
  //Handle the change of city input and call updateCity to update the UI
  const handleCityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateCity(e.target.value);
  };
  //Handle the change of date input and call updateStartDate to update the UI
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateStartDate(e.target.value);
  };

  return (
    <Drawer open={isOpen} onOpenChange={onClose}>
      <DrawerContent className="bg-white w-screen">
        <DrawerHeader>
          <DrawerTitle>Filter Events</DrawerTitle>
          <DrawerClose />
        </DrawerHeader>
        <div className="p-4 space-y-4">
          <Input
            type="text"
            placeholder="Filter By City"
            className="w-full"
            value={queryParams.city || ""}
            onChange={handleCityChange}
          />
          <Input
            type="date"
            placeholder="Start Date"
            className="w-full"
            value={queryParams.startDate || ""}
            onChange={handleDateChange}
          />
          <Select
            onValueChange={handleGenreChange}
            defaultValue={(queryParams as any).genre || undefined}          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Filter By Genre" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Sports">Sports</SelectItem>
              <SelectItem value="Music">Music</SelectItem>
              <SelectItem value="Arts & Theatre">Arts & Theatre</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </DrawerContent>
    </Drawer>
  );
};
