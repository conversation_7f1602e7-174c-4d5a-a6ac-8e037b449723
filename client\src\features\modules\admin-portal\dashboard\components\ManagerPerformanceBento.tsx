"use client";
import { TrendingUp, <PERSON> } from "lucide-react";
import { Bento<PERSON>ox } from "../../../shared/widgets/BentoBox";

export const ManagerPerformanceBento = () => {
  const managers = [
    {
      id: "1",
      name: "<PERSON>",
      totalEvents: 12,
      totalSales: 45000,
      activeEvents: 5,
      rating: 4.8,
    },
    {
      id: "2",
      name: "<PERSON>",
      totalEvents: 8,
      totalSales: 32000,
      activeEvents: 3,
      rating: 4.5,
    },
    {
      id: "3",
      name: "<PERSON>",
      totalEvents: 15,
      totalSales: 58000,
      activeEvents: 6,
      rating: 4.9,
    },
    // Added 5 more managers below
    {
      id: "4",
      name: "<PERSON>",
      totalEvents: 10,
      totalSales: 41000,
      activeEvents: 4,
      rating: 4.7,
    },
    {
      id: "5",
      name: "<PERSON>",
      totalEvents: 14,
      totalSales: 52000,
      activeEvents: 7,
      rating: 4.6,
    },
    {
      id: "6",
      name: "<PERSON>",
      totalEvents: 11,
      totalSales: 38000,
      activeEvents: 5,
      rating: 4.4,
    },
    {
      id: "7",
      name: "<PERSON>",
      totalEvents: 13,
      totalSales: 49000,
      activeEvents: 6,
      rating: 4.8,
    },
    {
      id: "8",
      name: "<PERSON>",
      totalEvents: 9,
      totalSales: 35000,
      activeEvents: 4,
      rating: 4.5,
    },
  ];

  return (
    <BentoBox
      title="Top Performing Managers"
      className="col-span-2 row-span-2"
      header={<TrendingUp className="h-5 w-5 text-primary" />}
      itemLimit={5} // Show only 5 items initially
      showMoreButton={true} // Enable show more button
    >
      {managers.map((manager) => (
        <div
          key={manager.id}
          className="p-4 rounded-lg bg-accent/50 hover:bg-accent/70 transition-colors mb-4"
        >
          <div className="flex justify-between items-start mb-2">
            <h4 className="font-medium">{manager.name}</h4>
            <div className="flex items-center">
              <Star className="h-4 w-4 text-yellow-400 mr-1" />
              <span>{manager.rating}</span>
            </div>
          </div>
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground">Events</p>
              <p className="font-medium">
                {manager.activeEvents}/{manager.totalEvents}
              </p>
            </div>
            <div>
              <p className="text-muted-foreground">Sales</p>
              <p className="font-medium">
                ${manager.totalSales.toLocaleString()}
              </p>
            </div>
            <div>
              <p className="text-muted-foreground">Status</p>
              <span className="text-green-600">Active</span>
            </div>
          </div>
        </div>
      ))}
    </BentoBox>
  );
};
