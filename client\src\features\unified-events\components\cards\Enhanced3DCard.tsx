"use client";
import React from "react";
import { format } from "date-fns";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import { MapPin, Calendar, Building2, CircleDollarSign, Star } from "lucide-react";
import { CardContainer, CardBody, CardItem } from "@/components/ui/3d-card";
import { UnifiedEvent } from "../../adapters/eventAdapter";
interface Enhanced3DCardProps {
  event: UnifiedEvent;
  onEventClick: (event: UnifiedEvent) => void;
}

export const Enhanced3DCard: React.FC<Enhanced3DCardProps> = ({
  event,
  onEventClick,
}) => {
  const formattedDate = event.date
    ? format(new Date(event.date), "MMM d, yyyy h:mm a")
    : "Not Available";

  // Determine the source badge color
  const sourceBadgeColor = event.source === 'manager' ? 'bg-emerald-100 text-emerald-800' : 'bg-blue-100 text-blue-800';
  const sourceLabel = event.source === 'manager' ? 'Buy tickets' : 'External Event';

  return (
    <div onClick={() => onEventClick(event)}>
      <CardContainer className="inter-var w-full cursor-pointer">
        <CardBody className="relative h-full w-full rounded-xl bg-white shadow-2xl 
          border
          group-hover:scale-[1.02] transition-transform duration-200 ease-out"
        >
          {/* Source badge - New addition */}
          <div className="absolute top-2 right-2 z-10">
            <span className={`text-xs font-medium px-2.5 py-0.5 rounded ${sourceBadgeColor} flex items-center`}>
              <Star className="h-3 w-3 mr-1" />
              {sourceLabel}
            </span>
          </div>
          
          <CardHeader className="space-y-3">
            {event.imageUrl && (
              <CardItem translateZ="110" className="w-full">
                <div className="relative w-full h-48 overflow-hidden rounded-t-lg">
                  <Image
                    src={event.imageUrl}
                    alt={event.name}
                    className="object-cover w-full h-full"
                    fill
                    sizes="(max-width: 767px) 100vw, (max-width: 1023px) 50vw, 25vw"
                    style={{ objectFit: "cover" }}
                    priority
                  />
                </div>
              </CardItem>
            )}
            <CardItem translateZ="75" className="w-full">
              <CardTitle className="line-clamp-1 text-lg font-bold">
                {event.name}
              </CardTitle>
            </CardItem>
            <CardItem translateZ="50" className="w-full">
              <CardDescription className="flex flex-col space-y-1">
                {event.venue && (
                  <span className="flex items-center text-sm">
                    <Building2 className="h-4 w-4 mr-1" />
                    {event.venue}
                  </span>
                )}
                {formattedDate && (
                  <span className="flex items-center text-sm">
                    <Calendar className="h-4 w-4 mr-1" />
                    {formattedDate}
                  </span>
                )}
              </CardDescription>
            </CardItem>
          </CardHeader>
          <CardContent>
            <CardItem translateZ="60" className="w-full">
              <div className="flex flex-wrap gap-1">
                {event.genre && (
                  <Badge variant="secondary">{event.genre}</Badge>
                )}
                {event.segment && (
                  <Badge variant="secondary">{event.segment}</Badge>
                )}
                {event.subGenre && (
                  <Badge variant="secondary">{event.subGenre}</Badge>
                )}
              </div>
            </CardItem>
          </CardContent>
          <CardFooter className="flex flex-col space-y-2 pt-4 border-t">
            <CardItem translateZ="30" className="w-full">
              <div className="w-full flex justify-between items-center">
                {event.city && event.country && (
                  <div className="flex items-center space-x-1.5">
                    <div className="bg-gray-100 p-1.5 rounded-full">
                      <MapPin className="h-4 w-4 text-gray-600" />
                    </div>
                    <span className="text-gray-600 text-sm font-medium">
                      {event.city}, {event.country}
                    </span>
                  </div>
                )}
                {event.priceRange?.min && event.priceRange?.max && (
                  <div className="flex items-center space-x-1.5">
                    <div className="bg-gray-100 p-1.5 rounded-full">
                      <CircleDollarSign className="h-4 w-4 text-gray-600" />
                    </div>
                    <span className="text-gray-700 text-sm font-bold">
                      {event.priceRange.currency || '$'} {event.priceRange.min} - {event.priceRange.max}
                    </span>
                  </div>
                )}
              </div>
            </CardItem>
          </CardFooter>
        </CardBody>
      </CardContainer>
    </div>
  );
};
