// Configuration for navigation based on user roles and permissions
import {
  Home,
  Users,
  Calendar,
  Settings,
  ChartBar,
  Ticket,
  CreditCard,
  LayoutDashboard,
  CheckCircle,
  ListPlus,
  User,
  FolderKanban,
  Coins
} from "lucide-react";
import { NavigationConfig } from "../types/navigation.types";
import { Permission } from "@/utils/permissions/types";

export const navigationConfig: NavigationConfig = {
  default: [
    {
      items: [
        {
          label: "Home",
          icon: Home,
          path: "/",
        },
        {
          label: "Events",
          icon: Calendar,
          path: "/events",
          permission: "events:read",
        },
        {
          label: "Settings",
          icon: Settings,
          path: "/settings",
          roleBasedPath: {
            ADMIN: "/admin/settings",
            MANAGER: "/manager/settings",
            VISITOR: "/visitor/settings"
          },
          permission: "settings:read"
        },
      ],
    },
  ],
  roleSpecific: {
    ADMIN: [
      {
        items: [
          {
            label: "Admin Dashboard",
            icon: LayoutDashboard,
            path: "/admin/dashboard",
            // permission: "dashboard:manage",
          },
          {
            label: "Visitor Management",
            icon: Users,
            path: "/admin/visitor-management",
            permission: "users:manage",
            // Badge configuration for trusted users
            badges: {
              TRUSTED_USER: {
                label: "Trusted",
                color: "emerald",
              },
              PREMIUM_SELLER: {
                label: "Premium",
                color: "amber",
              },
              VIP_ACCESS: {
                label: "VIP",
                color: "purple",
              },
            },
          },
          {
            label: "Event Management",
            icon: Calendar,
            path: "/admin/events", // Updated path
            permissions: [
              // Added multiple permissions
              "events:manage",
              "events:create",
              "events:read",
              "events:update",
              "events:delete",
            ],
          },
          // Inside ADMIN role-specific items
          {
            label: "Event Approvals",
            icon: CheckCircle,
            path: "/admin/event-approvals",
            permission: "events:manage",
            badges: {
              PENDING_APPROVALS: {
                label: "New",
                color: "amber",
              },
            },
          },

          {
            label: "Analytics",
            icon: ChartBar,
            path: "/admin/analytics",
            permission: "analytics:manage",
          },
          {
            label: "profile",
            icon: User,
            path: "/admin/profile",
          }
        ],
      },
    ],
    MANAGER: [
      {
        items: [
          {
            label: "Dashboard",
            icon: LayoutDashboard,
            path: "/manager/dashboard",
            // permission: "dashboard:read",
          },
          {
            label: "Event Listing",
            icon: ListPlus, // Use ListPlus icon for listing
            path: "/manager/event-listing",
            permissions: ["events:create", "events:manage"], // Require create and manage permissions
          },
          {
            label: "Inventory",
            icon: FolderKanban ,
            path: "/manager/events",
            permissions: [
              // Added multiple permissions
              "events:manage",
              "events:create",
              "events:read",

              "events:update",
            ],
          },
          {
            label: "Sales Overview",
            icon: ChartBar,
            path: "/manager/sales",
            permission: "analytics:read",
          },
          {
            label: "Analytics",
            icon: ChartBar,
            path: "/manager/analytics",
            permission: "analytics:manage",
          },
        ],
      },
    ],
    VISITOR: [
      {
        items: [
          {
            label: "Dashboard",
            icon: LayoutDashboard,
            path: "/visitor/dashboard",
            permission: "dashboard:read",
          },
          {
            label: " Visitor Events",
            icon: Calendar,
            path: "/visitor/events",
            permission: "events:read",
          },
          {
            label: "My Tickets",
            icon: Ticket,
            path: "/visitor/tickets",
            permission: "tickets:read",
          },
          {
            label: "Payments",
            icon: CreditCard,
            path: "/visitor/payments",
            permission: "tickets:book",
          },
        //  adding subscripton
          // {
          //   label: "Subscriptions",
          //   icon: Coins ,
          //   path: "/visitor/subscriptions",
          //   permission: "tickets:book",
          // },
        ],
      },
    ],
  },
};
