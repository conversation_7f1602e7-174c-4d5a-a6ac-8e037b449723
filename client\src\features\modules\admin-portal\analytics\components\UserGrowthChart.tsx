"use client"
import { Bar } from 'react-chartjs-2';
import { ChartContainer } from '@/features/modules/shared/charts/ChartContainer';
import { useEffect, useState } from 'react';
import { analyticsService } from '../services/analytics.service';
import type { ChartData } from '@/features/modules/shared/types/analytics.types';

export const UserGrowthChart = () => {
  const [data, setData] = useState<ChartData | null>(null);

  useEffect(() => {
    const loadData = async () => {
      const userData = await analyticsService.getUserGrowthData();
      setData(userData);
    };
    loadData();
  }, []);

  if (!data) return null;

  return (
    <ChartContainer title="User Growth" height={300}>
      <Bar 
        data={data}
        options={{
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              callbacks: {
                label: (context) => `New Users: ${context.raw}`
              }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: 'New Users'
              }
            }
          }
        }}
      />
    </ChartContainer>
  );
};
