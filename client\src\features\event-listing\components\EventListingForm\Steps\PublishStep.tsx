/**
 * @fileoverview PublishStep component for the event listing creation form.
 * @description This component represents the final step ('Publish') in the multi-step form for creating a new event listing.
 * It displays a summary preview of the selected event details and the configured inventory items.
 * Users can review the information and confirm the creation of the event listing.
 * It provides buttons to navigate back to previous steps ('Edit Event', 'Edit Inventory') for modifications.
 * It handles the final submission of the event listing data to the backend API upon clicking the 'Publish' button.
 * Includes user feedback mechanisms like loading indicators and toast notifications for success or errors.
 */
// This component represents the Publish step in the event listing form
// It displays a preview of the selected event, inventory, and purchase order (if applicable) for confirmation
// It also handles the submission of the event listing to the backend

"use client";

import React, { useState } from "react";
// --------------- Context and UI Imports ---------------
import { useEventListing } from "../EventListingContext"; // Hook to access shared state and actions for the event listing form
import { But<PERSON> } from "@/components/ui/button"; // Reusable Button component
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"; // Components for displaying tabular data
import { useCreateEventListingMutation } from "@/state/api"; // RTK Query mutation hook for creating an event listing
import { toast } from "sonner"; // Library for displaying toast notifications
import { EventPreviewCard } from "@/components/shared/EventPreviewCard"; // Component to display a preview of the event details
import { useSession } from "next-auth/react"; // Hook to access user session data
import { useRouter } from "next/navigation"; // Hook for programmatic navigation

/**
 * PublishStep functional component.
 * Renders the final confirmation step for creating an event listing.
 */
export const PublishStep: React.FC = () => {
  // --------------- Component State and Hooks ---------------
  const { data: session } = useSession(); // Get user session information
  const router = useRouter(); // Get router instance for navigation
  const {
    eventListingData,
    goToStep,
    handleEditInventory,
    handleDeleteInventory,
  } = useEventListing(); // Access data and functions from the event listing context
  const [isLoading, setIsLoading] = useState(false); // State to manage the loading status during API submission
  // Removed unused error variable from useCreateEventListingMutation
  const [createEventListing] = useCreateEventListingMutation(); // Hook to trigger the create event listing mutation

  /**
   * Handles the submission of the event listing data to the backend.
   * Constructs the payload, calls the mutation, handles responses, and navigates.
   */
  // console.log("EventListing payload☠️☠️☠️😂😂", eventListingData);
  const handlePublish = async () => {
    setIsLoading(true); // Set loading state to true to disable the button and show loading text
    const managerEmail = session?.user?.email; // Retrieve manager's email from the session
    // console.log("Manager Email:", managerEmail); // Log the manager email for debugging
    // Validate if the manager email exists in the session
    if (!managerEmail) {
      toast.error("User session not found"); // Show error toast if session/email is missing
      setIsLoading(false); // Reset loading state
      return; // Exit the function early
    }




    // --- Category Derivation ---
    let derivedCategory: string = "SPORTS"; // Default to a valid enum value




    const rawEventClassifications = eventListingData.selectedEvent?.metadata.rawEvent?.classifications;
    const segmentName = rawEventClassifications?.[0]?.segment?.name?.toLowerCase();

    console.log("🕵️‍♂️ Raw segment name from API (lowercase):", segmentName);

    if (segmentName) {
      if (segmentName.includes("sport")) { // Covers "sports", "sporting event", etc.
        derivedCategory = "SPORTS";
      } else if (segmentName.includes("music")) {
        derivedCategory = "MUSIC";
      } else if (segmentName.includes("theatre") || segmentName.includes("theater")) { // Prioritize "THEATER" if explicitly mentioned
        derivedCategory = "THEATER";
      } else if (segmentName.includes("art")) { // Covers "arts", "art exhibit"
        derivedCategory = "ARTS";
      } else {
        // Fallback if segment name doesn't match known categories
        // You might want to use the metadata.category here or a generic default
        const fallbackCategory = eventListingData.selectedEvent?.metadata.category?.toUpperCase();
        if (fallbackCategory && ["SPORTS", "MUSIC", "ARTS", "THEATER"].includes(fallbackCategory)) {
            derivedCategory = fallbackCategory;
        } else {
            derivedCategory = "SPORTS"; // Or another safe default from your enum
            console.warn(`⚠️ Unmapped segment name '${segmentName}', defaulting category to ${derivedCategory}`);
        }
      }
    } else if (eventListingData.selectedEvent?.metadata.category) {
        // Fallback to metadata.category if classifications are not available
        const fallbackCategory = eventListingData.selectedEvent.metadata.category.toUpperCase();
        if (["SPORTS", "MUSIC", "ARTS", "THEATER"].includes(fallbackCategory)) {
            derivedCategory = fallbackCategory;
        } else {
            derivedCategory = "SPORTS"; // Or another safe default
             console.warn(`⚠️ Unmapped metadata.category '${fallbackCategory}', defaulting category to ${derivedCategory}`);
        }
    }
    
    console.log("🏷️ Derived Event Category for Payload:", derivedCategory);

    try {
      // --------------- Payload Construction ---------------
      // Construct the payload object with data collected from previous steps
      const payload = {
        managerId: managerEmail, // Include manager's identifier
        eventId: eventListingData.selectedEvent?.metadata.id, // Event ID from selected event
        name: eventListingData.selectedEvent?.metadata.name, // Event name
        // source: eventListingData.selectedEvent?.metadata.source, // Original source commented out
        source: "manager", // Set source explicitly to 'manager'

        category: derivedCategory, // Use the derived and validated category
        date: eventListingData.selectedEvent?.metadata.date, // Event date
        venue: eventListingData.selectedEvent?.metadata.venue, // Event venue name
        city: eventListingData.selectedEvent?.metadata.city, // Event city
        // Attempt to get country from raw event data, fallback to 'Unknown'
        country:
          eventListingData.selectedEvent?.metadata.rawEvent?._embedded
            ?.venues?.[0]?.country?.name || "Unknown",
        image: eventListingData.selectedEvent?.metadata.image, // Event image URL
        rawEventData: eventListingData.selectedEvent?.metadata.rawEvent, // Include raw event data if available
        inventory: eventListingData.inventory, // Array of inventory items
        // Conditionally include purchase order details if 'generateDraft' is true
        purchaseOrder: eventListingData.purchaseOrder?.generateDraft
          ? eventListingData.purchaseOrder
          : null,
      };

      console.log("🚀 Publishing Payload:", JSON.parse(JSON.stringify(payload)));

      // --------------- API Call and Response Handling ---------------
      // Call the mutation function with the prepared payload
      const response = await createEventListing(payload).unwrap(); // '.unwrap()' handles potential errors and returns the actual response data
      toast.success("Event listing created successfully!"); // Show success toast notification
      console.log("Event listing created:", response); // Log the successful response for debugging

      // Add a small delay before redirecting to allow the user to see the success message
      setTimeout(() => {
        // Redirect the user to the manager's event inventory page after successful creation
        router.push("/manager/events");
      }, 1500); // 1.5 seconds delay
    } catch (error: any) {
      // --------------- Error Handling ---------------
      console.error("Error creating event listing:", error); // Log the error details to the console

      // Check if the error object has a specific structure (RTK Query error format)
      if (error && typeof error === "object" && "data" in error) {
        const errorData = error.data as { message?: string }; // Type assertion for error data
        // Display a more specific error message from the API response if available
        toast.error(
          `Error creating event listing: ${
            errorData.message || "Unknown error"
          }`
        );
      } else {
        // Display a generic error message if the error structure is unexpected
        toast.error("An unexpected error occurred.");
      }
    } finally {
      // --------------- Cleanup ---------------
      // This block executes regardless of success or failure
      setIsLoading(false); // Reset loading state in all cases
    }
  };

  // --------------- Component Rendering ---------------
  return (
    <div>
      <h2 className="text-lg font-semibold mb-4">Preview & Confirm</h2>

      {/* --------------- Event Details Preview --------------- */}
      {/* Conditionally render the event preview card if an event has been selected */}
      {eventListingData.selectedEvent && (
        <div className="mb-4">
          <h3 className="text-md font-semibold mb-2">Event Details</h3>
          {/* Render the event preview card component */}
          <EventPreviewCard event={eventListingData.selectedEvent} />
          {/* Button to navigate back to the first step (Event Selection) */}
          <Button onClick={() => goToStep(1)} className="mt-2">
            Edit Event
          </Button>
        </div>
      )}

      {/* --------------- Inventory Summary Table --------------- */}
      {/* Conditionally render the inventory summary table if inventory items exist */}
      {eventListingData.inventory.length > 0 && (
        <div className="mb-4">
          <h3 className="text-md font-semibold mb-2">Inventory Summary</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Qty</TableHead>
                <TableHead>Section/Row</TableHead>
                <TableHead>Seats</TableHead>
                <TableHead>List Price</TableHead>
                <TableHead>Service Fee</TableHead>
                <TableHead>Total</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {eventListingData.inventory.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>{item.quantity}</TableCell>
                  <TableCell>{`${item.section} / ${item.row}`}</TableCell>
                  <TableCell>
                    {item.seatingType === "GA"
                      ? "GA"
                      : item.seatingType === "Consecutive"
                      ? `${item.lowSeatNumber || 0} - ${
                          (item.lowSeatNumber || 0) + item.quantity - 1
                        }`
                      : item.seatingType === "Odd-even"
                      ? (() => {
                          const start = item.lowSeatNumber || 0;
                          const isOdd = start % 2 !== 0;
                          const lastSeat = start + (item.quantity - 1) * 2;
                          return `${start} (${
                            isOdd ? "Odd" : "Even"
                          }) - ${lastSeat}`;
                      })()
                    : "N/A"}
                  </TableCell>
                  <TableCell>${item.listPrice}</TableCell>

                  <TableCell>
                    ${item.serviceFee ? item.serviceFee : 0}
                  </TableCell>
                  <TableCell>
                    $
                    {item.listPrice * item.quantity +
                      (item.serviceFee ? item.serviceFee * item.quantity : 0)}
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        onClick={() => handleEditInventory(item)}
                        variant="outline"
                      >
                        Edit
                      </Button>
                      <Button
                        onClick={() => handleDeleteInventory(item.id)}
                        variant="destructive"
                      >
                        Delete
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          {/* Button to navigate back to the second step (Inventory Configuration) */}
          <Button onClick={() => goToStep(2)} className="mt-2">
            Edit Inventory
          </Button>
        </div>
      )}

      {/* --------------- Publish Button --------------- */}
      {/* Button to trigger the final publish action */}
      <Button
        onClick={handlePublish}
        disabled={isLoading}
        className="mt-4 w-full"
      >
        {/* Dynamically change button text based on loading state */}
        {isLoading ? "Publishing..." : "Publish Event Listing"}
      </Button>
    </div>
  );
};
