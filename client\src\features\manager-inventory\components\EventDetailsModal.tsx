// Modal component to display detailed information about a ManagerEvent listing.
import React from "react";
import Image from "next/image";
import { format, isValid } from "date-fns";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  EventDetailsModalProps,
  InventoryDetailItem,
} from "../types/inventory.types"; // Use correct types
import { ApprovalStatusBadge } from "./shared/ApprovalStatusBadge";
import { EventStatusBadge } from "./shared/EventStatusBadge";
import { cn } from "@/lib/utils";
import { Separator } from "@/components/ui/separator";

// Helper function to format seating details
const formatSeats = (item: InventoryDetailItem): string => {
  switch (item.seatingType) {
    case "GA":
      return "GA";
    case "Consecutive":
      return item.lowSeatNumber
        ? `${item.lowSeatNumber} - ${item.lowSeatNumber + item.quantity - 1}`
        : "N/A";
    case "Odd-even": {
      if (!item.lowSeatNumber) return "N/A";
      const start = item.lowSeatNumber;
      const isOdd = start % 2 !== 0;
      const lastSeat = start + (item.quantity - 1) * 2;
      return `${start} - ${lastSeat} (${isOdd ? "Odd" : "Even"})`;
    }
    default:
      return "N/A";
  }
};

// Helper function to safely format dates
const safeFormatDate = (
  date: Date | string | null | undefined,
  formatString: string
): string => {
  if (date instanceof Date && isValid(date)) {
    return format(date, formatString);
  }

  if (date && process.env.NODE_ENV === "development") {
    console.warn(`safeFormatDate received invalid date value:`, date);
  }

  return "N/A";
};

// Component to render a single inventory item as a card for mobile
const InventoryDetailCard: React.FC<{
  invItem: InventoryDetailItem;
  index: number;
}> = ({ invItem, index }) => {
  const hasDisclosures = invItem.disclosures && invItem.disclosures.length > 0;
  const hasAttributes = invItem.attributes && invItem.attributes.length > 0;
  const hasFeatures = hasDisclosures || hasAttributes;

  return (
    <div
      key={invItem.id || `inv-${index}`}
      className="border rounded-lg p-3 mb-3 bg-card shadow-sm text-sm space-y-2"
    >
      {/* --- Seating Details Group --- */}
      <div className="grid grid-cols-2 gap-x-4 gap-y-1">
        <h4 className="col-span-2 text-xs font-semibold uppercase text-muted-foreground mb-1">
          Seating
        </h4>
        <div>
          <span className="font-medium text-muted-foreground">Qty:</span>{" "}
          {invItem.quantity}
        </div>
        <div>
          <span className="font-medium text-muted-foreground">Section:</span>{" "}
          {invItem.section || "N/A"}
        </div>
        <div>
          <span className="font-medium text-muted-foreground">Row:</span>{" "}
          {invItem.row || "N/A"}
        </div>
        <div>
          <span className="font-medium text-muted-foreground">Seats:</span>{" "}
          {formatSeats(invItem)}
        </div>
        <div className="col-span-2">
          <span className="font-medium text-muted-foreground">Format:</span>{" "}
          {invItem.ticketFormat || "N/A"}
        </div>
      </div>

      {/* --- Features Group (Disclosures & Attributes) --- */}
      {hasFeatures && (
        <>
          <Separator className="my-2" />
          <div>
            <h4 className="text-xs font-semibold uppercase text-muted-foreground mb-1.5">
              Features
            </h4>
            {hasDisclosures && (
              <div className="mb-1.5">
                <span className="font-medium text-muted-foreground text-xs block mb-0.5">
                  Disclosures:
                </span>
                <div className="flex flex-wrap gap-1">
                  {invItem.disclosures.map((disc, i) => (
                    <Badge
                      key={i}
                      variant="outline"
                      className="text-xs font-normal px-1.5 py-0.5"
                    >
                      {disc}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            {hasAttributes && (
              <div>
                <span className="font-medium text-muted-foreground text-xs block mb-0.5">
                  Attributes:
                </span>
                <div className="flex flex-wrap gap-1">
                  {invItem.attributes.map((attr, i) => (
                    <Badge
                      key={i}
                      variant="secondary"
                      className="text-xs font-normal px-1.5 py-0.5"
                    >
                      {attr}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </>
      )}

      {/* --- Pricing Group --- */}
      <Separator className="my-2" />
      <div>
        <h4 className="text-xs font-semibold uppercase text-muted-foreground mb-1">
          Pricing
        </h4>
        <div className="grid grid-cols-2 gap-x-4 gap-y-1">
          <div>
            <span className="font-medium text-muted-foreground">Price:</span>{" "}
            <span className="font-semibold">
              ${invItem.listPrice.toFixed(2)}
            </span>
          </div>
          <div className="text-right">
            <span className="font-medium text-muted-foreground">Fee:</span> $
            {(invItem.serviceFee ?? 0).toFixed(2)}
          </div>
        </div>
      </div>
    </div>
  );
};

export const EventDetailsModal: React.FC<EventDetailsModalProps> = ({
  item,
  isOpen,
  onClose,
}) => {
  if (!item) return null; // Don't render if no item is selected

  const hasInventory = item.inventory && item.inventory.length > 0;

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-3xl w-full h-[90vh] flex flex-col p-0 sm:max-w-4xl">
        <DialogHeader className="p-6 pb-4 border-b">
          <DialogTitle className="text-xl sm:text-2xl font-semibold">
            {item.name}
          </DialogTitle>
          <DialogDescription>
            {item.venue} - {item.city}, {item.country} |{" "}
            {safeFormatDate(item.date, "PPP p")}
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="flex-grow px-6 py-4 overflow-y-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            {item.image && (
              <div className="md:col-span-1 relative aspect-[16/9] rounded-lg overflow-hidden bg-muted">
                <Image
                  src={item.image}
                  alt={item.name}
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 33vw, 25vw"
                  className="object-cover"
                  priority={false}
                />
              </div>
            )}
            <div
              className={cn(
                "space-y-3 text-sm",
                item.image ? "md:col-span-2" : "md:col-span-3"
              )}
            >
              <div className="flex items-center gap-2 sm:gap-4 flex-wrap">
                <EventStatusBadge
                  status={item.status}
                  isActive={item.isActive}
                />
                <ApprovalStatusBadge status={item.approvalStatus} />
                <Badge
                  variant={item.isActive ? "default" : "outline"}
                  className={cn(
                    "border text-xs font-medium",
                    item.isActive
                      ? "bg-green-100 text-green-800 border-green-300"
                      : "bg-red-100 text-red-800 border-red-300"
                  )}
                >
                  {item.isActive ? "Active" : "Inactive"}
                </Badge>
              </div>

              <div>
                <strong className="font-medium text-muted-foreground w-24 inline-block">
                  Category:
                </strong>{" "}
                <Badge variant="secondary" className="capitalize">
                  {item.category.toLowerCase()}
                </Badge>
              </div>
              <p>
                <strong className="font-medium text-muted-foreground w-24 inline-block">
                  Event ID:
                </strong>{" "}
                {item.eventId}
              </p>
              <p>
                <strong className="font-medium text-muted-foreground w-24 inline-block">
                  Listing ID:
                </strong>{" "}
                {item.id}
              </p>
              <p>
                <strong className="font-medium text-muted-foreground w-24 inline-block">
                  Source:
                </strong>{" "}
                {item.source}
              </p>
              <p>
                <strong className="font-medium text-muted-foreground w-24 inline-block">
                  Listing Added:
                </strong>{" "}
                {safeFormatDate(item.createdAt, "MMM d, yyyy, p")}
              </p>

              {item.approvedAt && (
                <p>
                  <strong className="font-medium text-muted-foreground w-24 inline-block">
                    Approved On:
                  </strong>{" "}
                  {safeFormatDate(item.approvedAt, "MMM d, yyyy, p")}
                </p>
              )}
              {item.approvedBy && (
                <p>
                  <strong className="font-medium text-muted-foreground w-24 inline-block">
                    Approved By:
                  </strong>{" "}
                  <span className="text-xs">{item.approvedBy}</span>
                </p>
              )}
              {item.approvalNotes && (
                <p>
                  <strong className="font-medium text-muted-foreground w-24 inline-block align-top">
                    Notes:
                  </strong>{" "}
                  <span className="italic text-muted-foreground">
                    {item.approvalNotes}
                  </span>
                </p>
              )}
            </div>
          </div>

          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-3 border-b pb-2">
              Inventory Details
            </h3>

            {!hasInventory ? (
              <p className="text-muted-foreground text-center py-4">
                No inventory details available for this listing.
              </p>
            ) : (
              <>
                <div className="hidden md:block border rounded-md overflow-hidden">
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow className="bg-muted/50">
                          <TableHead className="w-[50px]">Qty</TableHead>
                          <TableHead>Section</TableHead>
                          <TableHead>Row</TableHead>
                          <TableHead>Seats</TableHead>
                          <TableHead>Format</TableHead>
                          <TableHead>Disclosures</TableHead>
                          <TableHead>Attributes</TableHead>
                          <TableHead className="text-right">Price</TableHead>
                          <TableHead className="text-right w-[80px]">
                            Fee
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {item.inventory.map((invItem, index) => (
                          <TableRow key={invItem.id || `inv-${index}`}>
                            <TableCell>{invItem.quantity}</TableCell>
                            <TableCell>{invItem.section}</TableCell>
                            <TableCell>{invItem.row}</TableCell>
                            <TableCell>{formatSeats(invItem)}</TableCell>
                            <TableCell>{invItem.ticketFormat}</TableCell>
                            <TableCell className="text-xs max-w-[150px] truncate">
                              {(invItem.disclosures || []).join(", ")}
                            </TableCell>
                            <TableCell className="text-xs max-w-[150px] truncate">
                              {(invItem.attributes || []).join(", ")}
                            </TableCell>
                            <TableCell className="text-right font-mono">
                              ${invItem.listPrice.toFixed(2)}
                            </TableCell>
                            <TableCell className="text-right font-mono">
                              ${(invItem.serviceFee ?? 0).toFixed(2)}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>

                <div className="block md:hidden">
                  {item.inventory.map((invItem, index) => (
                    <InventoryDetailCard
                      key={invItem.id || `inv-${index}`}
                      invItem={invItem}
                      index={index}
                    />
                  ))}
                </div>
              </>
            )}
          </div>
          {/* //Todo! Add Purchase Order display if needed */}
          {item.purchaseOrder && (
            <div className="mt-6">
              <h3 className="text-lg font-semibold mb-3 border-b pb-2">
                Purchase Order
              </h3>
              <div className="p-4 border rounded bg-muted text-xs space-y-1">
                <p>
                  <strong className="font-medium">Exchange:</strong>{" "}
                  {(item.purchaseOrder as any)?.exchange}
                </p>
                <p>
                  <strong className="font-medium">Market:</strong>{" "}
                  {(item.purchaseOrder as any)?.market}
                </p>
                <p>
                  <strong className="font-medium">Price:</strong>{" "}
                  {(item.purchaseOrder as any)?.price}
                </p>
                <p>
                  <strong className="font-medium">Quantity:</strong>{" "}
                  {(item.purchaseOrder as any)?.quantity}
                </p>
                <p>
                  <strong className="font-medium">Generate Draft:</strong>{" "}
                  {String((item.purchaseOrder as any)?.generateDraft)}
                </p>
              </div>
            </div>
          )}
        </ScrollArea>

        <DialogFooter className="p-6 pt-4 border-t bg-background">
          <DialogClose asChild>
            <Button variant="outline">Close</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
