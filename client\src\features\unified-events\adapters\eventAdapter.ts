import { TmEvent } from "@/features/tm_events/types/tm.types";

// Define our unified event format
export interface UnifiedEvent {
  id: string;
  name: string;
  date: Date;
  venue: string;
  city: string;
  country: string;
  imageUrl: string | null;
  genre: string | null;
  segment: string | null;
  subGenre: string | null;
  priceRange?: {
    min: number | null;
    max: number | null;
    currency: string | null;
  };
  // Track the source of the event
  source: 'manager' | 'ticketmaster';
  // Original event data for when we need the complete object
  originalEvent: any;
}

/**
 * Converts a TmEvent to our UnifiedEvent format
 */
export const tmEventToUnified = (tmEvent: TmEvent): UnifiedEvent => {
  return {
    id: tmEvent.id,
    name: tmEvent.name,
    date: tmEvent.startDateTime ? new Date(tmEvent.startDateTime) : new Date(),
    venue: tmEvent.venueName || 'Unknown Venue',
    city: tmEvent.venueCity || 'Unknown City',
    country: tmEvent.venueCountry || 'Unknown Country',
    imageUrl: tmEvent.primaryImage || null,
    genre: tmEvent.genre || null,
    segment: tmEvent.segment || null,
    subGenre: tmEvent.subGenre || null,
    priceRange: tmEvent.priceRangeMin && tmEvent.priceRangeMax ? {
      min: tmEvent.priceRangeMin,
      max: tmEvent.priceRangeMax,
      currency: tmEvent.currency
    } : undefined,
    source: 'ticketmaster',
    originalEvent: tmEvent
  };
};

/**
 * Converts a ManagerEvent to our UnifiedEvent format
 */
export const managerEventToUnified = (managerEvent: any): UnifiedEvent => {
  return {
    id: managerEvent.id,
    name: managerEvent.name,
    date: new Date(managerEvent.date),
    venue: managerEvent.venue,
    city: managerEvent.city,
    country: managerEvent.country,
    imageUrl: managerEvent.image || null,
    genre: managerEvent.category || null,
    segment: null, // Manager events might not have segment
    subGenre: null, // Manager events might not have subGenre
    priceRange: managerEvent.inventory ? {
      min: extractMinPrice(managerEvent.inventory),
      max: extractMaxPrice(managerEvent.inventory),
      currency: 'USD' // Default currency, adjust as needed
    } : undefined,
    source: 'manager',
    originalEvent: managerEvent
  };
};

// Helper functions to extract price information from manager event inventory
function extractMinPrice(inventory: any): number | null {
  try {
    if (Array.isArray(inventory)) {
      const prices = inventory
        .filter(item => typeof item.price === 'number')
        .map(item => item.price);
      return prices.length > 0 ? Math.min(...prices) : null;
    }
    return null;
  } catch (error) {
    console.error("Error extracting min price:", error);
    return null;
  }
}

function extractMaxPrice(inventory: any): number | null {
  try {
    if (Array.isArray(inventory)) {
      const prices = inventory
        .filter(item => typeof item.price === 'number')
        .map(item => item.price);
      return prices.length > 0 ? Math.max(...prices) : null;
    }
    return null;
  } catch (error) {
    console.error("Error extracting max price:", error);
    return null;
  }
}
