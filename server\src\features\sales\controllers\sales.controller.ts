// server/src/features/sales/controllers/sales.controller.ts
// Controller to handle HTTP requests for manager sales overview with detailed checkout sessions

import { Request, Response } from "express";
import { asyncHandler } from "@/utils/asyncHandler";
import { SalesService } from "../services/sales.service";
import ApiError from "@/utils/ApiError";
import { UserRole } from "@prisma/client";

export class SalesController {

/**
 * @description Get detailed sales data for events managed by the logged-in manager,
 *              including individual checkout sessions.
 * @route GET /api/v1/sales/overview
 * @access Private (Manager only)
 */
static getSalesOverview = asyncHandler(async (req: Request, res: Response) => {
    const managerId = req.user?.userId;
    const userRole = req.user?.role;

    if (!managerId) {
        throw new ApiError(401, "Authentication required.");
    }
        
    // Explicitly check for Manager role
    if (userRole !== UserRole.MANAGER) {
         throw new ApiError(403, "Access denied. Only managers can view sales data.");
    }

    // Extract pagination parameters from query string
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    // Extract optional filtering/sorting parameters
    const sortBy = (req.query.sortBy as string) || 'date';
    const sortOrder = (req.query.sortOrder as string) || 'desc';
        
    // Extract date range parameters if provided
    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
        
    console.log(`📲 [SalesController] Request received for detailed sales data - Manager: ${managerId}, Page: ${page}, Limit: ${limit}`);

    // Call the service with all parameters
    // Note: The service method signature doesn't include sorting/filtering yet - 
    // You may want to extend it to support these parameters
    const result = await SalesService.getManagerSalesOverview(managerId, page, limit);

    // Use standard JSON response structure
    return res.status(200).json({
        success: true,
        message: "Detailed sales data retrieved successfully.",
        data: result
    });
});
}