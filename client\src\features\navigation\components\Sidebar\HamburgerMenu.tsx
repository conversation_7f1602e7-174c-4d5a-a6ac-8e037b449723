/**
 * @description HamburgerMenu Component
 *
 * A component that renders a hamburger menu icon for toggling the sidebar on small screens.
 * It uses Redux to manage the sidebar collapse state.
 */
import React from 'react';
import { useAppDispatch, useAppSelector } from '@/app/redux';
import {  setIsSidebarCollapsed } from '@/state';
import { Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';

export const HamburgerMenu: React.FC = () => {
    const dispatch = useAppDispatch();
    const isSidebarCollapsed = useAppSelector((state) => state.global.isSidebarCollapsed);


    const toggleSidebar = () => {
        dispatch(setIsSidebarCollapsed(!isSidebarCollapsed));
    };

    return (
        <Button variant={'ghost'} onClick={toggleSidebar} className="md:hidden">
            <Menu className="h-5 w-5"/>
        </Button>
    );
};
