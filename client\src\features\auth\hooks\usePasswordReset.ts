import { useState } from "react";
import { authApi } from "../api/credentialApi";
import { toast } from "sonner";

export const usePasswordReset = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const requestReset = async (email: string) => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await authApi.forgotPassword(email);
      toast.success(response.message || "Reset link sent successfully");
      return true;
    } catch (err) {
      const message =
        err instanceof Error ? err.message : "Failed to send reset link";
      setError(message);
      toast.error(message);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const resetPassword = async (token: string, newPassword: string) => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await authApi.resetPassword({ token, newPassword });
      toast.success(response.message || "Password reset successful");
      return true;
    } catch (err) {
      const message =
        err instanceof Error ? err.message : "Failed to reset password";
      setError(message);
      toast.error(message);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    error,
    requestReset,
    resetPassword,
  };
};
