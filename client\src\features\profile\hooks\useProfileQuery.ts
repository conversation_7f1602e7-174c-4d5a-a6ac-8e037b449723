/**
 * @module useProfileQuery
 * @description Custom hook for managing user profile data.
 * Provides functionality to fetch the user's profile, update it,
 * and handle email and mobile verification processes.
 * It leverages @tanstack/react-query for data fetching, caching, and state management.
 */
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { ProfileData } from "../types/profile.types";
import axiosInstance from "@/apiAxios/axios"; // Axios instance configured for API calls
import { toast } from "sonner"; // Library for showing toast notifications
import { useSession } from "next-auth/react"; // Import useSession

// Unique key for caching profile data in react-query
const PROFILE_QUERY_KEY = "profile";

export function useProfileQuery() {
  const queryClient = useQueryClient(); // Hook to interact with the react-query cache
  // Get session status from next-auth
  const { data: session, status } = useSession();

  // Check if user is authenticated
  const isAuthenticated = status === "authenticated";

  // --------------- Fetching Profile Data -------------------
  const {
    data: profile, // The fetched profile data, or null/undefined if not yet fetched or error occurred
    isLoading, // Boolean indicating if the query is currently fetching
    isError, // Boolean indicating if the query encountered an error
    error, // The error object if an error occurred
    refetch, // Function to manually refetch the profile data
  } = useQuery({
    queryKey: [PROFILE_QUERY_KEY], // Unique key for this query
    queryFn: async (): Promise<ProfileData | null> => {
      // Asynchronous function to fetch profile data
      try {
        // Added logging to track when query function actually runs
        console.log("🔍 Attempting to fetch profile data...");
        const response = await axiosInstance.get("api/v1/profile"); // API call to fetch profile
        console.log("📩 Profile data received from API:", response.data);
        // Assuming the actual profile data is nested under response.data.data
        return response.data.data;
      } catch (err) {
        // Log and re-throw the error for react-query to handle
        console.error("❌ Error fetching profile:", err);
        throw err instanceof Error
          ? err
          : new Error("Unknown error fetching profile");
      }
    },
    enabled: isAuthenticated, // Only run the query if the user is authenticated
    refetchOnWindowFocus: isAuthenticated ? "always" : false, // Only refetch on focus if authenticated
    staleTime: 5 * 60 * 1000, // Data is considered fresh for 5 minutes, reducing unnecessary refetches
    retry: (failureCount, error: any) => {
      // Don't retry on authentication errors
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        console.log(
          "🚫 Not retrying profile fetch due to auth error:",
          error.response.status
        );
        return false;
      }
      // For other errors, retry a couple of times
      return failureCount < 2;
    },
  });

  // --------------- Updating Profile Data -------------------
  const updateProfileMutation = useMutation({
    mutationFn: async (data: Partial<ProfileData>) => {
      // Asynchronous function to update profile data via PUT request
      const response = await axiosInstance.put("api/v1/profile", data);
      // Return the updated profile data from the response
      return response.data.data;
    },
    onSuccess: (data) => {
      // On successful update, update the cache optimistically with the new data
      queryClient.setQueryData([PROFILE_QUERY_KEY], data);
      // Show success notification
      toast.success("Profile updated successfully");
    },
    onError: (err) => {
      // Log error and show error notification on failure
      console.error("Error updating profile:", err);
      toast.error("Failed to update profile");
    },
  });

  // --------------- Email Verification -------------------
  // Mutation to send an email verification OTP
  const sendEmailVerificationMutation = useMutation({
    mutationFn: async (email: string) => {
      // API call to request sending verification email
      const response = await axiosInstance.post(
        "api/v1/profile/verify-email/send",
        { email }
      );
      return response.data;
    },
    onSuccess: () =>
      toast.success("Verification email sent. Please check your inbox."), // Show success message
    onError: (err) => {
      // Log error and show error message
      console.error("Error sending email verification:", err);
      toast.error("Failed to send verification email");
    },
  });

  // Mutation to confirm email verification using OTP
  const verifyEmailMutation = useMutation({
    mutationFn: async (otp: string) => {
      // API call to confirm email with OTP
      const response = await axiosInstance.post(
        "api/v1/profile/verify-email/confirm",
        { otp }
      );
      return response.data;
    },
    onSuccess: () => {
      // On successful verification, show success message
      toast.success("Email verified successfully");
      // Invalidate the profile query cache to refetch updated profile data (e.g., isEmailVerified status)
      queryClient.invalidateQueries({ queryKey: [PROFILE_QUERY_KEY] });
    },
    onError: (err) => {
      // Log error and show error message
      console.error("Error verifying email:", err);
      toast.error("Invalid OTP or verification failed");
    },
  });

  // --------------- Mobile Verification -------------------
  // Mutation to send a mobile verification OTP
  const sendMobileVerificationMutation = useMutation({
    mutationFn: async (mobile: string) => {
      // API call to request sending verification SMS
      const response = await axiosInstance.post(
        "api/v1/profile/verify-mobile/send",
        { mobile }
      );
      return response.data;
    },
    onSuccess: () => toast.success("Verification code sent to your mobile"), // Show success message
    onError: (err) => {
      // Log error and show error message
      console.error("Error sending mobile verification:", err);
      toast.error("Failed to send verification code");
    },
  });

  // Mutation to confirm mobile verification using OTP
  const verifyMobileMutation = useMutation({
    mutationFn: async (otp: string) => {
      // API call to confirm mobile number with OTP
      const response = await axiosInstance.post(
        "api/v1/profile/verify-mobile/confirm",
        { otp }
      );
      return response.data;
    },
    onSuccess: () => {
      // On successful verification, show success message
      toast.success("Mobile verified successfully");
      // Invalidate the profile query cache to refetch updated profile data (e.g., isMobileVerified status)
      queryClient.invalidateQueries({ queryKey: [PROFILE_QUERY_KEY] });
    },
    onError: (err) => {
      // Log error and show error message
      console.error("Error verifying mobile:", err);
      toast.error("Invalid OTP or verification failed");
    },
  });

  // --------------- Hook Return Value -------------------
  // Expose profile data, query status, refetch function, and mutation helpers
  return {
    // Profile query results
    profile,
    isLoading,
    isError,
    error,
    refetch,
    // Add session status for convenience
    isAuthenticated,
    isSessionLoading: status === "loading",
    // Update profile mutation helpers
    updateProfile: {
      mutate: updateProfileMutation.mutate, // Function to trigger the update
      isPending: updateProfileMutation.isPending, // Loading state for the update
      isError: updateProfileMutation.isError, // Error state for the update
    },
    // Send email verification mutation helpers
    sendEmailVerification: {
      mutate: sendEmailVerificationMutation.mutate, // Function to trigger sending email OTP
      isPending: sendEmailVerificationMutation.isPending, // Loading state
      isError: sendEmailVerificationMutation.isError, // Error state
    },
    // Verify email mutation helpers
    verifyEmail: {
      mutate: verifyEmailMutation.mutate, // Function to trigger email verification
      isPending: verifyEmailMutation.isPending, // Loading state
      isError: verifyEmailMutation.isError, // Error state
    },
    // Send mobile verification mutation helpers
    sendMobileVerification: {
      mutate: sendMobileVerificationMutation.mutate, // Function to trigger sending mobile OTP
      isPending: sendMobileVerificationMutation.isPending, // Loading state
      isError: sendMobileVerificationMutation.isError, // Error state
    },
    // Verify mobile mutation helpers
    verifyMobile: {
      mutate: verifyMobileMutation.mutate, // Function to trigger mobile verification
      isPending: verifyMobileMutation.isPending, // Loading state
      isError: verifyMobileMutation.isError, // Error state
    },
  };
}
