import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, X, Tag, CheckCircle } from 'lucide-react';
import { CouponDiscount } from '../types/checkout.types';
import { cn } from '@/lib/utils';

interface CouponInputProps {
  onApply: (code: string) => void;
  isApplying: boolean;
  appliedCoupon?: CouponDiscount;
  className?: string;
}

/**
 * Component for entering and applying coupon codes
 * Shows success state when a coupon is applied
 */
export const CouponInput: React.FC<CouponInputProps> = ({
  onApply,
  isApplying,
  appliedCoupon,
  className
}) => {
  const [couponCode, setCouponCode] = useState('');
  
  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (couponCode.trim() && !isApplying) {
      onApply(couponCode.trim());
    }
  };
  
  // Remove applied coupon (calls onApply with empty string)
  const handleRemoveCoupon = () => {
    onApply('');
  };
  
  // If a coupon is already applied, show the applied state
  if (appliedCoupon) {
    return (
      <div className={cn("space-y-2", className)}>
        <Label>Coupon Code</Label>
        <div className="bg-green-50 border border-green-200 rounded-md p-3">
          <div className="flex justify-between items-center">
            <div className="flex items-center text-green-700">
              <CheckCircle className="h-5 w-5 mr-2" />
              <div>
                <p className="font-medium">{appliedCoupon.couponCode}</p>
                <p className="text-sm">{appliedCoupon.description}</p>
              </div>
            </div>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleRemoveCoupon}
              className="h-8 text-muted-foreground hover:text-destructive"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <div className="mt-2 text-sm text-green-700 font-medium">
            Discount: ${appliedCoupon.discountAmount.toFixed(2)}
          </div>
        </div>
      </div>
    );
  }
  
  // Otherwise, show the input form
  return (
    <div className={cn("space-y-2", className)}>
      <Label>Coupon Code</Label>
      <form onSubmit={handleSubmit} className="flex space-x-2">
        <div className="relative flex-1">
          <Tag className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            type="text"
            placeholder="Enter coupon code"
            value={couponCode}
            onChange={(e) => setCouponCode(e.target.value)}
            className="pl-9"
            disabled={isApplying}
          />
        </div>
        <Button 
          type="submit" 
          disabled={!couponCode.trim() || isApplying}
        >
          {isApplying ? <Loader2 className="h-4 w-4 animate-spin" /> : "Apply"}
        </Button>
      </form>
    </div>
  );
};
