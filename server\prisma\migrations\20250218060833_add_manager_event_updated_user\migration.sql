-- Create<PERSON><PERSON>
CREATE TYPE "SeatingType" AS ENUM ('CONSECUTIVE', 'ODD_EVEN', 'GA');

-- CreateEnum
CREATE TYPE "TicketFormat" AS ENUM ('E_TICKET', 'MOBILE_TRANSFER', 'HARD_TICKETS');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "SellingPreference" AS ENUM ('ANY', 'PAIRS', 'FULL', 'AVOID_SINGLE');

-- DropIndex
DROP INDEX "PriorityEvent_batchId_idx";

-- DropIndex
DROP INDEX "PriorityEvent_eventId_source_key";

-- DropIndex
DROP INDEX "PriorityEvent_isPopular_idx";

-- AlterTable
ALTER TABLE "PriorityEvent" ALTER COLUMN "eventId" DROP NOT NULL,
ALTER COLUMN "rawEventData" DROP NOT NULL;

-- CreateTable
CREATE TABLE "ManagerEvent" (
    "id" TEXT NOT NULL,
    "managerId" TEXT NOT NULL,
    "priorityEventId" TEXT,
    "name" TEXT NOT NULL,
    "category" "EventCategory" NOT NULL,
    "source" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "venue" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "country" TEXT NOT NULL,
    "image" TEXT,
    "inventory" JSONB NOT NULL,
    "purchaseOrder" JSONB,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "status" TEXT NOT NULL DEFAULT 'LISTED',
    "addedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "ticketUrl" TEXT,
    "rawEventData" JSONB,

    CONSTRAINT "ManagerEvent_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ManagerEvent_managerId_idx" ON "ManagerEvent"("managerId");

-- CreateIndex
CREATE INDEX "ManagerEvent_priorityEventId_idx" ON "ManagerEvent"("priorityEventId");

-- CreateIndex
CREATE INDEX "ManagerEvent_date_idx" ON "ManagerEvent"("date");

-- CreateIndex
CREATE INDEX "ManagerEvent_category_idx" ON "ManagerEvent"("category");

-- CreateIndex
CREATE INDEX "PriorityEvent_eventId_idx" ON "PriorityEvent"("eventId");

-- AddForeignKey
ALTER TABLE "ManagerEvent" ADD CONSTRAINT "ManagerEvent_managerId_fkey" FOREIGN KEY ("managerId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ManagerEvent" ADD CONSTRAINT "ManagerEvent_priorityEventId_fkey" FOREIGN KEY ("priorityEventId") REFERENCES "PriorityEvent"("id") ON DELETE SET NULL ON UPDATE CASCADE;
