/**
 * Types related to coupon functionality
 */

export interface ApplyCouponRequest {
  sessionId: string;
  couponCode: string;
}

export interface CouponDiscount {
  couponId: string;
  couponCode: string;
  description: string;
  discountType: 'PERCENTAGE' | 'FIXED';
  discountValue: number; // Original value (percentage or fixed amount)
  discountAmount: number; // Calculated amount in currency
}

export interface ApplyCouponResponse {
  success: boolean;
  message: string;
  data: {
    isValid: boolean;
    session?: any; // Will contain updated session if successful
    error?: string;
  };
}