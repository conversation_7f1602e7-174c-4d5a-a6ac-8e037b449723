import axios from "axios";
import { getSession, signOut } from "next-auth/react";

// Create Axios instances for different API versions
export const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true,
});

export const apiV2 = axios.create({
  baseURL: `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v2`,
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true,
});

// Request interceptor for main api (v1) to attach auth token
api.interceptors.request.use(async (config) => {
  try {
    const session = await getSession();
    // console.log('➡️📦 Axios Interceptor: Checking session for token...', {session: session ? { id: session.user?.id, token: session.token ? '...exists' : '...missing' } : null});
    if (session?.token) {
      config.headers.Authorization = `Bearer ${session.token}`;
      // console.log('➡️🔑 Axios Interceptor: Token found and attached.');
    } else {
      // console.log('➡️❌ Axios Interceptor: No token found in session.');
    }
    return config;
  } catch (error) {
    // console.error('➡️💥 Axios Interceptor Error:', error);
    return config;
  }
});

// Request interceptor for apiV2 to attach auth token
apiV2.interceptors.request.use(async (config) => {
  try {
    const session = await getSession();
    // console.log('➡️📦 Axios V2 Interceptor: Checking session for token...', {session: session ? { id: session.user?.id, token: session.token ? '...exists' : '...missing' } : null});
    if (session?.token) {
      config.headers.Authorization = `Bearer ${session.token}`;
      // console.log('➡️🔑 Axios V2 Interceptor: Token found and attached.');
    } else {
      // console.log('➡️❌ Axios V2 Interceptor: No token found in session.');
    }
    return config;
  } catch (error) {
    // console.error('➡️💥 Axios V2 Interceptor Error:', error);
    return config;
  }
});

// Response interceptors for both instances to handle success/error and 401
[api, apiV2].forEach(instance => {
  instance.interceptors.response.use(
    (response) => {
      // console.log(`⬅️✅ Axios Response: ${response.config.method?.toUpperCase()} ${response.config.url} - ${response.status}`);
      return response;
    },
    async (error) => {
      // console.error(`⬅️❌ Axios Response Error: ${error.config?.method?.toUpperCase()} ${error.config?.url} - ${error.response?.status || error.message}`, error);
      // Handle 401 unauthorized errors
      if (error.response?.status === 401) {
        // console.warn('🚨 401 Unauthorized detected. Signing out...');
        await signOut({
          redirect: true,
          callbackUrl: "/",
        });
      }
      return Promise.reject(error);
    }
  );
});

export default api;