"use client";
import { Suspense, useState } from "react";
import { useHomeEvents } from "@/features/home/<USER>/useHomeEvents";
import { EventCarousel } from "./EventCarousel";
import { useGetPriorityEventsQuery } from "@/state/api";
import { UnifiedEvent } from "@/features/unified-events/adapters/eventAdapter";
import { EventModal } from "@/features/unified-events/components/event-details/EventModal";

/**
 * EventCarouselHomePage Component
 * This component renders a carousel of popular events to be displayed on the Home page.
 */
export const EventCarouselHomePage = () => {
  // RTK Query hook to get priority events info and the loading state.
  const { isLoading: apiLoading } = useGetPriorityEventsQuery();

  // Add modal state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<UnifiedEvent | null>(null);

  // Handle event click
  const handleEventClick = (event: any) => {
    // event here is PriorityEventData
    console.log("🔍 [EventCarouselHomePage] Full event data received:", event);

    // Extract the correct ManagerEvent ID for checkout
    const managerEventId = event.rawEventData?.id || event.id;
    console.log(
      `🔑 [EventCarouselHomePage] Using managerEventId for checkout: ${managerEventId}`
    );

    // Ensure we have inventory data
    let inventory = [];
    if (event.rawEventData?.inventory) {
      inventory = event.rawEventData.inventory;
    } else if (event.inventory) {
      inventory = event.inventory;
    }

    console.log("📦 [EventCarouselHomePage] Extracted inventory:", inventory);

    // 🆕 ADD DEBUG LOGGING
    console.log(
      "🎠 [EventCarouselHomePage] Converting priority event to unified event:"
    );
    console.log("- Event Name:", event.name);
    console.log("- Event Source:", event.source);
    console.log("- Event seatmapUrl:", event.seatmapUrl);
    console.log("- Event rawEventData:", event.rawEventData);

    const unifiedEvent: UnifiedEvent = {
      id: managerEventId, // Use the ManagerEvent ID here for checkout
      name: event.name,
      date: new Date(event.date), // Ensure date is a Date object
      venue: event.venue,
      city: event.city,
      country: event.country || "Unknown",
      imageUrl: event.image, // Assuming PriorityEventData uses 'image'
      genre: event.genre || null,
      segment: event.segment || event.category || null, // Ensure segment is mapped
      subGenre: event.subGenre || null,
      priceRange: event.priceRange || null,
      source: event.source === "ticketmaster" ? "ticketmaster" : "manager", // Be explicit
      originalEvent: {
        inventory: inventory,
        rawEventData: {
          ...event,
          seatmapUrl: event.seatmapUrl || null, // 🆕 ADD: Include seatmapUrl from database
        },
        url: event.url || event.ticketUrl || undefined,
      },
    };

    console.log(
      "✨ [EventCarouselHomePage] Converted UnifiedEvent:",
      unifiedEvent
    );

    setSelectedEvent(unifiedEvent);
    setIsModalOpen(true);
  };

  // Handle modal close
  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  // Inline fallback design to mimic HeroEventCard while loading.
  const FallBackHeroSkeleton = () => (
    <div className="relative w-full h-[60vh] rounded-2xl overflow-hidden bg-gray-300 animate-pulse">
      {/* Simulated background gradient */}
      <div className="absolute inset-0 bg-gradient-to-r from-gray-300 via-gray-200 to-gray-300"></div>
      {/* Simulated overlay content */}
      <div className="absolute bottom-0 left-0 right-0 p-6">
        {/* Placeholder for badges */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-2">
            <div className="w-20 h-6 bg-gray-400 rounded-full" />
            <div className="w-32 h-6 bg-gray-400 rounded-full" />
          </div>
        </div>
        {/* Placeholder for title */}
        <div className="mb-4">
          <div className="w-3/4 h-10 bg-gray-400 rounded" />
        </div>
        {/* Placeholder for button */}
        <div className="w-1/2 h-10 bg-gray-400 rounded-full" />
      </div>
    </div>
  );

  // This inner component combines popular events from different categories.
  const EventCarouselContent = () => {
    const { events: sportsEvents } = useHomeEvents("sports", {
      popularOnly: true,
    });
    const { events: musicEvents } = useHomeEvents("music", {
      popularOnly: true,
    });
    const { events: artsEvents } = useHomeEvents("arts", { popularOnly: true });

    // Combine all popular events from the various categories.
    const allPopularEvents = [...sportsEvents, ...musicEvents, ...artsEvents];

    return allPopularEvents.length > 0 ? (
      <EventCarousel
        events={allPopularEvents}
        onEventClick={handleEventClick}
      />
    ) : null;
  };

  return (
    <>
      <Suspense fallback={<FallBackHeroSkeleton />}>
        {apiLoading ? <FallBackHeroSkeleton /> : <EventCarouselContent />}
      </Suspense>

      {/* Add EventModal */}
      <EventModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        event={selectedEvent}
      />
    </>
  );
};
