/**
 * SMS Service
 * 
 * Handles sending SMS messages for verification and notifications
 */
import ApiError from '@/utils/ApiError';
import { SMS_CONFIG } from '../config/sms.config';
import { NODE_ENV } from '@/constants';

export class SmsService {
  /**
   * Send verification OTP via SMS
   * @param phoneNumber Recipient phone number
   * @param otp One-time password for verification
   */
  static async sendVerificationOTP(
    phoneNumber: string,
    otp: string
  ): Promise<{ success: boolean }> {
    const isDevelopment = NODE_ENV === "development";
    const message = SMS_CONFIG.TEMPLATES.VERIFICATION(otp);
    
    try {
      // Development mode - just log the OTP
      if (isDevelopment) {
        console.log(`📱 [DEV] SMS would be sent to: ${phoneNumber}`);
        console.log(`📱 [DEV] Actually sending to: ${SMS_CONFIG.TEST_PHONE}`);
        console.log(`📱 [DEV] Message: ${message}`);
        return { success: true };
      }
      
      // Production mode - select provider based on configuration
      switch (SMS_CONFIG.PROVIDER) {
        case 'twilio':
          return await this.sendViaTwilio(phoneNumber, message);
        
        case 'aws-sns':
          return await this.sendViaAwsSns(phoneNumber, message);
          
        default:
          throw new ApiError(500, `SMS provider '${SMS_CONFIG.PROVIDER}' not configured`);
      }
    } catch (error) {
      console.error('❌ Error sending SMS:', error);
      throw ApiError.internal('Failed to send verification SMS');
    }
  }
  
  /**
   * Send SMS via Twilio
   * @private
   */
  private static async sendViaTwilio(
    phoneNumber: string, 
    message: string
  ): Promise<{ success: boolean }> {
    try {
      // Dynamically import Twilio to avoid loading it unless needed
      const twilio = require('twilio');
      const client = twilio(
        SMS_CONFIG.PROVIDERS.TWILIO.ACCOUNT_SID,
        SMS_CONFIG.PROVIDERS.TWILIO.AUTH_TOKEN
      );
      
      const response = await client.messages.create({
        body: message,
        from: SMS_CONFIG.PROVIDERS.TWILIO.PHONE_NUMBER,
        to: phoneNumber
      });
      
      console.log(`📱 SMS sent via Twilio with SID: ${response.sid}`);
      return { success: true };
    } catch (error) {
      console.error('❌ Twilio SMS error:', error);
      throw new ApiError(500, 'Failed to send SMS via Twilio');
    }
  }
  
  /**
   * Send SMS via AWS SNS
   * @private
   */
  private static async sendViaAwsSns(
    phoneNumber: string, 
    message: string
  ): Promise<{ success: boolean }> {
    try {
      // AWS SNS implementation would go here
      // This is a placeholder for future implementation
      console.log(`📱 SMS would be sent via AWS SNS to: ${phoneNumber}`);
      return { success: true };
    } catch (error) {
      console.error('❌ AWS SNS error:', error);
      throw new ApiError(500, 'Failed to send SMS via AWS SNS');
    }
  }
}
