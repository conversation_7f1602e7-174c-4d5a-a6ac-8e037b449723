/**
 * This module defines the queue state slice for the application using Redux Toolkit.
 * It manages local state related to the waiting room/queue system, including UI state
 * that doesn't need to be persisted to the server.
 */

import { createSlice, PayloadAction } from "@reduxjs/toolkit"
import { QueueUserStatus } from "@/features/unified-events/types/queue.types"

// --------------- State Interface ---------------
export interface QueueState {
  // Track which events have active queues the user is aware of
  acknowledgedQueueEvents: Record<string, boolean>
  // UI state
  showQueueBanner: boolean
  // Local derived state that we want to access in multiple components
  activeEventQueues: Record<string, {
    isActive: boolean
    isInQueue: boolean
    isAdmitted: boolean
    position?: number
    status?: QueueUserStatus
    lastUpdated: number
  }>
}

// --------------- Initial State ---------------
const initialState: QueueState = {
  acknowledgedQueueEvents: {},
  showQueueBanner: false,
  activeEventQueues: {},
}

// --------------- Queue Slice ---------------
export const queueSlice = createSlice({
  name: "queue",
  initialState,
  reducers: {
    // Action to acknowledge a queue for an event
    acknowledgeQueue: (state, action: PayloadAction<string>) => {
      const eventId = action.payload
      state.acknowledgedQueueEvents[eventId] = true
    },
    
    // Action to toggle queue banner visibility
    setShowQueueBanner: (state, action: PayloadAction<boolean>) => {
      state.showQueueBanner = action.payload
    },
    
    // Action to clear a queue event from local state (e.g., when leaving event page)
    clearQueueEvent: (state, action: PayloadAction<string>) => {
      const eventId = action.payload
      if (state.activeEventQueues[eventId]) {
        delete state.activeEventQueues[eventId]
      }
    },
    
    // Action to reset all queue state (e.g., on logout)
    resetQueueState: (state) => {
      state.acknowledgedQueueEvents = {}
      state.showQueueBanner = false
      state.activeEventQueues = {}
    },
    
    // New action to update queue status manually (since we're not using RTK Query)
    updateQueueStatus: (state, action: PayloadAction<{
      eventId: string, 
      isActive: boolean, 
      userStatus?: {
        status: QueueUserStatus,
        position?: number
      }
    }>) => {
      const { eventId, isActive, userStatus } = action.payload
      
      state.activeEventQueues[eventId] = {
        isActive,
        isInQueue: !!userStatus,
        isAdmitted: userStatus?.status === QueueUserStatus.ACTIVE,
        position: userStatus?.position,
        status: userStatus?.status,
        lastUpdated: Date.now(),
      }
      
      // Set banner visibility if queue is active and not acknowledged
      if (isActive && !state.acknowledgedQueueEvents[eventId]) {
        state.showQueueBanner = true
      }
    }
  },
  // Remove extraReducers since we're not using RTK Query endpoints anymore
})

// Export action creators
export const {
  acknowledgeQueue,
  setShowQueueBanner,
  clearQueueEvent,
  resetQueueState,
  updateQueueStatus,
} = queueSlice.actions

// Export reducer
export default queueSlice.reducer