// Define types for Manager Events (inventory events created by managers)

import {
  Prisma,
  EventCategory,
  SeatingType,
  TicketFormat,
  SellingPreference,
  EventApprovalStatus,
} from "@prisma/client";

export interface InventoryItem {
  id: string;
  quantity: number;
  section: string;
  row: number;
  seatingType: SeatingType;
  ticketFormat: TicketFormat;
  listPrice: number;
  serviceFee?: number;
  publicNote?: string;
  internalNote?: string;
  termsAndConditions: boolean;
  sellingPreference: SellingPreference;
  disclosures: string[];
  attributes: string[];
}

export interface CreateManagerEventPayload {
  managerId: string;
  eventId: string; // Added eventId field
  name: string;
  category: EventCategory; // Updated to use EventCategory
  source: string;
  date: string; // ISO 8601 format
  venue: string;
  city: string;
  country: string;
  image?: string | null;
  inventory: InventoryItem[]; // Updated to use InventoryItem array
  purchaseOrder?: Prisma.JsonValue | null; // Updated type
  rawEventData?: Prisma.JsonValue; // Updated type
}

export interface UpdateManagerEventApprovalPayload {
  id: string; // ManagerEvent ID
  approvalStatus: EventApprovalStatus; // New status
  approvedBy?: string; // Admin's User ID
  approvalNotes?: string; // Optional notes
}

// Add pagination types at the end of existing types
export interface PaginationParams {
  page?: number;
  limit?: number;
}

export interface PaginationResponse {
  page: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
}

export interface PaginatedManagerEventsResponse {
  events: any[]; // Use appropriate type based on your needs
  pagination: PaginationResponse;
}
