import React, { useState, useEffect, useRef } from "react";
import { GlobalSearch } from "@/features/global-search/components/GlobalSearch";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Calendar, Search, Filter } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { format } from "date-fns";
import { UnifiedEvent } from "@/features/unified-events/adapters/eventAdapter";
import { useIsMobile } from "@/hooks/use-mobile";

interface EventsHeaderProps {
  toggleFilterDrawer: () => void;
  allEvents: UnifiedEvent[];
  onFilteredEventsChange: (filteredEvents: UnifiedEvent[]) => void;
  shouldAutoFocus: boolean;
}
export const EventsHeader: React.FC<EventsHeaderProps> = ({ 
  toggleFilterDrawer, 
  allEvents,
  onFilteredEventsChange,
  shouldAutoFocus
}) => {
  const isMobile = useIsMobile();
  
  // Filter state
  const [searchTerm, setSearchTerm] = useState("");
  const [cityFilter, setCityFilter] = useState("");
  const [dateFilter, setDateFilter] = useState<Date | undefined>(undefined);

  const searchInputRef = useRef<HTMLInputElement>(null);

// Add this useEffect after existing state
useEffect(() => {
  if (shouldAutoFocus && searchInputRef.current) {
    searchInputRef.current.focus();
  }
}, [shouldAutoFocus]);
  // Apply filters
  const applyFilters = () => {
    // Include both manager and ticketmaster events
    const filtered = allEvents.filter(event => {
      // Search term filter (checks name, city, venue, country)
      const searchTermMatch = !searchTerm || 
        event.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.city?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.venue?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.country?.toLowerCase().includes(searchTerm.toLowerCase());
      
      // City filter
      const cityMatch = !cityFilter || 
        event.city?.toLowerCase().includes(cityFilter.toLowerCase());
      
      // Date filter
      const dateMatch = !dateFilter || 
        (event.date && isSameDay(event.date, dateFilter));
      
      return searchTermMatch && cityMatch && dateMatch;
    });
    
    // Update parent component with filtered events
    onFilteredEventsChange(filtered);
  };

  // Helper function to check if two dates are the same day
  const isSameDay = (date1: Date, date2: Date): boolean => {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  };

  // Reset all filters
  const handleReset = () => {
    setSearchTerm("");
    setCityFilter("");
    setDateFilter(undefined);
    onFilteredEventsChange(allEvents.filter(event => event.source === "manager"));
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    // Apply filters on each change for real-time filtering
    setTimeout(applyFilters, 300);
  };

  // Handle city input change
  const handleCityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCityFilter(e.target.value);
    // Apply filters on each change for real-time filtering
    setTimeout(applyFilters, 300);
  };

  // Handle date selection
  const handleDateSelect = (date: Date | undefined) => {
    setDateFilter(date);
    // Apply filters immediately when date changes
    setTimeout(applyFilters, 300);
  };

  return (
    <div className="flex flex-col gap-4 mb-4 border p-4 rounded-lg shadow-sm">
      {/* Search bar and mobile filter button */}
      <div className="flex flex-row gap-2 items-center w-full">
        <div className="relative flex-grow">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <Input
          ref={searchInputRef}
          type="text"
          placeholder="Search events by name, city, venue..."
          className="pl-10 w-full"
          value={searchTerm}
          onChange={handleSearchChange}
          />
        </div>
        
        {/* Mobile filter button */}
        {isMobile && (
          <Button 
            size="sm" 
            onClick={toggleFilterDrawer} 
            variant="outline" 
            className="flex items-center"
          >
            <Filter className="h-4 w-4 mr-1" />
            <span>Filters</span>
          </Button>
        )}
      </div>
      
      {/* Desktop filter controls */}
      {!isMobile && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="city" className="text-sm font-medium mb-1 block">City</Label>
            <Input
              id="city"
              type="text"
              placeholder="Filter by city"
              value={cityFilter}
              onChange={handleCityChange}
            />
          </div>
          
          <div>
            <Label className="text-sm font-medium mb-1 block">Event Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-start text-left">
                  <Calendar className="mr-2 h-4 w-4" />
                  {dateFilter ? format(dateFilter, "PPP") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <CalendarComponent
                  mode="single"
                  selected={dateFilter}
                  onSelect={handleDateSelect}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          
          <div className="flex items-end">
            <Button 
              variant="outline" 
              className="w-full"
              onClick={handleReset}
            >
              Reset Filters
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
