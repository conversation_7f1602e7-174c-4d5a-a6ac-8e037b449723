// CategorySection component for displaying a list of priority events in a specific category
import React from "react";
import { EventCard } from "./SettingsEventCard";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { AlertCircle } from "lucide-react";
import { EventCategory } from "@/state/priorityEventsSlice";
import { PriorityEventData } from "./types/priority-events.types";

interface CategorySectionProps {
  title: string;
  categoryId: EventCategory;
  events: PriorityEventData[]; // Updated to use PriorityEventData
  onRemoveEvent: (eventId: string) => void;
}

export const CategorySection: React.FC<CategorySectionProps> = ({
  title,
  categoryId,
  events,
  onRemoveEvent,
}) => {
  const handleRemoveEvent = (eventId: string) => {
    console.log("🔄 Category section handling remove for:", eventId);
    onRemoveEvent(eventId);
  };

  return (
    <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
      <div className="flex flex-col space-y-1.5 p-4 sm:p-6">
        <div className="flex items-center justify-between">
          <h3 className="text-xl sm:text-2xl font-semibold leading-none tracking-tight">
            {title}
          </h3>
          <Badge variant="secondary">{events.length}/10</Badge>
        </div>
      </div>
      <Separator />
      <ScrollArea className="h-[250px] p-2 sm:p-4">
        {events.length > 0 ? (
          <div className="space-y-4">
            {events.map((event) => (
              <EventCard
                key={event.eventId} // Changed from event.metadata.id to event.eventId
                event={event}
                categoryId={categoryId}
                onRemove={handleRemoveEvent} // Changed from event.metadata.id to event.eventId
              />
            ))}
          </div>
        ) : (
          <div className="flex h-[200px] items-center justify-center text-muted-foreground">
            <div className="flex flex-col items-center gap-2">
              <AlertCircle className="h-8 w-8" />
              <p>No events added to this category</p>
            </div>
          </div>
        )}
      </ScrollArea>
    </div>
  );
};
