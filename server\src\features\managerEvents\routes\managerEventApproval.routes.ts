import { Router } from "express";
import { ManagerEventApprovalController } from "../controllers/managerEventApproval.controller";
// import { authenticate, authorize } from "@middleware/auth.middleware"; // Import auth middleware

const router = Router();

// Get all pending events (require authentication and ADMIN role)
router.get(
  "/pending",
//   authenticate,
//   authorize(["ADMIN"]),
  ManagerEventApprovalController.getPendingEvents
);

// Update event approval status (require authentication and ADMIN role)
router.patch(
  "/:id/approval",  // Use PATCH for partial updates
//   authenticate,
//   authorize(["ADMIN"]),
  ManagerEventApprovalController.updateApprovalStatus
);

// Get all approved events (with pagination and filtering)
router.get(
  "/approved",
//   authenticate, // Uncomment when auth is implemented
  ManagerEventApprovalController.getApprovedEvents
);

// Add this new route after the existing approved route
router.get(
  "/rejected",
//   authenticate, // Uncomment when auth is implemented
  ManagerEventApprovalController.getRejectedEvents
);

export default router;
