import * as z from "zod";

// Zod schema for profile form validation
export const profileFormSchema = z.object({
  fullName: z.string().min(2, "Full name must be at least 2 characters"),
  headline: z.string().max(100, "Headline must be less than 100 characters").optional(),
  bio: z.string().max(500, "Bio must be less than 500 characters").optional(),
  mobile: z.string().refine(
    (value) => !value || /^\+?[0-9\s\-()]{8,20}$/.test(value),
    "Invalid phone number format"
  ).optional(),
  location: z.string().optional(),
  website: z.string().url("Invalid website URL").optional().or(z.literal("")),
  
  // Professional information
  company: z.string().optional(),
  jobTitle: z.string().optional(),
  industry: z.string().optional(),
  
  // Skills
  skills: z.array(z.string()).optional(),
  
  // Social links as nested object
  socialLinks: z.object({
    twitter: z.string().url("Invalid Twitter URL").optional().or(z.literal("")),
    linkedin: z.string().url("Invalid LinkedIn URL").optional().or(z.literal("")),
    facebook: z.string().url("Invalid Facebook URL").optional().or(z.literal("")),
    instagram: z.string().url("Invalid Instagram URL").optional().or(z.literal("")),
    github: z.string().url("Invalid GitHub URL").optional().or(z.literal("")),
  }).optional(),
});

// Export type based on the schema
export type ProfileFormValues = z.infer<typeof profileFormSchema>;
