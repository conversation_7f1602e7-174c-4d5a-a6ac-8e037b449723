'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { ErrorDisplay } from "@/components/shared/ErrorDisplay"

export default function RootError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  const router = useRouter()

  return (
    <ErrorDisplay 
      title="An Error Occurred"
      message={error.message}
      actions={
        <>
          <Button onClick={() => router.push('/')}>Return Home</Button>
          <Button onClick={() => reset()} variant="outline">Try Again</Button>
        </>
      }
    />
  )
}
