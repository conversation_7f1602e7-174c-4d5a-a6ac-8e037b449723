import { useRoleNavigation } from "../../hooks/useRoleNavigation";
import { useAppSelector } from "@/app/redux";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { SidebarSection } from "./SidebarSection";
import { SidebarToggle } from "./SidebarToggle";
import { HamburgerMenu } from "./HamburgerMenu";

export const RoleSidebar = () => {
  const { navigationItems, userRole } = useRoleNavigation();
  const isSidebarCollapsed = useAppSelector(
    (state) => state.global.isSidebarCollapsed
  );
  return (
 
      <div className="flex flex-col h-full ">
        <motion.div
        initial={{ x: -300 }}
        animate={{ x: 0 }}
          className={cn(
            "p-4 border-b bg-gradient-to-r from-background to-accent/10",
            "transition-all duration-300 ease-in-out",
            isSidebarCollapsed ? "translate-x-0" : "-translate-x-full",
             "fixed inset-y-0 left-0 z-50",
             "bg-background/80 backdrop-blur-[12px]", // Updated backdrop-blur value
             "shadow-lg",
             "border-r border-border/40", // Added border color with opacity
            "w-64 md:w-64",
            "md:translate-x-0",
          )}
        >
          <div className="flex items-center justify-between">
            <div
            className={cn(
              "flex flex-col gap-1",
              isSidebarCollapsed ? "hidden" : "block",
              "md:block"
            )}
          >
            <h2 className="text-xl font-bold bg-gradient-to-r from-primary to-primary/50 bg-clip-text text-transparent">
              Fanseatmaster
            </h2>
            <p className="text-xs text-muted-foreground capitalize">
              {userRole.toLowerCase()} Portal
            </p>
          </div>
                <HamburgerMenu/>
            </div>
        </motion.div>

        <nav className="flex-1 overflow-y-auto p-4">
          {/* Replaced TooltipProvider and nested mapping with SidebarSection component */}
          {navigationItems.map((section, index) => (
            <SidebarSection
              key={section.title || index}
              section={section}
              collapsed={isSidebarCollapsed}
            />
          ))}
        </nav>
      
        <div className='md:hidden'>
        </div>
        <div className='hidden md:block'>
        <SidebarToggle />
        </div>
      </div>
 

   
  );
};




























// import { useRoleNavigation } from "../../hooks/useRoleNavigation";
// import { useAppSelector } from "@/app/redux";
// import { cn } from "@/lib/utils";
// import { motion } from "framer-motion";

// // Removed Tooltip imports
// // Added import for SidebarSection component
// import { SidebarSection } from "./SidebarSection";
// import { SidebarToggle } from "./SidebarToggle";

// export const RoleSidebar = () => {
//   const { navigationItems, userRole } = useRoleNavigation();
//   const isSidebarCollapsed = useAppSelector(
//     (state) => state.global.isSidebarCollapsed
//   );
//   return (
 
//       <div className="flex flex-col h-full ">
//         <motion.div
//           className="p-4 border-b bg-gradient-to-r from-background to-accent/10"
//           initial={{ opacity: 0 }}
//           animate={{ opacity: 1 }}
//         >
//           <div
//             className={cn(
//               "flex flex-col gap-1",
//               isSidebarCollapsed ? "hidden" : "block",
//               "md:block"
//             )}
//           >
//             <h2 className="text-xl font-bold bg-gradient-to-r from-primary to-primary/50 bg-clip-text text-transparent">
//               Fanseatmaster
//             </h2>
//             <p className="text-xs text-muted-foreground capitalize">
//               {userRole.toLowerCase()} Portal
//             </p>
//           </div>
//         </motion.div>

//         <nav className="flex-1 overflow-y-auto p-4">
//           {/* Replaced TooltipProvider and nested mapping with SidebarSection component */}
//           {navigationItems.map((section, index) => (
//             <SidebarSection
//               key={section.title || index}
//               section={section}
//               collapsed={isSidebarCollapsed}
//             />
//           ))}
//         </nav>
//       </div>
 

   
//   );
// };
