/**
 * MessageInput Component
 * 
 * Reusable message input component with validation and submission.
 * Used in TicketMessageModal, MessageReplyModal, and Admin messaging.
 */

import React, { useState, useRef, useEffect, KeyboardEvent } from 'react';
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { 
  Send, 
  Loader2, 
  AlertCircle,
  CheckCircle,
  Type,
  Paperclip
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useMessageValidation } from '../hooks/useMessaging';
import { MessageInputProps } from '../types/messaging.types';
import { toast } from 'sonner';

export const MessageInput: React.FC<MessageInputProps> = ({
  onSendMessage,
  placeholder = "Type your message...",
  disabled = false,
  isLoading = false,
  maxLength = 1000,
  minLength = 10,
}) => {
  const [message, setMessage] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  
  const { validateMessage } = useMessageValidation();

  // Validate current message
  const validation = validateMessage(message, minLength, maxLength);
  const trimmedLength = message.trim().length;
  const isNearLimit = trimmedLength > maxLength * 0.8;
  const isOverLimit = trimmedLength > maxLength;

  // Auto-resize textarea based on content
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      const newHeight = Math.min(textarea.scrollHeight, 120); // Max height of 120px
      textarea.style.height = `${newHeight}px`;
    }
  }, [message]);

  // Handle message submission
  const handleSubmit = async () => {
    if (!validation.isValid || isSubmitting || isLoading || disabled) {
      if (validation.error) {
        toast.error(validation.error);
      }
      return;
    }

    setIsSubmitting(true);
    try {
      await onSendMessage(message.trim());
      setMessage(''); // Clear input on successful send
      
      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error('Failed to send message. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    // Submit on Ctrl/Cmd + Enter
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      e.preventDefault();
      handleSubmit();
      return;
    }
    
    // Submit on Enter (but allow Shift + Enter for new lines)
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
      return;
    }
  };

  // Character count styling
  const getCounterStyling = () => {
    if (isOverLimit) {
      return "text-red-500 font-medium animate-pulse";
    }
    if (isNearLimit) {
      return "text-orange-500 font-medium";
    }
    return "text-gray-400";
  };

  // Get validation icon
  const getValidationIcon = () => {
    if (trimmedLength === 0) return <Type className="h-4 w-4 text-gray-400" />;
    if (!validation.isValid) return <AlertCircle className="h-4 w-4 text-red-500" />;
    return <CheckCircle className="h-4 w-4 text-green-500" />;
  };

  return (
    <div className="space-y-3">
      {/* Input Area */}
      <div 
        className={cn(
          "relative rounded-lg border transition-all duration-200",
          isFocused && "ring-2 ring-blue-500 ring-opacity-50",
          validation.isValid && trimmedLength > 0 && "border-green-300",
          !validation.isValid && trimmedLength > 0 && "border-red-300",
          disabled && "opacity-50 cursor-not-allowed"
        )}
      >
        {/* Textarea */}
        <Textarea
          ref={textareaRef}
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          disabled={disabled || isLoading || isSubmitting}
          className={cn(
            "min-h-[80px] max-h-[120px] resize-none border-0 focus-visible:ring-0 focus-visible:ring-offset-0",
            "pr-12 pb-12", // Space for icons and footer
            disabled && "cursor-not-allowed"
          )}
          maxLength={maxLength + 100} // Allow slight overage for better UX
        />

        {/* Validation Icon */}
        <div className="absolute top-3 right-3">
          {getValidationIcon()}
        </div>

        {/* Footer with character count and actions */}
        <div className="absolute bottom-0 left-0 right-0 flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-b-lg border-t">
          {/* Character Counter */}
          <div className="flex items-center space-x-2">
            <span className={cn("text-xs", getCounterStyling())}>
              {trimmedLength}/{maxLength}
            </span>
            
            {/* Minimum length hint */}
            {trimmedLength > 0 && trimmedLength < minLength && (
              <Badge variant="outline" className="text-xs text-orange-600 border-orange-200">
                Min {minLength} chars
              </Badge>
            )}
            
            {/* Validation error */}
            {!validation.isValid && trimmedLength > 0 && (
              <Badge variant="outline" className="text-xs text-red-600 border-red-200">
                {validation.error}
              </Badge>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-2">
            {/* Future: Attachment button */}
            <Button
              variant="ghost"
              size="sm"
              className="h-7 w-7 p-0 opacity-50 cursor-not-allowed"
              disabled={true}
              title="Attachments (Coming Soon)"
            >
              <Paperclip className="h-3 w-3" />
            </Button>

            {/* Send Button */}
            <Button
              onClick={handleSubmit}
              disabled={!validation.isValid || isSubmitting || isLoading || disabled}
              size="sm"
              className={cn(
                "h-7 px-3 transition-all duration-200",
                validation.isValid && "bg-blue-500 hover:bg-blue-600"
              )}
            >
              {isSubmitting || isLoading ? (
                <>
                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                  <span className="text-xs">Sending...</span>
                </>
              ) : (
                <>
                  <Send className="h-3 w-3 mr-1" />
                  <span className="text-xs">Send</span>
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Help Text */}
      <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
        <div className="flex items-center space-x-4">
          <span>Press Enter to send, Shift+Enter for new line</span>
          {isFocused && (
            <span className="text-blue-500">• Ctrl+Enter also sends</span>
          )}
        </div>
        
        {/* Status indicators */}
        <div className="flex items-center space-x-2">
          {isSubmitting && (
            <Badge variant="outline" className="text-blue-600 border-blue-200 animate-pulse">
              Sending...
            </Badge>
          )}
          
          {validation.isValid && trimmedLength >= minLength && (
            <Badge variant="outline" className="text-green-600 border-green-200">
              Ready to send
            </Badge>
          )}
        </div>
      </div>

      {/* Extended Error Display */}
      {!validation.isValid && trimmedLength > 0 && (
        <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-center space-x-2">
            <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
            <p className="text-sm text-red-700 dark:text-red-300">
              {validation.error}
            </p>
          </div>
          
          {/* Helpful suggestions */}
          {trimmedLength < minLength && (
            <p className="text-xs text-red-600 dark:text-red-400 mt-1 ml-6">
              Add {minLength - trimmedLength} more characters to send your message.
            </p>
          )}
          
          {trimmedLength > maxLength && (
            <p className="text-xs text-red-600 dark:text-red-400 mt-1 ml-6">
              Remove {trimmedLength - maxLength} characters to send your message.
            </p>
          )}
        </div>
      )}
    </div>
  );
};

// Compact version for smaller spaces
export const CompactMessageInput: React.FC<MessageInputProps> = ({
  onSendMessage,
  placeholder = "Type a message...",
  disabled = false,
  isLoading = false,
  maxLength = 500,
  minLength = 5,
}) => {
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const { validateMessage } = useMessageValidation();
  const validation = validateMessage(message, minLength, maxLength);

  const handleSubmit = async () => {
    if (!validation.isValid || isSubmitting || isLoading || disabled) return;

    setIsSubmitting(true);
    try {
      await onSendMessage(message.trim());
      setMessage('');
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSubmit();
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <div className="flex-1 relative">
        <input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled || isLoading || isSubmitting}
          maxLength={maxLength}
          className={cn(
            "w-full px-3 py-2 pr-10 border rounded-lg text-sm",
            "focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
            "disabled:opacity-50 disabled:cursor-not-allowed",
            !validation.isValid && message.trim().length > 0 && "border-red-300"
          )}
        />
        
        {/* Character count for compact version */}
        <span className={cn(
          "absolute right-2 top-1/2 transform -translate-y-1/2 text-xs",
          message.trim().length > maxLength * 0.8 ? "text-orange-500" : "text-gray-400"
        )}>
          {message.trim().length}/{maxLength}
        </span>
      </div>

      <Button
        onClick={handleSubmit}
        disabled={!validation.isValid || isSubmitting || isLoading || disabled}
        size="sm"
        className="h-9 px-3"
      >
        {isSubmitting || isLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Send className="h-4 w-4" />
        )}
      </Button>
    </div>
  );
};

// Default export for easier imports
export default MessageInput;