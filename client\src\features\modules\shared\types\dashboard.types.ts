export type BentoGridItemType = {
    title: string;
    description?: string;
    header?: React.ReactNode;
    className?: string;
    children?: React.ReactNode;
    itemLimit?: number;    // Added for pagination
    showMoreButton?: boolean;  // Added for show more functionalit
  };
  
  export type MetricCardType = {
    title: string;
    value: number | string;
    icon?: React.ReactNode;
    trend?: {
      value: number;
      isPositive: boolean;
    };
    className?: string;
  };
  
  export type ChartContainerType = {
    title: string;
    children: React.ReactNode;
    className?: string;
    height?: number | string;
  };
  