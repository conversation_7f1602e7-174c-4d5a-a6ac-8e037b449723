/**
 * Queue Service Module
 *
 * Provides core functionality for creating, managing, and interacting with
 * event waiting room queues. Handles operations like joining queues,
 * checking status, activating/deactivating queues, and processing admissions.
 */

import { PrismaClient, Prisma } from '@prisma/client';
import {
  QueueStatusResponse,
  JoinQueueResponse,
  QueueServiceInterface,
  QueueUserStatus, // Use our defined enum
  Queue,
  QueueUser
} from '../types/queue.types'; // Use types from our file
import { MembershipService } from '../../membership/services/membership.service'; // Import MembershipService
import ApiError from '@/utils/ApiError';
import { NODE_ENV } from '@/constants';

// Initialize Prisma Client
const prisma = new PrismaClient();

// Default session duration in minutes (how long a user can stay active before expiring)
const DEFAULT_SESSION_DURATION_MINUTES = 12;

// Default batch size for processing queue admissions
const DEFAULT_BATCH_SIZE = 20;

export class QueueService implements QueueServiceInterface {

  /**
   * Ensures a queue exists for a given event ID. Creates one if it doesn't.
   * @param eventId - The ID of the event.
   * @returns The existing or newly created Queue object.
   * @throws {ApiError} If database operation fails.
   * @private Internal helper method.
   */
  private async ensureQueueExists(eventId: string): Promise<Prisma.QueueGetPayload<{}>> { // Use Prisma type for return
    if (!eventId) {
       throw new ApiError(400, "Event ID is required to ensure queue exists.");
    }
    try {
      // NOTE: We use findFirst here because eventId is not @unique in the schema
      let queue = await prisma.queue.findFirst({
        where: { eventId }
      });

      if (!queue) {
        if (NODE_ENV === 'development') {
          console.log(`🆕 [QueueService] Creating new queue for event: ${eventId}`);
        }
        queue = await prisma.queue.create({
          data: {
            eventId,
            isActive: false // Queues are inactive by default
            // No totalWaiting field in the schema
          }
        });
      }
      return queue;
    } catch (error) {
      console.error(`❌ [QueueService] Error ensuring queue exists for event ${eventId}:`, error);
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
         throw new ApiError(500, `Database error ensuring queue for event ${eventId}: ${error.code}`);
      }
      throw new ApiError(500, `Failed to initialize queue for event ${eventId}`);
    }
  }

  /**
   * Gets the status of a queue for an event, optionally including user-specific status.
   * @param eventId - The ID of the event to check.
   * @param userId - Optional user ID to get personalized status.
   * @returns Promise resolving to the queue status information.
   * @throws {ApiError} If database operation fails.
   */
  async getQueueStatus(eventId: string, userId?: string): Promise<QueueStatusResponse> {
     if (!eventId) {
       throw new ApiError(400, "Event ID is required to get queue status.");
    }
    try {
       // NOTE: Use findFirst since eventId is not @unique
      const queue = await prisma.queue.findFirst({
        where: { eventId }
      });

      // If no queue exists, return inactive status
      if (!queue) {
        return {
          eventId,
          isActive: false
          // No queueState since there's no queue
        };
      }

      // Get waiting count separately since _count is not directly accessible
      const waitingCount = await prisma.queueUser.count({
        where: {
          queueId: queue.id,
          status: QueueUserStatus.WAITING
        }
      });

      // Base response with overall queue state
      const response: QueueStatusResponse = {
        eventId,
        isActive: queue.isActive,
        queueState: {
          totalWaiting: waitingCount
        }
      };

      // If a userId is provided, find their specific status within this queue
      if (userId) {
        const queueUser = await prisma.queueUser.findUnique({
          where: {
            userId_queueId: { // Use the compound unique key
              userId,
              queueId: queue.id
            }
          }
        });

        if (queueUser) {
          let position: number | undefined;
          // Calculate position only if the user is currently WAITING
          if (queueUser.status === QueueUserStatus.WAITING) {
            // Count users ahead in the queue:
            // 1. Higher priority users
            // 2. Same priority users who entered earlier
            position = await prisma.queueUser.count({
              where: {
                queueId: queue.id,
                status: QueueUserStatus.WAITING, // Only count waiting users
                OR: [
                  { priority: { gt: queueUser.priority } },
                  {
                    AND: [
                      { priority: queueUser.priority },
                      { entryTime: { lt: queueUser.entryTime } }
                    ]
                  }
                ]
              }
            });
            position += 1; // Position is 1-based
          }

          // Add user-specific status to the response
          response.userStatus = {
            status: queueUser.status as QueueUserStatus, // Cast to our defined enum
            position, // Will be undefined if not waiting
            enteredAt: queueUser.entryTime.toISOString(),
            admittedAt: queueUser.admittedAt?.toISOString(), // Optional chaining for null values
            expiresAt: queueUser.expiresAt?.toISOString()   // Optional chaining
          };
        }
        // If queueUser is not found for this userId/queueId, userStatus remains undefined
      }

      return response;
    } catch (error) {
      console.error(`❌ [QueueService] Error getting queue status for event ${eventId}:`, error);
      throw new ApiError(500, `Failed to retrieve queue status for event ${eventId}`);
    }
  }

  /**
   * Adds a user to the waiting room queue for an event.
   * Calculates priority automatically based on user's membership.
   * @param eventId - The ID of the event.
   * @param userId - The ID of the user joining.
   * @returns Promise resolving to information about the user's queue entry.
   * @throws {ApiError} If queue is inactive, user not found, or DB error occurs.
   */
  async joinQueueWithMembership(eventId: string, userId: string): Promise<JoinQueueResponse> {
    console.log(`➕ [QueueService] User ${userId} attempting to join queue for event ${eventId} with membership check.`);
     if (!eventId || !userId) {
       throw new ApiError(400, "Event ID and User ID are required to join the queue.");
    }
    try {
      // 1. Get user's priority from MembershipService
      const { priority } = await MembershipService.getUserPriority(userId);
      console.log(`ℹ️ [QueueService] User ${userId} has priority ${priority} for event ${eventId}.`);

      // 2. Call the core joinQueue logic with the determined priority
      return await this.joinQueue(eventId, userId, priority);

    } catch (error) {
       console.error(`❌ [QueueService] Error joining queue with membership for event ${eventId}, user ${userId}:`, error);
       // Re-throw specific ApiErrors or wrap generic ones
       if (error instanceof ApiError) {
         throw error;
       }
       throw new ApiError(500, `Failed to join waiting room using membership for event ${eventId}`);
    }
  }


  /**
   * Core logic to add a user to the queue with a specific priority.
   * Prefer using `joinQueueWithMembership` for automatic priority calculation.
   * @param eventId - The ID of the event.
   * @param userId - The ID of the user joining.
   * @param priority - The pre-calculated priority for the user.
   * @returns Promise resolving to information about the user's queue entry.
   * @throws {ApiError} If queue is inactive or DB error occurs.
   */
  async joinQueue(eventId: string, userId: string, priority: number): Promise<JoinQueueResponse> {
    console.log(`➕ [QueueService] Core join queue logic for User ${userId}, Event ${eventId}, Priority ${priority}.`);
     if (!eventId || !userId || priority === undefined) {
       throw new ApiError(400, "Event ID, User ID, and Priority are required for core joinQueue.");
    }
    try {
      // 1. Ensure the queue exists for this event
      const queue = await this.ensureQueueExists(eventId);

      // 2. Check if the queue is active
      if (!queue.isActive) {
        console.log(`🚪 [QueueService] Attempt to join inactive queue for event ${eventId} by user ${userId}.`);
        throw new ApiError(400, "Waiting room is not currently active for this event.");
      }

      // 3. Check if the user is already in this specific queue
      const existingEntry = await prisma.queueUser.findUnique({
        where: { userId_queueId: { userId, queueId: queue.id } }
      });

      // 4. Handle existing entry
      if (existingEntry) {
        console.log(`👤 [QueueService] User ${userId} already in queue ${queue.id} with status ${existingEntry.status}.`);
        let position: number | undefined;
        let message = "You are already in this queue.";

        if (existingEntry.status === QueueUserStatus.WAITING) {
           position = await prisma.queueUser.count({
              where: {
                queueId: queue.id,
                status: QueueUserStatus.WAITING,
                OR: [
                  { priority: { gt: existingEntry.priority } },
                  { AND: [{ priority: existingEntry.priority }, { entryTime: { lt: existingEntry.entryTime } }] }
                ]
              }
            }) + 1; // 1-based position
          message = `You are already in the waiting room at position ${position}.`;
        } else if (existingEntry.status === QueueUserStatus.ACTIVE) {
           message = "You have already been admitted. Proceed to purchase.";
        } else if (existingEntry.status === QueueUserStatus.EXPIRED) {
           message = "Your previous session expired. Please rejoin if needed.";
        } else { // COMPLETED
           message = "You have already completed your session in this queue.";
        }

        return {
          success: true, // Indicate success even if already joined
          queueId: queue.id,
          userId,
          status: existingEntry.status as QueueUserStatus,
          position,
          message
        };
      }

      // 5. Create new queue entry for the user
      console.log(`➕ [QueueService] Creating new queue entry for User ${userId} in Queue ${queue.id}.`);
      const newQueueUser = await prisma.queueUser.create({
        data: {
          userId,
          queueId: queue.id,
          status: QueueUserStatus.WAITING, // Start as waiting
          priority, // Use the provided priority
          entryTime: new Date() // Record entry time
        }
      });

      // 6. Calculate the user's initial position
      const initialPosition = await prisma.queueUser.count({
         where: {
           queueId: queue.id,
           status: QueueUserStatus.WAITING,
           OR: [
             { priority: { gt: newQueueUser.priority } },
             { AND: [{ priority: newQueueUser.priority }, { entryTime: { lt: newQueueUser.entryTime } }] }
           ]
         }
       }) + 1; // 1-based position

      console.log(`✅ [QueueService] User ${userId} joined queue ${queue.id} at position ${initialPosition}.`);

      return {
        success: true,
        queueId: queue.id,
        userId,
        status: newQueueUser.status as QueueUserStatus,
        position: initialPosition,
        message: `You have joined the waiting room at position ${initialPosition}.`
      };

    } catch (error) {
       console.error(`❌ [QueueService] Error in core joinQueue for event ${eventId}, user ${userId}:`, error);
       if (error instanceof ApiError) {
         throw error;
       }
       throw new ApiError(500, `Failed to join waiting room for event ${eventId}`);
    }
  }


  /**
   * Activates the queue for an event. Creates the queue if it doesn't exist.
   * @param eventId - The ID of the event whose queue should be activated.
   * @returns Promise resolving when the operation is complete.
   * @throws {ApiError} If database operation fails.
   */
  async activateQueue(eventId: string): Promise<void> {
     if (!eventId) {
       throw new ApiError(400, "Event ID is required to activate a queue.");
    }
    try {
      // Ensure queue exists first
      const queue = await this.ensureQueueExists(eventId);

      // Only update if it's not already active to avoid unnecessary writes
      if (!queue.isActive) {
          await prisma.queue.updateMany({
            where: { eventId }, // Use eventId for lookup
            data: {
              isActive: true,
              activatedAt: new Date(),
              deactivatedAt: null // Clear deactivation time if previously set
            }
          });
          console.log(`🟢 [QueueService] Queue activated for event ${eventId}`);
      } else {
          console.log(`ℹ️ [QueueService] Queue for event ${eventId} is already active.`);
      }
    } catch (error) {
      console.error(`❌ [QueueService] Error activating queue for event ${eventId}:`, error);
      throw new ApiError(500, `Failed to activate queue for event ${eventId}`);
    }
  }

  /**
   * Deactivates the queue for an event.
   * @param eventId - The ID of the event whose queue should be deactivated.
   * @returns Promise resolving when the operation is complete.
   * @throws {ApiError} If database operation fails.
   */
  async deactivateQueue(eventId: string): Promise<void> {
     if (!eventId) {
       throw new ApiError(400, "Event ID is required to deactivate a queue.");
    }
    try {
      // Find the queue first to avoid errors if it doesn't exist
      const queue = await prisma.queue.findFirst({ where: { eventId } });

      if (queue && queue.isActive) { // Only deactivate if it exists and is active
        await prisma.queue.updateMany({
          where: { eventId },
          data: {
            isActive: false,
            deactivatedAt: new Date()
          }
        });
        console.log(`🔴 [QueueService] Queue deactivated for event ${eventId}`);
      } else if (queue && !queue.isActive) {
         console.log(`ℹ️ [QueueService] Queue for event ${eventId} is already inactive.`);
      } else {
         console.log(`⚠️ [QueueService] No active queue found to deactivate for event ${eventId}.`);
      }
    } catch (error) {
      console.error(`❌ [QueueService] Error deactivating queue for event ${eventId}:`, error);
      throw new ApiError(500, `Failed to deactivate queue for event ${eventId}`);
    }
  }

  /**
   * Processes the next batch of users in the queue, admitting them based on priority and entry time.
   * @param eventId - The ID of the event queue to process.
   * @param batchSize - Optional number of users to admit (defaults to DEFAULT_BATCH_SIZE).
   * @returns Promise resolving to the number of users admitted in this batch.
   * @throws {ApiError} If queue not found or database error occurs.
   */
  async processNextBatch(eventId: string, batchSize: number = DEFAULT_BATCH_SIZE): Promise<number> {
     if (!eventId) {
       throw new ApiError(400, "Event ID is required to process the next batch.");
    }
    try {
      // 1. Find the active queue
      const queue = await prisma.queue.findFirst({ where: { eventId } });

      if (!queue) {
        throw ApiError.notFound(`Queue for event ${eventId} not found.`);
      }
      
      // Only process if queue is active
      if (!queue.isActive) {
          console.log(`🟡 [QueueService] Skipping batch processing for inactive queue: ${eventId}`);
          return 0;
      }

      // 2. Find the next batch of users to admit
      const usersToAdmit = await prisma.queueUser.findMany({
        where: {
          queueId: queue.id,
          status: QueueUserStatus.WAITING // Only admit waiting users
        },
        orderBy: [
          { priority: 'desc' }, // Highest priority first
          { entryTime: 'asc' }  // Within priority, earliest entry first
        ],
        take: batchSize
      });

      // 3. If no users are waiting, we're done for this cycle
      if (usersToAdmit.length === 0) {
        console.log(`ℹ️ [QueueService] No waiting users to admit for event ${eventId}.`);
        return 0;
      }

      // 4. Calculate admission and expiration times
      const now = new Date();
      const expiresAt = new Date(now.getTime() + DEFAULT_SESSION_DURATION_MINUTES * 60 * 1000);
      const userIdsToUpdate = usersToAdmit.map(user => user.id);

      // 5. Update the status of admitted users in bulk
      const updateResult = await prisma.queueUser.updateMany({
        where: {
          id: { in: userIdsToUpdate }
        },
        data: {
          status: QueueUserStatus.ACTIVE,
          admittedAt: now,
          expiresAt: expiresAt
        }
      });

      // 6. Update the queue's lastAdmittedAt timestamp
      if (updateResult.count > 0) {
        await prisma.queue.update({
          where: { id: queue.id },
          data: {
            lastAdmittedAt: now
            // No totalWaiting/totalAdmitted fields in schema
          }
        });
        console.log(`✅ [QueueService] Admitted ${updateResult.count} users for event ${eventId}.`);
      } else {
         console.warn(`⚠️ [QueueService] updateMany reported 0 updates for event ${eventId}, though ${usersToAdmit.length} users were selected.`);
      }

      return updateResult.count; // Return the actual number updated

    } catch (error) {
      console.error(`❌ [QueueService] Error processing next batch for event ${eventId}:`, error);
      throw new ApiError(500, `Failed to process next batch for event ${eventId}`);
    }
  }


  /**
   * Checks for and marks active user sessions as EXPIRED if their time limit has passed.
   * This should be run periodically by a background job.
   * @returns Promise resolving to the number of sessions expired.
   * @throws {ApiError} If database operation fails.
   */
  async expireActiveSessions(): Promise<number> {
    try {
      const now = new Date();

      // Find and update active sessions whose expiration time is in the past
      const updateResult = await prisma.queueUser.updateMany({
        where: {
          status: QueueUserStatus.ACTIVE, // Only check active users
          expiresAt: { lt: now }          // Whose expiration time is less than now
        },
        data: {
          status: QueueUserStatus.EXPIRED // Mark them as expired
        }
      });

      if (updateResult.count > 0 && NODE_ENV === 'development') {
        console.log(`⏱️ [QueueService] Expired ${updateResult.count} active user sessions.`);
      }

      return updateResult.count; // Return how many sessions were expired
    } catch (error) {
      console.error(`❌ [QueueService] Error expiring active sessions:`, error);
      throw new ApiError(500, `Failed to expire active sessions`);
    }
  }
}
