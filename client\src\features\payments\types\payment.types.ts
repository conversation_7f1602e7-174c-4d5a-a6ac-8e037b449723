/**
 * Frontend payment-related types
 * These types align with the backend payment data structures
 */

// ---- Common Types ----

/**
 * Standard currency codes
 */
export type SupportedCurrency = 'USD' | 'EUR' | 'GBP' | 'INR' | 'CAD' | 'AUD';

/**
 * Payment status enum (mirrors backend PaymentStatus)
 */
export enum PaymentStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  SUCCEEDED = 'SUCCEEDED',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED',
  PARTIALLY_REFUNDED = 'PARTIALLY_REFUNDED',
  CANCELED = 'CANCELED',
}

// ---- Direct Payment Types ----

/**
 * Request to create a payment intent from checkout
 */
export interface CreatePaymentIntentRequest {
  sessionId: string;
  // Optional additional configuration
  setupFutureUsage?: 'on_session' | 'off_session';
  paymentMethodTypes?: string[];
}

/**
 * Response from creating a payment intent
 */
export interface PaymentIntentResponse {
  success: boolean;
  clientSecret?: string;
  paymentIntentId?: string;
  amount?: number;
  currency?: SupportedCurrency;
  status?: string;
  error?: string;
}

/**
 * Payment method appearance data for display
 */
export interface PaymentMethodDisplay {
  type: string;  // 'card', 'paypal', etc.
  brand?: string; // 'visa', 'mastercard', etc.
  last4?: string; // Last 4 digits if card
  expiryMonth?: number;
  expiryYear?: number;
}

// ---- Payment History Types ----

/**
 * Payment record summary for displaying payment history
 * Matches the backend PaymentHistorySummary
 */
export interface PaymentRecord {
  id: string;
  amount: number;
  currency: string;
  status: string;
  processor: string; // 'STRIPE', 'PAYPAL', etc.
  processedAt: string; // ISO date string
  description: string | null;
  paymentMethodDetails: string | null;
  transactionId: string;
  receiptUrl?: string;
  refundedAmount?: number;
  refundedAt?: string; // ISO date string
}

/**
 * Response from fetching payment history
 */
export interface PaymentHistoryResponse {
  success: boolean;
  data: {
    payments: PaymentRecord[];
    total: number;
  };
  error?: string;
}

/**
 * Parameters for payment history listing
 */
export interface PaymentHistoryParams {
  page?: number;
  limit?: number;
  sortBy?: 'processedAt' | 'amount';
  sortDirection?: 'asc' | 'desc';
  status?: string;
}

// ---- Subscription Types ----

/**
 * Subscription plan types
 */
export enum SubscriptionPlanType {
  MONTHLY = 'MONTHLY',
  VIP = 'VIP'
}

/**
 * Subscription status information
 */
export interface SubscriptionStatus {
  active: boolean;
  planType: SubscriptionPlanType | null;
  currentPeriodEnd?: string;
  cancelAtPeriodEnd?: boolean;
  trialEnd?: string;
  stripeSubscriptionId?: string;
}

/**
 * Response from subscription-related actions
 */
export interface SubscriptionActionResponse {
  success: boolean;
  message: string;
  subscriptionStatus?: SubscriptionStatus;
  error?: string;
}

/**
 * Request to create a subscription checkout session
 */
export interface CreateSubscriptionCheckoutRequest {
  planType: SubscriptionPlanType;
  successUrl: string;
  cancelUrl: string;
}

/**
 * Response from creating a subscription checkout
 */
export interface CreateSubscriptionCheckoutResponse {
  success: boolean;
  url?: string;
  error?: string;
}