/**
 * @description DefaultSidebar Component
 *
 * A responsive sidebar component that provides navigation functionality with collapsible behavior.
 * Features smooth animations, gradient styling, and dynamic navigation rendering.
 * Integrates with Redux for sidebar collapse state management.
 */

import { useAppSelector } from '@/app/redux';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { useNavigation } from '../../hooks/useNavigation';
import { SidebarSection } from './SidebarSection';
import { SidebarToggle } from './SidebarToggle';
import { HamburgerMenu } from './HamburgerMenu';

// ---------------Redux State Management-------------------
export const DefaultSidebar = () => {
  // Controls the collapsed state of the sidebar from global Redux store
  const isSidebarCollapsed = useAppSelector(state => state.global.isSidebarCollapsed);
  // Fetches navigation items using custom hook, handles both authenticated and non-authenticated states
  const { navigationItems } = useNavigation();

  return (
    // ---------------Animation and Layout-------------------
    <motion.aside
      initial={{ x: -300 }}
      animate={{ x: 0 }}
      className={cn(
        "fixed inset-y-0 left-0 z-50",
        "bg-background/80 backdrop-blur-[12px]", // Updated backdrop-blur value
        "shadow-lg transition-all duration-300 ease-in-out",
        "border-r border-border/40", // Added border color with opacity
        // Mobile first approach
        "w-64 md:w-64", // Set initial width and hide on mobile
        isSidebarCollapsed ? "translate-x-0" : "-translate-x-full",
        "md:translate-x-0", // Show on tablet and desktop

      )}
    >
      <div className="flex flex-col h-full">
        {/* ---------------Header Section------------------- */}
        <motion.div 
          className="p-4 border-b bg-gradient-to-r from-background to-accent/10"
          // Fade-in animation for header
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
            <div className="flex items-center justify-between">
            <h2 className={cn(
              // Gradient text effect for brand name
              "text-xl font-bold bg-gradient-to-r from-primary to-primary/50 bg-clip-text text-transparent",
              // Visibility control based on sidebar state
              isSidebarCollapsed ? "hidden" : "block",
              // Always visible on medium screens
              "md:block"
          )}>
            Fanseatmaster
          </h2>
                <HamburgerMenu/>
            </div>
        </motion.div>

        {/* ---------------Navigation Section------------------- */}
        <nav className="flex-1 overflow-y-auto p-4">
          {/* Dynamically render navigation sections with collapse state */}
          {navigationItems.map((section, index) => (
            <SidebarSection
              key={section.title || index}
              section={section}
              collapsed={isSidebarCollapsed}
            />
          ))}
        </nav>


       <div className='md:hidden'>
       </div>
       <div className='hidden md:block'>
        <SidebarToggle />
        </div>
      </div>
    </motion.aside>
  );
};



























// /**
//  * DefaultSidebar Component
//  * 
//  * A responsive sidebar component that provides navigation functionality with collapsible behavior.
//  * Features smooth animations, gradient styling, and dynamic navigation rendering.
//  * Integrates with Redux for sidebar collapse state management.
//  */

// import { useAppSelector } from '@/app/redux';
// import { cn } from '@/lib/utils';
// import { motion } from 'framer-motion';
// import { useNavigation } from '../../hooks/useNavigation';
// import { SidebarSection } from './SidebarSection';
// import { SidebarToggle } from './SidebarToggle';

// // ---------------Redux State Management-------------------
// export const DefaultSidebar = () => {
//   // Controls the collapsed state of the sidebar from global Redux store
//   const isSidebarCollapsed = useAppSelector(state => state.global.isSidebarCollapsed);
//   // Fetches navigation items using custom hook, handles both authenticated and non-authenticated states
//   const { navigationItems } = useNavigation();

//   return (
//     // ---------------Animation and Layout-------------------
//     <motion.aside
//       initial={{ x: -100 }}
//       animate={{ x: 0 }}
//       className={cn(
//         "fixed inset-y-0 left-0 z-50",
//         "bg-background/80 backdrop-blur-[12px]", // Updated backdrop-blur value
//         "shadow-lg transition-all duration-300 ease-in-out",
//         "border-r border-border/40", // Added border color with opacity
//         // Mobile first approach
//         "w-[280px] -translate-x-full", // Set initial width and hide on mobile
//         // Tablet and desktop styles
//         "md:translate-x-0", // Show on tablet and desktop
//         isSidebarCollapsed ? "md:w-16" : "md:w-64" // Collapse behavior for tablet and desktop
//       )}
//     >
//       <div className="flex flex-col h-full">
//         {/* ---------------Header Section------------------- */}
//         <motion.div 
//           className="p-4 border-b bg-gradient-to-r from-background to-accent/10"
//           // Fade-in animation for header
//           initial={{ opacity: 0 }}
//           animate={{ opacity: 1 }}
//         >
//           <h2 className={cn(
//             // Gradient text effect for brand name
//             "text-xl font-bold bg-gradient-to-r from-primary to-primary/50 bg-clip-text text-transparent",
//             // Visibility control based on sidebar state
//             isSidebarCollapsed ? "hidden" : "block",
//             // Always visible on medium screens
//             "md:block"
//           )}>
//             Fanseatmaster
//           </h2>
//         </motion.div>

//         {/* ---------------Navigation Section------------------- */}
//         <nav className="flex-1 overflow-y-auto p-4">
//           {/* Dynamically render navigation sections with collapse state */}
//           {navigationItems.map((section, index) => (
//             <SidebarSection
//               key={section.title || index}
//               section={section}
//               collapsed={isSidebarCollapsed}
//             />
//           ))}
//         </nav>


//         <SidebarToggle />
//       </div>
//     </motion.aside>
//   );
// };