"use client";

import React, { useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import { InfoTooltipIcon } from "@/components/shared/InfoTooltipIcon";
import { pricingDetailsTooltips } from "./tooltipContent";
import { PriceIndicator } from "./PriceIndicator"; // Import the new component
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useFormContext } from "react-hook-form";

// Reusable logic to render a mandatory label with tooltip icon
const renderMandatoryLabel = (
  label: string,
  tooltipKey?: keyof typeof pricingDetailsTooltips
) => (
  <div className="flex items-center">
    <FormLabel>
      {label} <span className="text-red-500">*</span>
    </FormLabel>
    {tooltipKey && (
      <InfoTooltipIcon content={pricingDetailsTooltips[tooltipKey] || ""} />
    )}
  </div>
);

export const PricingDetails: React.FC = () => {
  const form = useFormContext();
  const listPriceChangedByUser = useRef(false);
  const previousListPrice = useRef<number | null>(null);

  // Watch the value of listPrice from the form
  const listPrice = form.watch("listPrice");

  //TODO: Placeholder for price category.  Replace with your logic.
  const getPriceCategory = (price: number) => {
    if (price < 50) return "low";
    if (price < 100) return "medium";
    if (price < 200) return "high";
    return "veryHigh";
  };

  // Whenever listPrice changes (by user input), calculate and update serviceFee
  useEffect(() => {
    if (
      listPriceChangedByUser.current &&
      previousListPrice.current !== listPrice
    ) {
      const fee = Number((listPrice * 0.1).toFixed(2)); // Ensuring 2 decimal places
      form.setValue("serviceFee", fee, {
        shouldValidate: true,
        shouldDirty: true,
      });
      previousListPrice.current = listPrice;
      listPriceChangedByUser.current = false;
    }
  }, [listPrice, form]);

  return (
    <div className="border-y-2 py-4 px-2">
      <h2 className="text-lg font-semibold">Pricing</h2>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        {/* List Price Field */}
        <FormField
          control={form.control}
          name="listPrice"
          render={({ field }) => (
            <FormItem>
              <div className="flex items-center gap-x-2">
                {renderMandatoryLabel("List Price", "listPrice")}
              </div>

              <FormControl>
                <Input
                  type="number"
                  step="0.01"
                  min="0"
                  onChange={(e) => {
                    const value = parseFloat(e.target.value);
                    if (!isNaN(value)) {
                      listPriceChangedByUser.current = true;
                      field.onChange(value);
                    } else {
                      field.onChange(0);
                    }
                  }}
                  value={field.value !== undefined ? field.value : ""}
                  onBlur={() => {
                    if (field.value) {
                      field.onChange(Number(field.value.toFixed(2)));
                    }
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Service Fee Field (auto-calculated so no marker required) */}
        <FormField
          control={form.control}
          name="serviceFee"
          render={({ field }) => (
            <FormItem>
              <div className="flex items-center">
                <FormLabel>Service Fee</FormLabel>
                <InfoTooltipIcon
                  content={pricingDetailsTooltips.serviceFee || ""}
                />
              </div>
              <FormControl>
                <Input type="number" readOnly {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Total Price (Read-only) */}
        <FormField
          control={form.control}
          name="listPrice"
          render={({ field }) => {
            const currentListPrice = Number(field.value) || 0;
            const currentServiceFee = Number(form.watch("serviceFee")) || 0;
            return (
              <FormItem>
                <FormLabel>Total Price</FormLabel>
                <FormControl>
                  <Input
                    className="my-0"
                    readOnly
                    value={(currentListPrice + currentServiceFee).toFixed(2)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            );
          }}
        />

        <PriceIndicator variant={getPriceCategory(listPrice)} />
      </div>
    </div>
  );
};
