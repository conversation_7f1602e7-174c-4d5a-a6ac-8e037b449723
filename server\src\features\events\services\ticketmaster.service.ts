import axios from 'axios';
import  ApiError  from '@/utils/ApiError';

// const TICKETMASTER_API_URL = 'https://app.ticketmaster.eu/mfxapi/v2';

// export class TicketmasterService {
//   private apiClient;

//   constructor() {
//     this.apiClient = axios.create({
//       baseURL: TICKETMASTER_API_URL,
//     });
//   }

//   async testEventsFetch() {
//     try {
//       // Adding apikey as a query parameter instead of header
//       const response = await this.apiClient.get('/events', {
//         params: {
//           apikey: process.env.TICKETMASTER_API_KEY,
//           rows: 10, // Limit results for testing
//           lang: 'en-us', // Specify language
//         }
//       });
      
//       console.log('Ticketmaster API Response:', response.data);
//       return response.data;
//     } catch (error) {
//       if (axios.isAxiosError(error)) {
//         console.error('Ticketmaster API Error:', error.response?.data || error.message);
//         throw ApiError.badRequest(error.response?.data?.message || 'Failed to fetch events from Ticketmaster');
//       }
//       throw error;
//     }
//   }

//   // Add a method to test fetching a specific event
//   async testSingleEventFetch(eventId: string) {
//     try {
//       const response = await this.apiClient.get(`/events/${eventId}`, {
//         params: {
//           apikey: process.env.TICKETMASTER_API_KEY,
//           lang: 'en-us',
//         }
//       });
      
//       console.log('Single Event Response:', response.data);
//       return response.data;
//     } catch (error) {
//       if (axios.isAxiosError(error)) {
//         console.error('Ticketmaster API Error:', error.response?.data || error.message);
//         throw ApiError.badRequest('Failed to fetch event details from Ticketmaster');
//       }
//       throw error;
//     }
//   }
// }



// Update the base URL for Discovery API
const TICKETMASTER_API_URL = 'https://app.ticketmaster.com/discovery/v2';

export class TicketmasterService {
  private apiClient;

  constructor() {
    this.apiClient = axios.create({
      baseURL: TICKETMASTER_API_URL,
      params: {
        apikey: process.env.TICKETMASTER_API_KEY
      }
    });
  }

  async testEventsFetch() {
    try {
      const response = await this.apiClient.get('/events');
      console.log('Ticketmaster API Response:', response.data);
      return response.data;
    } 
    catch (error) {
              if (axios.isAxiosError(error)) {
                console.error('Ticketmaster API Error:', error.response?.data || error.message);
                throw ApiError.badRequest('Failed to fetch event details from Ticketmaster');
              }
              throw error;
            }
  }

  // New method to fetch a single event by ID
  async testSingleEventFetch(eventId: string) {
    try {
      const response = await this.apiClient.get(`/events/${eventId}`);
      console.log('Single Event Response:', response.data);
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Ticketmaster API Error:', error.response?.data || error.message);
        throw ApiError.badRequest('Failed to fetch event details from Ticketmaster');
      }
      throw error;
    }
  }
}
