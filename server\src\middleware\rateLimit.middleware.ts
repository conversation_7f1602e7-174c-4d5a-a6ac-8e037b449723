import { Request, Response, NextFunction } from 'express';
import { RateLimiterMemory, RateLimiterRes } from 'rate-limiter-flexible';
import ApiError from '@/utils/ApiError';
import { CustomRequest } from '@/features/tm_events/types/tm.types';

// Configuration for rate limiter
const rateLimiter = new RateLimiterMemory({
  points: 10, // 10 requests
  duration: 60, // per 60 seconds
});

// Rate limiting middleware function
export const rateLimitMiddleware = async (
  req: CustomRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const ip = (
        req.headers['cf-connecting-ip'] ||  // Cloudfare
        req.headers['x-real-ip'] ||        // Nginx
        req.headers['x-client-ip'] ||      // Apache
        (typeof req.headers['x-forwarded-for'] === 'string' 
          ? req.headers['x-forwarded-for'].split(',')[0]
          : Array.isArray(req.headers['x-forwarded-for'])
            ? req.headers['x-forwarded-for'][0]
            : null) ||
        req.socket.remoteAddress ||
        '127.0.0.1'
      ) as string;

    const rateLimiterRes: RateLimiterRes = await rateLimiter.consume(ip);
    // Add rate limit info to request object
    req.rateLimit = {
      remaining: rateLimiterRes.remainingPoints,
      resetTime: rateLimiterRes.msBeforeNext,
    };
    next();
  } catch (error) {
    //If Rate limit exceeded, throw error
     if(error instanceof Error){
        throw new ApiError(429, 'Too many requests, please try again later', [error.message]);
     }
    // Handle any other errors
    console.error('Rate limiting middleware error:', error);
      throw new ApiError(500, 'Internal Server Error', error as any);
  }
};
