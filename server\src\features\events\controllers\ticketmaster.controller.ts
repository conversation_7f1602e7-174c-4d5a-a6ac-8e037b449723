// Controller for handling Ticketmaster-related requests
import { Request, Response } from 'express';
import { TicketmasterService } from '../services/ticketmaster.service';

export class TicketmasterController {
  private ticketmasterService: TicketmasterService;

  constructor() {
    this.ticketmasterService = new TicketmasterService();
  }

  // Use arrow functions to maintain 'this' context
  testEvents = async (req: Request, res: Response) => {
    try {
      const events = await this.ticketmasterService.testEventsFetch();
      res.json(events);
    } catch (error) {
      console.error('Controller Error:', error);
      res.status(500).json({ error: 'Failed to fetch events' });
    }
  }

  // New method to fetch a single event by ID using arrow function
  testSingleEvent = async (req: Request, res: Response) => {
    try {
      const { eventId } = req.params;
      const event = await this.ticketmasterService.testSingleEventFetch(eventId);
      res.json(event);
    } catch (error) {
      console.error('Controller Error:', error);
      res.status(500).json({ error: 'Failed to fetch event details' });
    }
  }
}