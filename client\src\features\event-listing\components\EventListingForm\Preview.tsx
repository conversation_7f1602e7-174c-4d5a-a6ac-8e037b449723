// Preview component for displaying event details and inventory summary
"use client";

import React from "react";
import { useEventListing } from "./EventListingContext";
import { EventPreviewCard } from "@/components/shared/EventPreviewCard";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
// import { InventoryItem } from "../../types/eventListing";
import { SeatmapDisplay } from "../SeatmapDisplay"; // Import the new component

export const Preview: React.FC = () => {
  const { eventListingData } = useEventListing();

 
   const previewInventory = eventListingData.tempInventoryItem
   ? [...eventListingData.inventory, {
       ...eventListingData.tempInventoryItem,
       id: `preview-${eventListingData.tempInventoryItem.id}` // Add preview prefix
     }]
   : eventListingData.inventory;

  //! Extract the seatmap URL
  const seatmapUrl =
    eventListingData.selectedEvent?.metadata.rawEvent?.seatmap?.staticUrl;

  return (
    <div className="p-4 border-l border-gray-200">
      <h2 className="text-lg font-semibold mb-4">Preview</h2>

      {/* Event Details */}
      {eventListingData.selectedEvent && (
        <div className="mb-4">
          <h3 className="text-md font-semibold mb-2">Event Details</h3>
          <EventPreviewCard event={eventListingData.selectedEvent} />
        </div>
      )}

      {/* Seatmap Display */}
      {/* Conditionally render SeatmapDisplay or message */}
      <div className="mb-4">
        <h3 className="text-md font-semibold mb-2">Venu-Map</h3>
        <SeatmapDisplay seatmapUrl={seatmapUrl} />
      </div>

      {/* Inventory Summary */}
      {previewInventory.length > 0 && (
        <div className="mb-4">
          <h3 className="text-md font-semibold mb-2">Inventory Summary</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Qty</TableHead>
                <TableHead>Section/Row</TableHead>
                <TableHead>Seats</TableHead>
                <TableHead>List Price</TableHead>
                <TableHead>Total</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {previewInventory.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>{item.quantity}</TableCell>
                  <TableCell>{`${item.section} / ${item.row}`}</TableCell>
                  <TableCell>
                    {item.seatingType === "GA"
                      ? "GA"
                      : item.seatingType === "Consecutive"
                      ? `${item.lowSeatNumber || 0} - ${
                          (item.lowSeatNumber || 0) + item.quantity - 1
                        }`
                      : item.seatingType === "Odd-even"
                      ? (() => {
                          const start = item.lowSeatNumber || 0; // Default to 0 if undefined
                          const isOdd = start % 2 !== 0;
                          const lastSeat = start + (item.quantity - 1) * 2;
                          return `${start} (${
                            isOdd ? "Odd" : "Even"
                          }) - ${lastSeat}`;
                        })()
                      : "N/A"}
                  </TableCell>
                  <TableCell>${item.listPrice}</TableCell>
                  <TableCell>
                    $
                    {item.listPrice * item.quantity +
                      (item.serviceFee ? item.serviceFee * item.quantity : 0)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
};
