import React, { useState } from 'react';
import { ChevronUp, ChevronDown } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

type DropdownOption = {
  value: string;
  label: string;
  flag?: string;
};

type CustomDropdownProps = {
  options: DropdownOption[];
  value: string;
  onChange: (value: string) => void;
  triggerClassName?: string;
};

const CustomDropdown: React.FC<CustomDropdownProps> = ({
  options,
  value,
  onChange,
  triggerClassName,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const selectedOption = options.find(option => option.value === value);

  return (
    <DropdownMenu onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          className={`flex items-center justify-between w-full px-4 py-2 text-sm font-medium bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${triggerClassName}`}
        >
          <span className="flex items-center justify-center">
            {selectedOption?.flag && <span className="mr-2">{selectedOption.flag}</span>}
            {selectedOption?.label || value}
          </span>
          {isOpen ? <ChevronUp className="w-5 h-5 ml-2" /> : <ChevronDown className="w-5 h-5 ml-2" />}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-full min-w-[100px] bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5">
        {options.map((option) => (
          <DropdownMenuItem 
            key={option.value} 
            onClick={() => onChange(option.value)}
            className="flex items-center cursor-pointer justify-center px-1 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
          >
            {option.flag && <span className="mr-2">{option.flag}</span>}
            {option.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default CustomDropdown;
