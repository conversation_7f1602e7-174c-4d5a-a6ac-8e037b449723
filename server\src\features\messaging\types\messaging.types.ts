/**
 * Messaging Types Module
 * 
 * Defines TypeScript interfaces and types for the ticket messaging system.
 * Includes request/response types, DTOs, and service interfaces.
 */

import { UserRole, MessageStatus, Prisma } from '@prisma/client'; // ✅ MessageStatus should now exist

// ============================================================================
// DATABASE TYPES (Using Prisma generators)
// ============================================================================

/**
 * Complete TicketMessage with all relations
 */
export type TicketMessageWithRelations = Prisma.TicketMessageGetPayload<{
  include: {
    sender: {
      select: {
        id: true;
        role: true;
        fullName: true;
        email: true;
      };
    };
    checkoutSession: {
      select: {
        id: true;
        eventId: true;
        userId: true;
        user: {
          select: {
            id: true;
            fullName: true;
            email: true;
          };
        };
      };
    };
    escalatedByUser: {
      select: {
        id: true;
        fullName: true;
        role: true;
      };
    };
  };
}>;

/**
 * Basic TicketMessage without relations
 */
export type TicketMessageBasic = Prisma.TicketMessageGetPayload<{}>;

// ============================================================================
// REQUEST/RESPONSE INTERFACES
// ============================================================================

/**
 * Request payload for sending a new message
 */
export interface SendMessageRequest {
  checkoutSessionId: string;
  message: string;
  conversationId?: string;
  parentMessageId?: string;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Request payload for getting conversation messages
 */
export interface GetConversationRequest {
  page?: number;
  limit?: number;
  includeResolved?: boolean;
  since?: string; // ISO date string
}

/**
 * Request payload for updating message status
 */
export interface UpdateMessageStatusRequest {
  messageId: string;
  status: MessageStatus;
}

/**
 * Request payload for admin message queries
 */
export interface AdminMessagesRequest {
  page?: number;
  limit?: number;
  status?: MessageStatus;
  senderRole?: UserRole;
  eventId?: string;
  dateFrom?: string;
  dateTo?: string;
  isEscalated?: boolean;
}

// ============================================================================
// RESPONSE DATA TRANSFER OBJECTS (DTOs)
// ============================================================================

/**
 * Cleaned message data for client consumption
 */
export interface MessageDTO {
  id: string;
  checkoutSessionId: string;
  senderId: string;
  senderRole: UserRole;
  senderName: string;
  message: string;
  status: MessageStatus;
  conversationId?: string;
  parentMessageId?: string;
  isEscalated: boolean;
  escalatedBy?: string;
  escalatedAt?: string;
  createdAt: string; // ISO string
  updatedAt: string; // ISO string
  avatarUrl?: string;
  metadata?: {
    ipAddress?: string;
    userAgent?: string;
    [key: string]: any;
  };
}

/**
 * Conversation summary with latest message info
 */
export interface ConversationDTO {
  checkoutSessionId: string;
  ticketId: string; // Shortened display ID
  eventName?: string;
  buyerName: string;
  managerName?: string;
  messages: MessageDTO[];
  unreadCount: number;
  lastMessageDate: string;
  status: 'ACTIVE' | 'RESOLVED' | 'ESCALATED';
  participants: {
    visitor: { id: string; name: string };
    manager: { id: string; name: string };
    admin?: { id: string; name: string };
  };
}

/**
 * Admin oversight data for conversations
 */
export interface AdminConversationOverviewDTO {
  id: string;
  ticketId: string; // checkoutSessionId for display
  eventName?: string;
  buyerDisplayName: string; // "Buyer #123"
  managerDisplayName?: string; // "Seller #456" 
  lastMessageDate: string;
  status: 'ACTIVE' | 'RESOLVED' | 'ESCALATED';
  messageCount: number;
  unreadCount: number;
  escalated: boolean;
}

// ============================================================================
// API RESPONSE WRAPPERS
// ============================================================================

/**
 * Standard API response for single message
 */
export interface SendMessageResponse {
  success: boolean;
  message: string;
  data: MessageDTO;
}

/**
 * Standard API response for conversation messages
 */
export interface GetConversationResponse {
  success: boolean;
  message: string;
  data: {
    conversation: ConversationDTO;
    pagination: {
      currentPage: number;
      totalPages: number;
      totalItems: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
    };
  };
}

/**
 * Standard API response for admin conversations list
 */
export interface AdminConversationsResponse {
  success: boolean;
  message: string;
  data: {
    conversations: ConversationDTO[];
    pagination: {
      currentPage: number;
      totalPages: number;
      totalItems: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
    };
    summary: {
      totalConversations: number;
      unreadMessages: number;
      escalatedConversations: number;
      activeConversations: number;
    };
  };
}

// ============================================================================
// SERVICE INTERFACES
// ============================================================================

/**
 * Interface for message service operations
 */
export interface IMessagingService {
  sendMessage(userId: string, messageData: SendMessageRequest): Promise<MessageDTO>;
  getConversation(userId: string, checkoutSessionId: string, options?: GetConversationRequest): Promise<ConversationDTO>;
  markAsRead(userId: string, messageId: string): Promise<void>;
  markConversationAsResolved(userId: string, checkoutSessionId: string): Promise<void>;
  escalateConversation(checkoutSessionId: string, adminId: string): Promise<void>;
  getAllConversations(options: AdminMessagesRequest): Promise<AdminConversationsResponse['data']>;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Message filtering options for queries
 */
export interface MessageFilters {
  status?: MessageStatus;
  senderRole?: UserRole;
  dateRange?: {
    start: Date;
    end: Date;
  };
  searchTerm?: string;
}

/**
 * Conversation participation info
 */
export interface ConversationParticipant {
  userId: string;
  role: UserRole;
  displayName: string;
  lastActiveAt?: Date;
}

export type ConversationWithMessages = {
  checkoutSessionId: string;
  messages: TicketMessageWithRelations[];
  checkoutSession: {
    id: string;
    eventId: string;
    userId: string;
    user: {
      id: string;
      fullName: string | null;
      email: string;
    };
  };
};