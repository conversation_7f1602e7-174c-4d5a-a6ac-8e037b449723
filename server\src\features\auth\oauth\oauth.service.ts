import { PrismaClient } from '@prisma/client';
import { OAuthUserData } from './oauth.types';
//! when getting error : Object literal may only specify known properties,......  --- run=for types definations--> pnpm prisma generate and...... then modifying database--> run pnpm prisma migrate dev --name <yourchange>
const prisma = new PrismaClient();

export class OAuthService {
  async syncUser(data: OAuthUserData) {
    const { profile, account } = data;

    try {
      // Check for existing user
      let user = await prisma.user.findFirst({
        where: {
          OR: [
            { email: profile.email },
            {
              AND: [
                { providerId: profile.id },
                { providerType: account.provider }
              ]
            }
          ]
        },
        include: { accounts: true }
      });

      if (!user) {
        // Create new user if doesn't exist
        user = await prisma.user.create({
          data: {
            email: profile.email,
            fullName: profile.name || '',
            providerType: account.provider,
            providerId: profile.id,
            accounts: {
              create: {
                provider: account.provider,
                type: account.type,
                providerAccountId: account.providerAccountId,
                access_token: account.access_token,
                token_type: account.token_type,
                expires_at: account.expires_at
              }
            }
          },
          include: { accounts: true }
        });
      } else if (!user.accounts.some(acc => acc.provider === account.provider)) {
        // Add new provider account if user exists but hasn't used this provider
        await prisma.account.create({
          data: {
            userId: user.id,
            provider: account.provider,
            type: account.type,
            providerAccountId: account.providerAccountId,
            access_token: account.access_token,
            token_type: account.token_type,
            expires_at: account.expires_at
          }
        });
      }

      return user;
    } catch (error) {
      console.error('OAuth sync error:', error);
      throw error;
    }
  }
}
