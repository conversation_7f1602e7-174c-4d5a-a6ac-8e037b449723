/*   ---------------Basic redux setup for any nextjs application-----inside app----------------------------------------
 * This module sets up the Redux store for the application, including:
 * - Redux Toolkit configuration
 * - Redux Persist for state persistence
 * - Custom hooks for typed dispatch and selector
 * - Store provider component for wrapping the app
 */

import { useRef } from "react";
import { combineReducers, configureStore } from "@reduxjs/toolkit";
import {
  TypedUseSelectorHook,
  useDispatch,
  useSelector,
  Provider,
} from "react-redux";
import globalReducer from "@/state";
import { api } from "@/state/api";
import { setupListeners } from "@reduxjs/toolkit/query";

import {
  persistStore,
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from "redux-persist";
import { PersistGate } from "redux-persist/integration/react";
import createWebStorage from "redux-persist/lib/storage/createWebStorage";
import { priorityEventsSlice } from "@/state/priorityEventsSlice";
import queueReducer from "@/state/queueSlice";

// ---------------Redux Persistence-------------------
/**
 * Creates a no-op storage object for environments where localStorage is not available (e.g., server-side rendering)
 */
const createNoopStorage = () => {
  return {
    getItem(_key: any) {
      return Promise.resolve(null);
    },
    setItem(_key: any, value: any) {
      return Promise.resolve(value);
    },
    removeItem(_key: any) {
      return Promise.resolve();
    },
  };
};

// Use appropriate storage based on environment
const storage =
  typeof window === "undefined"
    ? createNoopStorage()
    : createWebStorage("local");

// Configuration for Redux Persist
const persistConfig = {
  key: "root",
  storage,
  whitelist: ["global"], // Only persist the 'global' slice of the state
};

// Combine reducers and apply persistence
const rootReducer = combineReducers({
  global: globalReducer,
  [api.reducerPath]: api.reducer,
  priorityEvents: priorityEventsSlice.reducer,
  queue: queueReducer,
});
const persistedReducer = persistReducer(persistConfig, rootReducer);

// ---------------Redux Store-------------------
/**
 * Creates and configures the Redux store with middleware and persistence
 */
export const makeStore = () => {
  return configureStore({
    reducer: persistedReducer,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          // Ignore these action types for serializability checks
          ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
        },
      }).concat(api.middleware),
  });
};

// ---------------Redux Types-------------------
// Type definitions for better type inference in the application
export type AppStore = ReturnType<typeof makeStore>;
export type RootState = ReturnType<AppStore["getState"]>;
export type AppDispatch = AppStore["dispatch"];

// Custom hooks for typed dispatch and selector
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// ---------------Provider Component-------------------
/**
 * StoreProvider component that wraps the application with Redux Provider and PersistGate
 * It ensures that the store is created only once and persisted across renders
 */
export default function StoreProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const storeRef = useRef<AppStore>();
  if (!storeRef.current) {
    // Create store if it doesn't exist
    storeRef.current = makeStore();
    // Set up listeners for RTK-Query
    setupListeners(storeRef.current.dispatch);
  }
  const persistor = persistStore(storeRef.current);

  return (
    <Provider store={storeRef.current}>
      <PersistGate loading={null} persistor={persistor}>
        {children}
      </PersistGate>
    </Provider>
  );
}