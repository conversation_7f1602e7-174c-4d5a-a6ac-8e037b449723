import { useEffect, useState } from "react";
import { useAppSelector, useAppDispatch } from "@/app/redux";
import {
  EventCategory,
  removeFromPriorityEvents,
} from "@/state/priorityEventsSlice";
import { CategorySection } from "./CategorySection";
import { useDeletePriorityEventMutation } from "@/state/api";
// Update import to use our centralized date utilities
import { isEventExpired, isEventTooSoon } from "@/utils/dateUtils";
import { PriorityEventData } from "./types/priority-events.types";
import { toast } from "sonner";

export const PriorityEvents = () => {
  const priorityEvents = useAppSelector((state) => state.priorityEvents);
  const dispatch = useAppDispatch();
  const [deletePriorityEvent] = useDeletePriorityEventMutation();

  // Categories definition
  const categories: { id: EventCategory; title: string }[] = [
    { id: "sports", title: "Sports" },
    { id: "music", title: "Music" },
    { id: "arts", title: "Arts & Theatre" },
  ];

  // Auto-removal effect for expired events - now using our standardized isEventExpired
  useEffect(() => {
    const checkExpiredEvents = async () => {
      Object.entries(priorityEvents).forEach(([category, events]) => {
        if (!Array.isArray(events)) return;

        events.forEach(async (event: PriorityEventData) => {
          // Replace shouldRemoveEvent with isEventExpired
          if (isEventExpired(event.date)) {
            console.log("🕒 Removing expired event:", event.name);

            // Remove from Redux store first (optimistic update)
            dispatch(
              removeFromPriorityEvents({
                category: category as EventCategory,
                eventId: event.eventId,
              })
            );

            // Show info toast
            toast.info(
              `⏰ Event "${event.name}" is no longer visible (expired)`
            );
          }
          
          // Optional: Add warning for imminent events
          else if (isEventTooSoon(event.date)) {
            console.log("⚠️ Event happening soon:", event.name);
          }
        });
      });
    };

    // Initial check and hourly interval
    checkExpiredEvents();
    const interval = setInterval(checkExpiredEvents, 60 * 60 * 1000); // Check hourly

    return () => clearInterval(interval);
  }, [priorityEvents, dispatch, deletePriorityEvent]);

  // Manual Event removal
  const handleRemoveEvent = async (
    categoryId: EventCategory,
    eventId: string
  ) => {
    console.log("🎯 Remove event triggered:", { categoryId, eventId });

    // Find the event in the category
    const eventToRemove = priorityEvents[categoryId].find(
      (event) => event.eventId === eventId
    );

    if (!eventToRemove) {
      console.error("⚠️ Event not found:", { categoryId, eventId });
      toast.error("❌ Unable to remove event: Event not found");
      return;
    }

    console.log("📊 Event to remove:", eventToRemove);

    try {
      // Optimistic update with logging
      console.log("🔄 Dispatching remove action");
      dispatch(removeFromPriorityEvents({ category: categoryId, eventId }));

      // API call with logging
      console.log("📡 Calling delete API");
      await deletePriorityEvent(eventToRemove.id).unwrap();
      console.log("✅ Event deleted successfully");
      toast.success(`🗑️ "${eventToRemove.name}" removed successfully`);
    } catch (error) {
      console.error("❌ Delete operation failed:", {
        error,
        event: eventToRemove,
        timestamp: new Date().toISOString(),
      });
      toast.error(
        `❌ Failed to remove "${eventToRemove.name}". Please try again.`
      );
    }
  };

  // Optional: Add function to filter expired events before rendering
  const getActiveEventsForCategory = (category: EventCategory) => {
    if (!priorityEvents[category] || !Array.isArray(priorityEvents[category])) {
      return [];
    }
    
    return priorityEvents[category].filter(event => !isEventExpired(event.date));
  };

  return (
    <div className="space-y-6">
      {categories.map((category) => (
        <CategorySection
          key={category.id}
          title={category.title}
          categoryId={category.id}
          // Use filtered events here instead of directly using priorityEvents[category.id]
          events={getActiveEventsForCategory(category.id)}
          onRemoveEvent={(eventId) => handleRemoveEvent(category.id, eventId)}
        />
      ))}
    </div>
  );
};