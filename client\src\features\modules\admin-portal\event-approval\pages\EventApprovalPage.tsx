// EventApprovalPage component for handling event approvals

import { useState, useMemo, useCallback } from "react";
import { Card } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ApprovalStats } from "../components/ApprovalMetrics/ApprovalStats";
import { ApprovalList } from "../components/ApprovalQueue/ApprovalList";
import { Filter, MapPin, Search, Users } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ApprovalStatus } from "../types/approval.types";

export const EventApprovalPage = () => {
  // State for filters
  const [activeTab, setActiveTab] = useState<ApprovalStatus>("PENDING");
  
  // 🔄 ACTIVE FILTERS (Working)
  const [category, setCategory] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState<string>("");
  
  // 🚫 DISABLED FILTERS (Future Implementation)
  const [region, setRegion] = useState<string>("all");
  const [managerType, setManagerType] = useState<string>("all");

  // ✨ FIXED: Memoize activeFilters to prevent unnecessary re-renders
  const activeFilters = useMemo(() => {
    return {
      search: searchQuery.trim().length >= 3 ? searchQuery.trim() : "", // Only filter with 3+ characters
      category: category === "all" ? "" : category,
    };
  }, [searchQuery, category]);

  // ✨ FIXED: Memoize handlers to prevent re-renders
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  }, []);

  const handleCategoryChange = useCallback((value: string) => {
    setCategory(value);
  }, []);

  const handleClearFilters = useCallback(() => {
    setSearchQuery("");
    setCategory("all");
  }, []);

  // ✨ FIXED: Memoize FilterControls component to prevent re-renders
  const FilterControls = useMemo(() => (
    <div className="space-y-6">
      {/* 🔍 SEARCH FILTER - ACTIVE */}
      <div className="space-y-2">
        <Label htmlFor="search">Search Events</Label>
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            id="search"
            placeholder="Event name, venue, city... (min 3 chars)"
            className="pl-8"
            value={searchQuery}
            onChange={handleSearchChange}
            autoComplete="off"
          />
        </div>
        {searchQuery && (
          <p className="text-xs text-gray-500">
            {searchQuery.length < 3 
              ? `Type ${3 - searchQuery.length} more character(s) to search`
              : `Searching: "${searchQuery}"`
            }
          </p>
        )}
      </div>

      {/* 🏷️ CATEGORY FILTER - ACTIVE */}
      <div className="space-y-2">
        <Label htmlFor="category">Category</Label>
        <Select value={category} onValueChange={handleCategoryChange}>
          <SelectTrigger id="category">
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            <SelectItem value="SPORTS">Sports</SelectItem>
            <SelectItem value="MUSIC">Music</SelectItem>
            <SelectItem value="ARTS">Arts</SelectItem>
            <SelectItem value="THEATER">Theater</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* 🌍 REGION FILTER - DISABLED */}
      <div className="space-y-2 opacity-50">
        <Label htmlFor="region" className="text-gray-400">
          Region (Coming Soon)
        </Label>
        <Select value={region} onValueChange={setRegion} disabled>
          <SelectTrigger id="region" className="flex items-center bg-gray-50">
            <MapPin className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Select region" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Regions</SelectItem>
            {/* TODO: Add region options when backend supports geographical filtering */}
            <SelectItem value="north">North</SelectItem>
            <SelectItem value="south">South</SelectItem>
            <SelectItem value="east">East</SelectItem>
            <SelectItem value="west">West</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-gray-400">
          Region filtering will be available in future updates
        </p>
      </div>

      {/* 👥 MANAGER TYPE FILTER - DISABLED */}
      <div className="space-y-2 opacity-50">
        <Label htmlFor="managerType" className="text-gray-400">
          Manager Type (Coming Soon)
        </Label>
        <Select value={managerType} onValueChange={setManagerType} disabled>
          <SelectTrigger id="managerType" className="flex items-center bg-gray-50">
            <Users className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Select manager type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Managers</SelectItem>
            {/* TODO: Add manager type options when user tier system is implemented */}
            <SelectItem value="regular">Regular</SelectItem>
            <SelectItem value="trusted">Trusted</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-gray-400">
          Manager filtering will be available when user tiers are implemented
        </p>
      </div>

      {/* 🔄 FILTER ACTIONS */}
      <div className="space-y-2">
        <Button 
          onClick={handleClearFilters} 
          variant="outline" 
          className="w-full"
          disabled={!searchQuery && category === "all"}
        >
          Clear Active Filters
        </Button>
        {/* Show active filter count */}
        {(activeFilters.search || activeFilters.category) && (
          <p className="text-xs text-center text-blue-600">
            {[activeFilters.search && "search", activeFilters.category && "category"]
              .filter(Boolean)
              .length} filter(s) active
          </p>
        )}
      </div>
    </div>
  ), [searchQuery, category, region, managerType, handleSearchChange, handleCategoryChange, handleClearFilters, activeFilters.search, activeFilters.category]);

  return (
    <div className="container mx-auto p-3 md:p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">Event Approvals</h1>
        <ApprovalStats />
      </div>

      {/* Status Filter Tabs */}
      <Tabs
        defaultValue="PENDING"
        value={activeTab}
        onValueChange={(value) => setActiveTab(value as ApprovalStatus)}
        className="mb-6"
      >
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="PENDING">Pending</TabsTrigger>
          <TabsTrigger value="APPROVED">Approved</TabsTrigger>
          <TabsTrigger value="REJECTED">Rejected</TabsTrigger>
        </TabsList>
      </Tabs>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Filters - Sidebar on desktop, hidden on mobile */}
        <div className="hidden md:block">
          <Card className="p-4">
            <h2 className="font-semibold mb-4">Filters</h2>
            {FilterControls}
          </Card>
        </div>

        {/* Mobile Filter Button and Sheet */}
        <div className="md:hidden mb-4">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" className="w-full flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                Filters
                {/* Show active filter count on mobile */}
                {(activeFilters.search || activeFilters.category) && (
                  <span className="ml-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                    {[activeFilters.search && "search", activeFilters.category && "category"]
                      .filter(Boolean)
                      .length}
                  </span>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent side="left">
              <SheetHeader>
                <SheetTitle>Filters</SheetTitle>
                <SheetDescription>
                  Filter events by search and category
                </SheetDescription>
              </SheetHeader>
              <div className="py-4">
                {FilterControls}
              </div>
            </SheetContent>
          </Sheet>
        </div>

        {/* Approval Queue - Full width on mobile, 3/4 width on desktop */}
        <div className="md:col-span-3">
          {/* ✨ PASS MEMOIZED FILTERS TO APPROVAL LIST */}
          <ApprovalList status={activeTab} filters={activeFilters} />
        </div>
      </div>
    </div>
  );
};
