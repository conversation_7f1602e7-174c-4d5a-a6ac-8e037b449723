/**
 * Controller for coupon operations within the checkout feature
 */

import { Request, Response } from 'express';
import { asyncHandler } from '@/utils/asyncHandler';
import ApiError from '@/utils/ApiError';
import { CouponService } from '../services/coupon.service';
import { ApplyCouponRequest } from '../types/coupon.types';

export class CouponController {
  /**
   * Apply a coupon to a checkout session
   * POST /api/v1/checkout/coupon
   */
  static applyCoupon = asyncHandler(async (req: Request, res: Response) => {
    console.log("🎟️ CouponController: Hit applyCoupon handler");
    
    // Validate required fields in request body
    const { sessionId, couponCode } = req.body as ApplyCouponRequest;
    
    if (!sessionId || couponCode === undefined) {
      throw ApiError.badRequest('Session ID and coupon code are required');
    }
    
    // Get user ID from authenticated request
    const userId = req.user?.userId;
    
    if (!userId) {
      throw ApiError.unauthorized('User must be authenticated');
    }
    
    console.log(`🔍 Processing coupon application: ${couponCode} for session ${sessionId}`);
    
    // This service will throw ApiErrors for validation failures
    const result = await CouponService.applyCoupon(sessionId, couponCode, userId);
    
    console.log("✅ Coupon applied successfully");
    
    // Return success with 200 OK status
    return res.status(200).json(result);
  });
}