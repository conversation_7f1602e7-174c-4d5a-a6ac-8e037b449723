/**
 * AdminDashboardSummary Component
 * 
 * Displays a summary of key admin metrics and statistics.
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Database, Shield, Users, Calendar, BarChart } from 'lucide-react';

export function AdminDashboardSummary() {
  return (
    <Card className="bg-primary/5 border-primary/20">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center">
          <Shield className="h-5 w-5 mr-2 text-primary" />
          Admin Dashboard Summary
        </CardTitle>
        <CardDescription>
          System overview and key metrics
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <Users className="h-4 w-4 mr-2 text-blue-600" />
                <span className="text-sm font-medium">Total Users</span>
              </div>
              <span className="font-medium">1,245</span>
            </div>
            <Progress value={78} className="h-2" />
            <div className="text-xs text-muted-foreground flex justify-between">
              <span>+12.5% from last month</span>
              <span>Target: 1,500</span>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-green-600" />
                <span className="text-sm font-medium">Active Events</span>
              </div>
              <span className="font-medium">32</span>
            </div>
            <Progress value={85} className="h-2" />
            <div className="text-xs text-muted-foreground flex justify-between">
              <span>+4 this week</span>
              <span>98% on schedule</span>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <Database className="h-4 w-4 mr-2 text-purple-600" />
                <span className="text-sm font-medium">System Status</span>
              </div>
              <span className="font-medium text-green-600">Healthy</span>
            </div>
            <Progress value={99} className="h-2" />
            <div className="text-xs text-muted-foreground flex justify-between">
              <span>99.9% uptime</span>
              <span>No issues detected</span>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button variant="outline" size="sm" className="w-full sm:w-auto">
          <BarChart className="h-4 w-4 mr-2" />
          View Full Dashboard
        </Button>
      </CardFooter>
    </Card>
  );
}
