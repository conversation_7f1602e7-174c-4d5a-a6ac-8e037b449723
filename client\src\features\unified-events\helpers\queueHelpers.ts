/**
 * Helper functions for queue-related operations
 */

import { QueueUserStatus } from "../types/queue.types";

/**
 * Calculate estimated wait time based on position and processing rate
 * @param position Current position in queue
 * @param processingRate Average number of users processed per minute
 * @returns Estimated wait time in minutes
 */
export const calculateEstimatedWaitTime = (
  position: number,
  processingRate: number = 10
): number => {
  if (!position || position <= 0 || !processingRate || processingRate <= 0) {
    return 0;
  }
  
  // Calculate minutes rounded up
  return Math.ceil(position / processingRate);
};

/**
 * Format wait time into human-readable string
 * @param minutes Wait time in minutes
 * @returns Formatted wait time string
 */
export const formatWaitTime = (minutes: number): string => {
  if (minutes <= 0) return "Less than a minute";
  
  if (minutes < 60) {
    return `About ${minutes} minute${minutes === 1 ? "" : "s"}`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `About ${hours} hour${hours === 1 ? "" : "s"}`;
  }
  
  return `About ${hours} hour${hours === 1 ? "" : "s"} and ${remainingMinutes} minute${remainingMinutes === 1 ? "" : "s"}`;
};

/**
 * Get queue status message based on user status
 * @param status User's status in the queue
 * @param position User's position in the queue
 * @returns Status message to display to the user
 */
export const getQueueStatusMessage = (
  status?: QueueUserStatus,
  position?: number
): string => {
  if (!status) return "Not in queue";
  
  switch (status) {
    case QueueUserStatus.WAITING:
      return position 
        ? `Waiting in line - position ${position}` 
        : "Waiting in line";
    case QueueUserStatus.ACTIVE:
      return "Your turn - you may proceed to checkout";
    case QueueUserStatus.EXPIRED:
      return "Your queue position has expired";
    default:
      return "Unknown status";
  }
};

/**
 * Check if user needs to join the queue
 * @param isQueueActive Whether the queue is active for the event
 * @param isInQueue Whether the user is already in the queue
 * @returns Boolean indicating if user needs to join queue
 */
export const needsToJoinQueue = (
  isQueueActive: boolean,
  isInQueue: boolean
): boolean => {
  return isQueueActive && !isInQueue;
};

/**
 * Check if user can proceed to checkout
 * @param isQueueActive Whether the queue is active for the event
 * @param isAdmitted Whether the user has been admitted from the queue
 * @returns Boolean indicating if user can proceed to checkout
 */
export const canProceedToCheckout = (
  isQueueActive: boolean,
  isAdmitted: boolean
): boolean => {
  return !isQueueActive || isAdmitted;
};
