import express from "express";
import cors from "cors";
import cookieParser from "cookie-parser";
import { PrismaClient } from "@prisma/client";
import { DATABASE_URL, PORT, NODE_ENV } from "./constants";

import eventRoutes from "./routes/eventRoutes";
import authRoutes from "./features/auth/credential/credential.routes";
import oauthRoutes from "./features/auth/oauth/oauth.routes";
import priorityEventRoutes from "./features/priority_events/routes/priorityEvent.routes";
import openctxRoutes from "./features/search/routes/workflow.routes";
// import { errorHandler } from "./middleware/error.middleware";
import eventManagementRoutes from "@features/events/event.routes";
// import ticketmasterRoutes from "@/features/events/routes/ticketmaster.routes";
import ticketmasterRoutes from "@features/tm_events/routes/tm.routes";
import managerEventRoutes from "./features/managerEvents/routes/managerEvent.routes";
import managerEventApprovalRoutes from "./features/managerEvents/routes/managerEventApproval.routes";
import profileRoutes from "./features/profile/routes/profile.routes";

import eventRoutesV1 from "./routes/v1/eventRoutes";
import eventRoutesV2 from "./routes/v2/eventRoutes";
import queueRoutes from "./features/queue/routes/queue.routes";
import checkoutRoutes from "./features/checkout/routes/checkout.routes"; //  export default
import { salesRoutes } from "./features/sales/routes/sales.routes"; // export with specific name
import { ticketRoutes } from "./features/tickets/routes/ticket.routes";

// 🆕 NEW: Import messaging routes
import messagingRoutes from "./features/messaging/routes/messaging.routes";

// Existing Queue BG Job Service Import
import { backgroundJobService } from "./features/queue/services/backgroundJob.service";

// Existing Checkout BG Job Service Import
import { checkoutBackgroundJobService } from "./features/checkout/services/checkoutBackgroundJob.service";

import stripePaymentsRoutes from "./features/payments/processors/stripe/routes/stripe-payments.route";
import paymentHistoryRoutes from "./features/payments/history/routes/payment-history.route";
import subscriptionRoutes from "./features/payments/subscriptions/routes/subscription.route";
import { billingAddressRoutes } from "./features/billing-address/routes/billing-address.routes";


import { requestLogger } from './middleware/requestLogger.middleware';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: DATABASE_URL,
    },
  },
  log:
    NODE_ENV === "production" ? ["error"] : ["query", "info", "warn", "error"],
});

const app = express();

let corsOptions;

// Determine CORS origin based on environment
if (NODE_ENV === "production") {
  // Production: Allow the specific Vercel URL
  // Replace with your actual production URL
  corsOptions = {
    origin: [
      "https://fanseatmaster.com",
      "https://www.fanseatmaster.com",
      "https://fanseatmaster.vercel.app",
      "https://fanseatmaster-staging.vercel.app",
    ], // or your custom domain
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
  };
} else if (NODE_ENV === "development") {
  // Development: Allow localhost:3000
  corsOptions = {
    origin: "http://localhost:3000",
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
  };
} else {
  // Fallback:  Restrictive CORS policy
  corsOptions = {
    origin: false, // disables CORS
    credentials: true,
  };
}

app.use(cors(corsOptions));

// IMPORTANT: Register Stripe webhook endpoint with raw body parsing
// This special middleware is needed ONLY for the webhook endpoint to verify signatures
//***** Stripe Webhook Route--- always keep this befroe express.json */
app.use(
  "/api/v1/payments/stripe/webhook",
  express.raw({ type: "application/json" })
);

app.use(express.json());
app.use(cookieParser());
app.use(express.urlencoded({ extended: false }));

// Apply request logger first (before auth)
app.use(requestLogger);

// app.use((req, res, next) => {
//   console.log(`${req.method} ${req.url}`);
//   next();
// });

app.get("/", (req, res) => {
  res.send("Fanseatmaster API is running well");
});

// Version 1 Routes
app.use("/api/v1/events", eventRoutesV1);

// Version 2 Routes
app.use("/api/v2/events", eventRoutesV2);

// Test endpoints for each version
app.get("/api/v1/test", (req, res) => {
  console.log("V1 API Test endpoint hit");
  res.json({ version: "v1", message: "Hello from API V1!" });
});

app.get("/api/v2/test", (req, res) => {
  console.log("V2 API Test endpoint hit");
  res.json({ version: "v2", message: "Hello from API V2!" });
});

// Routes
app.use("/api/v1/events", eventRoutes);
app.use("/api/v1/eventsManagement", eventManagementRoutes);
app.use("/api/v1/auth", authRoutes);

app.use("/api/v1/auth/oauth", oauthRoutes);

app.use("/api/v1/priority-events", priorityEventRoutes);

// Manager event routes
app.use("/api/v1/manager-events", managerEventRoutes);

// NEW: Manager event APPROVAL routes (separate base path)
app.use("/api/v1/manager-events", managerEventApprovalRoutes);

// profile routes
app.use("/api/v1/profile", profileRoutes);

// !---------testing phase ---------
app.use("/api/v1/ticketmaster", ticketmasterRoutes);
// !---------testing phase ---------
app.use("/api/v1/openctx", openctxRoutes);

// --- NEW: Register Queue and Checkout Routes ---
app.use("/api/v1/queue", queueRoutes);
app.use("/api/v1/checkout", checkoutRoutes);

// Register billing address routes
app.use("/api/v1/billing-addresses", billingAddressRoutes);

// ------------------------------------------------------------------------
// IMPORTANT: Register the payment routes BEFORE the express.raw() middleware for Stripe webhooks
// ------------------------------------------------------------------------

// Register payment history routes
app.use("/api/v1/payments/history", paymentHistoryRoutes);

// Register subscription routes
app.use("/api/v1/payments/subscriptions", subscriptionRoutes);

// Register regular Stripe payment routes AFTER the webhook-specific middleware
app.use("/api/v1/payments/stripe", stripePaymentsRoutes);

app.use("/api/v1/sales", salesRoutes);
app.use("/api/v1/tickets", ticketRoutes);

// 🆕 NEW: Register messaging routes
app.use("/api/v1/messaging", messagingRoutes);

// Global error handler - returns JSON instead of HTML
app.use(
  (
    err: any,
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) => {
    console.error("Global Error Handler:", err);
    const statusCode = err.statusCode || 500;
    const message = err.message || "Internal Server Error 💀";

    res.status(statusCode).json({
      success: false,
      message: message,
      errors: err.errors || [], // Include any validation errors
      data: null, // Ensure data is null in case of errors
      stack: process.env.NODE_ENV === "development" ? err.stack : undefined,
    });
  }
);

app.listen(PORT, () => {
  console.log(`Server running on 👉 http://localhost:${PORT}`);
  console.log(`Server is running on ${NODE_ENV} mode`);

  // Start the background job service
  // backgroundJobService.start();
  // checkoutBackgroundJobService.start();
});
