import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, ScanF<PERSON> } from 'lucide-react';

const passkeyOptions = [
  { icon: Fingerprint, label: "Fingerprint" },
  { icon: ScanFace, label: "Face ID" },
  { icon: Key, label: " Key" }
];

export const PasskeyOptions = () => {
  const passkeyVariants = {
    initial: { scale: 1 },
    hover: {
      scale: 1.02,
      boxShadow: "0 0 25px rgba(59, 130, 246, 0.5)",
      transition: { duration: 0.3 }
    }
  };

  return (
    <div className="grid grid-cols-3 gap-4">
      {passkeyOptions.map(({ icon: Icon, label }) => (
        <motion.div
          key={label}
          variants={passkeyVariants}
          whileHover="hover"
          initial="initial"
          className="bg-gradient-to-b from-blue-600/30 to-blue-400/30 p-4 rounded-xl border border-blue-400/30 cursor-pointer hover:border-blue-400/60"
        >
          <div className="flex flex-col items-center space-y-2">
            <Icon className="w-8 h-8 text-blue-300" />
            <span className="text-sm text-blue-200">{label}</span>
          </div>
        </motion.div>
      ))}
    </div>
  );
};
