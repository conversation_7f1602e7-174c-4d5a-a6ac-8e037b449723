// 🎨 Sidebar-specific widget component
"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON>rk<PERSON>, CheckCircle2, ArrowRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useOnboardingCheck } from '../hooks/useOnboardingCheck';
import { OnboardingModal } from './OnboardingModal';

interface OnboardingWidgetProps {
  collapsed: boolean;
}

export const OnboardingWidget: React.FC<OnboardingWidgetProps> = ({ collapsed }) => {
  const [modalOpen, setModalOpen] = useState(false);
  const { onboardingStatus } = useOnboardingCheck();
  
  const progressPercentage = (onboardingStatus.completedSteps / onboardingStatus.totalSteps) * 100;
  const isComplete = onboardingStatus.currentStep === 'complete';

  // Don't show if user isn't authenticated
  if (!onboardingStatus.isAuthenticated && onboardingStatus.completedSteps === 0) {
    return null;
  }

  if (collapsed) {
    // Collapsed view - just the icon with progress indicator
    return (
      <>
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="relative"
        >
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setModalOpen(true)}
            className={cn(
              "w-12 h-12 relative",
              "hover:bg-primary/10 transition-colors",
              isComplete ? "text-emerald-600" : "text-primary"
            )}
          >
            {isComplete ? (
              <CheckCircle2 className="h-5 w-5" />
            ) : (
              <Sparkles className="h-5 w-5" />
            )}
          </Button>
          
          {/* Progress indicator dot */}
          {!isComplete && (
            <div className="absolute -top-1 -right-1">
              <Badge variant="secondary" className="h-5 w-5 p-0 text-xs font-bold rounded-full">
                {onboardingStatus.completedSteps}
              </Badge>
            </div>
          )}
        </motion.div>

        <OnboardingModal 
          open={modalOpen} 
          onOpenChange={setModalOpen}
        />
      </>
    );
  }

  // Expanded view - full widget
  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className={cn(
          "p-3 rounded-lg border transition-all",
          "bg-gradient-to-br from-background to-accent/5",
          "border-border/30 hover:border-border/50"
        )}
      >
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-center gap-2">
            <div className={cn(
              "flex items-center justify-center w-6 h-6 rounded-full",
              isComplete 
                ? "bg-emerald-100 dark:bg-emerald-900/30" 
                : "bg-primary/10"
            )}>
              {isComplete ? (
                <CheckCircle2 className="w-3 h-3 text-emerald-600 dark:text-emerald-400" />
              ) : (
                <Sparkles className="w-3 h-3 text-primary" />
              )}
            </div>
            <div className="flex-1 min-w-0">
              <h4 className="text-xs font-semibold truncate">
                {isComplete ? 'Setup Complete!' : 'Complete Setup'}
              </h4>
            </div>
          </div>

          {/* Progress */}
          {!isComplete && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground">
                  {onboardingStatus.completedSteps}/{onboardingStatus.totalSteps} steps
                </span>
                <span className="font-medium text-primary">
                  {Math.round(progressPercentage)}%
                </span>
              </div>
              <Progress 
                value={progressPercentage} 
                className="h-1.5 bg-secondary/50"
              />
            </div>
          )}

          {/* Action button */}
          <Button
            variant={isComplete ? "ghost" : "default"}
            size="sm"
            onClick={() => setModalOpen(true)}
            className={cn(
              "w-full h-8 text-xs gap-1.5",
              isComplete && "text-muted-foreground hover:text-foreground"
            )}
          >
            {isComplete ? (
              <>
                <CheckCircle2 className="w-3 h-3" />
                View Setup
              </>
            ) : (
              <>
                Continue Setup
                <ArrowRight className="w-3 h-3" />
              </>
            )}
          </Button>
        </div>
      </motion.div>

      <OnboardingModal 
        open={modalOpen} 
        onOpenChange={setModalOpen}
      />
    </>
  );
};