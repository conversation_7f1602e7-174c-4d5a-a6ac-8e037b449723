/**
 * Type definitions for the waiting room/queue functionality
 */

// Enum representing possible statuses of a user in a queue
export enum QueueUserStatus {
  WAITING = 'WAITING',
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  COMPLETED = 'COMPLETED'
}

// Response from queue status API
export interface QueueStatusResponse {
  eventId: string;
  isActive: boolean;
  queueId?: string;
  queueState?: {
    totalWaiting: number;
    estimatedWaitTime?: number; // in minutes, if calculable
  };
  userStatus?: {
    status: QueueUserStatus;
    position?: number; // Only available for WAITING status
    enteredAt: string; // ISO date string
    admittedAt?: string; // ISO date string, present if status is ACTIVE
    expiresAt?: string; // ISO date string, when active status expires
    queueId?: string;
  };
}

// Response from joining queue API
export interface JoinQueueResponse {
  success: boolean;
  queueId: string;
  userId: string;
  status: QueueUserStatus;
  position?: number;
  estimatedWaitTime?: number; // in minutes
  message: string;
}

// Queue state managed by our hook & context
export interface QueueState {
  isLoading: boolean;
  error: Error | null;
  isActive: boolean; // Is there an active queue for this event?
  isInQueue: boolean; // Is the user in the queue?
  status?: QueueUserStatus; // User's status if in queue
  position?: number; // User's position if WAITING
  estimatedWaitTime?: number; // Estimated wait time in minutes
  admittedAt?: string; // When user was admitted, if ACTIVE
  expiresAt?: string; // When active status expires, if applicable
  queueId?: string; // ID of the queue
  totalWaiting?: number; // Total number of users waiting
  lastUpdated: Date; // Timestamp of last update
}