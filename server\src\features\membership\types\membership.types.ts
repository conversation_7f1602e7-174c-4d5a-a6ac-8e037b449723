/**
 * Membership Types Module
 * 
 * Defines types related to user membership tiers, including enumerations, 
 * interfaces and utility functions for membership-related operations.
 */

import { Prisma } from '@prisma/client';

// Define our own enum to match the schema (not relying on Prisma namespace)
export enum MembershipTier {
  STANDARD = 'STANDARD',
  SUBSCRIBER = 'SUBSCRIBER',
  VIP = 'VIP'
}

/**
 * Priority values for each membership tier
 * Higher numbers indicate higher priority in queuing systems
 */
export const MEMBERSHIP_PRIORITIES = {
  [MembershipTier.VIP]: 300,       // Highest priority
  [MembershipTier.SUBSCRIBER]: 200, // Medium priority
  [MembershipTier.STANDARD]: 100    // Base priority
};

/**
 * Calculates the numeric priority value from a membership tier
 * @param tier The user's membership tier
 * @returns A numeric priority value (higher = more priority)
 */
export function getPriorityFromTier(tier: MembershipTier): number {
  return MEMBERSHIP_PRIORITIES[tier] || MEMBERSHIP_PRIORITIES[MembershipTier.STANDARD];
}

// Use Prisma's type generator for User model (with membership tier)
export type MembershipUser = Prisma.UserGetPayload<{
  select: {
    id: true,
    membershipTier: true
  }
}>;

/**
 * Membership service response for getting a user's priority
 */
export interface UserPriorityResponse {
  userId: string;
  tier: MembershipTier;
  priority: number;
}
