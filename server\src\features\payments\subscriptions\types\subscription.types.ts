/**
 * Subscription-related types
 */

// Re-export core payment types needed for subscriptions
export { SupportedCurrency } from '../../common/types/payment-common.types';

// Subscription plan types
export enum SubscriptionPlanType {
  MONTHLY = 'MONTHLY',
  VIP = 'VIP'
}

// Request to get subscription details
export interface GetSubscriptionRequest {
  userId: string;
}

// Request to change subscription plan
export interface ChangeSubscriptionPlanRequest {
  userId: string;
  newPlanType: SubscriptionPlanType;
}

// Request to cancel subscription
export interface CancelSubscriptionRequest {
  userId: string;
  // Whether to cancel immediately or at period end
  immediately?: boolean;
}

// Response for subscription status
export interface SubscriptionStatusResponse {
  active: boolean;
  planType: SubscriptionPlanType | null;
  currentPeriodEnd?: string; // ISO date string
  cancelAtPeriodEnd?: boolean;
  trialEnd?: string; // ISO date string
  stripeSubscriptionId?: string;
}

// Response for subscription operations
export interface SubscriptionActionResponse {
  success: boolean;
  message: string;
  subscriptionStatus?: SubscriptionStatusResponse;
  error?: string;
}