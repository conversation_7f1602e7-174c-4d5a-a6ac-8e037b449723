import { CheckCircle, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { PlanBenefit } from "../types/subscription.types";

interface BenefitsListProps {
  benefits: PlanBenefit[];
  className?: string;
}

export function BenefitsList({ benefits, className }: BenefitsListProps) {
  return (
    <ul className={cn("space-y-3", className)}>
      {benefits.map((benefit, i) => (
        <li key={i} className="flex items-start">
          {benefit.included ? (
            <CheckCircle className="h-5 w-5 text-green-500 mr-2 shrink-0" />
          ) : (
            <X className="h-5 w-5 text-muted-foreground mr-2 shrink-0" />
          )}
          <div>
            <span className={benefit.included ? "" : "text-muted-foreground"}>
              {benefit.title}
            </span>
            {benefit.description && (
              <p className="text-xs text-muted-foreground mt-0.5">
                {benefit.description}
              </p>
            )}
          </div>
        </li>
      ))}
    </ul>
  );
}