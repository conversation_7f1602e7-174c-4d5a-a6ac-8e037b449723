"use client"
import { Line } from 'react-chartjs-2';
import { ChartContainer } from '@/features/modules/shared/charts/ChartContainer';
import { useEffect, useState } from 'react';
import { analyticsService } from '../services/analytics.service';
import type { ChartData } from '@/features/modules/shared/types/analytics.types';

export const RevenueChart = () => {
  const [data, setData] = useState<ChartData | null>(null);

  useEffect(() => {
    const loadData = async () => {
      const revenueData = await analyticsService.getRevenueData();
      setData(revenueData);
    };
    loadData();
  }, []);

  if (!data) return null;

  return (
    <ChartContainer title="Revenue Overview" height={400}>
      <Line 
        data={data}
        options={{
          responsive: true,
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                callback: (value) => `$${value}`
              }
            }
          }
        }}
      />
    </ChartContainer>
  );
};
