// Component for rendering actions for a single inventory item row.
import React from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Eye, Edit, Trash2, ToggleLeft, ToggleRight, Loader2 } from 'lucide-react';
import { ManagerInventoryItem } from '../types/inventory.types';
import { toast } from 'sonner';

// ✨ Update Props Interface ✨
import { ManagerInventoryActionsProps } from '../types/inventory.types';

// ✨ Destructure new props ✨
export const ManagerInventoryActions: React.FC<ManagerInventoryActionsProps> = ({
    item,
    toggleActive,
    isToggling,
    deleteEvent,
    isDeleting,
    onViewDetails,
    onEditInventory
}) => {

  // Call mutation functions on click
  const handleToggleClick = () => {
      toggleActive({ eventId: item.id });
  };

  const handleDeleteClick = () => {
      deleteEvent({ eventId: item.id });
  };

  // Check if any action related to this item is pending
  const isActionPending = isToggling || isDeleting; // Add other pending states if needed

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        {/* Disable button if an action is pending for this item */}
        <Button variant="ghost" className="h-8 w-8 p-0" disabled={isActionPending}>
          <span className="sr-only">Open menu</span>
          {isActionPending ? <Loader2 className="h-4 w-4 animate-spin" /> : <MoreHorizontal className="h-4 w-4" />}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        {/* Disable items individually if needed, or rely on trigger */}
        <DropdownMenuItem onClick={() => onViewDetails(item)} disabled={isActionPending}>
          <Eye className="mr-2 h-4 w-4" />
          <span>View Details</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onEditInventory(item)} disabled={isActionPending}>
          <Edit className="mr-2 h-4 w-4" />
          <span>Edit Inventory</span>
        </DropdownMenuItem>
        {/* ✨ Connect Toggle Action ✨ */}
        <DropdownMenuItem onClick={handleToggleClick} disabled={isToggling}>
            {isToggling ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> :
             item.isActive ? <ToggleLeft className="mr-2 h-4 w-4 text-red-500" /> :
             <ToggleRight className="mr-2 h-4 w-4 text-green-500" />
            }
            <span>{item.isActive ? 'Deactivate' : 'Activate'}</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        {/* ✨ Connect Delete Action ✨ */}
        <DropdownMenuItem
            onClick={handleDeleteClick}
            className="text-red-600 hover:!text-red-600 hover:!bg-red-100"
            disabled={isDeleting}
        >
            {isDeleting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Trash2 className="mr-2 h-4 w-4" />}
            <span>Delete</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
