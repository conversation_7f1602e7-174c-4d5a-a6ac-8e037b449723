// client/src/lib/ai/types/tools.types.ts
// Define common types for all server-side AI tools

// Define types based on Zod schemas
export interface OpenCTXRequest {
    protocol: 'openctx/v1';    // Protocol identifier
    entity: string;         // Entity type (e.g., 'events')
    filters?: {          // Optional array of filters
            field: string,
           value?: string,
            values?: string[]
    }[]
    sort?: {            // Optional sorting criteria
        field: string,
        order?: 'asc' | 'desc'
    }
     pagination?: {        // Optional pagination details
         page: number,
       size: number
    }
    query?: string;      // Optional free text query for keyword searches
}

export interface SearchTool {
    userQuery: string;
    openCTXFilters?: any;
}
