// EventCard.tsx: Displays individual event details with 3D card effect
"use client";
import React from "react";
import { TmEvent } from "../types/tm.types";
import { format } from "date-fns";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import { MapPin, Calendar, Building2, CircleDollarSign } from "lucide-react";
import { CardContainer, CardBody, CardItem } from "@/components/ui/3d-card";

interface EventCardProps {
  event: TmEvent;
  onEventClick: (event: TmEvent) => void;
}

export const EventCard: React.FC<EventCardProps> = ({
  event,
  onEventClick,
}) => {
  const formattedDate = event.startDateTime
    ? format(new Date(event.startDateTime), "MMM d, yyyy h:mm a")
    : "Not Available";

  return (
    // Updated to use 3D card components
    <div onClick={() => onEventClick(event)}>
      <CardContainer className="inter-var w-full cursor-pointer">
        <CardBody className="relative h-full w-full rounded-xl bg-white shadow-2xl 
          border
          group-hover:scale-[1.02] transition-transform duration-200 ease-out"
>
          <CardHeader className="space-y-3">
            {event.primaryImage && (
              // Added 3D effect to image
              <CardItem translateZ="110" className="w-full">
                <div className="relative w-full h-48 overflow-hidden rounded-t-lg">
                  <Image
                    src={event.primaryImage}
                    alt={event.name}
                    className="object-cover w-full h-full"
                    fill
                    sizes="(max-width: 767px) 100vw, (max-width: 1023px) 50vw, 25vw"
                    style={{ objectFit: "cover" }}
                    priority
                  />
                </div>
              </CardItem>
            )}
            {/* Added 3D effect to title */}
            <CardItem translateZ="75" className="w-full">
              <CardTitle className="line-clamp-1 text-lg font-bold">
                {event.name}
              </CardTitle>
            </CardItem>
            {/* Added 3D effect to description */}
            <CardItem translateZ="50" className="w-full">
              <CardDescription className="flex flex-col space-y-1">
                {event.venueName && (
                  <span className="flex items-center text-sm">
                    <Building2 className="h-4 w-4 mr-1" />
                    {event.venueName}
                  </span>
                )}
                {formattedDate && (
                  <span className="flex items-center text-sm">
                    <Calendar className="h-4 w-4 mr-1" />
                    {formattedDate}
                  </span>
                )}
              </CardDescription>
            </CardItem>
          </CardHeader>
          <CardContent>
            {/* Added 3D effect to badges */}
            <CardItem translateZ="60" className="w-full">
              <div className="flex flex-wrap gap-1">
                {event.genre && (
                  <Badge variant="secondary">{event.genre}</Badge>
                )}
                {event.segment && (
                  <Badge variant="secondary">{event.segment}</Badge>
                )}
                {event.subGenre && (
                  <Badge variant="secondary">{event.subGenre}</Badge>
                )}
              </div>
            </CardItem>
          </CardContent>
          <CardFooter className="flex flex-col space-y-2 pt-4 border-t">
            {/* Added 3D effect to footer content */}
            <CardItem translateZ="30" className="w-full">
              <div className="w-full flex justify-between items-center">
                {event.venueCity && (
                  <div className="flex items-center space-x-1.5">
                    <div className="bg-gray-100 p-1.5 rounded-full">
                      <MapPin className="h-4 w-4 text-gray-600" />
                    </div>
                    <span className="text-gray-600 text-sm font-medium">
                      {event.venueCity}, {event.venueCountry}
                    </span>
                  </div>
                )}
                {/* {event.priceRangeMin && event.priceRangeMax && (
                <div className="flex items-center space-x-1.5">
                  <div className="bg-gray-100 p-1.5 rounded-full">
                    <CircleDollarSign className="h-4 w-4 text-gray-600" />
                  </div>
                  <span className="text-gray-700 text-sm font-bold">
                    {event.currency} {event.priceRangeMin} - {event.currency}{" "}
                    {event.priceRangeMax}
                  </span>
                </div>
              )} */}
              </div>
            </CardItem>
          </CardFooter>
        </CardBody>
      </CardContainer>
    </div>
  );
};
