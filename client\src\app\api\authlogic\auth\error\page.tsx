'use client';

import { useSearchParams } from 'next/navigation';

// This component displays authentication errors.
// It has been modified to remove any direct navigation links from its content.
export default function AuthError() {
  const searchParams = useSearchParams();
  const error = searchParams.get('error');

  return (
    <div className="p-8 max-w-md mx-auto mt-8 bg-red-50 border border-red-200 rounded-lg">
      <h1 className="text-2xl font-bold text-red-600 mb-4">Authentication Error</h1>
      <p className="mb-4 text-gray-800">
        An error occurred during authentication: <strong>{error || 'Unknown error'}</strong>
      </p>
      <div className="p-4 bg-white rounded border border-gray-200">
        <h2 className="font-semibold mb-2">Debug Information:</h2>
        <pre className="text-xs bg-gray-100 p-2 overflow-auto">
          {JSON.stringify({
            error,
            url: typeof window !== 'undefined' ? window.location.href : '',
            timestamp: new Date().toISOString(),
            searchParams: Object.fromEntries(searchParams.entries()),
          }, null, 2)}
        </pre>
      </div>
    </div>
  );
}