/**
 * @description A custom hook that manages navigation items based on user roles and permissions.
 * This hook combines common navigation items with role-specific items while applying permission checks.
 *
 * @returns {Object} An object containing filtered navigation items based on user's role and permissions, and the current pathname
 */

import { usePathname } from 'next/navigation';
import { usePermissions } from '@/utils/permissions/hooks/usePermissions';
import { NavItem, NavSection, NavigationConfig } from '../types/navigation.types';
import { navigationConfig } from '../config/navigation.config';

export const useNavigation = () => {
  const pathname = usePathname();
  const { hasPermission, hasAllPermissions, userRole } = usePermissions();

  /**
   * Generates navigation items by filtering role-specific items based on user permissions.
   * @returns {NavSection[]} Array of navigation sections with filtered items
   */
  const getNavigationItems = (): NavSection[] => {
    const roleSpecificNav = navigationConfig.roleSpecific[userRole] || [];
    const defaultNav = navigationConfig.default;

    // Updated filtering logic to handle both single and multiple permissions
    const filteredNav = roleSpecificNav.map((section) => ({
      ...section,
      items: section.items.filter((item) => {
        if (item.permission) {
          return hasPermission(item.permission);
        }
        if (item.permissions) {
          return hasAllPermissions(item.permissions);
        }
        return true; // If no permission is needed, show the item
      }),
    }));

    return [...defaultNav, ...filteredNav];
  };

  return {
    navigationItems: getNavigationItems(),
    pathname,
  };
};
