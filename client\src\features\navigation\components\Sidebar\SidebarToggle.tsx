import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useAppDispatch, useAppSelector } from "@/app/redux";
import { setIsSidebarCollapsed } from "@/state";

export const SidebarToggle = () => {
  const dispatch = useAppDispatch();
  const isSidebarCollapsed = useAppSelector((state) => state.global.isSidebarCollapsed);

  const toggleSidebar = () => {
    dispatch(setIsSidebarCollapsed(!isSidebarCollapsed));
  };

  return (
    <div className="mt-auto p-4 border-t bg-gradient-to-r from-background to-accent/10">
      <Button
        className="w-full font-bold flex items-center justify-center gap-2"
        variant="ghost"
        onClick={toggleSidebar}
      >
        {isSidebarCollapsed ? (
          <>
            <ChevronRight className="h-5 w-5" />
            {!isSidebarCollapsed && <span>Expand</span>}
          </>
        ) : (
          <>
            <ChevronLeft className="h-5 w-5" />
            {!isSidebarCollapsed && <span>Collapse</span>}
          </>
        )}
      </Button>
    </div>
  );
};























// import { Button } from "@/components/ui/button";
// import { ChevronLeft, ChevronRight } from "lucide-react";
// import { useAppDispatch, useAppSelector } from "@/app/redux";
// import { setIsSidebarCollapsed } from "@/state";

// export const SidebarToggle = () => {
//   const dispatch = useAppDispatch();
//   const isSidebarCollapsed = useAppSelector((state) => state.global.isSidebarCollapsed);

//   const toggleSidebar = () => {
//     dispatch(setIsSidebarCollapsed(!isSidebarCollapsed));
//   };

//   return (
//     <div className="mt-auto p-4 border-t bg-gradient-to-r from-background to-accent/10">
//       <Button
//         className="w-full font-bold flex items-center justify-center gap-2"
//         variant="ghost"
//         onClick={toggleSidebar}
//       >
//         {isSidebarCollapsed ? (
//           <>
//             <ChevronRight className="h-5 w-5" />
//             {!isSidebarCollapsed && <span>Expand</span>}
//           </>
//         ) : (
//           <>
//             <ChevronLeft className="h-5 w-5" />
//             {!isSidebarCollapsed && <span>Collapse</span>}
//           </>
//         )}
//       </Button>
//     </div>
//   );
// };
