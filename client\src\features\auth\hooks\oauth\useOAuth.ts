import { signIn, signOut, useSession } from 'next-auth/react';
import { useState } from 'react';
import { OAuthProvider } from '../../types/oauth.types';

export const useOAuth = () => {
  const { data: session, status } = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const signInWithProvider = async (provider: OAuthProvider) => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await signIn(provider, { redirect: false });
      
      if (result?.error) {
        setError(result.error);
        return false;
      }
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Authentication failed');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    session,
    isLoading,
    error,
    isAuthenticated: status === 'authenticated',
    signInWithProvider,
    signOut
  };
};
