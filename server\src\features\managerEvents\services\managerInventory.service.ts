// This service handles fetching and modifying inventory data specific to a manager.
import { PrismaClient, ManagerEvent,Prisma } from "@prisma/client";
import ApiError from "@/utils/ApiError";

const prisma = new PrismaClient();

export class ManagerInventoryService {
  /**
   * Retrieves all ManagerEvent records associated with a given manager ID.
   * Selects fields needed for the manager inventory view AND details modal.
   * @param managerId - The ID of the manager (User ID).
   * @returns Promise<ManagerEvent[]> - A promise that resolves to an array of FULL manager events.
   * Note: Returning full object now, frontend hook/types will handle it.
   */
  static async getEventsByManagerId(managerId: string): Promise<ManagerEvent[]> {
    if (!managerId) {
        console.error('❌ getEventsByManagerId called without managerId');
        throw ApiError.badRequest("Manager ID is required to fetch inventory.");
    }
    console.log(`🔍 Fetching inventory (full details) for manager ID: ${managerId}`);
    try {
      const events = await prisma.managerEvent.findMany({
        where: {
          managerId: managerId,
        },
        orderBy: {
        },
      });
      console.log(`✅ Found ${events.length} inventory items (full details) for manager ${managerId}`);
      // Prisma automatically handles JSON parsing for the 'inventory' field here
      return events;
    } catch (error) {
        console.error(`❌ Error fetching full inventory details for manager ${managerId}:`, error);
        throw error;
    }
  }

  /**
   * ✨ New Method: Toggles the isActive status of a specific ManagerEvent. ✨
   * Ensures the event belongs to the requesting manager before updating.
   * @param eventId - The ID of the ManagerEvent to update.
   * @param managerId - The ID of the manager requesting the change.
   * @returns Promise<ManagerEvent> - The updated manager event record.
   */
  static async toggleEventActiveStatus(eventId: string, managerId: string): Promise<ManagerEvent> {
      console.log(`🔄 Attempting to toggle isActive for event ${eventId} by manager ${managerId}`);

      // 1. Find the event and verify ownership in one step
      const event = await prisma.managerEvent.findUnique({
          where: { id: eventId },
      });

      if (!event) {
          console.error(`❌ Event not found: ${eventId}`);
          throw ApiError.notFound("Event listing not found.");
      }

      if (event.managerId !== managerId) {
          console.error(`❌ Forbidden: Manager ${managerId} attempted to modify event ${eventId} owned by ${event.managerId}`);
          throw ApiError.forbidden("You do not have permission to modify this event listing.");
      }

      // 2. Toggle the isActive status
      const newIsActiveStatus = !event.isActive;

      // 3. Update the event
      const updatedEvent = await prisma.managerEvent.update({
          where: { id: eventId },
          data: { isActive: newIsActiveStatus },
      });

      console.log(`✅ Event ${eventId} isActive status toggled to ${newIsActiveStatus}`);
      return updatedEvent;
  }

    /**
   * ✨ New Method: Deletes a specific ManagerEvent. ✨
   * Ensures the event belongs to the requesting manager before deleting.
   * @param eventId - The ID of the ManagerEvent to delete.
   * @param managerId - The ID of the manager requesting the deletion.
   * @returns Promise<ManagerEvent> - The deleted manager event record.
   */
    static async deleteEventById(eventId: string, managerId: string): Promise<ManagerEvent> {
      console.log(`🗑️ Attempting to delete event ${eventId} by manager ${managerId}`);

      // 1. Find the event and verify ownership
      const event = await prisma.managerEvent.findUnique({
        where: { id: eventId },
      });

      if (!event) {
        console.error(`❌ Event not found for deletion: ${eventId}`);
        throw ApiError.notFound("Event listing not found.");
      }

      if (event.managerId !== managerId) {
        console.error(`❌ Forbidden: Manager ${managerId} attempted to delete event ${eventId} owned by ${event.managerId}`);
        throw ApiError.forbidden("You do not have permission to delete this event listing.");
      }

      // 2. Delete the event
      const deletedEvent = await prisma.managerEvent.delete({
        where: { id: eventId },
      });

      console.log(`✅ Event ${eventId} deleted successfully by manager ${managerId}`);
      return deletedEvent;
    }

    /**
   * ✨ New Method: Updates the inventory list for a specific ManagerEvent. ✨
   * Ensures the event belongs to the requesting manager before updating.
   * @param eventId - The ID of the ManagerEvent to update.
   * @param managerId - The ID of the manager requesting the change.
   * @param updatedInventory - The new inventory array. Should match frontend's InventoryDetailItem structure.
   * @returns Promise<ManagerEvent> - The updated manager event record.
   */
  static async updateInventory(eventId: string, managerId: string, updatedInventory: Prisma.InputJsonValue): Promise<ManagerEvent> {
    console.log(`💾 Attempting to update inventory for event ${eventId} by manager ${managerId}`);

    // 1. Validate input inventory (basic check)
    if (!Array.isArray(updatedInventory)) {
        console.error(`❌ Bad Request: updatedInventory is not an array for event ${eventId}`);
        throw ApiError.badRequest("Invalid inventory data format. Expected an array.");
    }

    // 2. Find the event and verify ownership
    const event = await prisma.managerEvent.findUnique({
        where: { id: eventId },
    });

    if (!event) {
        console.error(`❌ Event not found for inventory update: ${eventId}`);
        throw ApiError.notFound("Event listing not found.");
    }

    if (event.managerId !== managerId) {
        console.error(`❌ Forbidden: Manager ${managerId} attempted to update inventory for event ${eventId} owned by ${event.managerId}`);
        throw ApiError.forbidden("You do not have permission to modify this event listing.");
    }

    // 3. Update the event's inventory field
    // Prisma expects the data in a format suitable for the JSON column,
    // passing the validated array directly should work.
    const updatedEvent = await prisma.managerEvent.update({
        where: { id: eventId },
        data: {
            inventory: updatedInventory // Pass the received array directly
        },
    });

    console.log(`✅ Inventory for event ${eventId} updated successfully.`);
    return updatedEvent;
}


}
