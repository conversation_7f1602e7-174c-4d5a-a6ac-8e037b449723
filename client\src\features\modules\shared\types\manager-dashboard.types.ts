export type ManagerMetricType = {
  title: string;
  value: number | string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  prefix?: string;
};

export type EventDataType = {
  id: string;
  name: string;
  date: string;
  ticketsAvailable: number;
  ticketsSold: number;
  price: number;
};

export type InventoryDataType = {
  eventId: string;
  eventName: string;
  totalTickets: number;
  soldTickets: number;
  status: 'low' | 'medium' | 'high';
};
