import winston from 'winston';
import { NODE_ENV } from '@/constants';
import { LOGGER_CONFIG } from './config';

/**
 * Sanitize sensitive information from logs
 */
const sanitizeLogData = (data: any): any => {
  if (NODE_ENV !== 'production') return data;
  
  if (typeof data !== 'object' || data === null) return data;
  
  const sanitized = { ...data };
  
  LOGGER_CONFIG.production.sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  });
  
  return sanitized;
};

/**
 * Development formatter - colorized and detailed
 */
export const developmentFormatter = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.colorize({ all: true }),
  winston.format.printf(({ timestamp, level, message, service, ...meta }) => {
    const metaStr = Object.keys(meta).length ? `\n${JSON.stringify(meta, null, 2)}` : '';
    const serviceStr = service ? `[${service}]` : '';
    return `${timestamp} ${level} ${serviceStr}: ${message}${metaStr}`;
  })
);

/**
 * Production formatter - structured JSON
 */
export const productionFormatter = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf((info) => {
    // Sanitize sensitive data
    const sanitized = sanitizeLogData(info);
    
    // Limit log message length
    if (sanitized.message && sanitized.message.length > LOGGER_CONFIG.production.maxLogLength) {
      sanitized.message = sanitized.message.substring(0, LOGGER_CONFIG.production.maxLogLength) + '...';
    }
    
    return JSON.stringify(sanitized);
  })
);

/**
 * File formatter - clean JSON for parsing
 */
export const fileFormatter = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);