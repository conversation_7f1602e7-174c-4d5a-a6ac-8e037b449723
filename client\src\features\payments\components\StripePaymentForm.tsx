'use client';

import { useState } from 'react';
import { useStripe, useElements, PaymentElement } from '@stripe/react-stripe-js';
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { LoadingSpinner } from '@/components/shared/LoadingSpinner';
import { CreditCard, LockIcon, AlertTriangle, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StripePaymentFormProps {
  clientSecret: string;
  onSuccess: (paymentIntentId: string) => void;
  onFailure: (error: Error) => void;
  onCancel: () => void;
  returnUrl: string;
  amount?: number;
  currency?: string;
  className?: string;
}

/**
 * Get user-friendly error message based on Stripe error code
 */
const getStripeErrorMessage = (errorCode: string): string => {
  const errorMessages: Record<string, string> = {
    'card_declined': 'Your card was declined. Please try a different payment method.',
    'expired_card': 'Your card has expired. Please use a different card.',
    'incorrect_cvc': 'Your card\'s security code is incorrect.',
    'insufficient_funds': 'Your card has insufficient funds to complete this purchase.',
    'invalid_expiry_month': 'Your card\'s expiration month is invalid.',
    'invalid_expiry_year': 'Your card\'s expiration year is invalid.',
    'invalid_number': 'Your card number is invalid.',
    'incorrect_number': 'Your card number is incorrect.',
    'incomplete_number': 'Your card number is incomplete.',
    'incomplete_cvc': 'Your card\'s security code is incomplete.',
    'incomplete_expiry': 'Your card\'s expiration date is incomplete.',
    'processing_error': 'An error occurred while processing your card. Please try again.',
    'rate_limit': 'Too many requests. Please try again later.',
  };

  return errorMessages[errorCode] || 'An unexpected error occurred with your payment. Please try again.';
};

/**
 * Stripe payment form component
 * Uses Stripe Elements for secure payment collection
 */
export function StripePaymentForm({
  clientSecret,
  onSuccess,
  onFailure,
  onCancel,
  returnUrl,
  amount,
  currency = 'USD',
  className,
}: StripePaymentFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  
  const [isProcessing, setIsProcessing] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [succeeded, setSucceeded] = useState(false);
  
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!stripe || !elements) {
      // Stripe.js hasn't loaded yet
      return;
    }
    
    setIsProcessing(true);
    setErrorMessage(null);
    
    try {
      // Confirm the payment
      const result = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: returnUrl,
        },
        redirect: 'if_required',
      });
      
      if (result.error) {
        // Enhanced error message handling
        let message = result.error.message || 'An error occurred with your payment';
        
        // Check for specific error types
        if (result.error.type === 'card_error' || result.error.type === 'validation_error') {
          // If we have a specific error code, get the user-friendly message
          if (result.error.code) {
            message = getStripeErrorMessage(result.error.code);
          }
        }
        
        // Show error to customer
        setErrorMessage(message);
        onFailure(new Error(message));
      } else if (result.paymentIntent) {
        // Payment succeeded
        setSucceeded(true);
        
        // Call onSuccess with the payment intent ID
        onSuccess(result.paymentIntent.id);
      }
    } catch (err) {
      const error = err as Error;
      setErrorMessage(error.message || 'An unexpected error occurred');
      onFailure(error);
    } finally {
      setIsProcessing(false);
    }
  };
  
  const formatAmount = (amount?: number): string => {
    if (!amount) return '';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };
  
  return (
    <Card className={cn("w-full max-w-md mx-auto border border-border shadow-md", className)}>
      <CardHeader className="bg-gradient-to-r from-primary/5 to-primary/10 border-b">
        <CardTitle className="flex items-center text-2xl">
          <CreditCard className="mr-2 h-5 w-5 text-primary" />
          Secure Payment
        </CardTitle>
        {amount ? (
          <div className="text-sm text-muted-foreground flex items-center mt-1">
            Amount: <span className="font-semibold ml-1">{formatAmount(amount)}</span>
          </div>
        ) : null}
      </CardHeader>
      
      <CardContent className="pt-6">
        {errorMessage && (
          <Alert variant="destructive" className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{errorMessage}</AlertDescription>
          </Alert>
        )}
        
        {succeeded ? (
          <div className="py-8 text-center">
            <div className="mx-auto bg-green-100 rounded-full p-2 w-16 h-16 flex items-center justify-center mb-4">
              <CheckCircle className="h-10 w-10 text-green-600" />
            </div>
            <h3 className="text-xl font-medium mb-2">Payment Successful!</h3>
            <p className="text-muted-foreground mb-4">Your payment has been processed successfully.</p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="min-h-[200px]">
              <PaymentElement 
                options={{
                  layout: {
                    type: 'tabs',
                    defaultCollapsed: false,
                  }
                }}
              />
            </div>
            
            <div className="flex items-center justify-center text-sm text-muted-foreground space-x-2 mt-2 mb-4">
              <LockIcon className="h-3 w-3" />
              <span>Your payment information is secure and encrypted</span>
            </div>
          
            <div className="flex gap-4">
              <Button 
                variant="outline" 
                type="button" 
                onClick={onCancel}
                disabled={isProcessing}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={isProcessing || !stripe || !elements || succeeded}
                className="flex-1 bg-primary hover:bg-primary/90"
              >
                {isProcessing ? (
                  <>
                    <LoadingSpinner  />
                    Processing...
                  </>
                ) : (
                  <>Pay Now</>
                )}
              </Button>
            </div>
          </form>
        )}
      </CardContent>
      
      <CardFooter className="bg-muted/20 border-t px-6 py-4 flex justify-center">
        <div className="flex items-center text-xs text-muted-foreground">
          Powered by Stripe
        </div>
      </CardFooter>
    </Card>
  );
}