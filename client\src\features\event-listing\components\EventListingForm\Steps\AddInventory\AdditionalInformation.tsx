"use client";


import React, { useState, useEffect } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { additionalInformationTooltips } from "./tooltipContent";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useFormContext } from "react-hook-form";
import { InfoTooltipIcon } from "@/components/shared/InfoTooltipIcon";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";



// Default options that will always be available
const defaultDisclosuresOptions = ["18+ Section", "21+ Section"];
const defaultAttributesOptions = ["Home Side", "VIP Club"];

export const AdditionalInformation: React.FC = () => {
  const form = useFormContext();
  
  // State for new custom options text inputs
  const [newDisclosureText, setNewDisclosureText] = useState("");
  const [newAttributeText, setNewAttributeText] = useState("");
  
  // State for tracking all available options (default + custom)
  const [currentDisclosureOptions, setCurrentDisclosureOptions] = useState<string[]>(defaultDisclosuresOptions);
  const [currentAttributeOptions, setCurrentAttributeOptions] = useState<string[]>(defaultAttributesOptions);
  
  // Get current form values for disclosures and attributes
  const formDisclosures = form.watch("disclosures") || [];
  const formAttributes = form.watch("attributes") || [];
  
  // Initialize options when component mounts or when form values change (e.g., when editing an item)
  useEffect(() => {
    // Add any disclosures from form that aren't in our current options
    const customDisclosures = formDisclosures.filter(
      (d: string) => !currentDisclosureOptions.includes(d)
    );
    
    if (customDisclosures.length > 0) {
      setCurrentDisclosureOptions((prev) => [...prev, ...customDisclosures]);
    }
    
    // Add any attributes from form that aren't in our current options
    const customAttributes = formAttributes.filter(
      (a: string) => !currentAttributeOptions.includes(a)
    );
    
    if (customAttributes.length > 0) {
      setCurrentAttributeOptions((prev) => [...prev, ...customAttributes]);
    }
  }, [formDisclosures, formAttributes]);
  
  // Handler to add a new custom disclosure
  const addCustomDisclosure = () => {
    if (!newDisclosureText.trim()) return;
    
    // Check if it already exists
    if (currentDisclosureOptions.includes(newDisclosureText.trim())) {
      // Option already exists, just clear the input
      setNewDisclosureText("");
      return;
    }
    
    // Add to available options
    setCurrentDisclosureOptions((prev) => [...prev, newDisclosureText.trim()]);
    
    // Reset input
    setNewDisclosureText("");
  };
  
  // Handler to add a new custom attribute
  const addCustomAttribute = () => {
    if (!newAttributeText.trim()) return;
    
    // Check if it already exists
    if (currentAttributeOptions.includes(newAttributeText.trim())) {
      // Option already exists, just clear the input
      setNewAttributeText("");
      return;
    }
    
    // Add to available options
    setCurrentAttributeOptions((prev) => [...prev, newAttributeText.trim()]);
    
    // Reset input
    setNewAttributeText("");
  };

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold">Additional Information</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Public Note */}
        <FormField
          control={form.control}
          name="publicNote"
          render={({ field }) => (
            <FormItem>
              <div className="flex items-center">
                <FormLabel htmlFor="publicNote">Public Note</FormLabel>
                <InfoTooltipIcon
                  content={additionalInformationTooltips.publicNote || ""}
                />
              </div>
              <FormControl>
                <Textarea {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Internal Note */}
        <FormField
          control={form.control}
          name="internalNote"
          render={({ field }) => (
            <FormItem>
              <div className="flex items-center">
                <FormLabel htmlFor="internalNote">Internal Note</FormLabel>
                <InfoTooltipIcon
                  content={additionalInformationTooltips.internalNote || ""}
                />
              </div>
              <FormControl>
                <Textarea {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Disclosures */}
      <div>
        <FormLabel>Disclosures</FormLabel>
        
        {/* Add custom disclosure input */}
        <div className="mb-2 flex gap-2 w-1/2">
          <Input
            placeholder="Add custom disclosure..."
            value={newDisclosureText}
            onChange={(e) => setNewDisclosureText(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                addCustomDisclosure();
              }
            }}
            className="flex-grow"
          />
          <Button 
            type="button" 
            onClick={addCustomDisclosure}
            size="sm"
            className="flex-shrink-0"
          >
            <Plus className="h-4 w-4 mr-1" /> Add
          </Button>
        </div>
        
        <div className="flex flex-wrap gap-2">

          {currentDisclosureOptions.map((option) => (
            <FormField
              key={option}
              control={form.control}
              name="disclosures"
              render={({ field }) => {
                return (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border shadow-sm p-2">
                    <Label className="mr-2 text-sm">{option}</Label>
                    <Switch
                      checked={field.value?.includes(option)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          field.onChange([...(field.value || []), option]);
                        } else {
                          field.onChange(
                            (field.value as string[] | undefined)?.filter(
                              (v) => v !== option
                            )
                          );
                        }
                      }}
                    />
                  </FormItem>
                );
              }}
            />
          ))}
        </div>
      </div>

      {/* Attributes */}
      <div>
        <FormLabel>Attributes</FormLabel>
        
        {/* Add custom attribute input */}
        <div className="mb-2 flex gap-2 w-1/2">
          <Input
            placeholder="Add custom attribute..."
            value={newAttributeText}
            onChange={(e) => setNewAttributeText(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                addCustomAttribute();
              }
            }}
            className="flex-grow"
          />
          <Button 
            type="button" 
            onClick={addCustomAttribute}
            size="sm"
            className="flex-shrink-0"
          >
            <Plus className="h-4 w-4 mr-1" /> Add
          </Button>
        </div>
        
        <div className="flex flex-wrap gap-2">

          {currentAttributeOptions.map((option) => (
            <FormField
              key={option}
              control={form.control}
              name="attributes"
              render={({ field }) => {
                return (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border shadow-sm p-2">
                    <Label className="mr-2 text-sm">{option}</Label>
                    <Switch
                      checked={field.value?.includes(option)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          field.onChange([...(field.value || []), option]);
                        } else {
                          field.onChange(
                            (field.value as string[] | undefined)?.filter(
                              (v) => v !== option
                            )
                          );
                        }
                      }}
                    />
                  </FormItem>
                );
              }}
            />
          ))}
        </div>
      </div>

      {/* Terms and Conditions */}
      <FormField
        control={form.control}
        name="termsAndConditions"
        render={({ field }) => (
          <FormItem className="flex items-center space-x-2">
            <FormControl>
              <Checkbox
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            </FormControl>
            <div className="flex items-center">
              <FormLabel
                htmlFor="termsAndConditions"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Accept Terms and Conditions
              </FormLabel>
              <InfoTooltipIcon
                content={additionalInformationTooltips.termsAndConditions || ""}
              />
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};
