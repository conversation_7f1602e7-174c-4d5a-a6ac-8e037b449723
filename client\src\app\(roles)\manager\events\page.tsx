"use client";
import { ManagerEventManagement } from "@/features/modules/manager-portal/events/ManagerEventManagement";
import { usePermissions } from '@/utils/permissions/hooks/usePermissions';

const ManagerEventsPageClient = () => {
    const { getEventOperations } = usePermissions();
    const operations = getEventOperations()
    return <ManagerEventManagement operations={operations} />
}
export default function ManagerEventsPage() {
    return <ManagerEventsPageClient />;
}
