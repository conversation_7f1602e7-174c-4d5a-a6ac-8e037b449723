// Displays a badge indicating the listing status (e.g., LISTED, SOLD) and activity status.
import React from 'react';
import { Badge } from "@/components/ui/badge";
import { EventStatusBadgeProps } from '../../types/inventory.types';
import { cn } from "@/lib/utils";

// Define possible listing statuses and their base styles
const statusConfig: Record<string, { text: string; className: string }> = {
  LISTED: { text: 'Listed', className: 'bg-blue-100 text-blue-800 border-blue-300 hover:bg-blue-200' },
  SOLD: { text: 'Sold', className: 'bg-purple-100 text-purple-800 border-purple-300 hover:bg-purple-200' },
  EXPIRED: { text: 'Expired', className: 'bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200' },
  // Add more statuses as needed
};

export const EventStatusBadge: React.FC<EventStatusBadgeProps> = ({ status, isActive }) => {
  const config = statusConfig[status.toUpperCase()] || { text: status, className: 'bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200' };

  return (
    <Badge
      variant="outline"
      className={cn(
        "font-medium capitalize",
        config.className,
        !isActive && "opacity-60 line-through" // Dim and strike-through if inactive
      )}
      title={!isActive ? `${config.text} (Inactive)` : config.text} // Add tooltip for inactive status
    >
      {config.text}
    </Badge>
  );
};
