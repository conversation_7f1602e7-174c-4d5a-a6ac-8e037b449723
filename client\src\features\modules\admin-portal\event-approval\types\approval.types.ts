// Import existing inventory types from event listing
import { InventoryItem } from "@/features/event-listing/types/eventListing";

// Event Categories matching your schema but as type
export type EventCategory = 'SPORTS' | 'MUSIC' | 'ARTS' | 'THEATER';

// Approval Status matching your schema
export type ApprovalStatus = 'PENDING' | 'APPROVED' | 'REJECTED';

// Manager information within the approval context
export interface ManagerInfo {
  id: string;
  email: string;
  fullName: string | null;
}

// Main interface for manager events in approval context
export interface PendingEventApproval {
  id: string;
  managerId: string;
  eventId: string;
  name: string;
  category: EventCategory;
  source: string;
  date: Date;
  venue: string;
  city: string;
  country: string;
  image?: string | null;
  
  // Inventory details
  inventory: InventoryItem[];
  
  // Approval specific fields
  approvalStatus: ApprovalStatus;
  approvedBy?: string | null;
  approvedAt?: Date | null;
  approvalNotes?: string | null;
  
  // Include manager information
  manager: ManagerInfo;
}

// Response type for the API
export interface ApprovalResponse {
  success: boolean;
  message: string;
  data: PendingEventApproval[];
}

// Type for approval action payload
export interface ApprovalActionPayload {
  eventId: string;
  status: ApprovalStatus;
  notes?: string;
}

// Add these interfaces at the end of the file
export interface PaginationParams {
  page?: number;
  limit?: number;
}

export interface PaginationInfo {
  page: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
}

// Update ApprovalResponse to include pagination
export interface PaginatedApprovalResponse {
  success: boolean;
  message: string;
  data: PendingEventApproval[];
  pagination: PaginationInfo;
}

// Add this interface for ApprovalCard props
export interface ApprovalCardProps {
  approval: PendingEventApproval;
  status: ApprovalStatus; // Add status prop
  onApprove: (id: string) => void;
  onReject: (id: string) => void;
  openModal: (event: PendingEventApproval) => void;
}
