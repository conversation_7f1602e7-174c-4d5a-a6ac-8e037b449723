// client/src/features/sales/components/SalesDataTable.tsx
"use client"

import React, { useState, useMemo } from 'react'
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  Row,
  Table as TanstackTableType,
  useReactTable,
  VisibilityState,
  SortingState,
} from "@tanstack/react-table"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { DateRange } from "react-day-picker";
import { Skeleton } from "@/components/ui/skeleton";
import {
  ChevronLeft,
  ChevronRight,
  Search,
  PackageOpen,
  Info,
  BarChart3,
  AlertCircle,
  CalendarIcon,
  ArrowUpDown,
  FilterIcon,
  SlidersHorizontal
} from "lucide-react"
import { 
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from "@/components/ui/collapsible";
import { Separator } from "@/components/ui/separator";
import { formatCurrency, formatDate } from "@/utils/format";

import { useSalesOverview } from '../hooks/useSalesOverview'
import { columns, EventSalesColumn } from './SalesDataTable.columns'
import { PurchaseAccordion } from './PurchaseAccordion'
import { ManagerEventSalesDetailsDTO, CheckoutSessionDetailDTO } from '../types/sales.types';

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  renderSubComponent: (props: { row: Row<TData> }) => React.ReactElement
  getRowCanExpand: (row: Row<TData>) => boolean
  table: TanstackTableType<TData>
}

function DataTableInner<TData, TValue>({
  columns,
  data,
  renderSubComponent,
  getRowCanExpand,
  table,
}: DataTableProps<TData, TValue>) {
  return (
    <div className="rounded-md border w-full">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  // Add responsive classes based on column ID
                  let className = "px-4";
                  
                  // Hide less important columns on small screens
                  if (["eventVenue", "sessionsCount"].includes(header.id)) {
                    className += " hidden md:table-cell";
                  }
                  if (["eventDate", "totalTicketsSoldThisEvent"].includes(header.id)) {
                    className += " hidden sm:table-cell";
                  }
                  
                  return (
                    <TableHead key={header.id} className={className}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <React.Fragment key={row.id}>
                  <TableRow data-state={row.getIsSelected() && "selected"} className="group hover:bg-muted/50">
                    {row.getVisibleCells().map((cell) => {
                      // Add responsive classes based on column ID
                      let className = "px-4";
                      
                      // Hide less important columns on small screens
                      if (["eventVenue", "sessionsCount"].includes(cell.column.id)) {
                        className += " hidden md:table-cell";
                      }
                      if (["eventDate", "totalTicketsSoldThisEvent"].includes(cell.column.id)) {
                        className += " hidden sm:table-cell";
                      }
                      
                      return (
                        <TableCell key={cell.id} className={className}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      )
                    })}
                  </TableRow>
                  {row.getIsExpanded() && (
                    <TableRow className="bg-muted/30">
                      <TableCell colSpan={row.getVisibleCells().length} className="p-0">
                        {renderSubComponent({ row })}
                      </TableCell>
                    </TableRow>
                  )}
                </React.Fragment>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}


export function SalesDataTable() {
  const {
    eventsWithSales,
    pagination,
    isLoading,
    isError,
    error,
    goToPage,
    updateFilterOptions,
    filterOptions: hookFilterOptions,
    refetch,
  } = useSalesOverview()

  const [globalFilter, setGlobalFilter] = useState("")
  const [sorting, setSorting] = useState<SortingState>([
    { id: hookFilterOptions.sortBy, desc: hookFilterOptions.sortOrder === 'desc' }
  ])
  const [dateRange, setDateRange] = useState<DateRange | undefined>(
    hookFilterOptions.dateRange ? { from: hookFilterOptions.dateRange.start || undefined, to: hookFilterOptions.dateRange.end || undefined } : undefined
  );
  
  // State to control the filters section on mobile
  const [filtersOpen, setFiltersOpen] = useState(false);
  
  // Memoize columns to prevent re-renders
  const tableColumns = useMemo(() => columns, []);

  const table = useReactTable({
    data: eventsWithSales as EventSalesColumn[],
    columns: tableColumns,
    state: {
      sorting,
      globalFilter,
    },
    onSortingChange: (updater) => {
      const newSorting = typeof updater === 'function' ? updater(sorting) : updater;
      setSorting(newSorting);
      if (newSorting.length > 0) {
          const sortInfo = newSorting[0];
          updateFilterOptions({ 
              sortBy: sortInfo.id as any, 
              sortOrder: sortInfo.desc ? 'desc' : 'asc' 
          });
      } else {
          updateFilterOptions({ sortBy: 'eventDate', sortOrder: 'desc' });
      }
    },
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    getRowCanExpand: (row) => (row.original as ManagerEventSalesDetailsDTO).sessions?.length > 0,
    manualPagination: true,
    manualSorting: true,
    manualFiltering: true,
    pageCount: pagination.totalPages,
  })

  const handleMessagesClick = (sessionId: string) => {
    console.log("💬 Navigate to messages for session:", sessionId);
  };

  const renderSubComponent = ({ row }: { row: Row<EventSalesColumn> }) => {
    const event = row.original as ManagerEventSalesDetailsDTO;
    if (!event.sessions || event.sessions.length === 0) {
      return (
        <div className="p-4 text-sm text-center text-gray-500 bg-slate-50">
          No purchases found for this event.
        </div>
      );
    }
    return (
      <div className="bg-muted/30 p-2 space-y-2">
        <h4 className="px-3 pt-2 text-sm font-semibold text-foreground/80">Purchases for {event.eventName}</h4>
        <div className="bg-green-50 dark:bg-green-900/20 p-3 mx-1 rounded-md">
          <p className="text-sm text-green-800 dark:text-green-300 font-medium">Your Total Payout (All Purchases)</p>
          <p className="text-xl font-bold text-green-700 dark:text-green-400">
            {formatCurrency(
              event.sessions?.reduce((total, session) => total + (session.managerPayout || 0), 0),
              event.currency
            )}
          </p>
          <p className="text-xs text-muted-foreground mt-1">Paid after event completion and visitor satisfaction</p>
        </div>
        <div className="space-y-2 px-1">
          {event.sessions.map((session: CheckoutSessionDetailDTO) => (
            <PurchaseAccordion
              key={session.sessionId}
              session={session}
              eventId={event.eventId}
              onMessagesClick={handleMessagesClick}
            />
          ))}
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <Card className="w-full shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl font-bold flex items-center">
            <BarChart3 className="mr-3 h-6 w-6 text-primary" /> Detailed Sales Overview
          </CardTitle>
          <CardDescription>
            Loading your detailed event sales data and individual purchases...
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Skeleton className="h-12 w-full mb-4" />
          {[...Array(3)].map((_, i) => (
            <div key={i} className="space-y-1">
              <Skeleton className="h-10 w-full" />
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  if (isError) {
    return (
      <Card className="w-full shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-red-600 flex items-center">
            <AlertCircle className="mr-3 h-6 w-6" /> Error Loading Sales Data
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center py-10">
          <p className="text-red-700 mb-3">
            We encountered a problem retrieving your sales details: {error?.message || "Unknown error"}
          </p>
          <Button onClick={() => refetch()} variant="outline">
            <ArrowUpDown className="mr-2 h-4 w-4" /> Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }
  
  if (eventsWithSales.length === 0 && !isLoading) {
     return (
      <Card className="w-full shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl font-bold flex items-center">
            <BarChart3 className="mr-3 h-6 w-6 text-primary" /> Detailed Sales Overview
          </CardTitle>
          <CardDescription>
            Sales summary for your managed events including individual purchases.
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center py-16">
          <PackageOpen className="h-16 w-16 mx-auto text-gray-400 mb-4" />
          <h3 className="text-xl font-semibold mb-2">No Sales Data Yet</h3>
          <p className="text-gray-600">
            Once tickets are sold for your events, you&apos;ll see detailed purchase information here.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="shadow-md">
        <CardHeader>
          <CardTitle className="text-2xl font-bold flex items-center">
            <BarChart3 className="mr-3 h-6 w-6 text-primary" /> Detailed Sales Overview
          </CardTitle>
          <CardDescription>
            Manage sales for your events. Expand rows to view individual purchases and upload tickets.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Mobile Collapsible Filters */}
          <Collapsible 
            open={filtersOpen} 
            onOpenChange={setFiltersOpen}
            className="md:hidden mb-4 border rounded-lg"
          >
            <CollapsibleTrigger asChild>
              <Button variant="outline" className="w-full flex justify-between">
                <span className="flex items-center">
                  <SlidersHorizontal className="mr-2 h-4 w-4" />
                  Filter & Search Options
                </span>
                <FilterIcon className="h-4 w-4" />
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="px-4 pt-2 pb-4 space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search displayed events..."
                  value={globalFilter}
                  onChange={(e) => setGlobalFilter(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start text-left font-normal">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateRange?.from ? (
                      dateRange.to ? 
                        `${formatDate(dateRange.from.toISOString())} - ${formatDate(dateRange.to.toISOString())}` 
                        : formatDate(dateRange.from.toISOString())
                    ) : (
                      <span>Event Date Range</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="center">
                  <Calendar 
                    initialFocus 
                    mode="range" 
                    defaultMonth={dateRange?.from} 
                    selected={dateRange} 
                    onSelect={(range) => {
                        setDateRange(range);
                        updateFilterOptions({ dateRange: range ? { start: range.from || null, end: range.to || null } : undefined });
                    }}
                    numberOfMonths={1} 
                  />
                </PopoverContent>
              </Popover>
              
              <Button 
                onClick={() => {
                    setGlobalFilter("");
                    setDateRange(undefined);
                    updateFilterOptions({ sortBy: 'eventDate', sortOrder: 'desc', dateRange: undefined });
                    table.resetSorting();
                }} 
                variant="secondary" 
                className="w-full"
                disabled={!globalFilter && !dateRange && hookFilterOptions.sortBy === 'eventDate' && hookFilterOptions.sortOrder === 'desc'}
                >Clear Filters
              </Button>
            </CollapsibleContent>
          </Collapsible>
          
          {/* Desktop Filters */}
          <div className="hidden md:flex md:flex-row justify-between items-center mb-4 gap-4 p-4 border rounded-lg bg-slate-50/70 dark:bg-slate-900/30">
            <div className="flex-1 w-full relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search displayed events..."
                value={globalFilter}
                onChange={(e) => setGlobalFilter(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex flex-row gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="whitespace-nowrap justify-start text-left font-normal">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateRange?.from ? (
                      dateRange.to ? 
                        `${formatDate(dateRange.from.toISOString())} - ${formatDate(dateRange.to.toISOString())}` 
                        : formatDate(dateRange.from.toISOString())
                    ) : (
                      <span>Event Date Range</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="end">
                  <Calendar 
                    initialFocus 
                    mode="range" 
                    defaultMonth={dateRange?.from} 
                    selected={dateRange} 
                    onSelect={(range) => {
                        setDateRange(range);
                        updateFilterOptions({ dateRange: range ? { start: range.from || null, end: range.to || null } : undefined });
                    }}
                    numberOfMonths={2} 
                  />
                </PopoverContent>
              </Popover>
              <Button 
                onClick={() => {
                    setGlobalFilter("");
                    setDateRange(undefined);
                    updateFilterOptions({ sortBy: 'eventDate', sortOrder: 'desc', dateRange: undefined });
                    table.resetSorting();
                }} 
                variant="ghost" 
                className="whitespace-nowrap"
                disabled={!globalFilter && !dateRange && hookFilterOptions.sortBy === 'eventDate' && hookFilterOptions.sortOrder === 'desc'}
                >
                Clear Filters
              </Button>
            </div>
          </div>
          
          {/* Data Table - Improved container for better responsive behavior */}
          <div className="w-full overflow-hidden">
            <DataTableInner
              columns={tableColumns}
              data={eventsWithSales as EventSalesColumn[]}
              renderSubComponent={renderSubComponent}
              getRowCanExpand={(row) => (row.original as ManagerEventSalesDetailsDTO).sessions?.length > 0}
              table={table}
            />
          </div>
          
          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6">
              <div className="text-sm text-muted-foreground">
                Showing page {pagination.currentPage} of {pagination.totalPages}
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => goToPage(pagination.currentPage - 1)}
                  disabled={!pagination.hasPrevPage}
                  className="h-8 w-8 p-0 sm:h-9 sm:w-9 sm:p-0"
                >
                  <span className="sr-only">Go to previous page</span>
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <div className="text-sm font-medium hidden sm:block">
                  Page {pagination.currentPage} of {pagination.totalPages}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => goToPage(pagination.currentPage + 1)}
                  disabled={!pagination.hasNextPage}
                  className="h-8 w-8 p-0 sm:h-9 sm:w-9 sm:p-0"
                >
                  <span className="sr-only">Go to next page</span>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
