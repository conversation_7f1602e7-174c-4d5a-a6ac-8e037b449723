// Handles shared Stripe initialization and customer management.
import <PERSON><PERSON> from "stripe";
import ApiError from "@/utils/ApiError";
import { prisma } from "@/lib/prisma"; // Centralized Prisma client

export class StripeBaseService {
    protected static stripe: Stripe; // Changed to protected for potential inheritance if needed

    /**
     * Initializes the Stripe instance.
     */
    protected static initialize(): void {
        if (!this.stripe) {
            if (!process.env.STRIPE_SECRET_KEY) {
                console.error("❌ STRIPE_SECRET_KEY is not defined.");
                throw new ApiError(500, "Payment config error: Missing Stripe key.");
            }
            this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
                apiVersion: "2025-03-31.basil",
                typescript: true,
            });
            console.log("💳 Stripe Base Service Initialized");
        }
    }

    /**
     * Gets or creates a Stripe customer ID for a user.
     * @param userId - The application's user ID.
     * @param email - The user's email.
     * @returns The Stripe Customer ID.
     */
    static async getOrCreateStripeCustomer(userId: string, email: string): Promise<string> {
        this.initialize(); // Ensure Stripe is ready
        try {
            const user = await prisma.user.findUnique({
                where: { id: userId },
                select: { stripeCustomerId: true },
            });
            if (!user) throw new ApiError(404, `User not found: ${userId}`);
            if (user.stripeCustomerId) {
                console.log(`✅ Using existing Stripe customer ${user.stripeCustomerId} for user ${userId}`);
                return user.stripeCustomerId;
            }

            console.log(`🆕 Creating new Stripe customer for user ${userId}`);
            const customer = await this.stripe.customers.create({
                email,
                metadata: { userId }, // Link Stripe customer to app user
            });

            await prisma.user.update({
                where: { id: userId },
                data: { stripeCustomerId: customer.id },
            });
            console.log(`✅ Created Stripe customer ${customer.id} for user ${userId}`);
            return customer.id;
        } catch (error) {
            console.error(`❌ Error getting/creating Stripe customer for ${userId}:`, error);
            if (error instanceof ApiError) throw error;
            throw new ApiError(500, `Stripe customer error: ${error instanceof Error ? error.message : "Unknown"}`);
        }
    }

     // Method to expose the initialized stripe client if needed by other services securely
     static getStripeClient(): Stripe {
        this.initialize();
        return this.stripe;
    }
}