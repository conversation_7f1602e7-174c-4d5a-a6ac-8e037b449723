// API route for handling search requests
import express from 'express';
import { SearchController } from '@/features/search/controllers/searchController';
import { authMiddleware } from '@/middleware/auth.middleware';

const router = express.Router();

// Updated: Removed parentheses from authMiddleware as it's likely a middleware function, not a function call
//! authMiddleware has issues not working  XXXXXX
// router.get('/search', authMiddleware, SearchController.search);
router.get('/search', SearchController.search);

export default router;
