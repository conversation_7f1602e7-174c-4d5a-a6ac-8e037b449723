import { CheckoutSessionStatus, Prisma } from "@prisma/client";
import { CheckoutItem, CheckoutItemRequest, CreateCheckoutSessionRequest } from "../types/checkout.types";
import ApiError from "@/utils/ApiError";
import { toPrism<PERSON><PERSON><PERSON> } from "@/utils/prismaHelpers";
import { prisma } from "@/lib/prisma";
import { CheckoutSession } from '@prisma/client';


// Configuration constants
const RESERVATION_EXPIRES_MINUTES = 2; // Session expiration time in minutes 
const SERVICE_FEE_PERCENTAGE = 0.10; // 10% service fee - Updated to 10%
const SERVICE_FEE_MINIMUM = 7.0; // Minimum service fee in USD - Updated to $7.0
const TAX_RATE = 0.03;              // 3% Tax Rate - Adjust as needed

// First, let's add a new interface to represent the billing address input parameter
interface CreateReservationWithBillingRequest {
  userId: string;
  eventId: string;
  requestedItems: CheckoutItemRequest[];
  billingAddressId?: string; // Optional - user may not provide a billing address
  clientInfo?: { ipAddress?: string; userAgent?: string };
}

export class CheckoutService {
  /**
   * Create a reservation for a checkout session
   */
  static async createReservation(
    userId: string,
    eventId: string,
    requestedItems: CheckoutItemRequest[],
    clientInfo?: { ipAddress?: string; userAgent?: string },
    billingAddressId?: string // Added as optional parameter at the end to maintain compatibility
  ) {
    try {
      console.log("🔍 Finding event:", eventId);

      // Step 1: Fetch the event to get inventory data
      const event = await prisma.managerEvent.findFirst({
        where: {
          id: eventId,
          isActive: true,
        },
      });

      if (!event) {
        console.log("❌ Event not found or inactive:", eventId);
        return {
          success: false,
          message: "Event not found or is not active",
          error: "EVENT_NOT_FOUND",
        };
      }

      console.log("✅☠️☠️💰☠️☠️🌻 Found event:", event.name);

      // Step 2: Parse the inventory JSON
      const inventory = event.inventory as any[]; // Cast to any[] for easier access initially

      if (!Array.isArray(inventory)) {
        console.log("❌ Invalid inventory format for event:", eventId);
        return {
          success: false,
          message: "Invalid inventory format",
          error: "INVALID_INVENTORY",
        };
      }

      console.log(
        `📦 Processing ${requestedItems.length} requested items against ${inventory.length} inventory items`
      );

      // Step 3: Check availability and build checkout items
      const availableItems: CheckoutItem[] = []; // Use the Backend CheckoutItem type
      const unavailableItems: CheckoutItemRequest[] = [];

      for (const requestedItem of requestedItems) {
        // Skip items with quantity <= 0
        if (requestedItem.quantity <= 0) {
          console.log(
            `⏭️ Skipping item with zero or negative quantity: ${requestedItem.inventoryId}`
          );
          continue;
        }

        // Find the matching inventory item from the event's inventory
        const inventoryItem = inventory.find(
          (item) => item.id === requestedItem.inventoryId
        );

        if (!inventoryItem) {
          console.log(
            `❌ Inventory item not found in event's inventory: ${requestedItem.inventoryId}`
          );
          unavailableItems.push(requestedItem);
          continue;
        }

        // --- Extract details from the found inventory item ---
        // *** Use 'listPrice' from the JSON structure ***
        const itemPrice = inventoryItem.listPrice || 0;
        const itemSection = inventoryItem.section || undefined;
        const itemRow = inventoryItem.row || undefined;
        const itemName = `Ticket Section ${itemSection || 'N/A'}, Row ${itemRow || 'N/A'}`; // Construct a name
        const itemAttributes = inventoryItem.attributes || [];
        const itemTicketFormat = inventoryItem.ticketFormat || undefined;
        // Add other details you want to store (e.g., disclosures)
        // const itemDisclosures = inventoryItem.disclosures || [];

        console.log(`🔍 Found inventory item details for ${inventoryItem.id}: Price=${itemPrice}, AvailableQty=${inventoryItem.quantity}`);


        // Check if requested quantity is available
        if (
          !inventoryItem.quantity || // Check if quantity exists and is > 0
          inventoryItem.quantity < requestedItem.quantity
        ) {
          console.log(
            `⚠️ Partial or no availability for: ${inventoryItem.id}. Available: ${inventoryItem.quantity}, Requested: ${requestedItem.quantity}`
          );

          // If some quantity is available but not all, add what's available
          if (inventoryItem.quantity > 0) {
            const quantityAvailable = inventoryItem.quantity;
            console.log(
              `➕ Adding partial quantity: ${quantityAvailable} of ${requestedItem.quantity}`
            );
            availableItems.push({










              inventoryId: inventoryItem.id,
              quantity: quantityAvailable, // Use available quantity
              name: itemName,
              price: itemPrice, // Price per ticket
              subtotal: itemPrice * quantityAvailable, // Subtotal for available quantity
              section: itemSection,
              row: itemRow,
              seat: inventoryItem.seat || "", // Add seat property (empty string if not present)
              attributes: itemAttributes,
              ticketFormat: itemTicketFormat,
              // disclosures: itemDisclosures, // Optional
            });
          }

          // Add to unavailable items (either full requested quantity or remainder)
          const unavailableQty =
            requestedItem.quantity - (inventoryItem.quantity || 0);
          if (unavailableQty > 0) {
            console.log(`➖ Adding to unavailable: ${unavailableQty} units`);
            unavailableItems.push({
              inventoryId: requestedItem.inventoryId,
              quantity: unavailableQty,
            });
          }

          continue; // Go to next requested item
        }

        // Item is fully available
        console.log(
          `✅ Item fully available: ${inventoryItem.id} x ${requestedItem.quantity}`
        );
        const quantityReserved = requestedItem.quantity;
        availableItems.push({








          inventoryId: inventoryItem.id,
          quantity: quantityReserved, // Use requested quantity
          name: itemName,
          price: itemPrice, // Price per ticket
          subtotal: itemPrice * quantityReserved, // Subtotal for requested quantity
          section: itemSection,
          row: itemRow,
          attributes: itemAttributes,
          ticketFormat: itemTicketFormat,
          seat: inventoryItem.seat || "", // Add seat property (empty string if not present)
          // disclosures: itemDisclosures, // Optional
        });
      } // End of loop for requestedItems

      // Step 4: Check if we have any available items reserved
      if (availableItems.length === 0) {
        console.log("❌ No available items could be reserved from the request.");
        return {
          success: false,
          message: "No tickets available for the selected items at this time.",
          error: "NO_AVAILABLE_TICKETS",
          data: { unavailableItems }, // Still return which items were unavailable
        };
      }


      // Step 5: Calculate totals for the session (Order-level fees)
      const subtotal = availableItems.reduce(

        (sum, item) => sum + item.subtotal, // Sum the item subtotals (price * qty)
        0
      );
      console.log(`💲 Subtotal calculated based on available items: ${subtotal.toFixed(2)}`);




      // Calculate service fee based on the whole subtotal
      const calculatedServiceFee = subtotal * SERVICE_FEE_PERCENTAGE; // Uses 10%
      const serviceFee = Math.max(calculatedServiceFee, SERVICE_FEE_MINIMUM); // Uses min $7
      console.log(`💲 Service fee calculated (10% or min $7): ${serviceFee.toFixed(2)}`);

     
      const tax = subtotal * TAX_RATE; 
      console.log(`💲 Tax calculated (3% of subtotal): ${tax.toFixed(2)}`);

      const total = subtotal + serviceFee + tax; 
      console.log(`💲 Total calculated: ${total.toFixed(2)} (Subtotal + ServiceFee + Tax)`);

      // New step: Fetch billing address if provided
      let billingAddressData = null;
      if (billingAddressId) {
        console.log(`🏠 Fetching billing address: ${billingAddressId} for user: ${userId}`);
        const billingAddress = await prisma.billingAddress.findFirst({
          where: {
            id: billingAddressId,
            userId: userId, // Ensure the address belongs to this user
          },
        });

        if (billingAddress) {
          // Transform to a plain object to store as JSON
          billingAddressData = {
            id: billingAddress.id,
            addressLine1: billingAddress.addressLine1,
            addressLine2: billingAddress.addressLine2,
            city: billingAddress.city,
            state: billingAddress.state,
            postalCode: billingAddress.postalCode,
            country: billingAddress.country,
            name: billingAddress.name,      
            email: billingAddress.email,    
          };
          console.log(`✅ Billing address found and will be stored with session`);
        } else {
          console.log(`⚠️ Billing address not found or doesn't belong to user: ${billingAddressId}`);
          // Continue without billing address rather than failing
        }
      }

      // Step 6: Create the checkout session in the database
      const expiresAt = new Date();
      expiresAt.setMinutes(
        expiresAt.getMinutes() + RESERVATION_EXPIRES_MINUTES
      );
      console.log(`⏱️ Setting expiration: ${expiresAt.toISOString()}`);

      console.log("💾 Creating checkout session in database");
      const session = await prisma.checkoutSession.create({
        data: {
          userId,
          eventId,
          status: CheckoutSessionStatus.RESERVED,
          items: toPrismaJson(availableItems), // Use the detailed availableItems array
          subtotal,                           // Use the recalculated subtotal
          serviceFee,                         // Use the calculated serviceFee
          tax,                                // MODIFY: Include tax value (not null anymore)
          total,                              // Use the recalculated total (now includes tax)
          currency: "USD",
          expiresAt,
          ipAddress: clientInfo?.ipAddress || "",
          userAgent: clientInfo?.userAgent || "",
          // Add billing address data
          billingAddress: billingAddressData ? toPrismaJson(billingAddressData) : Prisma.JsonNull,
          // Ensure couponDiscount and appliedPoints are null initially
          couponDiscount: Prisma.JsonNull,
          appliedPoints: Prisma.JsonNull,
        },
      });
      console.log(`✅ Session created with ID: ${session.id}`);

      // Step 7: Return the session and any unavailable items
      return {
        success: true,
        message: "Reservation created successfully",
        data: {
          session, // Return the full session object created
          unavailableItems:
            unavailableItems.length > 0 ? unavailableItems : undefined,
        },
      };
    } catch (error) {
      console.error("💥 Error creating reservation:", error);

      // Handle Prisma specific errors
      if (
        typeof error === "object" &&
        error !== null &&
        "code" in error &&
        (error as any).code === "P2002"
      ) {
        return {
          success: false,
          message: "Constraint violation while creating reservation",
          error: "DATABASE_CONSTRAINT_ERROR",
        };
      }

      return {
        success: false,
        message: "Failed to create reservation",
        error: error instanceof Error ? error.message : "UNKNOWN_ERROR",
      };
    }
  }

  /**
   * Get a checkout session by ID
   */
  static async getSession(sessionId: string) {
    try {
      console.log(`🔍 Looking up session: ${sessionId}`);

      const session = await prisma.checkoutSession.findUnique({
        where: { id: sessionId },
      });

      if (!session) {
        console.log(`❌ Session not found: ${sessionId}`);
        return {
          success: false,
          message: "Checkout session not found",
          error: "SESSION_NOT_FOUND",
        };
      }

      // Fetch event details to enhance the session response
      console.log(`🔍 Fetching event details for eventId: ${session.eventId}`);
      const event = await prisma.managerEvent.findFirst({
        where: { id: session.eventId },
      });

      // Create an enhanced session with event details
      const enhancedSession = {
        ...session,
        // Add event details if found, otherwise use placeholders
        eventName: event?.name ,
        eventDate: event?.date ,
        eventVenue: event?.venue ,
        eventCity: event?.city ,
        eventCountry: event?.country ,
        eventImage: event?.image || null,
      };

      console.log(`✅ Session found and enhanced with event details: ${sessionId}`);
      return {
        success: true,
        data: enhancedSession,
      };
    } catch (error) {
      console.error("💥 Error fetching session:", error);

      return {
        success: false,
        message: "Failed to fetch checkout session",
        error: error instanceof Error ? error.message : "UNKNOWN_ERROR",
      };
    }
  }

  /**
   * Extend a checkout session (refresh its expiration time)
   */
  static async refreshSession(sessionId: string) {
    try {
      console.log(`🔍 Finding session to refresh: ${sessionId}`);

      // Find the session
      const session = await prisma.checkoutSession.findUnique({
        where: { id: sessionId },
      });

      if (!session) {
        console.log(`❌ Session not found: ${sessionId}`);
        return {
          success: false,
          message: "Checkout session not found",
          error: "SESSION_NOT_FOUND",
        };
      }

      // Check if session is in a valid state to refresh
      if (session.status !== CheckoutSessionStatus.RESERVED) {
        console.log(`❌ Cannot refresh session with status: ${session.status}`);
        return {
          success: false,
          message: `Cannot refresh session with status ${session.status}`,
          error: "INVALID_SESSION_STATUS",
        };
      }

      // Extend the expiration time
      const expiresAt = new Date();
      expiresAt.setMinutes(
        expiresAt.getMinutes() + RESERVATION_EXPIRES_MINUTES
      );
      console.log(`⏱️ Setting new expiration: ${expiresAt.toISOString()}`);

      // Update the session
      console.log(`📝 Updating session: ${sessionId}`);
      const updatedSession = await prisma.checkoutSession.update({
        where: { id: sessionId },
        data: {
          expiresAt,
          updatedAt: new Date(),
        },
      });
      console.log(`✅ Session refreshed successfully: ${sessionId}`);

      return {
        success: true,
        message: "Session refreshed successfully",
        data: {
          session: {
            id: updatedSession.id,
            expiresAt: updatedSession.expiresAt,
          },
        },
      };
    } catch (error) {
      console.error("💥 Error refreshing session:", error);
      return {
        success: false,
        message: "Failed to refresh checkout session",
        error: error instanceof Error ? error.message : "UNKNOWN_ERROR",
      };
    }
  }

  /**
   * Update a checkout session status
   * 
   * @param sessionId - The ID of the checkout session
   * @param status - The new status to set
   * @param userId - The ID of the authenticated user
   * @returns Result of the update operation
   */
  static async updateSessionStatus(
    sessionId: string,
    status: CheckoutSessionStatus,
    userId: string
  ) {
    try {
      console.log(`🔍 Finding session to update status: ${sessionId}`);

      // Step 1: Find the session
      const session = await prisma.checkoutSession.findUnique({
        where: { id: sessionId },
      });

      if (!session) {
        console.log(`❌ Session not found: ${sessionId}`);
        return {
          success: false,
          message: "Checkout session not found",
          error: "SESSION_NOT_FOUND",
        };
      }

      // Verify the session belongs to the user
      if (session.userId !== userId) {
        console.log(`❌ Session belongs to different user: ${sessionId}`);
        return {
          success: false,
          message: "You don't have permission for this session",
          error: "UNAUTHORIZED",
        };
      }

      // Step 2: Check if status transition is valid
      // Typically, we only want to allow specific transitions
      // For example, prevent changing COMPLETED to EXPIRED
      if (session.status === CheckoutSessionStatus.COMPLETED) {
        console.log(`❌ Cannot change COMPLETED session status: ${sessionId}`);
        return {
          success: false,
          message: "Cannot change status of a COMPLETED session",
          error: "INVALID_STATUS_TRANSITION",
        };
      }

      // Add additional status transition validation if needed
      // For example, prevent changing CANCELLED to RESERVED
      if (
        (session.status === CheckoutSessionStatus.CANCELLED && status === CheckoutSessionStatus.RESERVED) ||
        (session.status === CheckoutSessionStatus.EXPIRED && status === CheckoutSessionStatus.RESERVED)
      ) {
        console.log(`❌ Invalid status transition from ${session.status} to ${status}`);
        return {
          success: false,
          message: `Cannot change status from ${session.status} to ${status}`,
          error: "INVALID_STATUS_TRANSITION",
        };
      }

      // Step 3: Update the session status
      console.log(`📝 Updating session status for: ${sessionId}`);
      
      const updatedSession = await prisma.checkoutSession.update({
        where: { id: sessionId },
        data: {
          status: status,
          updatedAt: new Date(),
        },
      });
      
      console.log(`✅ Session status updated to ${status} for: ${sessionId}`);

      return {
        success: true,
        message: `Session status updated to ${status}`,
        data: {
          session: {
            id: updatedSession.id,
            status: updatedSession.status,
          },
        },
      };
    } catch (error) {
      console.error("💥 Error updating session status:", error);

      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        // Handle specific Prisma errors if needed
        return {
          success: false,
          message: `Database error: ${error.message}`,
          error: `PRISMA_ERROR_${error.code}`,
        };
      }
      
      return {
        success: false,
        message: "Failed to update session status",
        error: error instanceof Error ? error.message : "UNKNOWN_ERROR",
      };
    }
  }

  /**
   * Create a checkout session with the provided information
   * @param userId User ID creating the session
   * @param payload Session creation payload with items, event, and optional billing address
   * @returns The created session
   */
  static async createCheckoutSession(
    userId: string,
    payload: CreateCheckoutSessionRequest
  ): Promise<CheckoutSession> {
    try {
      console.log(`🛒 [CheckoutService] Creating checkout session for user ${userId}, event ${payload.eventId}`);
      
      const { eventId, items, billingAddressId, couponCode, pointsToApply } = payload;
      
      // Get client IP and user agent if needed
      const clientInfo = {
        ipAddress: '', // These would come from the request in the controller
        userAgent: ''  // But we're getting the payload directly here
      };
      
      // Corrected: Call createReservation with positional arguments
      const result = await this.createReservation(
        userId,
        eventId,
        items, // Pass `items` from payload directly
        clientInfo,
        billingAddressId
      );
      
      if (!result.success) {
        console.error(`❌ [CheckoutService] Failed to create checkout session: ${result.error}`);
        // Ensure result.data exists before trying to access result.data.session
        const errorMessage = result.message || 'Failed to create checkout session';
        throw new ApiError(400, errorMessage);
      }
      
      // Add a check for result.data and result.data.session
      if (!result.data || !result.data.session) {
        console.error('❌ [CheckoutService] Reservation succeeded but session data is missing in the result.');
        throw new ApiError(500, 'Reservation succeeded but session data is missing');
      }
      const session = result.data.session;
      
      // Apply coupon if provided
      if (couponCode) {
        console.log(`🎟️ [CheckoutService] Applying coupon ${couponCode} to session ${session.id}`);
        // Implement coupon application logic here
        // This would typically involve:
        // 1. Verifying the coupon exists and is valid
        // 2. Calculating the discount
        // 3. Updating the session with the applied coupon and new total
        // For now, let's assume this logic will update the session object in place or return a new one.
        // If it returns a new one, you'd do: session = await applyCouponLogic(session, couponCode);
      }
      
      // Apply points if provided
      if (pointsToApply && pointsToApply > 0) {
        console.log(`🎯 [CheckoutService] Applying ${pointsToApply} points to session ${session.id}`);
        // Implement points application logic here
        // Similar to coupons, this might modify the session object.
        // session = await applyPointsLogic(session, pointsToApply);
      }
      
      return session;
    } catch (error) {
      console.error('💥 [CheckoutService] Error creating checkout session:', error);
      if (error instanceof ApiError) throw error;
      throw new ApiError(500, 'Failed to create checkout session');
    }
  }
}
