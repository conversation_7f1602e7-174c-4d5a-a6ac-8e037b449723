'use client'

import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardHeader, CardContent, CardTitle } from '@/components/ui/card'
import { useSettings } from '@/features/settings/hooks/useSettings'


const formSchema = z.object({
    resultsPerPage: z.number().min(5).max(50),
    sortBy: z.enum(['date', 'popularity', 'price']),
  });


export function DefaultSettings() {
  const { settings, updateSettings } = useSettings()

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zod<PERSON><PERSON><PERSON>ver(formSchema),
        defaultValues: {
            resultsPerPage: settings.resultsPerPage || 10,
            sortBy: settings.sortBy || 'date',
        }
    })

    const onSubmit = async (values: z.infer<typeof formSchema>) => {
        try {
            await updateSettings({...settings, ...values})
             console.log(values)
        } catch (error) {
            console.error('Failed to update settings', error)
        }
    }
  return (
    <Card>
      <CardHeader>
          <CardTitle className="text-2xl font-semibold">Default Settings</CardTitle>
      </CardHeader>
      <CardContent>
          <Form {...form}>
             <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                    control={form.control}
                    name="resultsPerPage"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel>Results Per Page</FormLabel>
                            <FormControl>
                                <Input
                                    type="number"
                                    placeholder="Enter results per page"
                                    {...field}
                                />
                            </FormControl>
                            <FormDescription>
                                Set how many results to show per page.
                            </FormDescription>
                            <FormMessage />
                        </FormItem>
                    )}
                />
                <FormField
                    control={form.control}
                    name="sortBy"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel>Sort By</FormLabel>
                            <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                            >
                                <FormControl>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select sorting order" />
                                    </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                    <SelectItem value="date">Date</SelectItem>
                                    <SelectItem value="popularity">Popularity</SelectItem>
                                    <SelectItem value="price">Price</SelectItem>
                                </SelectContent>
                            </Select>
                            <FormDescription>
                                Choose the order to sort the results.
                            </FormDescription>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                <Button type="submit">Save Settings</Button>
            </form>
        </Form>
      </CardContent>
    </Card>
  )
}
