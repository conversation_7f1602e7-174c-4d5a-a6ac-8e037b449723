  // // Hooks for fetching pending approval events
  // import { useQuery } from '@tanstack/react-query';
  // import api from '@/api/axios';
  // import { ApprovalResponse } from '../types/approval.types';

  // export const usePendingApprovals = () => {
  //   return useQuery<ApprovalResponse>({
  //     queryKey: ['pendingApprovals'],
  //     queryFn: async () => {
  //       const response = await api.get('/api/v1/manager-events/pending');
  //       return response.data;
  //     },
  //     // refetchInterval: 5000, // Refetch every 5 seconds (adjust as needed)
  //   });
  // };
