// tm.api.ts: Fetches Ticketmaster events from the backend using Axios
import { api } from "@/apiAxios/axios";
import {
  TmEventQueryParams,
  PaginatedTmEventResponse,
} from "../types/tm.types";

export const fetchTmEvents = async (
  params: TmEventQueryParams
): Promise<PaginatedTmEventResponse> => {
  try {
    // Make a GET request to the backend using the Axios instance
    const response = await api.get("/api/v1/ticketmaster", { params });
    console.log("API Response:", response);
    // Return the data from the response
    return response.data.data;
  } catch (error: any) {
    // Log and throw the error
    console.error("Error fetching Ticketmaster events:", error.message);
    throw error;
  }
};
