import { useQuery } from "@tanstack/react-query";
import { Clock, CheckCircle, TrendingUp } from "lucide-react";
import {
  ApprovalStats as ApprovalStatsType,
  DEFAULT_APPROVAL_STATS,
} from "../../types/stats.types";
import { StatCard } from "@/features/modules/shared/components/Stats/StatCard";
import api from "@/apiAxios/axios";

export const ApprovalStats = () => {
  const { data: stats, isLoading } = useQuery<ApprovalStatsType>({
    queryKey: ["approvalStats"],
    queryFn: async () => {
      try {
        // TODO: Backend API Endpoint Implementation Required
        // The server needs to implement an endpoint at /api/v1/manager-events/stats
        // that returns statistics about event approvals
        //
        // This endpoint should return:
        // - pending: Number of events awaiting approval
        // - approvedToday: Number of events approved in the last 24 hours
        // - approvalRate: Percentage of events that get approved overall
        // - total: Total number of events processed
        //
        // Example endpoint implementation in managerEventApproval.controller.ts:
        // static getApprovalStats = asyncHandler(async (req: Request, res: Response) => {
        //   const stats = await ManagerEventService.getApprovalStats();
        //   res.status(200).json({
        //     success: true,
        //     message: "Approval stats retrieved successfully",
        //     data: stats,
        //   });
        // });
        //
        // And in managerEventApproval.routes.ts:
        // router.get("/stats", ManagerEventApprovalController.getApprovalStats);

        // For now, using default stats
        const response = await api.get("/api/v1/manager-events/stats");
        return response.data?.data || DEFAULT_APPROVAL_STATS;
      } catch (error) {
        console.error("Error fetching stats:", error);
        return DEFAULT_APPROVAL_STATS;
      }
    },
    staleTime: 60000, // Stats remain fresh for 1 minute
    refetchOnWindowFocus: false,
  });

  const currentStats = stats || DEFAULT_APPROVAL_STATS;

  return (
    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
      <StatCard
        title="Pending Approvals"
        value={currentStats.pending}
        icon={<Clock className="h-4 w-4" />}
        description="Events waiting for review"
      />
      <StatCard
        title="Approved Today"
        value={currentStats.approvedToday}
        icon={<CheckCircle className="h-4 w-4" />}
        description="Events accepted in last 24h"
      />
      <StatCard
        title="Approval Rate"
        value={`${currentStats.approvalRate}%`}
        icon={<TrendingUp className="h-4 w-4" />}
        description="Overall acceptance rate"
      />
    </div>
  );
};
