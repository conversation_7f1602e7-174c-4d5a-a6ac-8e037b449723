import { Router } from "express";
import {
  register,
  login,
  forgotPassword,
  verifyResetToken,
  resetPassword,
} from "./credential.controller";
import {
  enrichRequestWithGeoData,
  validateSchema,
} from "../../../middleware/validate.middleware";
import {
  ForgotPasswordSchema,
  LoginSchema,
  RegisterSchema,
  ResetPasswordSchema,
  VerifyResetTokenSchema,
} from "../../../validators/auth.validator";

const router = Router();
//Middleware on specific routes - validateSchema(schemaCheck)
router.post(
  "/register",
  validateSchema(RegisterSchema),
  enrichRequestWithGeoData,
  register
);
router.post("/login", validateSchema(LoginSchema), login);

router.post(
  "/forgot-password",
  validateSchema(ForgotPasswordSchema),
  forgotPassword
);
router.post(
  "/verify-reset-token",
  validateSchema(VerifyResetTokenSchema),
  verifyResetToken
);

router.post(
  "/reset-password",
  validateSchema(ResetPasswordSchema),
  resetPassword
);

export default router;
