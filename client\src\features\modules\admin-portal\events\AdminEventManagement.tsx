// Admin Event Management component with dynamic CRUD access based on permissions
import { useState } from 'react';
import { EventOperations } from "@/utils/permissions/types";
import { AdminCommunicationModal } from './components/AdminCommunicationModal';
import { Button } from '@/components/ui/button';
import { MessageSquare, Shield } from 'lucide-react';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Badge } from '@/components/ui/badge';

interface AdminEventManagementProps {
    operations: EventOperations;
}

// Mock data for demonstration - will be replaced with real API data
const mockCommunications = [
  { 
    id: '1', 
    ticketId: 'cs_123456', 
    eventName: 'Summer Music Festival',
    buyerName: '<PERSON>',
    managerName: 'Event Manager 1',
    lastMessageDate: '2023-06-15T14:30:00Z',
    status: 'ACTIVE',
    messageCount: 3
  },
  { 
    id: '2', 
    ticketId: 'cs_789012', 
    eventName: 'Tech Conference 2023',
    buyerName: '<PERSON>',
    managerName: 'Event Manager 2',
    lastMessageDate: '2023-06-14T09:15:00Z',
    status: 'RESOLVED',
    messageCount: 5
  },
];

export const AdminEventManagement = ({ operations }: AdminEventManagementProps) => {
  const [communicationModalOpen, setCommunicationModalOpen] = useState(false);
  const [selectedCommunication, setSelectedCommunication] = useState<any>(null);

  const handleViewCommunication = (communication: any) => {
    setSelectedCommunication(communication);
    setCommunicationModalOpen(true);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Admin Event Management</h1>
        <Badge className="bg-purple-600">Admin Portal</Badge>
      </div>
      
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center gap-2 mb-4">
          <Shield className="h-5 w-5 text-purple-600" />
          <h2 className="text-xl font-semibold">Ticket Communications Oversight</h2>
        </div>
        
        <p className="text-gray-600 mb-6">
          Monitor and intervene in communications between visitors and event managers regarding ticket issues.
        </p>
        
        <Table>
          <TableCaption>Active ticket communications requiring oversight</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead>Ticket ID</TableHead>
              <TableHead>Event</TableHead>
              <TableHead>Visitor</TableHead>
              <TableHead>Manager</TableHead>
              <TableHead>Last Activity</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Messages</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {mockCommunications.map((comm) => (
              <TableRow key={comm.id}>
                <TableCell className="font-medium">{comm.ticketId.substring(0, 8)}...</TableCell>
                <TableCell>{comm.eventName}</TableCell>
                <TableCell>{comm.buyerName}</TableCell>
                <TableCell>{comm.managerName}</TableCell>
                <TableCell>{formatDate(comm.lastMessageDate)}</TableCell>
                <TableCell>
                  <Badge className={comm.status === 'ACTIVE' ? 'bg-blue-500' : 'bg-green-500'}>
                    {comm.status}
                  </Badge>
                </TableCell>
                <TableCell>{comm.messageCount}</TableCell>
                <TableCell>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleViewCommunication(comm)}
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    View
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      
      {/* Admin Communication Modal */}
      {selectedCommunication && (
        <AdminCommunicationModal
          open={communicationModalOpen}
          onOpenChange={setCommunicationModalOpen}
          ticketId={selectedCommunication.ticketId}
          buyerName={selectedCommunication.buyerName}
          managerName={selectedCommunication.managerName}
          eventName={selectedCommunication.eventName}
          messages={[]} // This will be populated from API in the future
        />
      )}
    </div>
  );
};
