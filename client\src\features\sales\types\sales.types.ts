// client/src/features/sales/types/sales.types.ts
// Types for the revamped manager sales feature, focusing on detailed checkout sessions.

/**
 * Minimal buyer information displayed to the manager.
 * Prioritize privacy; avoid direct contact details unless absolutely necessary and compliant.
 */
export interface BuyerInfoDTO {
  userId: string;
  // Consider adding anonymized display name or placeholder if needed for UI
  // e.g., displayName?: string; (like "Visitor Xyz")
}

/**
 * Details of a purchased item from a checkout session.
 */
export interface PurchasedItemDetailDTO {
  inventoryId: string;
  quantity: number;
  name: string; // e.g., "Section 101, Row A" or "General Admission Ticket"
  price: number; // Price paid by visitor (per ticket) for this item
  subtotal: number; // price * quantity for this item
  section?: string;
  row?: string | number;
  seat?: string;
  attributes?: string[]; // e.g., ["restricted view", "adult"]
  ticketFormat?: string; // e.g., "E_TICKET", "MOBILE_TRANSFER"
}

/**
 * Billing address information as stored with the checkout session.
 */
export interface BillingAddressDTO {
  id?: string; // Original ID of the billing address record, if available
  name: string;          // Add this if not present
  email: string;         // Add this if not present
  addressLine1: string;
  addressLine2?: string | null;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

/**
 * Represents the details of a single checkout session (a purchase).
 */
export interface CheckoutSessionDetailDTO {
  sessionId: string;
  status: string; // e.g., "COMPLETED", "PENDING" (from CheckoutSessionStatus enum on backend)
  purchaseDate: string; // ISO string format of the completion/update date
  buyerInfo: BuyerInfoDTO;
  billingAddress?: BillingAddressDTO | null; // Optional, as it might not always be provided or stored
  items: PurchasedItemDetailDTO[];
  totalAmountPaid: number; // Total amount the visitor paid (includes items, fees, tax)
  subtotal: number; // Sum of item subtotals (price * quantity for all items in session)
  serviceFee: number; // Service fee charged for this session
  tax?: number; // Tax amount if applicable for this session
  currency: string; // Currency code (e.g., "USD")
  managerPayout: number; // Calculated amount the manager receives for this specific purchase
  ticketUploadStatus?: 'pending' | 'uploaded' | 'failed'; // For UI feedback
  // Messaging related fields
  hasUnreadMessages?: boolean; 
  lastMessagePreview?: string;
  messageCount?: number;
  buyerDisplayName?: string; // "Buyer #abc12345"
  eventDisplayName?: string; // "Event #abc12345"
}

/**
 * Aggregated sales information for a single event, now including its detailed checkout sessions.
 */
export interface ManagerEventSalesDetailsDTO {
  eventId: string;
  eventName: string;
  eventDate: string; // ISO string format
  eventVenue: string;
  eventCity?: string;
  eventCountry?: string;
  totalTicketsSoldThisEvent: number; // Sum of quantities from all COMPLETED sessions for this event
  totalManagerRevenueThisEvent: number; // Sum of managerPayout from all COMPLETED sessions for this event
  currency: string; // Currency of the event's pricing
  eventImageUrl?: string | null;
  sessions: CheckoutSessionDetailDTO[]; // Array of individual purchases for this event
}

/**
 * Structure for the API response containing detailed sales information with pagination for events.
 */
export interface ManagerSalesDetailedResponse {
  data: ManagerEventSalesDetailsDTO[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number; // Total number of *events* with sales for this manager
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

/**
 * Filter/sort options for the sales data display (primarily for events).
 */
export interface SalesFilterOptions {
  sortBy: 'eventDate' | 'eventName' | 'totalRevenue'; // Adjusted to sort events
  sortOrder: 'asc' | 'desc';
  dateRange?: {
    start: Date | null; // Event date start
    end: Date | null;   // Event date end
  };
  // We might add filters for sessions later if needed (e.g., filter by purchaseDate within an event)
}

/**
 * Type for the useSalesOverview hook's return value.
 */
export interface UseManagerSalesOverviewResult {
  eventsWithSales: ManagerEventSalesDetailsDTO[]; // Changed from salesData
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => Promise<any>;
  goToPage: (page: number) => void;
  updateFilterOptions: (options: Partial<SalesFilterOptions>) => void; // Renamed from updateSortOptions
  filterOptions: SalesFilterOptions;
}

/**
 * Message types that will integrate with the new messaging system
 */
export interface TicketMessageDTO {
  id: string;
  checkoutSessionId: string;
  senderId: string;
  senderRole: 'VISITOR' | 'MANAGER' | 'ADMIN';
  message: string;
  status: 'UNREAD' | 'READ' | 'RESOLVED' | 'ARCHIVED';
  createdAt: string; // ISO string
  updatedAt: string; // ISO string
  readAt?: string; // ISO string
  conversationId?: string;
}

/**
 * Conversation summary for manager dashboard
 */
export interface ConversationSummaryDTO {
  checkoutSessionId: string;
  buyerDisplayName: string; // e.g., "Buyer #abc12345"
  lastMessageDate: string;
  messageCount: number;
  unreadCount: number;
  latestMessagePreview: string;
  status: 'ACTIVE' | 'RESOLVED' | 'ARCHIVED';
}
