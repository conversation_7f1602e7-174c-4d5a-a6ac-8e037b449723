// Service for handling search queries using OpenCTX
// This service orchestrates data fetching from multiple sources

import { databaseProvider, tmApiProvider } from '@/lib/openctx/providers';
import { validateOpenCTXRequest } from '@/lib/openctx/validator';
import { OpenCTXRequest, Context } from '@/lib/openctx/types';
import { SearchOptions } from '../types/search.types';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export class SearchService {
  // Updated to include ManagerEvents in search results
  static async search({ query, userRole }: SearchOptions): Promise<Context[]> {
    if (!query) {
      throw new Error('Search query is required.');
    }

    const validationResult = validateOpenCTXRequest(query, userRole);
    if (!validationResult.valid) {
      throw new Error(`Invalid search request: ${validationResult.error}`);
    }

    const validQuery = validationResult.data as OpenCTXRequest;
    
    // Get search term from query
    const searchTerm = validQuery.query || '';
    
    // Updated results type to Context[]
    let results: Context[] = [];

    // First, fetch ManagerEvents that match the search term
    const managerEvents = await this.fetchManagerEvents(searchTerm);
    
    // Then fetch regular results based on entity type
    let entityResults: Context[] = [];
    if (validQuery.entity === 'tmEvent') {
      entityResults = await databaseProvider.fetch(validQuery);
    } else if (validQuery.entity === 'events') {
      entityResults = await tmApiProvider.fetch(validQuery);
    } else {
      throw new Error(`Unsupported entity type: ${validQuery.entity}`);
    }
    
    // Combine results with manager events first
    results = [...managerEvents, ...entityResults];
    
    return results;
  }
  
  // New method to fetch ManagerEvents that match the search term
  private static async fetchManagerEvents(searchTerm: string): Promise<Context[]> {
    if (!searchTerm || searchTerm.length < 3) {
      return [];
    }
    
    try {
      // Fetch approved manager events that match the search term
      const events = await prisma.managerEvent.findMany({
        where: {
          AND: [
            { approvalStatus: 'APPROVED' },
            {
              OR: [
                { name: { contains: searchTerm, mode: 'insensitive' } },
                { city: { contains: searchTerm, mode: 'insensitive' } },
                { venue: { contains: searchTerm, mode: 'insensitive' } },
              ]
            }
          ]
        },
        take: 5, // Limit to top 5 matches
        orderBy: { date: 'asc' } // Show upcoming events first
      });
      
      // Convert to Context format with proper structure for EventDetailCard
      return events.map(event => ({
        type: 'managerEvent',
        text: event.name,
        metadata: {
          source: 'manager',
          id: `manager-${event.id}`,
          name: event.name,
          venue: event.venue,
          date: event.date.toISOString(),
          image: event.image || '',
          city: event.city,
          country: event.country,
          category: event.category,
          rawEvent: {
            // Include all fields needed by EventDetailCard
            id: event.id,
            name: event.name,
            venue: event.venue,
            city: event.city,
            date: event.date,
            category: event.category,
            country: event.country,
            inventory: event.inventory,
            status: event.status
          }
        }
      }));
    } catch (error) {
      console.error('Error fetching manager events for search:', error);
      return [];
    }
  }
}
