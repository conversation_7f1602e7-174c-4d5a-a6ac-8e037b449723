import { createResetPasswordTemplate } from "../templates/resetPassword.template";
import { createOtpEmailTemplate } from "../templates/otp-email.template";
import { NODE_ENV } from '@/constants';
import { EMAIL_CONFIG, resend } from "../config/email.config";
import { createServiceLogger } from "@/utils/logger/index";

const logger = createServiceLogger('EmailService');

export class EmailService {
  /**
   * Send password reset email
   */
  static async sendPasswordReset(
    email: string,
    resetToken: string,
    userName: string
  ) {
    const isDevelopment = NODE_ENV === "development";
    const clientUrl = process.env.CLIENT_URL;
    const resetUrl = `${clientUrl}/reset-password/${resetToken}`;
    
    const logContext = {
      action: 'sendPasswordReset',
      environment: NODE_ENV,
      recipientEmail: email,
      resetUrl: isDevelopment ? resetUrl : '[REDACTED]',
      userName
    };
    
    logger.info('Initiating password reset email', logContext);
    
    try {
      const emailPayload = {
        from: EMAIL_CONFIG.FROM.RESET,
        to: isDevelopment ? EMAIL_CONFIG.TEST_EMAIL : email,
        subject: EMAIL_CONFIG.SUBJECTS.RESET_PASSWORD,
        html: createResetPasswordTemplate(resetUrl, userName),
      };
      
      logger.debug('Email payload prepared', {
        ...logContext,
        from: emailPayload.from,
        to: emailPayload.to,
        subject: emailPayload.subject
      });

      const response = await resend.emails.send(emailPayload);
      
      logger.info('Password reset email sent successfully', {
        ...logContext,
        emailId: response.data?.id,
        actualRecipient: isDevelopment ? EMAIL_CONFIG.TEST_EMAIL : email
      });

      return response;
    } catch (error) {
      logger.error('Failed to send password reset email', {
        ...logContext,
        error: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  }

  /**
   * Send email verification OTP
   */
  static async sendVerificationOTP(
    email: string,
    otp: string,
    userName: string
  ) {
    const isDevelopment = NODE_ENV === "development";
    
    const logContext = {
      action: 'sendVerificationOTP',
      environment: NODE_ENV,
      recipientEmail: email,
      userName,
      otpLength: otp.length
    };
    
    logger.info('Initiating email verification OTP', logContext);
    
    try {
      const emailPayload = {
        from: EMAIL_CONFIG.FROM.VERIFICATION,
        to: isDevelopment ? EMAIL_CONFIG.TEST_EMAIL : email,
        subject: EMAIL_CONFIG.SUBJECTS.EMAIL_VERIFICATION,
        html: createOtpEmailTemplate(otp, userName),
      };
      
      logger.debug('Email OTP payload prepared', {
        ...logContext,
        from: emailPayload.from,
        to: emailPayload.to,
        subject: emailPayload.subject,
        // Only log OTP in development
        otp: isDevelopment ? otp : '[REDACTED]'
      });

      const response = await resend.emails.send(emailPayload);
      
      logger.info('Email verification OTP sent successfully', {
        ...logContext,
        emailId: response.data?.id,
        actualRecipient: isDevelopment ? EMAIL_CONFIG.TEST_EMAIL : email
      });

      return response;
    } catch (error) {
      logger.error('Failed to send email verification OTP', {
        ...logContext,
        error: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
        resendApiConfigured: !!process.env.RESEND_API_KEY
      });
      throw error;
    }
  }

  // Add this method to your existing EmailService class
  static async debugEmailConfig() {
    const logger = createServiceLogger('EmailService');
    
    logger.info('🔍 Email Configuration Debug', {
      environment: NODE_ENV,
      hasApiKey: !!process.env.RESEND_API_KEY,
      fromAddresses: {
        verification: EMAIL_CONFIG.FROM.VERIFICATION,
        reset: EMAIL_CONFIG.FROM.RESET
      },
      testEmail: EMAIL_CONFIG.TEST_EMAIL
    });

    // Test domain status
    try {
      const domains = await resend.domains.list();
      logger.info('📧 Domain Status', {
        domains: Array.isArray(domains.data)
          ? domains.data.map(d => ({
              name: d.name,
              status: d.status,
              createdAt: d.created_at
            }))
          : []
      });
    } catch (error) {
      logger.error('❌ Failed to fetch domains', { error: error instanceof Error ? error.message : String(error) });
    }
  }
}























// import { createResetPasswordTemplate } from "../templates/resetPassword.template";
// import { createOtpEmailTemplate } from "../templates/otp-email.template";
// import { NODE_ENV } from '@/constants';
// import { EMAIL_CONFIG, resend } from "../config/email.config";

// export class EmailService {
//   /**
//    * Send password reset email
//    */
//   static async sendPasswordReset(
//     email: string,
//     resetToken: string,
//     userName: string
//   ) {
//     const isDevelopment = NODE_ENV === "development";
//     const clientUrl = process.env.CLIENT_URL;
//     const resetUrl = `${clientUrl}/reset-password/${resetToken}`;
    
//     try {
//       // Log the intended recipient for debugging
//       console.log(`Attempting to send email to: ${email}`);
//       console.log(`Reset URL: ${resetUrl}`);

//       const response = await resend.emails.send({
//         from: EMAIL_CONFIG.FROM.RESET,
//         to: isDevelopment ? EMAIL_CONFIG.TEST_EMAIL : email,
//         subject: EMAIL_CONFIG.SUBJECTS.RESET_PASSWORD,
//         html: createResetPasswordTemplate(resetUrl, userName),
//       });

//       // Development mode logging
//       if (isDevelopment) {
//         console.log("Development Mode:");
//         console.log(`- Intended recipient: ${email}`);
//         console.log(`- Actually sent to: ${EMAIL_CONFIG.TEST_EMAIL}`);
//       }

//       return response;
//     } catch (error) {
//       console.error("Email sending failed:", error);
//       throw error;
//     }
//   }

//   /**
//    * Send email verification OTP
//    */
//   static async sendVerificationOTP(
//     email: string,
//     otp: string,
//     userName: string
//   ) {
//     const isDevelopment = NODE_ENV === "development";
    
//     try {


//       console.log(`🔍 [${NODE_ENV}] Attempting to send verification OTP`);
//       console.log(`📧 From: ${EMAIL_CONFIG.FROM.VERIFICATION}`);
//       console.log(`📧 To: ${isDevelopment ? EMAIL_CONFIG.TEST_EMAIL : email}`);
//       console.log(`🔑 API Key configured: ${!!process.env.RESEND_API_KEY}`);

//       const response = await resend.emails.send({
//         from: EMAIL_CONFIG.FROM.VERIFICATION,
//         to: isDevelopment ? EMAIL_CONFIG.TEST_EMAIL : email,
//         subject: EMAIL_CONFIG.SUBJECTS.EMAIL_VERIFICATION,
//         html: createOtpEmailTemplate(otp, userName),
//       });









//       console.log(`✅ Resend Response:`, response);
    
//       return response;
//     } catch (error) {

//       console.error("❌ Email sending failed:", error);
//       console.error("❌ Error details:", JSON.stringify(error, null, 2));
//       throw error;
//     }
//   }
// }
