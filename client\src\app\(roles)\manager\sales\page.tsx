// Manager Sales Overview Page

"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  BarChart,
  Calendar,
  ListFilter,
  ArrowDownUp,
  DollarSign,
  ReceiptText,
  TrendingUp,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { SalesDataTable } from "@/features/sales/components/SalesDataTable";
import { Separator } from "@/components/ui/separator";

export default function SalesOverviewPage() {
  const [view, setView] = useState<"table" | "chart">("table");
  const [timeframe, setTimeframe] = useState("all-time");

  // This would be replaced with actual data from your data fetching logic
  const summaryData = {
    totalRevenue: 42850.75,
    ticketsSold: 237,
    totalPayouts: 38565.68,
    averageTicketPrice: 180.8,
    currency: "USD",
  };

  // Helper to format currency values
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    // Main container for the page
    // Increased padding for better spacing, especially on mobile
    <div className="min-h-screen bg-background text-foreground min-w-fit">
      <div className="container mx-auto py-6 px-4 sm:px-6 lg:px-8 space-y-6">
        {/* Page header with title and description */}
        <div className="flex flex-col gap-2">
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">
            Sales Overview
          </h1>
          <p className="text-muted-foreground">
            View sales statistics and performance for your event listings.
          </p>
        </div>

        {/* Sales Summary Cards - Responsive grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
          <Card className="shadow-sm">
            <CardContent className="p-4 flex flex-col">
              <div className="flex justify-between items-center mb-2">
                <p className="text-sm text-muted-foreground">Revenue</p>
                <DollarSign className="h-4 w-4 text-green-600" />
              </div>
              <p className="text-lg md:text-xl font-bold text-green-600">
                {formatCurrency(summaryData.totalRevenue)}
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                Lifetime earnings
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-sm">
            <CardContent className="p-4 flex flex-col">
              <div className="flex justify-between items-center mb-2">
                <p className="text-sm text-muted-foreground">Tickets</p>
                <ReceiptText className="h-4 w-4 text-blue-600" />
              </div>
              <p className="text-lg md:text-xl font-bold">
                {summaryData.ticketsSold}
              </p>
              <p className="text-xs text-muted-foreground mt-1">Units sold</p>
            </CardContent>
          </Card>

          <Card className="shadow-sm">
            <CardContent className="p-4 flex flex-col">
              <div className="flex justify-between items-center mb-2">
                <p className="text-sm text-muted-foreground">Payouts</p>
                <TrendingUp className="h-4 w-4 text-purple-600" />
              </div>
              <p className="text-lg md:text-xl font-bold text-purple-600">
                {formatCurrency(summaryData.totalPayouts)}
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                After platform fees
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-sm">
            <CardContent className="p-4 flex flex-col">
              <div className="flex justify-between items-center mb-2">
                <p className="text-sm text-muted-foreground">Avg. Price</p>
                <ArrowDownUp className="h-4 w-4 text-amber-600" />
              </div>
              <p className="text-lg md:text-xl font-bold">
                {formatCurrency(summaryData.averageTicketPrice)}
              </p>
              <p className="text-xs text-muted-foreground mt-1">Per ticket</p>
            </CardContent>
          </Card>
        </div>

        {/* Time Period Filter - Shows above tabs */}
        <div className="flex justify-between items-center flex-wrap gap-2 pt-2">
          <h2 className="text-xl font-semibold">Detailed Sales</h2>
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground hidden sm:block" />
            <Select value={timeframe} onValueChange={setTimeframe}>
              <SelectTrigger className="w-full sm:w-[180px] h-9">
                <SelectValue placeholder="Select Time Period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-time">All Time</SelectItem>
                <SelectItem value="this-month">This Month</SelectItem>
                <SelectItem value="last-month">Last Month</SelectItem>
                <SelectItem value="this-quarter">This Quarter</SelectItem>
                <SelectItem value="this-year">This Year</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Separator />

        {/* Main Tabs for Table/Chart Views */}
        <Tabs
          defaultValue="table"
          className="w-full"
          onValueChange={(v: string) => setView(v as "table" | "chart")}
        >
          <div className="flex justify-between items-center mb-4 flex-wrap gap-2">
            <TabsList className="h-10">
              <TabsTrigger
                value="table"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                <ListFilter className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Detailed</span> View
              </TabsTrigger>
              <TabsTrigger
                value="chart"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                <BarChart className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Chart</span> View
              </TabsTrigger>
            </TabsList>

            {/* Export Button - Only visible on desktop */}
            <Button variant="outline" size="sm" className="hidden md:flex h-10">
              <BarChart className="h-4 w-4 mr-2" />
              Export Data
            </Button>
          </div>

          <TabsContent value="table" className="mt-0">
            {/* Table container with proper overflow handling */}
            <div className="w-full overflow-auto border rounded-md">
              <SalesDataTable />
            </div>
          </TabsContent>

          <TabsContent value="chart" className="mt-0">
            {/* Enhanced Chart Container */}
            <Card>
              <CardHeader>
                <CardTitle>Sales Visualization</CardTitle>
                <CardDescription>
                  Graphical representation of your event sales data.
                </CardDescription>
              </CardHeader>
              <CardContent className="flex items-center justify-center h-[400px]">
                <div className="text-center">
                  <BarChart className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground mb-2">
                    Chart visualization coming soon...
                  </p>
                  <Button variant="outline" disabled>
                    Request Early Access
                  </Button>
                </div>
              </CardContent>
              <CardFooter className="border-t pt-4 flex justify-between items-center text-sm text-muted-foreground">
                <p>Data updates automatically</p>
                <p>Last refreshed: {new Date().toLocaleTimeString()}</p>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
