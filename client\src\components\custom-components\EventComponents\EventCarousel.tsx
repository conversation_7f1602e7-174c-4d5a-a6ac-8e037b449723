import { FC, useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { EventCard } from './EventCard';
import { PriorityEventData } from '@/features/settings/components/PriorityEvents/types/priority-events.types';
import { HeroEventCard } from './HeroEventCard';

interface EventCarouselProps {
  events: PriorityEventData[];
  onEventClick?: (event: PriorityEventData) => void;
}

export const EventCarousel = ({ events, onEventClick }: EventCarouselProps): JSX.Element => {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % events.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [events.length]);

  return (
    <div className="w-full h-[60vh] overflow-hidden relative">
      <AnimatePresence initial={false}>
        <motion.div
          key={currentIndex}
          className="w-full h-full"
          initial={{ opacity: 0, x: 300 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -300 }}
          transition={{ duration: 0.5 }}
        >
          <HeroEventCard 
            event={events[currentIndex]} 
            onEventClick={onEventClick}
          />
        </motion.div>
      </AnimatePresence>
    </div>
  );
};
