"use client";
import { Calendar } from "lucide-react";
import { BentoBox } from "../../../modules/shared/widgets/BentoBox";

export const EventsDiscoveryGrid = () => {
  return (
    <BentoBox
      title="Upcoming Events"
      description="Events you might be interested in"
      className="col-span-2 row-span-2"
      header={<Calendar className="h-5 w-5 text-primary" />}
    >
      {/* Event Grid Implementation */}
      <div className="grid grid-cols-2 gap-4 mt-4">
        {/* Sample Event Cards - To be replaced with real data */}
        {[1, 2, 3, 4].map((index) => (
          <div
            key={index}
            className="rounded-lg bg-accent/50 p-4 hover:bg-accent/70 transition-colors"
          >
            <h4 className="font-medium">Event {index}</h4>
            <p className="text-sm text-muted-foreground">Coming Soon</p>
          </div>
        ))}
      </div>
    </BentoBox>
  );
};
