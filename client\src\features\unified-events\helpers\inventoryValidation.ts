import { InventoryItem } from "@/features/event-listing/types/eventListing"; // Adjust path if needed

type SellingPreference = InventoryItem['sellingPreference'];

interface ValidationResult {
    isValid: boolean;
    message?: string; // Optional feedback message
    maxAllowed?: number; // Maximum quantity user can actually select right now
    minAllowed?: number; // Minimum quantity user must select if > 0
}

/**
 * Validates a selected quantity against available quantity and selling preferences.
 * @param selectedQty The quantity the user wants to select.
 * @param availableQty The total quantity available in this inventory lot.
 * * @param preference The seller's selling preference rule.
 * @returns ValidationResult object.
 */
export const validateSelection = (
    selectedQty: number,
    availableQty: number,
    preference: SellingPreference
): ValidationResult => {

    const maxAllowed = availableQty; // Base max is total available
    let minAllowed = 1; // Base min is 1

    // Initial bounds check
    if (selectedQty < 0 || selectedQty > availableQty) {
        return { isValid: false, message: `Select between 0 and ${availableQty}`, maxAllowed, minAllowed: 0 };
    }
    if (selectedQty === 0) {
         // Technically valid to select 0, but maybe not actionable
        return { isValid: true, maxAllowed, minAllowed: 0 };
    }

    switch (preference) {
        case 'Any':
            // Already passed bounds check
            return { isValid: true, maxAllowed, minAllowed };

        case 'Pairs':
            minAllowed = 2;
            if (selectedQty % 2 !== 0) {
                return { isValid: false, message: 'Must select in pairs (even quantity)', maxAllowed, minAllowed };
            }
            return { isValid: true, maxAllowed, minAllowed };

        case 'Full':
             minAllowed = availableQty;
            if (selectedQty !== availableQty) {
                return { isValid: false, message: `Must purchase all ${availableQty} tickets`, maxAllowed: availableQty, minAllowed };
            }
            return { isValid: true, maxAllowed: availableQty, minAllowed };

        case 'Avoid Leaving Single':
            // Cannot leave exactly one ticket remaining
            if (availableQty > 1 && (availableQty - selectedQty === 1)) {
                // If user selected qty that leaves 1, it's invalid
                // The max they can select is available - 2 (or available if that's valid)
                const safeMax = (availableQty > 2) ? availableQty - 2 : 0; // If available=2, must buy 2, handled below
                const message = `Cannot leave a single ticket (Select ${availableQty} or ${safeMax > 0 ? `up to ${safeMax}`: 'none'})`;

                 // Special case: If only 2 available, must buy 2
                if (availableQty === 2 && selectedQty === 1) {
                     return { isValid: false, message: 'Must purchase both tickets', maxAllowed: 2, minAllowed: 2 };
                }
                 // General case for leaving 1
                 return { isValid: false, message, maxAllowed: Math.max(safeMax, availableQty) /* allow selecting all */, minAllowed };
            }
             // Special case: If only 2 available, must buy 2
            if (availableQty === 2) {
                minAllowed = 2;
                if (selectedQty !== 2) {
                     return { isValid: false, message: 'Must purchase both tickets', maxAllowed: 2, minAllowed: 2 };
                }
            }
            // Otherwise, the selection is valid under this rule (doesn't leave 1)
            return { isValid: true, maxAllowed, minAllowed };

        default:
            // Unknown preference, treat as 'Any' for safety
             console.warn(`Unknown selling preference: ${preference}`);
             return { isValid: true, maxAllowed, minAllowed };
    }
};
