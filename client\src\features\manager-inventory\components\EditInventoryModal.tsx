import React, { useState, useEffect, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid'; // For generating temporary IDs for new items
import {
  Dialog, DialogContent, DialogHeader, DialogTitle,
  DialogDescription, DialogFooter, DialogClose,
} from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ScrollArea } from '@/components/ui/scroll-area';
import { Trash2, Plus, Save, XCircle, Edit2,Loader2 } from 'lucide-react'; // Icons
import { Input } from '@/components/ui/input'; // Example form element
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'; // Example form element
import { Checkbox } from '@/components/ui/checkbox'; // Example form element
import { EditInventoryModalProps, InventoryDetailItem } from '../types/inventory.types';
import { toast } from 'sonner';

// Placeholder for a single inventory item form (You'd replace this with your actual reusable component or form fields)
const InventoryItemFormFields: React.FC<{ itemData: Partial<InventoryDetailItem>; onChange: (field: keyof InventoryDetailItem, value: any) => void }> = ({ itemData, onChange }) => {
    // --- TODO: Replace this placeholder with your actual form fields ---
    // Example fields:
    return (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 border bg-muted rounded-md mb-4">
             <div><Label htmlFor="qty">Qty</Label><Input id="qty" type="number" value={itemData.quantity || ''} onChange={e => onChange('quantity', parseInt(e.target.value) || 0)} /></div>
             <div><Label htmlFor="section">Section</Label><Input id="section" value={itemData.section || ''} onChange={e => onChange('section', e.target.value)} /></div>
             <div><Label htmlFor="row">Row</Label><Input id="row" value={itemData.row || ''} onChange={e => onChange('row', e.target.value)} /></div>
              <div><Label htmlFor="price">Price</Label><Input id="price" type="number" step="0.01" value={itemData.listPrice || ''} onChange={e => onChange('listPrice', e.target.value)} /></div>
              <div><Label htmlFor="fee">Service Fee (10%)</Label><Input id="fee" type="number" value={(itemData.serviceFee ?? 0).toFixed(2)} readOnly disabled className="bg-gray-100" /></div>
              {/* Add Select for seatingType, ticketFormat, sellingPreference */}
              {/* Add Input/Textarea for notes */}
              {/* Add TagInput or similar for disclosures/attributes */}
              {/* Add Checkbox for termsAndConditions */}
              {/* Add Input for lowSeatNumber */}
        </div>
    );
     // --- End Placeholder ---
};


export const EditInventoryModal: React.FC<EditInventoryModalProps> = ({ item, isOpen, onClose, updateInventory, isUpdatingInventory }) => {
  const [inventoryState, setInventoryState] = useState<InventoryDetailItem[]>([]);
  const [editingRow, setEditingRow] = useState<Partial<InventoryDetailItem> | null>(null); // Holds data for the item being added/edited
  const [isAdding, setIsAdding] = useState(false); // Flag to differentiate add vs edit mode for the form

  // Effect to reset internal state when the modal is opened with a new item
  useEffect(() => {
    if (isOpen && item) {
        console.log("Modal received item inventory:", item.inventory);
        // Create a deep copy to avoid mutating the original prop
        setInventoryState(item.inventory ? JSON.parse(JSON.stringify(item.inventory)) : []);
        setEditingRow(null); // Close any open edit forms
        setIsAdding(false);
    } else if (!isOpen) {
        // Clear state when modal closes
        setInventoryState([]);
        setEditingRow(null);
        setIsAdding(false);
    }
  }, [isOpen, item]); // Dependency array ensures reset on item change or open/close

  const handleAddItemClick = () => {
    // Initialize a new row with default values (and a temporary unique ID)
     const newItemDefaults: Partial<InventoryDetailItem> = {
        id: `temp-${uuidv4()}`, // Temporary ID for list key, replace on save if needed
        quantity: 1,
        section: '',
        row: 0,
        listPrice: 0,
        termsAndConditions: true, // Example default
        // Add other necessary defaults
     };
    setEditingRow(newItemDefaults);
    setIsAdding(true);
  };

  const handleEditItemClick = (invItem: InventoryDetailItem) => {
    // Set the form data to the item being edited
    setEditingRow(JSON.parse(JSON.stringify(invItem))); // Use deep copy
    setIsAdding(false);
  };

  const handleDeleteItemClick = (itemId: string) => {
    // Check if this is the last item
    if (inventoryState.length <= 1) {
      toast.error("Cannot delete the last inventory item. An event must have at least one.");
      return; // Prevent deletion
    }
    // Proceed with deletion if more than one item exists
    setInventoryState(prevState => prevState.filter(inv => inv.id !== itemId));
    toast.success("Inventory item removed."); // Optional feedback
  };

  const handleFormChange = useCallback((field: keyof InventoryDetailItem, value: any) => {
      setEditingRow(prev => {
          if (!prev) return null;

          const updatedRow = { ...prev };

          // Update the specific field first
          (updatedRow as any)[field] = value;

          // If listPrice changed, calculate and update serviceFee
          if (field === 'listPrice') {
              const price = parseFloat(value);
              // Calculate 10% fee, ensure it's a number, default to 0 if price is invalid/zero
              const newServiceFee = !isNaN(price) && price > 0 ? price * 0.10 : 0;

              // Update the price field (handle potential NaN from parseFloat)
              updatedRow.listPrice = !isNaN(price) ? price : 0;
              // Update the service fee field
              updatedRow.serviceFee = newServiceFee;

              console.log(`🧾 Price changed to: ${updatedRow.listPrice}, Service Fee calculated: ${updatedRow.serviceFee}`);
          }

          return updatedRow;
      });
  }, []);

  const handleSaveItem = () => {
      if (!editingRow) return;

      // Basic validation placeholder
      if (!editingRow.section || !editingRow.row || !editingRow.quantity || !editingRow.listPrice) {
          toast.error("Please fill in required inventory fields (Qty, Section, Row, Price).");
          return;
      }

      if (isAdding) {
          // Add new item - ensure ID is properly handled (replace temp if needed by backend later)
          setInventoryState(prev => [...prev, editingRow as InventoryDetailItem]);
      } else {
          // Update existing item
          setInventoryState(prev => prev.map(inv => inv.id === editingRow.id ? (editingRow as InventoryDetailItem) : inv));
      }
      setEditingRow(null); // Close form
      setIsAdding(false);
  };

  const handleCancelEdit = () => {
    setEditingRow(null); // Close form
    setIsAdding(false);
  };

  // ✨ Modify handleSaveChanges to call the mutation ✨
  const handleSaveChanges = () => {
    if (isUpdatingInventory) return; // Prevent multiple clicks

    console.log("💾 Submitting updated inventory:", inventoryState);
    if (item?.id) {
        // Call the mutation function passed via props
        updateInventory({
            eventId: item.id,
            inventory: inventoryState,
         

        });
    } else {
        console.error("Cannot save, item ID is missing.");
        toast.error("Cannot save inventory, item ID is missing.");
    }
  };

  if (!item) return null; // Should not happen if rendered conditionally, but safe check

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-4xl w-full h-[90vh] flex flex-col p-0">
        <DialogHeader className="p-6 pb-4 border-b">
          <DialogTitle className="text-xl sm:text-2xl font-semibold">Edit Inventory</DialogTitle>
          <DialogDescription>
            For event: <span className="font-medium">{item.name}</span>
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="flex-grow px-6 py-4 overflow-y-auto">
            {/* Add/Edit Form Area */}
            {editingRow && (
                <div className="mb-6">
                    <h4 className="text-lg font-semibold mb-2">{isAdding ? 'Add New Inventory Item' : 'Edit Inventory Item'}</h4>
                     {/* --- TODO: Render actual Inventory Item Form component/fields here --- */}
                     <InventoryItemFormFields itemData={editingRow} onChange={handleFormChange} />
                    <div className="flex justify-end gap-2 mt-2">
                         <Button variant="ghost" size="sm" onClick={handleCancelEdit}><XCircle className="mr-1 h-4 w-4"/> Cancel</Button>
                         <Button size="sm" onClick={handleSaveItem}><Save className="mr-1 h-4 w-4"/> Save Item</Button>
                    </div>
                </div>
            )}

            {/* Inventory Table */}
             <div className="flex justify-between items-center mb-3">
                 <h3 className="text-lg font-semibold">Current Inventory</h3>
                 {/* {!editingRow && ( // Only show Add button when not editing/adding another row
                     <Button size="sm" onClick={handleAddItemClick}><Plus className="mr-1 h-4 w-4"/> Add Row</Button>
                 )} */}
             </div>

            <div className="border rounded-md overflow-hidden">
                 <Table>
                     <TableHeader>
                         <TableRow>
                             {/* Adjust heads based on fields you actually show/edit */}
                             <TableHead>Qty</TableHead>
                             <TableHead>Section</TableHead>
                             <TableHead>Row</TableHead>
                             <TableHead>Price</TableHead>
                             <TableHead>Format</TableHead>
                             <TableHead className="text-right">Actions</TableHead>
                         </TableRow>
                     </TableHeader>
                     <TableBody>
                         {inventoryState.length === 0 ? (
                             <TableRow><TableCell colSpan={6} className="text-center text-muted-foreground h-24">No inventory items added yet.</TableCell></TableRow>
                         ) : (
                             inventoryState.map((invItem) => (
                                 <TableRow key={invItem.id} className={editingRow?.id === invItem.id ? 'bg-muted/60' : ''}>
                                     <TableCell>{invItem.quantity}</TableCell>
                                     <TableCell>{invItem.section}</TableCell>
                                     <TableCell>{invItem.row}</TableCell>
                                     <TableCell>${invItem.listPrice.toFixed(2)}</TableCell>
                                     <TableCell>{invItem.ticketFormat}</TableCell>
                                     <TableCell className="text-right">
                                         {/* Disable buttons if another row is being edited */}
                                         <Button variant="ghost" size="icon" className="h-7 w-7 mr-1" onClick={() => handleEditItemClick(invItem)} disabled={!!editingRow}>
                                             <Edit2 className="h-4 w-4" />
                                         </Button>
                                         <Button
                                             variant="ghost"
                                             size="icon"
                                             className="h-7 w-7 text-destructive hover:text-destructive"
                                             onClick={() => handleDeleteItemClick(invItem.id)}
                                             // Disable if another row is being edited OR if it's the last item
                                             disabled={!!editingRow || inventoryState.length <= 1}
                                             title={inventoryState.length <= 1 ? "Cannot delete the last item" : "Delete item"}
                                         >
                                             <Trash2 className="h-4 w-4" />
                                         </Button>
                                     </TableCell>
                                 </TableRow>
                             ))
                         )}
                     </TableBody>
                 </Table>
             </div>

        </ScrollArea>

        <DialogFooter className="p-6 pt-4 border-t bg-background">
            <DialogClose asChild>
                 <Button variant="outline" onClick={onClose}>Cancel</Button>
            </DialogClose>
             {/* Disable save if form is open */}
             <Button onClick={handleSaveChanges} disabled={!!editingRow || isUpdatingInventory}>
                 {isUpdatingInventory ? (
                    <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving...</>
                 ) : (
                    <><Save className="mr-2 h-4 w-4" /> Save All Changes</>
                 )}
            </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// --- Helper Label Component (if not imported globally) ---
const Label: React.FC<React.LabelHTMLAttributes<HTMLLabelElement>> = (props) => {
    return <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mb-1 block" {...props} />;
};
