/**
 * Service for points operations within the checkout feature
 */

import { PrismaClient, CheckoutSessionStatus } from "@prisma/client";
import { toPrismaJson } from "@/utils/prismaHelpers";
import { AppliedPoints } from "../types/points.types";
import ApiError from "@/utils/ApiError";

const prisma = new PrismaClient();

// Constants
const POINTS_VALUE_PER_UNIT = 0.01; // $0.01 per point

export class PointsService {
  /**
   * Apply points to a checkout session
   * 
   * @param sessionId - The ID of the checkout session
   * @param pointsToApply - The number of points to apply
   * @param userId - The ID of the authenticated user
   * @returns The updated session with points applied
   * @throws ApiError if session not found, unauthorized, or points invalid
   */
  static async applyPoints(
    sessionId: string,
    pointsToApply: number,
    userId: string
  ) {
    try {
      console.log(`🔍 Finding session to apply points: ${sessionId}`);

      // Special case: If 0 points, remove any existing points
      if (pointsToApply === 0) {
        return this.removeAppliedPoints(sessionId, userId);
      }

      // Step 1: Find the session
      const session = await prisma.checkoutSession.findUnique({
        where: { id: sessionId },
      });

      if (!session) {
        console.log(`❌ Session not found: ${sessionId}`);
        throw ApiError.notFound("Checkout session not found");
      }

      // Verify the session belongs to the user
      if (session.userId !== userId) {
        console.log(`❌ Session belongs to different user: ${sessionId}`);
        throw ApiError.forbidden("You don't have permission for this session");
      }

      // Step 2: Check if session is in a valid state
      if (session.status !== CheckoutSessionStatus.RESERVED) {
        console.log(`❌ Cannot apply points to session with status: ${session.status}`);
        throw ApiError.badRequest(`Cannot apply points to session with status ${session.status}`);
      }

      // Step 3: Get user's points balance and referral status
      const user = await prisma.user.findUnique({
        where: { id: userId },
        // In a real application, you would also fetch:
        // - User's order history to determine if they're a first/second time buyer
        // - Referral information to determine if they're a referee
      });

      if (!user) {
        console.log(`❌ User not found: ${userId}`);
        throw ApiError.notFound("User not found");
      }

      // For this implementation, we'll simulate points balance and referee status
      // In a real app, these would come from the user record and related tables
      const userPointsBalance = 2000; // Simulated balance
      const isReferee = true;         // Simulated referee status
      const orderCount = 1;           // Simulated first order

      // Check if user has enough points
      if (pointsToApply > userPointsBalance) {
        console.log(`❌ Insufficient points: ${pointsToApply} > ${userPointsBalance}`);
        throw ApiError.badRequest("Insufficient points balance");
      }

      // Step 4: Apply special rules for referees
      // First & second orders for referees get only 50% value for points
      const discountMultiplier = isReferee && orderCount <= 2 ? 0.5 : 1.0;
      
      // Calculate the points discount amount
      const rawDiscountAmount = pointsToApply * POINTS_VALUE_PER_UNIT * discountMultiplier;
      
      // Step 5: Create the appliedPoints object
      const appliedPoints: AppliedPoints = {
        pointsUsed: pointsToApply,
        pointsValuePerUnit: POINTS_VALUE_PER_UNIT,
        discountAmount: rawDiscountAmount,
        refereeStatus: isReferee ? {
          isReferee,
          orderCount,
          discountMultiplier
        } : undefined
      };

      // Step 6: Calculate the effective discount (never more than remaining after coupon)
      // Get coupon discount if any
      let couponDiscount = 0;
      try {
        const couponInfo = session.couponDiscount as any;
        if (couponInfo && typeof couponInfo.discountAmount === 'number') {
          couponDiscount = couponInfo.discountAmount;
        }
      } catch (e) {
        console.log('⚠️ Error parsing coupon discount:', e);
      }
      
      // Ensure we don't exceed the remaining subtotal after coupon
      const maxAllowableDiscount = session.subtotal - couponDiscount;
      const effectiveDiscount = Math.min(rawDiscountAmount, maxAllowableDiscount);
      
      // Update the discount amount to the effective value
      appliedPoints.discountAmount = effectiveDiscount;

      // Step 7: Calculate new total
      let newTotal = session.subtotal + session.serviceFee + (session.tax || 0) - couponDiscount - effectiveDiscount;

      // Step 8: Update the session with the applied points
      console.log(`📝 Updating session with applied points: ${sessionId}`);
      
      const updatedSession = await prisma.checkoutSession.update({
        where: { id: sessionId },
        data: {
          appliedPoints: toPrismaJson(appliedPoints),
          total: newTotal,
          updatedAt: new Date(),
        },
      });
      
      console.log(`✅ Session updated with applied points: ${sessionId}`);

      return {
        success: true,
        message: "Points applied successfully",
        data: {
          success: true,
          session: updatedSession,
          userPointsBalance: userPointsBalance - pointsToApply
        }
      };
    } catch (error) {
      // If it's already an ApiError, rethrow it
      if (error instanceof ApiError) {
        throw error;
      }
      
      // Otherwise, log and convert to an ApiError
      console.error("💥 Error applying points:", error);
      throw ApiError.internal("Failed to apply points");
    }
  }

  /**
   * Remove applied points from a session
   * Used when pointsToApply = 0
   */
  private static async removeAppliedPoints(
    sessionId: string, 
    userId: string
  ) {
    try {
      // Step 1: Find the session
      const session = await prisma.checkoutSession.findUnique({
        where: { id: sessionId },
      });

      if (!session) {
        throw ApiError.notFound("Checkout session not found");
      }

      // Verify ownership
      if (session.userId !== userId) {
        throw ApiError.forbidden("You don't have permission for this session");
      }

      // If no points applied, return success with no changes
      const appliedPointsData = session.appliedPoints as any;
      if (!appliedPointsData) {
        return {
          success: true,
          message: "No points were applied to remove",
          data: {
            success: true,
            session,
            userPointsBalance: 2000 // Simulated balance, use real one in production
          }
        };
      }

      // Calculate new total (adding back any points discount)
      let pointsDiscount = 0;
      let pointsUsed = 0;
      
      try {
        if (appliedPointsData && appliedPointsData.discountAmount) {
          pointsDiscount = appliedPointsData.discountAmount;
        }
        if (appliedPointsData && appliedPointsData.pointsUsed) {
          pointsUsed = appliedPointsData.pointsUsed;
        }
      } catch (e) {
        console.log('⚠️ Error parsing applied points:', e);
      }

      const newTotal = session.total + pointsDiscount;

      // Update session to remove points
      const updatedSession = await prisma.checkoutSession.update({
        where: { id: sessionId },
        data: {
          appliedPoints: undefined,
          total: newTotal,
          updatedAt: new Date(),
        },
      });

      return {
        success: true,
        message: "Points removed successfully",
        data: {
          success: true,
          session: updatedSession,
          userPointsBalance: 2000 + pointsUsed // Add back the used points to the balance
        }
      };
    } catch (error) {
      // If it's already an ApiError, rethrow it
      if (error instanceof ApiError) {
        throw error;
      }
      
      // Otherwise, log and convert to an ApiError
      console.error("💥 Error removing points:", error);
      throw ApiError.internal("Failed to remove points");
    }
  }
}