// server/src/lib/ai/tools/search/searchTool.ts
// Define the search tool for making a server request to search for data using openctx

import { SearchService } from '@/features/search/services/searchService';
import { OpenCTXRequest } from '@/lib/openctx/types';
import { SearchTool } from '../types/tool.types';

// Define the search tool
export const searchTool: SearchTool = {
    description: 'Searches events based on the user query and optional filters.',
    parameters: {
        userQuery: 'string',
        openCTXFilters: 'any',
    },
    execute: async ({ userQuery, openCTXFilters }): Promise<OpenCTXRequest> => {
        try {
            // Make a call to the search service with the structured query
            const results = await SearchService.search({
                query: {
                    protocol: 'openctx/v1',
                    entity: 'events',
                    query: userQuery,
                    filters: openCTXFilters
                }
            });
             // Throw an error if there are no results
             if (!results|| results.length === 0) {
                    throw new Error('No results found matching your query.');
                }

                return {
                    protocol: 'openctx/v1',
                    entity: 'events',
                    query: userQuery,
                    filters: openCTXFilters
                };

        } catch (error: any) {
            console.error('Error in search tool:', error);
            throw new Error(`Error in search tool: ${error.message}`);
        }
    },
};
