// server/src/features/tickets/services/ticket.service.ts
// Service layer for handling visitor ticket logic

import { prisma } from "@/lib/prisma";
import ApiError from "@/utils/ApiError";
import { Prisma, CheckoutSessionStatus, CheckoutSession } from "@prisma/client";
import { VisitorTicketDTO, VisitorTicketsResponse, PurchasedTicketItem, TicketDownloadInfo } from "../types/ticket.types";

// Define a type for the CheckoutSession items JSON structure for better type safety
// Adjust this based on the actual structure stored in CheckoutSession.items
interface CheckoutSessionItem {
    inventoryId: string;
    quantity: number;
    name: string; // Assuming name is stored
    price: number;
    section?: string;
    row?: string | number;
    // add other fields if they exist in your JSON
}


export class TicketService {

    /**
     * Parses the items JSON from a CheckoutSession into PurchasedTicketItem[]
     * Includes basic error handling for JSON parsing.
     */
    private static parseCheckoutItems(itemsJson: Prisma.JsonValue): PurchasedTicketItem[] {
        try {
            // Validate that itemsJson is an array and not null/undefined
            if (!Array.isArray(itemsJson)) {
                 console.warn("🛒 Checkout items is not an array:", itemsJson);
                 return [];
            }
            // Assuming itemsJson is an array of CheckoutSessionItem objects
            const items = itemsJson as unknown as CheckoutSessionItem[];

            return items.map(item => ({
                inventoryId: item.inventoryId,
                quantity: item.quantity,
                name: item.name ?? `Item ${item.inventoryId}`, // Provide a fallback name
                price: item.price,
                section: item.section,
                row: item.row,
            }));
        } catch (error) {
            console.error("❌ Error parsing checkout session items:", error);
            return []; // Return empty array on parsing error
        }
    }

    /**
     * Fetches completed ticket purchases for a specific visitor with pagination.
     * @param userId - The ID of the visitor.
     * @param page - The current page number (1-based).
     * @param limit - The number of items per page.
     * @returns Promise<VisitorTicketsResponse>
     */
    static async getVisitorTickets(userId: string, page: number = 1, limit: number = 10): Promise<VisitorTicketsResponse> {
        console.log(`🎟️ [TicketService] Fetching tickets for user: ${userId}, Page: ${page}, Limit: ${limit}`);
        const skip = (page - 1) * limit;

        try {
            const whereCondition: Prisma.CheckoutSessionWhereInput = {
                userId: userId,
                status: CheckoutSessionStatus.COMPLETED,
                // Ensure items is not null/empty if critical? Maybe not needed if COMPLETED implies items.
            };

            const totalItems = await prisma.checkoutSession.count({
                where: whereCondition,
            });

            if (totalItems === 0) {
                 console.log(`ℹ️ [TicketService] No completed tickets found for user ${userId}.`);
                 return {
                    data: [],
                    pagination: { currentPage: 1, totalPages: 0, totalItems: 0, hasNextPage: false, hasPrevPage: false }
                 };
            }


            const sessions = await prisma.checkoutSession.findMany({
                where: whereCondition,
                include: {
                    // We need event details. Assuming eventId links to ManagerEvent for details
                    // Adjust this relation if eventId points elsewhere (e.g., a central Event table)
                    // Fetching ManagerEvent assumes the checkout session's eventId maps to ManagerEvent.id
                    // If checkoutSession.eventId refers to a *different* event identifier (like TmEvent.id),
                    // you'll need a different approach, possibly looking up ManagerEvent via that ID.
                    // For now, assuming checkoutSession.eventId === ManagerEvent.id (or a related PriorityEvent)
                    // Let's try fetching related ManagerEvent via eventId. This requires a relation exists or adjustment.
                    // TEMP: Need relation on CheckoutSession to ManagerEvent or query ManagerEvent separately.
                    // Let's fetch ManagerEvents separately for now based on eventIds from sessions.

                    // NOTE: Directly including related event details is more efficient if relations are set up.
                    // Example if relation existed:
                    // managerEvent: { select: { name: true, date: true, venue: true, city: true, country: true, image: true }}

                },
                orderBy: {
                    completedAt: 'desc', // Show most recent purchases first
                },
                skip: skip,
                take: limit,
            });

             // --- Fetch ManagerEvent details separately (Less efficient but works without direct relation) ---
             const eventIds = sessions.map(s => s.eventId).filter((id): id is string => id !== null); // Get unique, non-null event IDs
             const managerEvents = await prisma.managerEvent.findMany({
                 where: {
                     // Assuming checkoutSession.eventId matches ManagerEvent.id
                     // If it matches ManagerEvent.eventId (e.g., TM ID), use that instead.
                     id: { in: eventIds }
                    // OR: eventId: { in: eventIds } // if checkoutSession.eventId stores the *external* ID
                 },
                 select: {
                     id: true, // Match back to session.eventId
                    //  eventId: true, // The external ID if needed
                     name: true,
                     date: true,
                     venue: true,
                     city: true,
                     country: true,
                     image: true,
                 }
             });
             const managerEventMap = new Map(managerEvents.map(me => [me.id, me]));
             // --- End separate fetch ---


            const visitorTickets: VisitorTicketDTO[] = sessions.map(session => {
                const eventDetails = managerEventMap.get(session.eventId); // Find the matching event details

                return {
                    checkoutSessionId: session.id,
                    eventId: session.eventId, // or eventDetails?.eventId if that's the one needed
                    eventName: eventDetails?.name ?? "Unknown Event",
                    eventDate: eventDetails?.date.toISOString() ?? new Date(0).toISOString(),
                    eventVenue: eventDetails?.venue ?? "Unknown Venue",
                    eventCity: eventDetails?.city ?? "N/A",
                    eventCountry: eventDetails?.country ?? "N/A",
                    eventImageUrl: eventDetails?.image,
                    purchaseDate: session.completedAt?.toISOString() ?? session.createdAt.toISOString(), // Fallback to createdAt if completedAt is null
                    tickets: this.parseCheckoutItems(session.items),
                    totalAmount: session.total,
                    currency: session.currency,
                    status: session.status,
                    receiptUrl: session.receiptUrl,
                    downloadUrl: null, // Placeholder
                };
            });

            const totalPages = Math.ceil(totalItems / limit);

            return {
                data: visitorTickets,
                pagination: {
                    currentPage: page,
                    totalPages: totalPages,
                    totalItems: totalItems,
                    hasNextPage: page < totalPages,
                    hasPrevPage: page > 1,
                }
            };

        } catch (error) {
            console.error(`❌ [TicketService] Error fetching visitor tickets for user ${userId}:`, error);
            if (error instanceof ApiError) throw error;
            throw new ApiError(500, "Could not retrieve ticket purchase history.");
        }
    }

    /**
     * Gets information needed to download a ticket (e.g., future S3 link).
     * Verifies ownership and completion status.
     * @param userId - The ID of the user requesting the download.
     * @param checkoutSessionId - The ID of the purchase session.
     * @returns Promise<TicketDownloadInfo>
     */
    static async getTicketDownloadInfo(userId: string, checkoutSessionId: string): Promise<TicketDownloadInfo> {
         console.log(`ℹ️ [TicketService] Requesting download info for session ${checkoutSessionId} by user ${userId}`);
         try {
             const session = await prisma.checkoutSession.findUnique({
                 where: { id: checkoutSessionId },
                 select: { userId: true, status: true, eventId: true } // Select only necessary fields
             });

             if (!session) {
                 throw new ApiError(404, "Purchase session not found.");
             }

             if (session.userId !== userId) {
                 throw new ApiError(403, "You are not authorized to access this ticket.");
             }

             if (session.status !== CheckoutSessionStatus.COMPLETED) {
                 throw new ApiError(400, "Ticket purchase is not completed.");
             }

             // --- Future PDF/S3 Logic Placeholder ---
             // 1. Check if a PDF/link already exists (e.g., in a related model or field on CheckoutSession/ManagerEvent)
             // 2. If not, potentially trigger a generation process (maybe async)
             // 3. For now, just return a placeholder message/status.

             const downloadUrl = null; // Replace with actual logic later

             console.log(`✅ [TicketService] Download info access granted for session ${checkoutSessionId}.`);
             return {
                 success: true,
                 message: downloadUrl ? "Download link retrieved." : "Ticket verified. Download not yet available.",
                 downloadUrl: downloadUrl
             };

         } catch (error) {
             console.error(`❌ [TicketService] Error getting download info for session ${checkoutSessionId}:`, error);
             if (error instanceof ApiError) throw error;
             throw new ApiError(500, "Could not retrieve ticket download information.");
         }
    }
}