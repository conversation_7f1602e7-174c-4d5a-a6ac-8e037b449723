import { z } from 'zod';

// Event validation schema
export const eventSchema = z.object({
  title: z.string().min(3),
  description: z.string().min(10),
  date: z.string().or(z.date()),
  location: z.string(),
  timezone: z.string(),
  features: z.array(z.string()),
  category: z.array(z.string()),
  status: z.enum(['draft', 'published', 'cancelled']),
  isPopular: z.boolean().default(false),
});

export type CreateEventDTO = z.infer<typeof eventSchema>;
