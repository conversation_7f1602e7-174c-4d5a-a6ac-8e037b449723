'use client'

import { useSession } from "next-auth/react"
import { useState } from "react"

export default function AuthDebugPage() {
  const { data: session, status } = useSession()
  const [fetchResult, setFetchResult] = useState<any>(null)
  const [fetchError, setFetchError] = useState<string | null>(null)
  
  const testFetch = async () => {
    try {
      setFetchError(null)
      const res = await fetch('/api/authlogic/auth/session', {
        credentials: 'include'
      })
      const data = await res.text() // Use text() instead of json() to see raw response
      setFetchResult({
        status: res.status,
        headers: Object.fromEntries([...res.headers.entries()]),
        data
      })
    } catch (err: any) {
      setFetchError(err.message)
    }
  }
  
  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">NextAuth Debug Page</h1>
      
      <div className="p-4 bg-gray-100 rounded mb-6">
        <h2 className="text-xl font-semibold mb-2">Session Status</h2>
        <p>Status: <strong>{status}</strong></p>
        <pre className="mt-4 p-3 bg-black text-green-400 overflow-auto rounded">
          {JSON.stringify(session, null, 2)}
        </pre>
      </div>
      
      <div className="p-4 bg-gray-100 rounded mb-6">
        <h2 className="text-xl font-semibold mb-2">Manual Fetch Test</h2>
        <button 
          onClick={testFetch}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Test Session Endpoint
        </button>
        
        {fetchError && (
          <div className="mt-4 p-3 bg-red-100 text-red-800 rounded">
            <p>Error: {fetchError}</p>
          </div>
        )}
        
        {fetchResult && (
          <div className="mt-4">
            <p>Status: <strong>{fetchResult.status}</strong></p>
            <h3 className="font-semibold mt-2">Headers:</h3>
            <pre className="mt-2 p-3 bg-gray-200 overflow-auto rounded">
              {JSON.stringify(fetchResult.headers, null, 2)}
            </pre>
            <h3 className="font-semibold mt-2">Response:</h3>
            <pre className="mt-2 p-3 bg-black text-green-400 overflow-auto rounded">
              {fetchResult.data}
            </pre>
          </div>
        )}
      </div>
      
      <div className="p-4 bg-gray-100 rounded">
        <h2 className="text-xl font-semibold mb-2">Environment Info</h2>
        <pre className="mt-2 p-3 bg-gray-200 overflow-auto rounded">
          {JSON.stringify({
            host: typeof window !== 'undefined' ? window.location.host : null,
            pathname: typeof window !== 'undefined' ? window.location.pathname : null,
            protocol: typeof window !== 'undefined' ? window.location.protocol : null
          }, null, 2)}
        </pre>
      </div>
    </div>
  )
}