import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import Image from "next/image";
import { PriorityEventData } from "@/features/settings/components/PriorityEvents/types/priority-events.types";
import { EventPlaceholder } from "@/components/ui/icons/EventPlaceholder";
import { Calendar, MapPin, Clock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface EventCardProps {
  event: PriorityEventData;
  onEventClick?: (event: PriorityEventData) => void; // Make sure this is in the props
}

export const EventCard = ({ event, onEventClick }: EventCardProps): JSX.Element => {
  const eventDate = new Date(event.date);
  const formattedDate = eventDate.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
  });
  const formattedTime = eventDate.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
  });

  // Handle click event
  const handleClick = () => {
    if (onEventClick) {
      onEventClick(event);
    }
  };

  return (
    <motion.div
      className="w-full h-[450px] p-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      onClick={handleClick} // Add onClick handler to the entire card
    >
      <Card className="relative h-full group rounded-2xl overflow-hidden border-0 bg-white dark:bg-gray-900 shadow-xl hover:shadow-2xl transition-all duration-300">
        {/* Image Section - 60% height */}
        <div className="absolute top-0 left-0 right-0 h-[60%] overflow-hidden">
          {event.image ? (
            <div className="relative w-full h-full">
              <Image
                src={event.image}
                alt={event.name}
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                className="object-cover transition-transform duration-500 group-hover:scale-110"
                priority
              />
              <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white dark:to-gray-900" />
            </div>
          ) : (
            // In the fallback div where EventPlaceholder is used:
            <div className="w-full h-full bg-gradient-to-br from-purple-900 via-blue-900 to-violet-900 flex items-center justify-center">
              <div className="transform transition-transform duration-300 group-hover:scale-110">
                <EventPlaceholder />
              </div>
            </div>
          )}

          {/* Enhanced Category Badge */}
          <Badge
            variant="secondary"
            className="absolute top-4 left-4 bg-black/20 backdrop-blur-md text-white border border-white/20 px-3 py-1.5"
          >
            {event.category}
          </Badge>
        </div>

        {/* Content Section - Fixed height and alignment */}
        <div className="absolute bottom-0 left-0 right-0 h-[45%] bg-white dark:bg-gray-900 rounded-t-3xl">
          <div className="h-full flex flex-col p-6">
            {/* Main Content Area */}
            <div className="flex-1 space-y-3">
              {/* Date Badge */}
              <Badge
                variant="secondary"
                className="bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300"
              >
                {formattedDate} • {formattedTime}
              </Badge>

              {/* Title - Fixed height with ellipsis */}
              <h2 className="text-xl font-bold text-gray-900 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-300 line-clamp-2 h-[3rem]">
                {event.name}
              </h2>

              {/* Event Details */}
              <div className="text-sm text-gray-600 dark:text-gray-300">
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-purple-500" />
                  <span className="truncate">
                    {event.venue}, {event.city}
                  </span>
                </div>
              </div>
            </div>

            {/* Fixed Position Button Container */}
            <div className="absolute left-0 right-0 bottom-0 p-6 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300 ease-out bg-gradient-to-t from-white dark:from-gray-900">
              <Button
                className="w-full bg-purple-600 hover:bg-purple-700 text-white"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent double triggering
                  handleClick();
                }}
              >
                <span className="flex items-center justify-center gap-2">
                  Get Tickets
                  <span className="transform translate-x-2 opacity-0 group-hover:translate-x-0 group-hover:opacity-100 transition-all duration-300">
                    →
                  </span>
                </span>
              </Button>
            </div>
          </div>
        </div>
      </Card>
    </motion.div>
  );
};
