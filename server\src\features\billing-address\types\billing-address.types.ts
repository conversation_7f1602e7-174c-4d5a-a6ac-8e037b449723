// server/src/features/billing-address/types/billing-address.types.ts
import { BillingAddress as PrismaBillingAddress } from '@prisma/client';

export type BillingAddressDTO = Omit<PrismaBillingAddress, 'userId' | 'user'>;

export interface CreateBillingAddressPayload {
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  isDefault?: boolean;
}

export interface UpdateBillingAddressPayload {
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  isDefault?: boolean;
}


