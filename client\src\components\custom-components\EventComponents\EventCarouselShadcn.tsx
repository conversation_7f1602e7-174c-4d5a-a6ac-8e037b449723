/**
 * EventCarouselShadcn Component
 * Displays a carousel of events with optional autoplay and multiple items view.
 * While loading, it displays skeleton placeholders.
 * Once loading is complete, if no events are available, it displays a message box.
 */
import React, { useRef } from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import { PriorityEventData } from '@/features/settings/components/PriorityEvents/types/priority-events.types';
import { EventCard } from "./EventCard";
import { EventCardSkeleton } from "./EventCardSkeleton";

interface EventCarouselProps {
  title: string;
  showMultiple?: boolean;
  height?: string;
  autoplay?: boolean;
  events: PriorityEventData[];
  isLoading: boolean;
  onEventClick?: (event: PriorityEventData) => void;
}

export function EventCarouselShadcn({
  title,
  showMultiple = false,
  autoplay = false,
  events = [],
  isLoading,
  onEventClick
}: EventCarouselProps) {
  const plugin = useRef(Autoplay({ delay: 3000, stopOnInteraction: true }));
  const carouselPlugins = autoplay ? [plugin.current] : [];

  // Debugging output to help track the current state.
  console.log(`EventCarouselShadcn - Title: ${title}, showMultiple: ${showMultiple}, events count: ${events.length}, isLoading: ${isLoading}`);

  // Determine UI state:
  // 1. If still loading, show the skeleton placeholders.
  // 2. If loading is finished but no events are returned, show a message box.
  // 3. Otherwise, show the event cards.
  const renderContent = () => {
    if (isLoading) {
      // Loading state: show skeleton cards.
      return Array(6)
        .fill(0)
        .map((_, index) => (
          <CarouselItem
            key={index}
            className={showMultiple ? "sm:basis-full md:basis-1/2 lg:basis-1/3" : "basis-full"}
          >
            <EventCardSkeleton />
          </CarouselItem>
        ));
    } else if (events.length === 0) {
      // No data state: show a message box.
      return (
        <CarouselItem className="basis-full">
          <div className="flex items-center justify-center h-80">
            <div className="px-4 py-2 bg-red-100 text-red-600 rounded-md">
              No events found.
            </div>
          </div>
        </CarouselItem>
      );
    } else {
      // Data present: map events to Event Cards.
      return events.map((event, index) => (
        <CarouselItem key={event.id || index} className={showMultiple ? "md:basis-1/2 lg:basis-1/3" : ""}>
          <EventCard event={event} onEventClick={onEventClick} />
        </CarouselItem>
      ));
    }
  };

  return (
    <section className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 py-16 mt-12 rounded-xl">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-extrabold text-center mb-12 text-white tracking-tight">
          {title} {isLoading ? '(Loading...)' : events.length === 0 ? '(No events)' : '(Loaded)'}
        </h2>
        <Carousel
          plugins={carouselPlugins}
          className="w-full h-full"
          onMouseEnter={autoplay ? plugin.current.stop : undefined}
          onMouseLeave={autoplay ? plugin.current.reset : undefined}
        >
          <CarouselContent>
            {renderContent()}
          </CarouselContent>
          <CarouselPrevious className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/10 text-white hover:bg-white/20" />
          <CarouselNext className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/10 text-white hover:bg-white/20" />
        </Carousel>
      </div>
    </section>
  );
}
