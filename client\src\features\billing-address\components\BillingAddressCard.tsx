import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  MoreVertical, 
  MapPin, 
  Star, 
  Pencil, 
  Trash2,
  CheckCircle
} from 'lucide-react';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { BillingAddress } from '../types/billing-address.types';
import { formatCountryCode } from '@/utils/country-codes';

interface BillingAddressCardProps {
  address: BillingAddress;
  onEdit: (address: BillingAddress) => void;
  onDelete: (id: string) => void;
  onSetDefault: (id: string) => void;
}

export function BillingAddressCard({ 
  address, 
  onEdit, 
  onDelete, 
  onSetDefault 
}: BillingAddressCardProps) {
  return (
    <Card className="relative hover:shadow-md transition-shadow duration-200">
      <CardContent className="p-5 flex flex-col h-full">
        {/* Header with Default badge and actions */}
        <div className="flex items-center justify-between mb-3">
          {address.isDefault ? (
            <Badge className="bg-green-100 text-green-800 hover:bg-green-100 flex items-center gap-1">
              <CheckCircle className="h-3 w-3" />
              <span>Default</span>
            </Badge>
          ) : (
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-xs text-muted-foreground hover:text-primary"
              onClick={() => onSetDefault(address.id)}
            >
              <Star className="mr-1 h-3 w-3" />
              Set Default
            </Button>
          )}
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit(address)} className="cursor-pointer">
                <Pencil className="mr-2 h-4 w-4" />
                <span>Edit</span>
              </DropdownMenuItem>
              {!address.isDefault && (
                <DropdownMenuItem 
                  onClick={() => onDelete(address.id)}
                  className="text-red-600 hover:text-red-700 focus:text-red-600 cursor-pointer"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  <span>Delete</span>
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        
        {/* Address content */}
        <div className="flex-1">
          <div className="flex">
            <MapPin className="h-5 w-5 text-primary mr-2 flex-shrink-0 mt-0.5" />
            <div>
              {address.name && (
                <p className="font-medium text-foreground">{address.name}</p>
              )}
              {address.email && (
                <p className="text-sm text-muted-foreground mt-1">{address.email}</p>
              )}
              <p className="font-medium text-foreground mt-2">{address.addressLine1}</p>
              {address.addressLine2 && (
                <p className="text-sm text-muted-foreground mt-1">{address.addressLine2}</p>
              )}
              <p className="text-sm text-muted-foreground mt-2">
                {address.city}, {address.state} {address.postalCode}
              </p>
              <p className="text-sm text-muted-foreground mt-1">
                {formatCountryCode(address.country)}
              </p>
            </div>
          </div>
        </div>
        
        {/* Edit button at the bottom */}
        <div className="mt-4 pt-4 border-t">
          <Button 
            variant="outline" 
            size="sm" 
            className="w-full"
            onClick={() => onEdit(address)}
          >
            <Pencil className="mr-2 h-3.5 w-3.5" />
            Edit Address
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}