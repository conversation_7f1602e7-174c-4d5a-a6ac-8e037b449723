/*
  Warnings:

  - You are about to drop the column `name` on the `users` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[mobile]` on the table `users` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `mobile` to the `users` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "users" DROP COLUMN "name",
ADD COLUMN     "fullName" TEXT,
ADD COLUMN     "mobile" TEXT,
ADD COLUMN     "mobileVerified" TIMESTAMP(3);

-- CreateIndex
CREATE UNIQUE INDEX "users_mobile_key" ON "users"("mobile");

-- Update existing rows with a default value
UPDATE "users" SET "mobile" = '0000000000';

-- Then make the column required
ALTER TABLE "users" ALTER COLUMN "mobile" SET NOT NULL;
