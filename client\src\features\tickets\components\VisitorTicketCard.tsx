// Component for displaying a single ticket/purchase

import { useState } from 'react';
import { 
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { CalendarIcon, MapPin, Download, Receipt, MessageCircle, Clock, Ticket } from "lucide-react";
import { VisitorTicketDTO, CheckoutSessionStatus } from "../types/ticket.types";
import { formatCurrency } from "@/utils/format";
import { format } from "date-fns";
import { TicketMessageModal } from './TicketMessageModal';

interface VisitorTicketCardProps {
  ticket: VisitorTicketDTO;
  onDownloadRequest: (sessionId: string) => Promise<any>;
  isDownloading: boolean;
}

export function VisitorTicketCard({ 
  ticket, 
  onDownloadRequest,
  isDownloading 
}: VisitorTicketCardProps) {
  const [isRequestingDownload, setIsRequestingDownload] = useState(false);
  const [messageModalOpen, setMessageModalOpen] = useState(false);

  // Format the event date
  const formatEventDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'EEEE, MMMM d, yyyy'); // e.g., "Monday, June 15, 2023"
    } catch (error) {
      return 'Invalid Date';
    }
  };

  // Format the purchase date
  const formatPurchaseDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'PPpp'); // e.g., "June 15, 2023, 3:30 PM"
    } catch (error) {
      return 'Invalid Date';
    }
  };

  // Get status badge color
  const getStatusColor = (status: CheckoutSessionStatus) => {
    switch (status) {
      case CheckoutSessionStatus.COMPLETED:
        return "bg-green-100 text-green-800";
      case CheckoutSessionStatus.PENDING:
        return "bg-yellow-100 text-yellow-800";
      case CheckoutSessionStatus.CANCELLED:
      case CheckoutSessionStatus.FAILED:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Handle download request
  const handleDownload = async () => {
    setIsRequestingDownload(true);
    try {
      await onDownloadRequest(ticket.checkoutSessionId);
    } finally {
      setIsRequestingDownload(false);
    }
  };

  return (
    <Card className="w-full overflow-hidden transition-all hover:shadow-md">
      <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 pb-8 relative">
        {/* <div className="absolute top-3 right-3">
          <Badge className={getStatusColor(ticket.status)}>
            {ticket.status}
          </Badge>
        </div> */}
        <CardTitle className="text-xl font-bold truncate">
          {ticket.eventName}
        </CardTitle>
        <div className="flex flex-col space-y-1 text-gray-500 text-sm">
          <div className="flex items-center">
            <CalendarIcon className="h-4 w-4 mr-2" />
            <span>{formatEventDate(ticket.eventDate)}</span>
          </div>
          <div className="flex items-center">
            <MapPin className="h-4 w-4 mr-2" />
            <span>{ticket.eventVenue}, {ticket.eventCity}</span>
          </div>
          <div className="flex items-center">
            <Clock className="h-4 w-4 mr-2" />
            <span>Purchased: {formatPurchaseDate(ticket.purchaseDate)}</span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Ticket className="h-5 w-5 text-blue-500" />
            <span className="font-medium">
              {ticket.tickets.reduce((sum, item) => sum + item.quantity, 0)} Tickets
            </span>
          </div>
          <div className="font-bold text-lg">
            {formatCurrency(ticket.totalAmount, ticket.currency)}
          </div>
        </div>

        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="items">
            <AccordionTrigger className="py-2 text-sm font-medium">
              View Ticket Details
            </AccordionTrigger>
            <AccordionContent>
              <div className="space-y-3 text-sm">
                {ticket.tickets.map((item, index) => (
                  <div key={index} className="border-b pb-2 last:border-0">
                    <div className="flex justify-between">
                      <span className="font-medium">{item.name}</span>
                      <span>{item.quantity}x</span>
                    </div>
                    {(item.section || item.row) && (
                      <div className="text-gray-500 mt-1">
                        {item.section && `Section: ${item.section}`}
                        {item.section && item.row && " • "}
                        {item.row && `Row: ${item.row}`}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </CardContent>

      <CardFooter className="flex flex-col border-t pt-4 bg-gray-50">
        <div className="flex justify-between w-full">
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open(ticket.receiptUrl || "#", "_blank")}
            disabled={!ticket.receiptUrl}
          >
            <Receipt className="h-4 w-4 mr-2" />
            Receipt
          </Button>

          <Button
            variant="default"
            size="sm"
            onClick={handleDownload}
            disabled={isRequestingDownload || isDownloading}
          >
            <Download className="h-4 w-4 mr-2" />
            {isRequestingDownload || isDownloading ? "Processing..." : "Download"}
          </Button>
        </div>
        
        {/* Contact Manager Button */}
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => setMessageModalOpen(true)}
          className="mt-3 w-full bg-gray-100 hover:bg-gray-200 border-gray-300 text-gray-700"
        >
          <MessageCircle className="h-4 w-4 mr-2" />
          Contact Ticket Seller
        </Button>
      </CardFooter>
      
      {/* Ticket Message Modal */}
      <TicketMessageModal
        open={messageModalOpen}
        onOpenChange={setMessageModalOpen}
        ticketId={ticket.checkoutSessionId}
        eventName={ticket.eventName}
      />
    </Card>
  );
}
