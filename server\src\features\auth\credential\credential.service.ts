import { PrismaClient, User, UserRole } from "@prisma/client";
import bcrypt from "bcrypt";
import { RegisterDTO, LoginDTO } from "./credential.types";
// import jwt from 'jsonwebtoken'
// import { generateToken } from '../config/jwt.config'
import crypto from "crypto";
import { EmailService } from "../../email/services/email.service";
import { TokenUtils } from "../../../utils/token.utils";

const prisma = new PrismaClient();

export class AuthService {
  async createUser({
    email,
    password,
    role,
    fullName,
    mobile,
    geoData,
  }: RegisterDTO): Promise<User> {
    try {
      const existingUser = await prisma.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        throw new Error("User already exists");
      }

      const hashedPassword = await bcrypt.hash(password, 10);

      return await prisma.user.create({
        data: {
          email,
          password: hashedPassword,
          role: role as UserRole,
          fullName,
          mobile,
          userMetadata: {
            create: {
              ip: geoData?.ip || "",
              city: geoData?.city || "",
              country: geoData?.country || "",
              timezone: geoData?.timezone || "",
              latitude: geoData?.latitude || null,
              longitude: geoData?.longitude || null,
              registrationDate: new Date(),
            },
          },
        },
        include: {
          userMetadata: true,
        },
      });
    } catch (error) {
      console.error("Create user error:", error);
      throw error;
    }
  }

  async validateUser(email: string, password: string): Promise<User> {
    const user = await prisma.user.findUnique({ where: { email } });
    if (!user) {
      throw new Error("Invalid credentials");
    }

    // Add null check for password
    if (!user.password) {
      throw new Error("This account uses OAuth authentication");
    }

    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      console.log("password validation failed : backend bcrypt");
      throw new Error("Invalid credentials");
    }

    return user;
  }

  async initiatePasswordReset(email: string) {
    // Add debugging
    console.log("Initiating password reset for email:", email);

    const user = await prisma.user.findUnique({
      where: { email },
    });

    console.log("User found:", user?.id);
    if (!user) throw new Error("User not found");

    const resetToken = crypto.randomBytes(32).toString("hex");
    const hashedToken = crypto
      .createHash("sha256")
      .update(resetToken)
      .digest("hex");

    await prisma.user.update({
      where: { id: user.id },
      data: {
        // the keys must  be in prisma schema  of user model
        resetPasswordToken: hashedToken,
        resetPasswordExpires: new Date(Date.now() + 1200000), // 20 minutes
      },
    });

    const fullName = user.fullName || "User";
    await EmailService.sendPasswordReset(email, resetToken, fullName);
    return true;
  }
  /**
   * Verifies if a password reset token is valid and not expired
   * @param token - The reset token to verify
   * @returns {Promise<boolean>} Token validity
   */
  async verifyResetToken(token: string): Promise<boolean> {
    console.log("Verifying token on server:", token);

    const hashedToken = crypto.createHash("sha256").update(token).digest("hex");

    console.log("Hashed token:", hashedToken);

    const user = await prisma.user.findFirst({
      where: {
        resetPasswordToken: hashedToken,
        resetPasswordExpires: {
          gt: new Date(),
        },
      },
    });

    console.log("User found:", user?.id);
    return !!user;
  }
  /**
   * Resets user's password using a valid reset token
   * @param token - Reset token
   * @param newPassword - New password to set
   */
  async resetPassword(token: string, newPassword: string): Promise<void> {
    const hashedToken = crypto.createHash("sha256").update(token).digest("hex");

    const user = await prisma.user.findFirst({
      where: {
        resetPasswordToken: hashedToken,
        resetPasswordExpires: {
          gt: new Date(),
        },
      },
    });

    if (!user) {
      throw new Error("Invalid or expired reset token");
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    // Update password and clear reset token
    await prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        resetPasswordToken: null,
        resetPasswordExpires: null,
      },
    });

    // Optionally send password change confirmation email
    // await EmailService.sendPasswordChangeConfirmation(user.email);
  }
}
