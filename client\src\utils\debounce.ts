// Utility function for debouncing asynchronous function calls
export function debounce<T extends (...args: any[]) => Promise<void>>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null;

  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(async () => {
      try {
        // Execute the debounced function asynchronously
        await func(...args);
      } catch (error) {
        // Handle any errors that occur during execution
        console.error('Debounced function error:', error);
      }
      timeoutId = null;
    }, delay);
  };
}
