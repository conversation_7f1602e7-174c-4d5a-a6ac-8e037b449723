/*
  Warnings:

  - A unique constraint covering the columns `[stripeCustomerId]` on the table `users` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateEnum
CREATE TYPE "PaymentProcessor" AS ENUM ('STRIPE', 'PAYPAL');

-- <PERSON>reate<PERSON>num
CREATE TYPE "PaymentRecordStatus" AS ENUM ('SUCCEEDED', 'FAILED', 'REFUNDED', 'PARTIALLY_REFUNDED', 'CANCELED');

-- AlterTable
ALTER TABLE "users" ADD COLUMN     "stripeCustomerId" TEXT;

-- CreateTable
CREATE TABLE "payment_records" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "checkoutSessionId" TEXT,
    "processor" "PaymentProcessor" NOT NULL,
    "processorPaymentId" TEXT NOT NULL,
    "status" "PaymentRecordStatus" NOT NULL,
    "amount" INTEGER NOT NULL,
    "currency" TEXT NOT NULL,
    "paymentMethodDetails" TEXT,
    "description" TEXT,
    "processedAt" TIMESTAMP(3) NOT NULL,
    "refundedAmount" INTEGER,
    "refundedAt" TIMESTAMP(3),
    "metadata" JSONB,

    CONSTRAINT "payment_records_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "payment_records_userId_processedAt_idx" ON "payment_records"("userId", "processedAt");

-- CreateIndex
CREATE INDEX "payment_records_checkoutSessionId_idx" ON "payment_records"("checkoutSessionId");

-- CreateIndex
CREATE INDEX "payment_records_processor_processorPaymentId_idx" ON "payment_records"("processor", "processorPaymentId");

-- CreateIndex
CREATE UNIQUE INDEX "users_stripeCustomerId_key" ON "users"("stripeCustomerId");

-- AddForeignKey
ALTER TABLE "payment_records" ADD CONSTRAINT "payment_records_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payment_records" ADD CONSTRAINT "payment_records_checkoutSessionId_fkey" FOREIGN KEY ("checkoutSessionId") REFERENCES "CheckoutSession"("id") ON DELETE SET NULL ON UPDATE CASCADE;
