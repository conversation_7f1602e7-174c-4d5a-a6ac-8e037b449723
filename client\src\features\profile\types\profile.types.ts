/**
 * Profile Feature Type Definitions
 * 
 * These types define the shape of data and props used across the profile feature.
 * Centralizing types ensures consistency and helps with TypeScript inference.
 */

import { User } from "next-auth";

// Extended user profile information
export interface ProfileData extends User {
  joinedDate?: string;
  role: 'VISITOR' | 'MANAGER' | 'ADMIN';
  fullName: string;
  bio?: string;
  location?: string;
  headline?: string;
  mobile?: string;
  website?: string;
  company?: string;
  jobTitle?: string;
  industry?: string;
  token: string;
  
  // Added fields to match database schema
  phoneVisible?: boolean;
  displayEmail?: boolean;
  theme?: 'light' | 'dark' | 'system';
  
  socialLinks?: {
    twitter?: string;
    linkedin?: string;
    github?: string;
    facebook?: string; // Added to match schema
    instagram?: string; // Added to match schema
    website?: string;
  };
  skills?: string[];
  stats?: {
    eventsAttended?: number;
    eventsManaged?: number;
    followers?: number;
    following?: number;
  };
  emailVerified?: Date | null;
  mobileVerified?: Date | null;
  
  pointsBalance: number | null;
}

// Common props for profile components
export interface ProfileComponentProps {
  profile: ProfileData;
  isEditable?: boolean;
  isLoading?: boolean;
}

// Profile action types
export type ProfileAction = 
  | 'edit'
  | 'follow'
  | 'message'
  | 'share'
  | 'report';

// Theme options for profile layouts
export type ProfileTheme = 'light' | 'dark' | 'system';
