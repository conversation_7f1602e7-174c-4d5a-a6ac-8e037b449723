import { DateTime } from 'luxon';

/**
 * Determines if an event has already expired (is in the past)
 */
export const isEventExpired = (eventDate: string | Date | null | undefined): boolean => {
  if (!eventDate) return true;
  
  const now = DateTime.now();
  const eventDateTime = eventDate instanceof Date 
    ? DateTime.fromJSDate(eventDate)
    : DateTime.fromISO(String(eventDate));
  
  if (!eventDateTime.isValid) return true;
  
  // Event is expired if it's before the current time
  return eventDateTime < now;
};

/**
 * Determines if an event is too close to starting time (< 24 hours)
 * We want to remove these to avoid ticket distribution issues
 */
export const isEventTooSoon = (eventDate: string | Date | null | undefined): boolean => {
  if (!eventDate) return false;
  
  const now = DateTime.now();
  const eventDateTime = eventDate instanceof Date 
    ? DateTime.fromJSDate(eventDate)
    : DateTime.fromISO(String(eventDate));
  
  if (!eventDateTime.isValid) return false;
  
  // Calculate hours until event
  const hoursUntil = eventDateTime.diff(now, 'hours').hours;
  
  // Event is too soon if it's less than 24 hours away but still in the future
  return hoursUntil >= 0 && hoursUntil < 24;
};

/**
 * Determines if an event should be displayed based on our business rules:
 * - Not expired
 * - Not happening within 24 hours
 */
export const shouldDisplayEvent = (eventDate: string | Date | null | undefined): boolean => {
  // Don't show if expired or too soon
  return !isEventExpired(eventDate) && !isEventTooSoon(eventDate);
};

/**
 * Filter function that can be applied to any array of events
 * Works with unified events, TM events, and any object with date fields
 */
export const filterDisplayableEvents = <T extends { 
  date?: string | Date | null | undefined, 
  startDateTime?: string | Date | null | undefined 
}>(events: T[]): T[] => {
  return events.filter(event => {
    // Handle different date field names across event types
    const eventDate = event.date || event.startDateTime;
    return shouldDisplayEvent(eventDate);
  });
};

/**
 * Format date for display
 */
export const formatEventDate = (date: string | Date | null): string => {
  if (!date) return 'Date not available';
  
  try {
    return DateTime.fromJSDate(new Date(date)).toFormat('EEE, MMM d, yyyy h:mm a');
  } catch (error) {
    return 'Invalid date';
  }
};
