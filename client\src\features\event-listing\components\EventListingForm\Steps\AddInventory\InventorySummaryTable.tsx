"use client";
import React from "react";
import { InventoryItem } from "../../../../types/eventListing";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useEventListing } from "../../../EventListingForm/EventListingContext";
import { Button } from "@/components/ui/button";

// Component to display a summary table of inventory items
export const InventorySummaryTable: React.FC<{ inventory: InventoryItem[] }> = ({
  inventory,
}) => {
  const { setEventListingData, goToStep } = useEventListing();

  // Handler to edit an inventory item
  const handleEdit = (item: InventoryItem) => {
    setEventListingData((prevData) => ({
      ...prevData,
      tempInventoryItem: item,
    }));
    goToStep(2);
  };

  // Handler to delete an inventory item
  const handleDelete = (itemId: string) => {
    setEventListingData((prevData) => ({
      ...prevData,
      inventory: prevData.inventory.filter((item) => item.id !== itemId),
    }));
  };

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Qty</TableHead>
          <TableHead>Section/Row</TableHead>
          <TableHead>Seats</TableHead>
          <TableHead>List Price</TableHead>
          <TableHead>Total</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {inventory.map((item) => (
          <TableRow key={item.id}>
            <TableCell>{item.quantity}</TableCell>
            <TableCell>{`${item.section} / ${item.row}`}</TableCell>
            <TableCell>
              {item.seatingType === "GA"
                ? "GA"
                : item.lowSeatNumber
                ? `${item.lowSeatNumber}${
                    item.seatingType === "Consecutive"
                      ? ` - ${item.lowSeatNumber + item.quantity - 1}`
                      : ""
                  }`
                : "N/A"}
            </TableCell>
            <TableCell>${item.listPrice}</TableCell>
            <TableCell>
              ${item.listPrice * item.quantity + (item.serviceFee ? item.serviceFee * item.quantity : 0)}
            </TableCell>
            <TableCell>
              <div className="flex gap-2">
                <Button onClick={() => handleEdit(item)} variant="outline">Edit</Button>
                <Button onClick={() => handleDelete(item.id)} variant="destructive">Delete</Button>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};
