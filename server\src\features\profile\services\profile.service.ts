/**
 * Profile Service
 * 
 * Contains business logic for profile operations with proper error handling
 */
import { PrismaClient, Profile,User } from '@prisma/client';
import ApiError from '@/utils/ApiError';
import { ProfileResponseDTO, ProfileUpdateDTO } from '../types/profile.types';

const prisma = new PrismaClient();

export class ProfileService {
  /**
   * Get user profile data - creates a profile if none exists
   */
  /**
   * Get user profile data - creates a profile if none exists
   */
  static async getUserProfile(userId: string): Promise<ProfileResponseDTO> {
    try {
      // First try to find an existing profile with linked user details
      const profile = await prisma.profile.findUnique({
        where: { userId },
        include: {
          user: {
            select: {
              fullName: true,
              mobile: true,
              email: true,
              role: true,
              emailVerified: true,
              mobileVerified: true,
              isActive: true,
              lastLogin: true,
              image: true
            }
          }
        }
      });
      
      // If no profile exists, create a default one
      if (!profile) {
        console.log(`🆕 Creating new profile for user: ${userId}`);
        const newProfile = await prisma.profile.create({
          data: { userId },
          include: {
            user: {
              select: {
                fullName: true,
                mobile: true,
                email: true,
                role: true,
                emailVerified: true,
                mobileVerified: true,
                isActive: true,
                lastLogin: true,
                image: true
              }
            }
          }
        });
        
        // Transform to DTO
        return this.mapToProfileDTO(newProfile);
      }
      
      console.log(`✅ Profile found for user: ${userId}`);
      return this.mapToProfileDTO(profile);
    } catch (error) {
      console.error('❌ Error retrieving profile:', error);
      throw ApiError.internal('Failed to retrieve user profile');
    }
  }

 /**
 * Utility method to map Prisma Profile+User data to ProfileResponseDTO
 */
private static mapToProfileDTO(profileWithUser: Profile & { user: any }): ProfileResponseDTO {
  const { user, twitter, linkedin, facebook, instagram, github, ...profileData } = profileWithUser;
  
  // Return formatted DTO with fields from both profile and user
  // Reconstruct the socialLinks object from individual fields
  return {
    ...profileData,
    fullName: user?.fullName || null,
    email: user?.email || null,
    mobile: user?.mobile || null,
    role: user?.role || null,
    emailVerified: user?.emailVerified || null,
    mobileVerified: user?.mobileVerified || null,
    isActive: user?.isActive || false,
    lastLogin: user?.lastLogin || null,
    image: user?.image || null,
    // Create socialLinks object from individual fields
    socialLinks: {
      twitter: twitter || undefined,
      linkedin: linkedin || undefined,
      facebook: facebook || undefined,
      instagram: instagram || undefined,
      github: github || undefined
    }
  };
}

 /**
 * Update user profile data with proper error handling
 */
// static async updateProfile(userId: string, profileData: ProfileUpdateDTO): Promise<ProfileResponseDTO> {
//   try {
//     // Extract User-specific fields and Profile-specific fields
//     const { fullName, mobile, ...profileFields } = profileData;
    
//     // Begin a transaction to update both tables
//     const result = await prisma.$transaction(async (tx) => {
//       // 1. Update the User table if we have user fields
//       if (fullName !== undefined || mobile !== undefined) {
//         await tx.user.update({
//           where: { id: userId },
//           data: {
//             ...(fullName !== undefined ? { fullName } : {}),
//             ...(mobile !== undefined ? { mobile } : {})
//           }
//         });
//       }
      
//       // 2. Update or create the Profile
//       const updatedProfile = await tx.profile.upsert({
//         where: { userId },
//         update: {
//           ...profileFields,
//           updatedAt: new Date()
//         },
//         create: {
//           userId,
//           ...profileFields
//         },
//         include: {
//           user: {
//             select: {
//               fullName: true,
//               mobile: true,
//               email: true,
//               role: true,
//               emailVerified: true,
//               mobileVerified: true,
//               isActive: true,
//               lastLogin: true,
//               image: true
//             }
//           }
//         }
//       });
      
//       return updatedProfile;
//     });
    
//     // Map to DTO and return
//     return this.mapToProfileDTO(result);
//   } catch (error) {
//     console.error('❌ Error updating profile:', error);
    
//     if (error instanceof ApiError) {
//       throw error;
//     }
    
//     throw ApiError.internal('Failed to update profile');
//   }
// }
/**
 * Update user profile data with proper error handling
 */
static async updateProfile(userId: string, profileData: ProfileUpdateDTO): Promise<ProfileResponseDTO> {
  try {
    // Extract User-specific fields, Social Links, and Profile-specific fields
    const { fullName, mobile, socialLinks, ...profileFields } = profileData;
    
    // Begin a transaction to update both tables
    const result = await prisma.$transaction(async (tx) => {
      // 1. Update the User table if we have user fields
      if (fullName !== undefined || mobile !== undefined) {
        await tx.user.update({
          where: { id: userId },
          data: {
            ...(fullName !== undefined ? { fullName } : {}),
            ...(mobile !== undefined ? { mobile } : {})
          }
        });
      }
      
      // 2. Update or create the Profile
      const updatedProfile = await tx.profile.upsert({
        where: { userId },
        update: {
          ...profileFields,
          // Spread out individual social links instead of using socialLinks object
          ...(socialLinks?.twitter !== undefined ? { twitter: socialLinks.twitter } : {}),
          ...(socialLinks?.linkedin !== undefined ? { linkedin: socialLinks.linkedin } : {}),
          ...(socialLinks?.facebook !== undefined ? { facebook: socialLinks.facebook } : {}),
          ...(socialLinks?.instagram !== undefined ? { instagram: socialLinks.instagram } : {}),
          ...(socialLinks?.github !== undefined ? { github: socialLinks.github } : {}),
          updatedAt: new Date()
        },
        create: {
          userId,
          ...profileFields,
          // Spread out individual social links for creation as well
          twitter: socialLinks?.twitter || null,
          linkedin: socialLinks?.linkedin || null,
          facebook: socialLinks?.facebook || null,
          instagram: socialLinks?.instagram || null,
          github: socialLinks?.github || null,
          phoneVisible: false, // Default values for required fields
          displayEmail: false
        },
        include: {
          user: {
            select: {
              fullName: true,
              mobile: true,
              email: true,
              role: true,
              emailVerified: true,
              mobileVerified: true,
              isActive: true,
              lastLogin: true,
              image: true
            }
          }
        }
      });
      
      return updatedProfile;
    });
    
    // Need to modify mapToProfileDTO to reconstruct socialLinks object
    return this.mapToProfileDTO(result);
  } catch (error) {
    console.error('❌ Error updating profile:', error);
    
    if (error instanceof ApiError) {
      throw error;
    }
    
    throw ApiError.internal('Failed to update profile');
  }
}


  /**
   * Update user social links with proper validation
   */
  static async updateSocialLinks(
    userId: string, 
    socialLinks: { 
      twitter?: string; 
      linkedin?: string; 
      facebook?: string; 
      instagram?: string; 
      github?: string;
      website?: string;
    }
  ): Promise<Profile> {
    try {
      // Validate that the user exists
      const userExists = await prisma.user.findUnique({ 
        where: { id: userId } 
      });
      
      if (!userExists) {
        throw ApiError.notFound('User not found');
      }
      
      // Update social links
      return await prisma.profile.upsert({
        where: { userId },
        update: socialLinks,
        create: {
          userId,
          ...socialLinks
        }
      });
    } catch (error) {
      console.error('❌ Error updating social links:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw ApiError.internal('Failed to update social links');
    }
  }
}
