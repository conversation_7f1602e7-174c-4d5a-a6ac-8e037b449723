import { Request, Response, NextFunction } from "express";
import { UserRole } from "@prisma/client";
import { verifyToken } from "../config/jwt.config";
import { TokenPayload } from "../features/auth/credential/credential.types";

export const checkRole = (allowedRoles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const authHeader = req.headers.authorization;
      if (!authHeader) {
        return res.status(401).json({ message: "No token provided" });
      }

      const token = authHeader.split(" ")[1];
      const decoded = verifyToken(token) as TokenPayload;

      if (!allowedRoles.includes(decoded.role)) {
        return res.status(403).json({ message: "Insufficient permissions" });
      }

      // Attach user to request for further use
      (req as any).user = decoded;
      next();
    } catch (error) {
      return res.status(401).json({
        message: error instanceof Error ? error.message : "Invalid token",
      });
    }
  };
};
