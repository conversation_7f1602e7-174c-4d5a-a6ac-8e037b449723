/**
 * Configuration constants for the Demand Detection and Queue System.
 * Values are hardcoded here for simplicity but could be moved to environment variables.
 */

/**
 * The calculated demand score threshold above which the waiting room queue
 * for an event will be automatically activated.
 * Example: A score > 5 triggers queue activation.
 */
export const QUEUE_ACTIVATION_THRESHOLD: number = 5;

/**
 * The calculated demand score threshold below which an active waiting room queue
 * will be considered for deactivation (subject to minimum active duration).
 * This should be lower than ACTIVATION_THRESHOLD to prevent rapid flapping.
 * Example: A score < 2 triggers queue deactivation.
 */
export const QUEUE_DEACTIVATION_THRESHOLD: number = 2;

/**
 * A weighting factor applied to checkout attempts when calculating the demand score.
 * This makes checkout attempts contribute more significantly to the score than simple views.
 * Example: A weight of 3 means one checkout attempt has the same score impact as 3 views.
 */
export const QUEUE_CHECKOUT_WEIGHT: number = 3;

/**
 * The duration (in seconds) for which Redis view and checkout counters are considered valid.
 * After this time, the counters automatically expire, representing activity within this window.
 * Example: Counters expire after 60 seconds.
 */
export const QUEUE_COUNTER_EXPIRY_SECONDS: number = 60;

/**
 * The minimum duration (in seconds) a queue must remain active once activated,
 * before it can be considered for automatic deactivation, even if demand drops
 * below the deactivation threshold. Prevents rapid on/off switching.
 * Set to 0 to disable this minimum duration.
 * Example: Queue stays active for at least 300 seconds (5 minutes).
 */
export const QUEUE_MIN_ACTIVE_SECONDS: number = 300;

// Optional: Log the configuration during development for verification
if (process.env.NODE_ENV === 'development') {
    console.log('⚙️ Queue Demand Configuration Loaded (Hardcoded):', {
        QUEUE_ACTIVATION_THRESHOLD,
        QUEUE_DEACTIVATION_THRESHOLD,
        QUEUE_CHECKOUT_WEIGHT,
        QUEUE_COUNTER_EXPIRY_SECONDS,
        QUEUE_MIN_ACTIVE_SECONDS,
    });
}
