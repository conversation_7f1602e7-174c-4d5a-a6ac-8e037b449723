import { PrismaClient } from '@prisma/client';
import { CreateEventDTO } from './event.types';
import { logger } from '../../utils/logger';

const prisma = new PrismaClient();

export const createEvent = async (eventData: CreateEventDTO) => {
    try {
        logger.info('Creating new event', { eventData });
        
        const event = await prisma.event.create({
            data: {
                ...eventData,
                date: new Date(eventData.date),
                createdAt: new Date(),
            }
        });
        
        logger.info('Event created successfully', { eventId: event.id });
        return event;
    } catch (error) {
        logger.error('Error creating event', { error, eventData });
        throw error;
    }
};
