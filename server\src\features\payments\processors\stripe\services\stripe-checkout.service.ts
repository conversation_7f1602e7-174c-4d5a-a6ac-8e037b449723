// // <PERSON>les creation of Stripe Payment Intents for checkout sessions.
// import <PERSON><PERSON> from "stripe";
// import { CheckoutSessionStatus, Prisma } from "@prisma/client";
// import ApiError from "@/utils/ApiError";
// import { prisma } from "@/lib/prisma";
// import { StripeBaseService } from "./stripe-base.service"; // Import base service
// import { StripePaymentIntentRequest, StripePaymentIntentResponse } from "../types/stripe-payments.types";
// import { SupportedCurrency } from "../../../common/types/payment-common.types";

// export class StripeCheckoutService {

//     /**
//      * Creates a Stripe PaymentIntent based on an application checkout session.
//      */
//     static async createPaymentIntentFromCheckoutSession(
//         sessionId: string,
//         userId: string,
//         options: Partial<StripePaymentIntentRequest> = {}
//     ): Promise<StripePaymentIntentResponse> {
//         const stripe = StripeBaseService.getStripeClient(); // Get initialized client
//         try {
//             const checkoutSession = await prisma.checkoutSession.findUnique({
//                 where: { id: sessionId },
//                 include: { user: { select: { email: true } } },
//             });

//             if (!checkoutSession) throw new ApiError(404, `Checkout session not found: ${sessionId}`);
//             if (checkoutSession.userId !== userId) throw new ApiError(403, "Forbidden access to checkout session");
//             if (checkoutSession.status !== CheckoutSessionStatus.RESERVED) { // Use Prisma enum
//                 throw new ApiError(400, `Cannot process payment for status: ${checkoutSession.status}`);
//             }

//             console.log(`💰 Creating payment intent for checkout session: ${sessionId}`);
//             const customerEmail = checkoutSession.user?.email;
//             if (!customerEmail) throw new ApiError(400, "User email required for payment");

//             // Use Base Service for customer handling
//             const stripeCustomerId = await StripeBaseService.getOrCreateStripeCustomer(userId, customerEmail);
//             const amountInCents = Math.round(checkoutSession.total * 100);

//             const paymentIntentParams: Stripe.PaymentIntentCreateParams = {
//                 amount: amountInCents,
//                 currency: (checkoutSession.currency || "USD").toLowerCase(),
//                 customer: stripeCustomerId,
//                 setup_future_usage: options.setupFutureUsage || "on_session",
//                 payment_method_types: options.paymentMethodTypes || ["card"],
//                 metadata: {
//                     userId,
//                     checkoutSessionId: sessionId,
//                     ...(options.metadata || {}),
//                 },
//                 receipt_email: options.receiptEmail || customerEmail,
//                 description: options.description || `Purchase for Event ID: ${checkoutSession.eventId}`, // Corrected typo
//             };
//             if (options.statementDescriptor) paymentIntentParams.statement_descriptor = options.statementDescriptor.substring(0, 22);
//             if (options.shipping) paymentIntentParams.shipping = options.shipping;

//             const paymentIntent = await stripe.paymentIntents.create(paymentIntentParams);

//             await prisma.checkoutSession.update({
//                 where: { id: sessionId },
//                 data: { paymentIntentId: paymentIntent.id },
//             });
//             console.log(`✅ Created payment intent ${paymentIntent.id} for session ${sessionId}`);

//             return {
//                 success: true,
//                 clientSecret: paymentIntent.client_secret || undefined,
//                 paymentIntentId: paymentIntent.id,
//                 amount: paymentIntent.amount,
//                 currency: paymentIntent.currency.toUpperCase() as SupportedCurrency,
//                 status: paymentIntent.status,
//             };
//         } catch (error) {
//             console.error(`❌ Error creating payment intent for session ${sessionId}:`, error);
//             if (error instanceof ApiError) throw error;
//             if (error instanceof Stripe.errors.StripeError) {
//                 return { success: false, error: { message: error.message, code: error.code } }; // Return error object
//             }
//             // Return generic error object for unexpected issues
//              return { success: false, error: { message: `Payment intent creation failed: ${error instanceof Error ? error.message : "Unknown server error"}`}};
//         }
//     }
// }