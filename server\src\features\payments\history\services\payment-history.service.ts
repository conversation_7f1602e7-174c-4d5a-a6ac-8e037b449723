/**
 * Payment History Service
 * 
 * Handles retrieval of user payment records.
 */

import { prisma } from '@/lib/prisma';
import ApiError from '@/utils/ApiError';
import { PaymentHistorySummary, ListPaymentHistoryRequest } from '../types/payment-history.types';

export class PaymentHistoryService {
  /**
   * Get payment history for a user
   * @param userId User ID
   * @param options Pagination and filtering options
   * @returns List of payment records
   */
  static async getUserPaymentHistory(
    userId: string,
    options: ListPaymentHistoryRequest = {}
  ): Promise<{ payments: PaymentHistorySummary[], total: number }> {
    try {
      if (!userId) {
        throw new ApiError(400, 'User ID is required');
      }

      // Set defaults for options
      const page = options.page || 1;
      const limit = options.limit || 10;
      const skip = (page - 1) * limit;
      const sortBy = options.sortBy || 'processedAt';
      const sortDirection = options.sortDirection || 'desc';
      
      // Prepare filter conditions
      const whereCondition: any = { userId };
      
      if (options.status) {
        whereCondition.status = options.status;
      }

      // Get total count for pagination
      const total = await prisma.paymentRecord.count({
        where: whereCondition
      });

      // Get payment records
      const records = await prisma.paymentRecord.findMany({
        where: whereCondition,
        orderBy: {
          [sortBy]: sortDirection
        },
        skip,
        take: limit,
      });

      // Transform records to client-friendly format
      const payments: PaymentHistorySummary[] = records.map(record => ({
        id: record.id,
        amount: record.amount,
        currency: record.currency,
        status: record.status,
        processor: record.processor,
        processedAt: record.processedAt.toISOString(),
        description: record.description,
        paymentMethodDetails: record.paymentMethodDetails,
        transactionId: record.processorPaymentId,
        // Include optional fields if they exist
        refundedAmount: record.refundedAmount || undefined,
        refundedAt: record.refundedAt?.toISOString(),
      }));

      return {
        payments,
        total,
      };
    } catch (error) {
      console.error(`❌ Error getting payment history for user ${userId}:`, error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError(500, `Failed to retrieve payment history: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get details of a specific payment record
   * @param paymentId Payment record ID
   * @param userId User ID (for authorization)
   * @returns Detailed payment record
   */
  static async getPaymentDetails(
    paymentId: string,
    userId: string
  ): Promise<PaymentHistorySummary> {
    try {
      if (!paymentId || !userId) {
        throw new ApiError(400, 'Payment ID and User ID are required');
      }

      const payment = await prisma.paymentRecord.findUnique({
        where: { id: paymentId }
      });

      if (!payment) {
        throw new ApiError(404, 'Payment record not found');
      }

      // Verify the payment belongs to the user
      if (payment.userId !== userId) {
        throw new ApiError(403, 'You do not have permission to view this payment record');
      }

      return {
        id: payment.id,
        amount: payment.amount,
        currency: payment.currency,
        status: payment.status,
        processor: payment.processor,
        processedAt: payment.processedAt.toISOString(),
        description: payment.description,
        paymentMethodDetails: payment.paymentMethodDetails,
        transactionId: payment.processorPaymentId,
        // Include optional fields if they exist
        refundedAmount: payment.refundedAmount || undefined,
        refundedAt: payment.refundedAt?.toISOString(),
      };
    } catch (error) {
      console.error(`❌ Error getting payment details for ID ${paymentId}:`, error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError(500, `Failed to retrieve payment details: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}