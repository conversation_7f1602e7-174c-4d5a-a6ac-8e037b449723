export type VisitorStatus = 'active' | 'inactive' | 'suspended';

export interface Visitor {
  id: string;
  fullName: string;
  email: string;
  mobile: string;
  joinDate: string;
  lastActive: string;
  status: VisitorStatus;
  totalBookings: number;
  totalSpent: number;
  avatarUrl?: string;
  location?: string;
}

export interface VisitorFilters {
  search: string;
  status: VisitorStatus | 'all';
  dateRange?: {
    from: Date;
    to: Date;
  };
  sortBy?: 'name' | 'bookings' | 'spent' | 'joinDate';
  sortOrder?: 'asc' | 'desc';
}
