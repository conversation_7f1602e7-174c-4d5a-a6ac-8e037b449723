'use client';
import React, { useState } from 'react';
import { useTmEvents } from '../hooks/useTmEvents';
import { Button } from '@/components/ui/button';
import { Search } from 'lucide-react';
import { SearchModal } from '@/features/global-search/components/SearchModal';

export const SearchBar: React.FC = () => {
  // Get the updateKeyword and queryParams from the hook
 const { updateKeyword } = useTmEvents();
    const [isSearchOpen, setIsSearchOpen] = useState(false);

   const toggleSearchModal = () => {
       setIsSearchOpen(!isSearchOpen);
   };
   const handleSearch = (value:string) => {
       updateKeyword(value);
       setIsSearchOpen(false);
   }

 return (
    <div className="relative">
       <Button
           variant="ghost"
           size="sm"
           onClick={toggleSearchModal}
           className="rounded-md w-40 h-10 bg-black/10 hover:bg-black/20 transition-colors flex items-center gap-2"
       >
           <Search className="h-5 w-5 text-gray-700" />
           <span className="text-gray-700">Search Events</span>
       </Button>
       <SearchModal isOpen={isSearchOpen} onClose={toggleSearchModal} onSearch={handleSearch} />
     </div>
 );
};
