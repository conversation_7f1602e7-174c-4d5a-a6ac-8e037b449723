'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardHeader, CardContent, CardTitle, CardDescription } from '@/components/ui/card'
import { BillingAddressManager } from '@/features/billing-address/components/BillingAddressManager'
import { Ticket, Home, User } from 'lucide-react'

const formSchema = z.object({
  preferredCategory: z.string(),
})

export function VisitorSettings() {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      preferredCategory: 'all',
    },
  })

  function onSubmit(values: z.infer<typeof formSchema>) {
      // Will implement API call later
      console.log(values)
  }

  return (
    <Tabs defaultValue="preferences" className="w-full space-y-6">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="preferences" className="flex items-center">
          <Ticket className="mr-2 h-4 w-4" /> Preferences
        </TabsTrigger>
        <TabsTrigger value="billing" className="flex items-center">
          <Home className="mr-2 h-4 w-4" /> Billing Addresses
        </TabsTrigger>
      </TabsList>
      
      <TabsContent value="preferences">
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-semibold">Event Preferences</CardTitle>
            <CardDescription>
              Customize your event browsing experience
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                <FormField
                  control={form.control}
                  name="preferredCategory"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Preferred Category</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="all">All Events</SelectItem>
                          <SelectItem value="sports">Sports</SelectItem>
                          <SelectItem value="music">Music</SelectItem>
                          <SelectItem value="arts">Arts & Theater</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select your preferred category for events.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button type="submit">Save Preferences</Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="billing">
        <BillingAddressManager />
      </TabsContent>
    </Tabs>
  )
}
