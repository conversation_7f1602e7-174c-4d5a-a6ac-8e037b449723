import { useState, useEffect, useMemo, useCallback } from 'react';
import { UnifiedEvent } from '../adapters/eventAdapter';
import { InventoryItem } from '@/features/event-listing/types/eventListing';
import { validateSelection } from '../helpers/inventoryValidation';

type Selections = Record<string, number>; // Map of Inventory Item ID -> Selected Quantity

/**
 * Custom hook for managing ticket selection state and calculations
 */
export const useTicketSelection = (event: UnifiedEvent | null, isOpen: boolean) => {
  // State for selected tickets and their quantities
  const [selections, setSelections] = useState<Selections>({});
  const [totalCost, setTotalCost] = useState<number>(0);
  const [totalTickets, setTotalTickets] = useState<number>(0);
  const [animateCost, setAnimateCost] = useState<boolean>(false);

  // Memoize inventory data
  const managerInventory: InventoryItem[] = useMemo(() =>
    (event?.source === 'manager' && Array.isArray(event.originalEvent?.inventory))
      ? event.originalEvent.inventory as InventoryItem[] // Explicitly cast for safety
      : [],
    [event]
  );

  const hasManagerInventory = managerInventory.length > 0;

  // Handler for quantity changes
  const handleQuantityChange = useCallback((itemId: string, quantity: number) => {
    setSelections(prev => {
      const newSelections = { ...prev };
      if (quantity > 0) {
        newSelections[itemId] = quantity;
      } else {
        delete newSelections[itemId];
      }
      return newSelections;
    });
  }, []);

  // Calculate totals whenever selections change
  useEffect(() => {
    let currentSubtotal = 0; // Calculate subtotal first
    let currentTickets = 0;

    if (event?.source === 'manager') {
      for (const itemId in selections) {
        const quantity = selections[itemId];
        const item = managerInventory.find(inv => inv.id === itemId);
        if (item && quantity > 0) {
          // Use listPrice for subtotal calculation, same as backend
          const ticketPrice = item.listPrice; // Assuming listPrice is the base price
          currentSubtotal += ticketPrice * quantity;
          currentTickets += quantity;
        }
      }

      // --- Calculate Service Fee and Total (Matching Backend Logic) ---
      // Define local constants matching backend configuration
      const SERVICE_FEE_PERCENTAGE = 0.10; // 10% (Matches backend)
      const SERVICE_FEE_MINIMUM = 7.0; // $7.0 (Matches backend)

      // Calculate service fee based on the subtotal with the minimum
      const calculatedServiceFee = currentSubtotal * SERVICE_FEE_PERCENTAGE;
      const serviceFee = Math.max(calculatedServiceFee, SERVICE_FEE_MINIMUM);

      // Calculate the final total cost
      const currentTotal = currentSubtotal + serviceFee; // Add service fee to subtotal

      // --- Update State ---
      if (currentTotal !== totalCost) {
        setTotalCost(currentTotal);
        setAnimateCost(true); // Trigger animation if cost changes
      }
       // Always update total tickets
      setTotalTickets(currentTickets);

    } else {
      // Handle non-manager events if needed, though footer is hidden for them
      setTotalCost(0);
      setTotalTickets(0);
    }

  }, [selections, managerInventory, event?.source, totalCost]); // Dependencies

  // Reset animations
  useEffect(() => {
    if (animateCost) {
      const timer = setTimeout(() => setAnimateCost(false), 300);
      return () => clearTimeout(timer);
    }
  }, [animateCost]);

  // Reset selections when modal opens/event changes
  useEffect(() => {
    // Only reset if the modal is opening or a new event is loaded
    if (isOpen) {
      console.log('🔄 Resetting ticket selections for new event/modal open'); // Debug log
      setSelections({});
      setTotalCost(0);
      setTotalTickets(0);
    } else {
       // Optional: Clear selections when modal closes too, if that's desired behavior
       // setSelections({});
       // setTotalCost(0);
       // setTotalTickets(0);
    }
  }, [isOpen, event?.id]); // Depend on isOpen and event ID to reset correctly

  // Validation checks
  const hasSelections = totalTickets > 0;

  // Re-calculate validity whenever selections or inventory change
  const hasInvalidSelections = useMemo(() => {
      return Object.entries(selections).some(([itemId, qty]) => {
          const item = managerInventory.find(inv => inv.id === itemId);
          if (!item) return true; // Selection for non-existent item is invalid
          // Use the validation helper
          return !validateSelection(qty, item.quantity, item.sellingPreference).isValid;
      });
  }, [selections, managerInventory]);


  // Can proceed if there are selections AND no invalid selections
  const canProceedInventory = hasSelections && !hasInvalidSelections;

  return {
    // State
    selections,
    totalCost,
    totalTickets,
    animateCost,

    // Handlers
    handleQuantityChange,

    // Derived values
    hasManagerInventory,
    hasSelections,
    hasInvalidSelections,
    canProceedInventory
  };
};
