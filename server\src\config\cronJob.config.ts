/**
 * Central configuration for all cron jobs.
 * Define patterns, enabling flags, and potentially shared utilities.
 */

// Define schedule patterns in one place
export const CRON_SCHEDULES = {
  // Queue-related jobs
  queueDeactivation: '*/30 * * * * *', // Check every 30 seconds
  queueBatchProcessing: '*/15 * * * * *', // Process batches every 15 seconds (includes session expiry check)

  // Checkout-related jobs
  checkoutExpiration: '*/2 * * * *', // Check every 2 minutes
  checkoutCleanup: '0 0 * * *', // Once daily at midnight
};

// Optional: Define names for logging consistency
export const CRON_JOB_NAMES = {
  queueDeactivation: 'Queue Deactivation Check',
  queueBatchProcessing: 'Queue Batch Processing & Session Expiry',
  checkoutExpiration: 'Checkout Session Expiration Check',
  checkoutCleanup: 'Checkout Session Cleanup'
};

// Optional: Add enabling flags if you want to control jobs via env vars
// export const CRON_JOBS_ENABLED = {
//   queue: process.env.ENABLE_QUEUE_CRON !== 'false', // default true
//   checkout: process.env.ENABLE_CHECKOUT_CRON !== 'false', // default true
// };

console.log('⚙️ Cron Job Schedules Loaded:', CRON_SCHEDULES);