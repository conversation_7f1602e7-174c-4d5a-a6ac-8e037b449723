import { Router } from 'express';
import { CheckoutController } from '../controllers/checkout.controller';
import { CouponController } from '../controllers/coupon.controller';
import { PointsController } from '../controllers/points.controller';
import { authMiddleware } from '@/middleware/auth.middleware';

const router = Router();

// ============ MAIN CHECKOUT ROUTES ============

// Route to create a reservation
// POST /api/v1/checkout/reserve
router.post(
    '/reserve',
    authMiddleware, // Ensure user is authenticated
    CheckoutController.createReservation
);

// Route to get a checkout session
// GET /api/v1/checkout/session/:sessionId
router.get(
    '/session/:sessionId',
    authMiddleware, // Ensure user is authenticated
    CheckoutController.getSession
);

// Route to refresh a checkout session (extend expiration time)
// POST /api/v1/checkout/session/:sessionId/refresh
router.post(
    '/session/:sessionId/refresh',
    authMiddleware, // Ensure user is authenticated
    CheckoutController.refreshSession
);

// NEW: Route to update a checkout session status
// PATCH /api/v1/checkout/session/:sessionId/status
router.patch(
    '/session/:sessionId/status',
    authMiddleware, // Ensure user is authenticated
    CheckoutController.updateSessionStatus
);

// ============ COUPON ROUTES ============

// Route to apply a coupon code to a checkout session
// POST /api/v1/checkout/coupon
router.post(
    '/coupon',
    authMiddleware, // Ensure user is authenticated
    CouponController.applyCoupon
);

// ============ POINTS ROUTES ============

// Route to apply credit points to a checkout session
// POST /api/v1/checkout/points
router.post(
    '/points',
    authMiddleware, // Ensure user is authenticated
    PointsController.applyPoints
);

export default router;