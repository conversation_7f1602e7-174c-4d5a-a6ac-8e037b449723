/**
 * Stripe Payments Service
 *
 * Handles interactions with Stripe API for payment processing.
 * Best Practices:
 * - API Keys via Environment Variables: Never hardcode keys. Use process.env.
 * - API Version Pinning: Use a specific API version for stability.
 * - Error Handling: Catch specific Stripe errors and use ApiError for custom issues.
 * - Webhook Security: Always validate webhook signatures using the secret.
 * - Idempotency (Production): Use idempotency keys for critical requests like payment creation
 *   to prevent accidental duplicates. (See comment in createPaymentIntent).
 */

import Strip<PERSON> from "stripe";
import { CheckoutSessionStatus } from "@prisma/client";
import ApiError from "@/utils/ApiError";
import { prisma } from "@/lib/prisma"; // Use the centralized Prisma client
import {
  StripePaymentIntentRequest,
  StripePaymentIntentResponse,
  StripeWebhookValidationResult,
} from "../types/stripe-payments.types";
import { SupportedCurrency } from "../../../common/types/payment-common.types";
export class StripePaymentsService {
  private static stripe: Stripe;

  /**
   * Initialize the Stripe instance with API key
   */
  private static initialize(): void {
    if (!this.stripe) {
      if (!process.env.STRIPE_SECRET_KEY) {
        console.error(
          "❌ STRIPE_SECRET_KEY is not defined in environment variables"
        );
        throw new ApiError(
          500,
          "Payment service configuration error: Missing Stripe secret key."
        );
      }

      this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
        apiVersion: "2025-03-31.basil",
        typescript: true, // Enables stricter TypeScript checking with the Stripe library
      });

      console.log("💳 Stripe service initialized");
    }
  }

  /**
   * Get or create a Stripe customer for a user
   * @param userId User ID in your database
   * @param email User's email address
   * @returns Stripe Customer ID
   */
  static async getOrCreateStripeCustomer(
    userId: string,
    email: string
  ): Promise<string> {
    try {
      this.initialize();

      // Find the user in your database
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { stripeCustomerId: true },
      });

      if (!user) {
        throw new ApiError(404, `User not found: ${userId}`);
      }

      // If user already has a Stripe customer ID, return it
      if (user.stripeCustomerId) {
        console.log(
          `✅ Using existing Stripe customer for user ${userId}: ${user.stripeCustomerId}`
        );
        return user.stripeCustomerId;
      }

      // Otherwise, create a new Stripe customer
      console.log(`🆕 Creating new Stripe customer for user ${userId}`);

      const customer = await this.stripe.customers.create({
        email,
        metadata: { userId },
      });

      // Save the Stripe customer ID to the user record
      await prisma.user.update({
        where: { id: userId },
        data: { stripeCustomerId: customer.id },
      });

      console.log(
        `✅ Created new Stripe customer for user ${userId}: ${customer.id}`
      );
      return customer.id;
    } catch (error) {
      console.error(
        `❌ Error in getOrCreateStripeCustomer for user ${userId}:`,
        error
      );

      if (error instanceof ApiError) {
        throw error;
      }

      throw new ApiError(
        500,
        `Failed to get or create Stripe customer: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Create a payment intent from a checkout session
   * @param sessionId Checkout session ID in your database
   * @param userId User ID making the payment
   * @param options Optional configuration options for the payment intent
   * @returns Payment intent with client secret
   */
  static async createPaymentIntentFromCheckoutSession(
    sessionId: string,
    userId: string,
    options: Partial<StripePaymentIntentRequest> = {}
  ): Promise<StripePaymentIntentResponse> {
    try {
      this.initialize();

      // Fetch the checkout session
      const checkoutSession = await prisma.checkoutSession.findUnique({
        where: { id: sessionId },
        include: { user: { select: { email: true } } },
      });

      if (!checkoutSession) {
        throw new ApiError(404, `Checkout session not found: ${sessionId}`);
      }

      // Verify the session belongs to the user
      if (checkoutSession.userId !== userId) {
        throw new ApiError(
          403,
          "You do not have permission to access this checkout session"
        );
      }

      // Check session status - should only allow creating payment for valid status
      if (checkoutSession.status !== CheckoutSessionStatus.RESERVED) {
        throw new ApiError(
          400,
          `Cannot process payment for checkout session with status: ${checkoutSession.status}`
        );
      }

      console.log(
        `💰 Creating payment intent for checkout session: ${sessionId}`
      );

      // Get or create Stripe customer
      const customerEmail = checkoutSession.user?.email;
      if (!customerEmail) {
        throw new ApiError(
          400,
          "User email is required for payment processing"
        );
      }

      const stripeCustomerId = await this.getOrCreateStripeCustomer(
        userId,
        customerEmail
      );

      //!---todo:modify in case of diffenet total amount // Convert amount to cents (Stripe uses smallest currency unit)
      const amountInCents = Math.round(checkoutSession.total );

      // Use the options parameter to allow configuration flexibility
      // while providing sensible defaults
      const paymentIntentParams: Stripe.PaymentIntentCreateParams = {
        amount: amountInCents,
        currency: (checkoutSession.currency || "USD").toLowerCase(),
        customer: stripeCustomerId,
        setup_future_usage: options.setupFutureUsage || "on_session",
        payment_method_types: options.paymentMethodTypes || ["card"],
        metadata: {
          userId,
          checkoutSessionId: sessionId,
          ...(options.metadata || {}),
        },
        receipt_email: options.receiptEmail || customerEmail,
        description:
          options.description ||
          `Purchase for Event ID: ${checkoutSession.eventId}`,
      };

      // Add optional parameters only if provided
      if (options.statementDescriptor) {
        paymentIntentParams.statement_descriptor =
          options.statementDescriptor.substring(0, 22); // Stripe limit
      }

      if (options.shipping) {
        paymentIntentParams.shipping = options.shipping;
      }

      // Create a payment intent
      const paymentIntent = await this.stripe.paymentIntents.create(
        paymentIntentParams
      );

      // Update the checkout session with the payment intent ID
      await prisma.checkoutSession.update({
        where: { id: sessionId },
        data: { paymentIntentId: paymentIntent.id },
      });

      console.log(
        `✅ Created payment intent for session ${sessionId}: ${paymentIntent.id}`
      );

      return {
        success: true,
        clientSecret: paymentIntent.client_secret || undefined,
        paymentIntentId: paymentIntent.id,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency.toUpperCase() as SupportedCurrency,
        status: paymentIntent.status,
      };
    } catch (error) {
      console.error(
        `❌ Error creating payment intent for session ${sessionId}:`,
        error
      );

      if (error instanceof ApiError) {
        throw error;
      }

      if (error instanceof Stripe.errors.StripeError) {
        throw new ApiError(
          400,
          `Stripe Error: ${error.message} (Code: ${error.code})`
        );
      }

      throw new ApiError(
        500,
        "Failed to create payment intent due to an unexpected server error."
      );
    }
  }

  /**
   * Create a subscription checkout session
   * @param userId User ID
   * @param priceId Stripe Price ID for the subscription
   * @param successUrl URL to redirect on success
   * @param cancelUrl URL to redirect on cancellation
   * @returns Checkout session URL
   */
  static async createSubscriptionCheckoutSession(
    userId: string,
    priceId: string,
    successUrl: string,
    cancelUrl: string
  ) {
    try {
      this.initialize();

      // Get user's email
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { email: true },
      });

      if (!user || !user.email) {
        throw new ApiError(404, "User not found or missing email");
      }

      // Get or create Stripe customer
      const stripeCustomerId = await this.getOrCreateStripeCustomer(
        userId,
        user.email
      );

      // Create checkout session
      const session = await this.stripe.checkout.sessions.create({
        customer: stripeCustomerId,
        payment_method_types: ["card"],
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        mode: "subscription",
        subscription_data: {
          metadata: { userId },
        },
        success_url: successUrl,
        cancel_url: cancelUrl,
        metadata: { userId },
      });

      return {
        success: true,
        url: session.url,
      };
    } catch (error) {
      console.error(
        `❌ Error creating subscription checkout for user ${userId}:`,
        error
      );

      if (error instanceof ApiError) {
        throw error;
      }

      if (error instanceof Stripe.errors.StripeError) {
        throw new ApiError(
          400,
          `Stripe Error: ${error.message} (Code: ${error.code})`
        );
      }

      throw new ApiError(
        500,
        "Failed to create subscription checkout due to an unexpected server error."
      );
    }
  }

  /**
   * Create a customer portal session for managing subscriptions
   * @param userId User ID
   * @param returnUrl URL to return after managing subscription
   * @returns Portal session URL
   */
  static async createCustomerPortalSession(userId: string, returnUrl: string) {
    try {
      this.initialize();

      // Get user with Stripe customer ID
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { stripeCustomerId: true },
      });

      if (!user || !user.stripeCustomerId) {
        throw new ApiError(
          404,
          "User not found or has no associated Stripe customer"
        );
      }

      // Create customer portal session
      const session = await this.stripe.billingPortal.sessions.create({
        customer: user.stripeCustomerId,
        return_url: returnUrl,
      });

      return {
        success: true,
        url: session.url,
      };
    } catch (error) {
      console.error(
        `❌ Error creating customer portal for user ${userId}:`,
        error
      );

      if (error instanceof ApiError) {
        throw error;
      }

      if (error instanceof Stripe.errors.StripeError) {
        throw new ApiError(
          400,
          `Stripe Error: ${error.message} (Code: ${error.code})`
        );
      }

      throw new ApiError(
        500,
        "Failed to create customer portal due to an unexpected server error."
      );
    }
  }

  /**
   * Validate and parse a Stripe webhook event
   * @param payload The raw request body (Buffer or string)
   * @param signature The Stripe-Signature header value
   * @returns The validated Stripe event or an error object
   */
  static validateWebhookEvent(
    payload: string | Buffer,
    signature: string
  ): StripeWebhookValidationResult {
    try {
      this.initialize(); // Ensure Stripe is initialized

      const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
      if (!webhookSecret) {
        console.error(
          "❌ STRIPE_WEBHOOK_SECRET is not defined in environment variables. Cannot validate webhook."
        );
        // Returning valid: false signals an internal configuration issue, distinct from an invalid signature
        return {
          valid: false,
          error: "Webhook secret not configured on the server.",
        };
      }

      console.log("🔑 Validating Stripe webhook signature...");

      // Construct the event. Throws an error if the signature is invalid.
      const event = this.stripe.webhooks.constructEvent(
        payload,
        signature,
        webhookSecret
      );

      console.log(
        `✅ Webhook signature validated. Event ID: ${event.id}, Type: ${event.type}`
      );

      // Return the successfully validated event
      return { valid: true, event };
    } catch (error) {
      // Handle specific signature verification errors
      if (error instanceof Stripe.errors.StripeSignatureVerificationError) {
        console.error(
          "❌ Webhook signature verification failed:",
          error.message
        );
        return {
          valid: false,
          error: `Webhook signature verification failed: ${error.message}`,
        };
      }
      // Handle other potential errors during construction
      else if (error instanceof Error) {
        console.error(
          "❌ Unexpected error during webhook construction:",
          error.message
        );
        return {
          valid: false,
          error: `Webhook construction error: ${error.message}`,
        };
      }
      // Fallback for unknown errors
      else {
        console.error("❌ Unknown error during webhook validation");
        return {
          valid: false,
          error: "An unknown error occurred during webhook validation.",
        };
      }
    }
  }

  /**
   * Process a webhook event
   * @param event The Stripe event
   */
  static async handleWebhookEvent(event: any) {
    try {
      this.initialize();

      console.log(
        `⚡ Processing Stripe webhook event: ${event.type} [${event.id}]`
      );

      switch (event.type) {
        case "payment_intent.succeeded":
          await this.handlePaymentIntentSucceeded(event.data.object);
          break;

        case "payment_intent.payment_failed":
          await this.handlePaymentIntentFailed(event.data.object);
          break;

        case "checkout.session.completed":
          await this.handleCheckoutSessionCompleted(event.data.object);
          break;

        // Add more event handlers as needed
        default:
          console.log(`⏩ Unhandled webhook event type: ${event.type}`);
      }

      return { success: true };
    } catch (error) {
      console.error(`❌ Error processing webhook event:`, error);

      if (error instanceof ApiError) {
        throw error;
      }

      throw new ApiError(
        500,
        `Failed to process webhook: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Handle payment_intent.succeeded webhook event
   * @param paymentIntent The Stripe PaymentIntent object
   */
  private static async handlePaymentIntentSucceeded(
    paymentIntent: Stripe.PaymentIntent
  ) {
    const { userId, checkoutSessionId } = paymentIntent.metadata || {};

    console.log(
      `💰 Payment succeeded: ${paymentIntent.id}, Amount: ${paymentIntent.amount}, UserId: ${userId}`
    );

    if (!userId || !checkoutSessionId) {
      console.warn(`⚠️ Missing metadata in PaymentIntent ${paymentIntent.id}`);
      return;
    }

    // Update checkout session status
    if (checkoutSessionId) {
      await prisma.checkoutSession.update({
        where: { id: checkoutSessionId },
        data: {
          status: CheckoutSessionStatus.COMPLETED,
          completedAt: new Date(),
          paymentStatus: "succeeded",
          // If the charge has a receipt URL, store it
          receiptUrl:
            paymentIntent.latest_charge &&
            typeof paymentIntent.latest_charge !== "string"
              ? paymentIntent.latest_charge.receipt_url
              : undefined,
        },
      });

      console.log(
        `✅ Updated checkout session ${checkoutSessionId} to COMPLETED`
      );
    }

    // Extract payment method details if available
    let paymentMethodDetails = "";
    const paymentMethod = await this.stripe.paymentMethods.retrieve(
      paymentIntent.payment_method as string
    );
    if (paymentMethod && paymentMethod.card) {
      const { brand, last4 } = paymentMethod.card;
      paymentMethodDetails = `${brand} **** ${last4}`;
    }

    // Create payment record
    await prisma.paymentRecord.create({
      data: {
        userId,
        checkoutSessionId,
        processor: "STRIPE",
        processorPaymentId: paymentIntent.id,
        status: "SUCCEEDED",
        amount: paymentIntent.amount,
        currency: paymentIntent.currency.toUpperCase(),
        paymentMethodDetails,
        processedAt: new Date(paymentIntent.created * 1000), // Convert from Unix timestamp
        description: paymentIntent.description || "Payment successful",
      },
    });

    console.log(
      `✅ Created payment record for PaymentIntent ${paymentIntent.id}`
    );
  }

  /**
   * Handle payment_intent.payment_failed webhook event
   * @param paymentIntent The Stripe PaymentIntent object
   */
  private static async handlePaymentIntentFailed(
    paymentIntent: Stripe.PaymentIntent
  ) {
    const { userId, checkoutSessionId } = paymentIntent.metadata || {};

    console.log(
      `❌ Payment failed: ${paymentIntent.id}, Reason: ${paymentIntent.last_payment_error?.message}`
    );

    if (!userId) {
      console.warn(`⚠️ Missing userId in PaymentIntent ${paymentIntent.id}`);
      return;
    }

    // Update checkout session status if available
    if (checkoutSessionId) {
      await prisma.checkoutSession.update({
        where: { id: checkoutSessionId },
        data: {
          status: CheckoutSessionStatus.FAILED,
          paymentStatus: "failed",
        },
      });

      console.log(`✅ Updated checkout session ${checkoutSessionId} to FAILED`);
    }

    // Create payment record
    await prisma.paymentRecord.create({
      data: {
        userId,
        checkoutSessionId,
        processor: "STRIPE",
        processorPaymentId: paymentIntent.id,
        status: "FAILED",
        amount: paymentIntent.amount,
        currency: paymentIntent.currency.toUpperCase(),
        processedAt: new Date(paymentIntent.created * 1000), // Convert from Unix timestamp
        description:
          paymentIntent.last_payment_error?.message || "Payment failed",
      },
    });

    console.log(
      `✅ Created payment record for failed PaymentIntent ${paymentIntent.id}`
    );
  }

  /**
   * Handle checkout.session.completed webhook event
   * @param session The Stripe Checkout Session object
   */
  private static async handleCheckoutSessionCompleted(
    session: Stripe.Checkout.Session
  ) {
    const { userId } = session.metadata || {};

    console.log(
      `✅ Checkout session completed: ${session.id}, Mode: ${session.mode}`
    );

    if (!userId) {
      console.warn(`⚠️ Missing userId in Checkout Session ${session.id}`);
      return;
    }

    // Handle subscription checkout
    if (session.mode === "subscription" && session.subscription) {
      const subscriptionId =
        typeof session.subscription === "string"
          ? session.subscription
          : session.subscription.id;

      console.log(`📅 Processing subscription: ${subscriptionId}`);

      // Retrieve subscription details
      const subscription = await this.stripe.subscriptions.retrieve(
        subscriptionId
      );

      // Update user with subscription info (adjust fields as needed for your schema)
      await prisma.user.update({
        where: { id: userId },
        data: {
          // Assuming you have these fields in your User model - adjust as needed
          membershipTier:
            subscription.items.data[0].price.nickname === "VIP"
              ? "VIP"
              : "SUBSCRIBER",
          // Add any other subscription fields you want to track
        },
      });

      console.log(
        `✅ Updated user ${userId} with subscription ${subscriptionId}`
      );

      // Create payment record for initial subscription payment
      if (subscription.latest_invoice) {
        const invoice =
          typeof subscription.latest_invoice === "string"
            ? await this.stripe.invoices.retrieve(subscription.latest_invoice)
            : subscription.latest_invoice;

        // Use type assertion for properties not recognized by TypeScript
        if ((invoice as any).paid) {
          await prisma.paymentRecord.create({
            data: {
              userId,
              processor: "STRIPE",
              processorPaymentId:
                ((invoice as any).payment_intent as string) ?? invoice.id,
              status: "SUCCEEDED",
              amount: invoice.amount_paid,
              currency: invoice.currency.toUpperCase(),
              description: `Initial payment for ${
                subscription.items.data[0].price.nickname || "subscription"
              }`,
              processedAt: new Date(invoice.created * 1000),
            },
          });

          console.log(
            `✅ Created payment record for initial subscription payment`
          );
        }
      }
    }
  }
}
