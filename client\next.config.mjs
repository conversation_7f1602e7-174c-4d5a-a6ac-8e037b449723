/** @type {import('next').NextConfig} */
// Configuration for Next.js application
const nextConfig = {
  output: "standalone",
  distDir: ".next",
  // Add TypeScript configuration to ignore build errors in CI
  //  typescript: {
  //   // This allows production builds to complete even with TypeScript errors
  //   ignoreBuildErrors: true,
  // },

  // Add ESLint configuration to ignore warnings in CI
  eslint: {
    // This allows production builds to complete even with ESLint warnings
    ignoreDuringBuilds: true,
  },

  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "images.pexels.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "unsplash.com",
        port: "",
        pathname: "/**",
      },
      // Added new remote pattern for Ticketmaster images
      {
        protocol: "https",
        hostname: "s1.ticketm.net",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "mapsapi.tmol.io",
        port: "",
        pathname: "/**",
      },
      // {
      //   protocol: 'https',
      //   hostname: 'i.ticketweb.com',
      //   port: '',
      // },

      //🌻working -> Add a wildcard pattern for all domains (less secure but very flexible)
      {
        protocol: "https",
        hostname: "*",
        port: "",
        pathname: "/**",
      },
    ],
  },
  async rewrites() {
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
    console.log("API Base URL ➡️:", apiBaseUrl);
    return [
      // You can add a specific rewrite to maintain auth paths in local dev
      {
        source: "/api/auth/:path*",
        destination: "/api/authlogic/auth/:path*",
      },
      {
        source: "/api/v1/:path*",
        destination: `${apiBaseUrl}/api/v1/:path*`,
      },
      {
        source: "/api/v2/:path*",
        destination: `${apiBaseUrl}/api/v2/:path*`,
      },
    ];
  },
};

export default nextConfig;
