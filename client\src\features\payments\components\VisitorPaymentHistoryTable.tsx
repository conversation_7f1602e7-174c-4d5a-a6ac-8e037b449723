// Renamed component (content remains the same as the previous PaymentHistoryTable)
'use client';

import { useState } from 'react';
// ... other imports remain the same (Table, Badge, Pagination, etc.) ...
import { PaymentRecord } from '../types/payment.types'; // Assuming this type is still relevant for visitors
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { LoadingSpinner } from '@/components/shared/LoadingSpinner';
import { Button } from '@/components/ui/button';
import { MoreVertical, Eye, ExternalLink, Download, RefreshCw } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Badge } from '@/components/ui/badge';


interface VisitorPaymentHistoryTableProps { // Renamed prop interface
  payments: PaymentRecord[]; // Use appropriate type for visitor payments
  isLoading: boolean;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  onRefresh: () => void;
  className?: string;
}

// Renamed functional component
export function VisitorPaymentHistoryTable({
  payments,
  isLoading,
  currentPage,
  totalPages,
  onPageChange,
  onRefresh,
  className,
}: VisitorPaymentHistoryTableProps) { // Use renamed props
  const [refreshing, setRefreshing] = useState(false); // Keep internal state if needed

  const handleRefreshClick = async () => { // Renamed internal handler if needed
    setRefreshing(true);
    await onRefresh();
    setRefreshing(false);
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount / 100); // Assuming amount is in smallest currency unit (cents)
  };

  const getStatusBadge = (status: string) => {
    // Keep the status badge logic as it was for PaymentRecordStatus
     const statusMap: Record<string, { variant: "default" | "destructive" | "outline" | "secondary" | "success"; label: string }> = {
       SUCCEEDED: { variant: "success", label: "Successful" },
       FAILED: { variant: "destructive", label: "Failed" },
       REFUNDED: { variant: "secondary", label: "Refunded" },
       PARTIALLY_REFUNDED: { variant: "secondary", label: "Partially Refunded" },
       CANCELED: { variant: "outline", label: "Canceled" },
       PENDING: { variant: "outline", label: "Pending" } // Add pending if needed
     };
     const statusConfig = statusMap[status.toUpperCase()] || { variant: "default", label: status };
     return <Badge variant={statusConfig.variant as any}>{statusConfig.label}</Badge>;
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-12 border rounded-md min-h-[200px]">
        <LoadingSpinner />
        <span className="ml-3 text-muted-foreground">Loading payment history...</span>
      </div>
    );
  }

  if (payments.length === 0) {
    return (
      <div className="text-center p-8 border rounded-md bg-muted/20 min-h-[200px] flex flex-col justify-center items-center">
        <h3 className="font-medium text-lg mb-2">No Transactions Found</h3>
        <p className="text-muted-foreground mb-4">You haven&apos;t made any payments yet.</p>
        <Button onClick={handleRefreshClick} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>
    );
  }

  // Keep the table structure, headers, and data mapping as before,
  // ensuring it displays the correct fields for a visitor's purchase history.
  return (
    <div className={cn("w-full", className)}>
      {/* Optional: Add Refresh Button here if not handled by parent */}
      {/* <div className="flex justify-end mb-4"> ... refresh button ... </div> */}

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[130px]">Date</TableHead>
              <TableHead>Description</TableHead>
              <TableHead className="w-[100px]">Amount</TableHead>
              <TableHead className="w-[120px]">Status</TableHead>
              <TableHead className="text-right w-[80px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {payments.map((payment) => (
              <TableRow key={payment.id}>
                <TableCell className="font-medium">
                  {format(new Date(payment.processedAt), 'MMM d, yyyy')}
                  <div className="text-xs text-muted-foreground">
                    {format(new Date(payment.processedAt), 'h:mm a')}
                  </div>
                </TableCell>
                <TableCell>
                  {payment.description || 'Purchase'} {/* Use description from PaymentRecord */}
                  <div className="text-xs text-muted-foreground truncate max-w-[200px] sm:max-w-[300px]">
                    ID: {payment.transactionId}
                  </div>
                </TableCell>
                <TableCell>{formatAmount(payment.amount, payment.currency)}</TableCell>
                <TableCell>{getStatusBadge(payment.status)}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreVertical className="h-4 w-4" />
                        <span className="sr-only">Open menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                     <DropdownMenuContent align="end">
                       <DropdownMenuLabel>Actions</DropdownMenuLabel>
                       <DropdownMenuSeparator />
                       <DropdownMenuItem disabled> {/* Add real link later */}
                         <Eye className="h-4 w-4 mr-2" />
                         View Details
                       </DropdownMenuItem>
                       {payment.receiptUrl && (
                         <DropdownMenuItem
                           onClick={() => window.open(payment.receiptUrl, '_blank')}
                         >
                           <ExternalLink className="h-4 w-4 mr-2" />
                           View Receipt (Stripe)
                         </DropdownMenuItem>
                       )}
                       <DropdownMenuItem disabled> {/* Add real link later */}
                         <Download className="h-4 w-4 mr-2" />
                         Download Invoice
                       </DropdownMenuItem>
                     </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination - unchanged */}
       {totalPages > 1 && (
         <Pagination className="mt-6">
           <PaginationContent>
             <PaginationItem>
               <PaginationPrevious
                 href="#"
                 onClick={(e) => {
                   e.preventDefault();
                   if (currentPage > 1) onPageChange(currentPage - 1);
                 }}
                 className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
               />
             </PaginationItem>
             {/* Generate pagination links */}
             {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
               <PaginationItem key={page}>
                 <PaginationLink
                   href="#"
                   onClick={(e) => {
                     e.preventDefault();
                     onPageChange(page);
                   }}
                   isActive={page === currentPage}
                 >
                   {page}
                 </PaginationLink>
               </PaginationItem>
             ))}
             <PaginationItem>
               <PaginationNext
                 href="#"
                 onClick={(e) => {
                   e.preventDefault();
                   if (currentPage < totalPages) onPageChange(currentPage + 1);
                 }}
                 className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
               />
             </PaginationItem>
           </PaginationContent>
         </Pagination>
       )}
    </div>
  );
}