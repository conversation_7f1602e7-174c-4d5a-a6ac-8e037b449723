// This hook is responsible for fetching the list of events listed by the current manager
// AND handling mutations (toggle active, delete) for those events.
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"; // Added useMutation, useQueryClient
import axiosInstance from "@/apiAxios/axios"; // Use the configured axios instance
import {
  InventoryDetailItem,
  ManagerInventoryItem,
} from "../types/inventory.types";
import { toast } from "sonner"; // For error notifications

// Unique query key for manager inventory data
const MANAGER_INVENTORY_QUERY_KEY = "managerInventory";

// --- Interfaces for API responses and Payloads ---

// Fetch response
interface FetchApiResponse {
  success: boolean;
  message: string;
  data: ManagerInventoryItem[];
}

// Toggle mutation payload & response
interface ToggleActivePayload {
  eventId: string;
}
interface ToggleApiResponse {
  success: boolean;
  message: string;
  data: ManagerInventoryItem;
}

// Delete mutation payload & response
interface DeleteEventPayload {
  eventId: string;
}
interface DeleteApiResponse {
  success: boolean;
  message: string;
  data: { id: string };
}

interface UpdateInventoryPayload {
  eventId: string;
  inventory: InventoryDetailItem[]; // The updated inventory array
}
interface UpdateInventoryApiResponse {
  success: boolean;
  message: string;
  data: ManagerInventoryItem; // Expecting the full updated event back
}

// --- End Interfaces ---

export function useManagerInventoryQuery() {
  // Get QueryClient instance for invalidation
  const queryClient = useQueryClient();

  // --- Fetch Query ---
  const {
    data: inventoryItems = [], // Default to empty array
    isLoading: isFetching, // Renamed for clarity vs mutation loading states
    isError,
    error,
    refetch, // Function to manually refetch data
  } = useQuery<ManagerInventoryItem[], Error>({
    // Specify the expected data type and error type
    queryKey: [MANAGER_INVENTORY_QUERY_KEY],
    queryFn: async (): Promise<ManagerInventoryItem[]> => {
      // Real fetch logic enabled
      try {
        console.log("🚀 Fetching real manager inventory from API...");
        // ✨ Make sure the endpoint matches the backend route ✨
        const response = await axiosInstance.get<FetchApiResponse>(
          "/api/v1/manager-events/inventory"
        );
        console.log("📦 Manager inventory API response received:", response); // Log the whole response

        // Check for successful response structure
        if (
          response.data &&
          response.data.success &&
          Array.isArray(response.data.data)
        ) {
          console.log(
            `✅ Successfully fetched ${response.data.data.length} items.`
          );
          // Extract the array from response.data.data
          const items = response.data.data;

          // Data transformation (e.g., converting date strings to Date objects)
          return items.map((item) => ({
            ...item,
            // Ensure date fields are Date objects for consistent handling
            date: new Date(item.date),
            createdAt: new Date(item.createdAt),
            updatedAt: item.updatedAt ? new Date(item.updatedAt) : undefined,
            approvedAt: item.approvedAt ? new Date(item.approvedAt) : null,
            inventory: Array.isArray(item.inventory) ? item.inventory : [], // Basic validation
          }));
        } else {
          // Handle unexpected response structure
          console.error("❌ Unexpected API response structure:", response.data);
          toast.error("Received invalid data structure from server.");
          throw new Error("Invalid data structure received.");
        }
      } catch (err: any) {
        console.error("❌ Error fetching manager inventory:", err); // Log the full error object
        // Extract message from AxiosError or standard Error
        const errorMessage =
          err.response?.data?.message ||
          err.message ||
          "Failed to fetch inventory";
        console.error(`📉 Error Message: ${errorMessage}`);
        toast.error(errorMessage);
        // Re-throw the error to be caught by React Query's error handling
        throw new Error(errorMessage);
      }
    },
    enabled: true, // Keep enabled
    refetchOnWindowFocus: true, // Keep enabled
    staleTime: 5 * 60 * 1000, // Keep enabled
    gcTime: 10 * 60 * 1000, // Keep enabled
  });

  // --- Toggle Active Status Mutation ---
  const { mutate: toggleActiveMutate, isPending: isToggling } = useMutation<
    ToggleApiResponse,
    Error,
    ToggleActivePayload
  >({
    mutationFn: async ({
      eventId,
    }: ToggleActivePayload): Promise<ToggleApiResponse> => {
      console.log(
        `🚀 Calling API to toggle active status for event: ${eventId}`
      );
      const response = await axiosInstance.patch<ToggleApiResponse>(
        `/api/v1/manager-events/inventory/${eventId}/toggle-active`
      );
      console.log("✅ Toggle API Response:", response.data);
      return response.data;
    },
    onSuccess: (data) => {
      console.log("🎉 Toggle success:", data.message);
      toast.success(data.message || "Listing status updated successfully!");
      queryClient.invalidateQueries({
        queryKey: [MANAGER_INVENTORY_QUERY_KEY],
      });
    },
    onError: (error: any) => {
      console.error("❌ Error toggling active status:", error);
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        "Failed to update listing status";
      toast.error(errorMessage);
    },
  });

  // --- Delete Event Mutation ---
  const { mutate: deleteEventMutate, isPending: isDeleting } = useMutation<
    DeleteApiResponse,
    Error,
    DeleteEventPayload
  >({
    mutationFn: async ({
      eventId,
    }: DeleteEventPayload): Promise<DeleteApiResponse> => {
      console.log(`🚀 Calling API to delete event: ${eventId}`);
      const response = await axiosInstance.delete<DeleteApiResponse>(
        `/api/v1/manager-events/inventory/${eventId}`
      );
      console.log("✅ Delete API Response:", response.data);
      return response.data;
    },
    onSuccess: (data) => {
      console.log("🎉 Delete success:", data.message);
      toast.success(data.message || "Event listing deleted successfully!");
      queryClient.invalidateQueries({
        queryKey: [MANAGER_INVENTORY_QUERY_KEY],
      });
    },
    onError: (error: any) => {
      console.error("❌ Error deleting event:", error);
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        "Failed to delete event listing";
      toast.error(errorMessage);
    },
  });

  // --- Helper functions to call mutations ---
  const toggleActive = (payload: ToggleActivePayload) => {
    toggleActiveMutate(payload);
  };

  const deleteEvent = (payload: DeleteEventPayload) => {
    // Optional: Confirmation dialog
    if (
      confirm(
        "Are you sure you want to delete this listing? This action cannot be undone."
      )
    ) {
      deleteEventMutate(payload);
    }
    // deleteEventMutate(payload); // Uncomment this if you don't want confirmation
  };

  // ✨ New: Update Inventory Mutation ✨
  const { mutate: updateInventoryMutate, isPending: isUpdatingInventory } =
    useMutation<
      UpdateInventoryApiResponse, // Return type
      Error, // Error type
      UpdateInventoryPayload // Input type
    >({
      mutationFn: async ({
        eventId,
        inventory,
      }: UpdateInventoryPayload): Promise<UpdateInventoryApiResponse> => {
        console.log(`🚀 Calling API to update inventory for event: ${eventId}`);
        const response = await axiosInstance.patch<UpdateInventoryApiResponse>(
          `/api/v1/manager-events/inventory/${eventId}`, // Use the new PATCH endpoint
          { inventory } // Send inventory array in the request body
        );
        console.log("✅ Update Inventory API Response:", response.data);
        return response.data;
      },
      onSuccess: (data, variables) => {
        // variables includes { eventId, inventory }
        console.log(
          `🎉 Inventory update success for event: ${variables.eventId}`
        );
        toast.success(data.message || "Inventory updated successfully!");
        queryClient.invalidateQueries({
          queryKey: [MANAGER_INVENTORY_QUERY_KEY],
        });
        // We might need to manually trigger modal close via a callback later
      },
      onError: (error: any, variables) => {
        console.error(
          `❌ Error updating inventory for event ${variables.eventId}:`,
          error
        );
        const errorMessage =
          error.response?.data?.message ||
          error.message ||
          "Failed to update inventory";
        toast.error(errorMessage);
      },
    });

  // ✨ New helper function for updating inventory ✨
  const updateInventory = (payload: UpdateInventoryPayload) => {
    updateInventoryMutate(payload);
  };

  // --- Return object includes query state AND mutation functions/states ---
  return {
    // Query state
    inventoryItems,
    isLoading: isFetching, // Use renamed loading state for query
    isError,
    error,
    refetch,
    // Mutation functions and states
    toggleActive,
    isToggling,
    deleteEvent,
    isDeleting,

    // ✨ New: Update Inventory Mutation State and Function ✨
    updateInventory,
    isUpdatingInventory,
  };
}
