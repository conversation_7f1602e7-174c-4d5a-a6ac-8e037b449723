// client/src/features/sales/hooks/useSalesOverview.ts
// Custom hook for fetching and managing detailed sales overview data for managers.

import { useQuery } from "@tanstack/react-query";
import { useState, useCallback } from "react";
import axiosInstance from "@/apiAxios/axios"; // Assuming this is your configured axios instance
import {
  ManagerSalesDetailedResponse,
  SalesFilterOptions,
  UseManagerSalesOverviewResult,
} from "../types/sales.types";
import { toast } from "sonner";

// Query key for caching
const MANAGER_DETAILED_SALES_OVERVIEW_QUERY_KEY =
  "managerDetailedSalesOverview";

export function useSalesOverview(): UseManagerSalesOverviewResult {
  // State for pagination (applies to events) and filtering/sorting
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [filterOptions, setFilterOptions] = useState<SalesFilterOptions>({
    sortBy: "eventDate", // Default sort for events
    sortOrder: "desc",
    // dateRange can be added by UI interaction
  });

  // React Query hook for data fetching
  const { data, isLoading, isError, error, refetch } = useQuery<
    ManagerSalesDetailedResponse,
    Error
  >({
    queryKey: [
      MANAGER_DETAILED_SALES_OVERVIEW_QUERY_KEY,
      currentPage,
      filterOptions,
    ],
    queryFn: async (): Promise<ManagerSalesDetailedResponse> => {
      try {
        console.log(
          `🧑‍💼 [useSalesOverview] Fetching detailed sales overview - Page: ${currentPage}, Filters:`,
          filterOptions
        );

        const params = new URLSearchParams();
        params.append("page", currentPage.toString());
        params.append("limit", "5"); // How many events per page; adjust as needed

        if (filterOptions.sortBy) {
          params.append("sortBy", filterOptions.sortBy); // Backend should handle this for events
          params.append("sortOrder", filterOptions.sortOrder);
        }

        if (filterOptions.dateRange?.start) {
          params.append(
            "eventStartDate",
            filterOptions.dateRange.start.toISOString().split("T")[0]
          );
        }
        if (filterOptions.dateRange?.end) {
          params.append(
            "eventEndDate",
            filterOptions.dateRange.end.toISOString().split("T")[0]
          );
        }

        const response = await axiosInstance.get<{
          success: boolean;
          message: string;
          data: ManagerSalesDetailedResponse; // Expecting the new detailed response structure
        }>(`/api/v1/sales/overview?${params.toString()}`);

        console.log(
          "📊 [useSalesOverview] Detailed sales overview data received:",
          response.data
        );

        if (response.data.success && response.data.data) {
          return response.data.data;
        } else {
          toast.error(
            response.data.message || "Failed to fetch detailed sales data"
          );
          throw new Error(
            response.data.message || "Failed to fetch detailed sales data"
          );
        }
      } catch (err: any) {
        console.error(
          "❌ [useSalesOverview] Error fetching detailed sales overview:",
          err
        );
        const errorMessage =
          err.response?.data?.message ||
          err.message ||
          "An unknown error occurred";
        toast.error(errorMessage);
        throw new Error(errorMessage);
      }
    },
    staleTime: 3 * 60 * 1000, // 3 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // Handler for changing the page of events
  const goToPage = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // Handler for updating filter options for events
  const updateFilterOptionsCallback = useCallback(
    (options: Partial<SalesFilterOptions>) => {
      setFilterOptions((prev) => ({
        ...prev,
        ...options,
      }));
      setCurrentPage(1); // Reset to first page when filters change
    },
    []
  );

  return {
    eventsWithSales: data?.data || [], // This now returns ManagerEventSalesDetailsDTO[]
    pagination: data?.pagination || {
      currentPage,
      totalPages: 0,
      totalItems: 0,
      hasNextPage: false,
      hasPrevPage: false,
    },
    isLoading,
    isError,
    error: error as Error | null, // Ensure error is typed correctly
    refetch,
    goToPage,
    updateFilterOptions: updateFilterOptionsCallback, // Use the renamed callback
    filterOptions,
  };
}
