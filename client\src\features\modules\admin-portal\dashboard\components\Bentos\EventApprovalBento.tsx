import { BentoBox } from "@/features/modules/shared/widgets/BentoBox";
import { useQuery } from "@tanstack/react-query";
import { usePendingApprovals } from "../../../event-approval/hooks/useApprovalHooks";
import { Clock } from "lucide-react";
import { TrustLevelBadge } from "@/features/modules/shared/components/Badge/TrustLevelBadge";
import { ApprovalRequest } from "@/features/modules/shared/types/approval.types";

export const EventApprovalBento = () => {
  const { data: pendingApprovals } = usePendingApprovals();

  return (
    <BentoBox
      title="Pending Approvals"
      className="col-span-1 row-span-1"
      header={<Clock className="h-5 w-5 text-primary" />}
    >
      <div className="space-y-4">
        {pendingApprovals?.slice(0, 3).map((approval: ApprovalRequest) => (
          <div
            key={approval.id}
            className="flex justify-between items-center p-2 bg-accent/50 rounded-lg"
          >
            <div>
              <p className="font-medium">{approval.event.title}</p>
              <p className="text-sm text-muted-foreground">
                by {approval.manager.name}
              </p>
            </div>
            <TrustLevelBadge level={approval.manager.trustLevel} />
          </div>
        ))}
      </div>
    </BentoBox>
  );
};
