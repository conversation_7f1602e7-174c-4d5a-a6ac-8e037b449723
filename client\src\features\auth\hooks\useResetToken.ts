import { useState, useEffect } from "react";
import { authApi } from "../api/credentialApi";

export const useResetToken = (token: string) => {
  const [isValidToken, setIsValidToken] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const verifyToken = async () => {
      try {
        const isValid = await authApi.verifyResetToken(token);
        setIsValidToken(isValid);
      } catch (error) {
        setIsValidToken(false);
      } finally {
        setIsLoading(false);
      }
    };

    verifyToken();
  }, [token]);

  return { isValidToken, isLoading };
};
