// This controller handles API requests related to retrieving and managing a manager's own inventory.
import { Request, Response } from "express";
import { asyncHand<PERSON> } from "@/utils/asyncHandler";
import { ManagerInventoryService } from "../services/managerInventory.service";
import ApiError from "@/utils/ApiError";
import { UserRole } from "@prisma/client";
// import { PrismaClient } from "@prisma/client";

// const prisma = new PrismaClient();
import { prisma } from "@/lib/prisma";

interface AuthenticatedRequest extends Request {
    user?: {
        userId: string;
        email: string;
        role: UserRole;
    };
}

export class ManagerInventoryController {
  /**
   * GET /api/v1/manager-events/inventory
   * Retrieves the inventory list for the currently authenticated manager.
   */
  static getManagerInventory = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    console.log("🛒 Request received for manager inventory");
    console.log("👤 Authenticated User Object:", req.user); // Log the whole object again for confirmation

    // 1. Extract manager ID from authenticated user
    // ✨ Access req.user?.userId instead of req.user?.id ✨
    const managerId = req.user?.userId;
    if (!managerId) {
        console.error('❌ Authentication error: User ID (`userId`) not found in request user object.');
        // Ensure authMiddleware is correctly implemented and used before this route
        throw ApiError.unauthorized("User not authenticated or essential user data missing.");
    }
    // Optional: Check role if needed
    // if (req.user?.role !== 'MANAGER') {
    //     throw ApiError.forbidden("Access denied. User is not a manager.");
    // }

    console.log(`👤 Authenticated Manager User ID: ${managerId}`);

    // 2. Call the service to fetch inventory
    const inventoryItems = await ManagerInventoryService.getEventsByManagerId(managerId);

    console.log(`✅ Successfully retrieved ${inventoryItems.length} inventory items.`);

    // 3. Send success response
    res.status(200).json({
      success: true,
      message: "Manager inventory retrieved successfully",
      data: inventoryItems, // Send the array of events
    });
  });

  /**
   * ✨ New Method: PATCH /api/v1/manager-events/inventory/:id/toggle-active ✨
   * Toggles the isActive status for a specific event listing owned by the manager.
   */
  static toggleActive = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
      const { id: eventId } = req.params; // Get event ID from URL parameters
      const managerId = req.user?.userId;

      console.log(`▶️ Request received to toggle isActive for event: ${eventId}`);

      if (!managerId) {
          console.error('❌ Authentication error: User ID (`userId`) not found.');
          throw ApiError.unauthorized("User not authenticated.");
      }
       if (!eventId) {
           console.error('❌ Bad Request: Event ID missing from URL parameters.');
           throw ApiError.badRequest("Event ID is required.");
       }

      const updatedEvent = await ManagerInventoryService.toggleEventActiveStatus(eventId, managerId);

      res.status(200).json({
          success: true,
          message: `Event listing isActive status toggled successfully to ${updatedEvent.isActive}`,
          data: updatedEvent, // Return the full updated event
      });
  });

   /**
   * ✨ New Method: DELETE /api/v1/manager-events/inventory/:id ✨
   * Deletes a specific event listing owned by the manager.
   */
  static deleteEvent = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id: eventId } = req.params;
    const managerId = req.user?.userId;

    console.log(`🗑️ Request received to delete event: ${eventId}`);

    if (!managerId) {
      console.error('❌ Authentication error: User ID (`userId`) not found.');
      throw ApiError.unauthorized("User not authenticated.");
    }
    if (!eventId) {
      console.error('❌ Bad Request: Event ID missing from URL parameters.');
      throw ApiError.badRequest("Event ID is required.");
    }

    const deletedEvent = await ManagerInventoryService.deleteEventById(eventId, managerId);

    res.status(200).json({
      success: true,
      message: "Event listing deleted successfully",
      data: { id: deletedEvent.id }, // Return confirmation ID
    });
  });

  /**
   * ✨ New Method: PATCH /api/v1/manager-events/inventory/:id ✨
   * Updates the inventory list for a specific event listing owned by the manager.
   */
  static updateEventInventory = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
      const { id: eventId } = req.params; // Get event ID from URL parameters
      const managerId = req.user?.userId;
      const { inventory: updatedInventory } = req.body; // Get updated inventory from request body

      console.log(`💾 Request received to update inventory for event: ${eventId}`);

      // Basic validation
      if (!managerId) throw ApiError.unauthorized("User not authenticated.");
      if (!eventId) throw ApiError.badRequest("Event ID is required.");
      if (!updatedInventory) throw ApiError.badRequest("Inventory data is required in the request body.");
      // Service method will validate if it's an array

      const updatedEvent = await ManagerInventoryService.updateInventory(
          eventId,
          managerId,
          updatedInventory // Pass the inventory data from the body
      );

      res.status(200).json({
          success: true,
          message: `Inventory for event listing updated successfully.`,
          data: updatedEvent, // Return the full updated event
      });
  });


  /**
 * Gets current available inventory for a specific event
 * Accounts for both base inventory and any reserved quantities in active checkout sessions
 */
// !--------------create seperate service ----------
static  getEventInventory = asyncHandler(
  async (req: Request, res: Response) => {
    const { eventId } = req.params;
    
    if (!eventId) {
      throw new ApiError(400, 'Event ID is required');
    }
    
    // 1. Get the manager event with its inventory
    const managerEvent = await prisma.managerEvent.findUnique({
      where: { id: eventId },
      select: { 
        inventory: true,
        name: true,
        venue: true,
        date: true
      }
    });
    
    if (!managerEvent) {
      throw new ApiError(404, 'Event not found');
    }
    
    // 2. Get all active/pending checkout sessions for this event
    const now = new Date();
    const activeSessions = await prisma.checkoutSession.findMany({
      where: {
        eventId,
        status: { in: ['PENDING', 'ACTIVE'] },
        expiresAt: { gt: now }
      },
      select: {
        id: true,
        items: true,
      }
    });
    
    // 3. Create a map of reserved inventory by inventoryId
    const reservedByItemId: Record<string, number> = {};
    
    for (const session of activeSessions) {
      try {
        const items = typeof session.items === 'string' 
          ? JSON.parse(session.items) 
          : session.items;
        
        if (Array.isArray(items)) {
          for (const item of items) {
            if (item.inventoryId && typeof item.quantity === 'number') {
              reservedByItemId[item.inventoryId] = (reservedByItemId[item.inventoryId] || 0) + item.quantity;
            }
          }
        }
      } catch (error) {
        console.error(`Error parsing items for session ${session.id}:`, error);
      }
    }
    
    // 4. Adjust inventory quantities to reflect available amounts
    const updatedInventory = Array.isArray(managerEvent.inventory) 
      ? managerEvent.inventory.map((item: any) => {
          if (item.id && typeof item.quantity === 'number') {
            const reserved = reservedByItemId[item.id] || 0;
            return {
              ...item,
              quantity: Math.max(0, item.quantity - reserved),
              totalQuantity: item.quantity, // Original amount before reservations
              reserved: reserved // Add this for transparency (optional)
            };
          }
          return item;
        })
      : [];
    
    // 5. Calculate totals for metadata
    const totalBaseInventory = Array.isArray(managerEvent.inventory)
      ? managerEvent.inventory.reduce((sum: number, item: any) => 
          sum + (typeof item.quantity === 'number' ? item.quantity : 0), 0)
      : 0;
      
    const totalReserved = Object.values(reservedByItemId).reduce((sum, qty) => sum + qty, 0);
    const totalAvailable = Math.max(0, totalBaseInventory - totalReserved);
    
    // 6. Return the updated inventory with availability info
    res.status(200).json({
      success: true,
      message: 'Event inventory retrieved successfully',
      data: {
        inventory: updatedInventory,
        metadata: {
          eventId,
          eventName: managerEvent.name,
          totalBaseInventory,
          totalReserved,
          totalAvailable,
          reservationCount: activeSessions.length
        }
      }
    });
  }
);

}
