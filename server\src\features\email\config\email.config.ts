import { NODE_ENV } from '@/constants';

import { Resend } from 'resend';

 export const resend = new Resend(process.env.RESEND_API_KEY);
/**
 * Email Configuration
 * 
 * Centralized configuration for email services
 */
export const EMAIL_CONFIG = {
  // Email sender addresses
  FROM: {
    // During initial Resend setup, use their onboarding email
    // After domain verification, switch to your domain
    RESET: NODE_ENV === "development" 
      ? "Fanseatmaster <<EMAIL>>"
      : "Fanseatmaster <<EMAIL>>",
    
    VERIFICATION: NODE_ENV === "development"
      ? "Fanseatmaster <<EMAIL>>"
      : "Fanseatmaster <<EMAIL>>",
    
    SUPPORT: NODE_ENV === "development"
      ? "Fanseatmaster <<EMAIL>>"
      : "Fanseatmaster <<EMAIL>>"
  },
  
  // Test recipient for development environment
  TEST_EMAIL: "<EMAIL>",
  
  // Email subjects
  SUBJECTS: {
    RESET_PASSWORD: "Reset Your Password - Fanseatmaster",
    EMAIL_VERIFICATION: "Verify Your Email - Fanseatmaster",
    WELCOME: "Welcome to Fanseatmaster!"
  },
  
  // OTP settings
  OTP: {
    EXPIRY_MINUTES: 15,
    LENGTH: 6
  }
};

// Log configuration in development mode
if (NODE_ENV === 'development') {
  console.log("📧 Email Configuration:", {
    environment: NODE_ENV,
    testEmail: EMAIL_CONFIG.TEST_EMAIL,
    fromAddresses: {
      reset: EMAIL_CONFIG.FROM.RESET,
      verification: EMAIL_CONFIG.FROM.VERIFICATION
    }
  });
}