"use client";
import { MetricCard } from "../../../shared/stats/MetricCard";
import { Users, DollarSign, Calendar, ActivitySquare } from "lucide-react";

export const GlobalMetricsGrid = () => {
  const metrics = [
    {
      title: "Total Platform Revenue",
      value: "$124,567",
      trend: { value: 15, isPositive: true },
      icon: <DollarSign className="h-4 w-4" />,
    },
    {
      title: "Active Users",
      value: "2,345",
      subtext: "1,234 Visitors | 89 Managers",
      trend: { value: 8, isPositive: true },
      icon: <Users className="h-4 w-4" />,
    },
    {
      title: "Live Events",
      value: "45",
      trend: { value: 12, isPositive: true },
      icon: <Calendar className="h-4 w-4" />,
    },
    {
      title: "Today's Transactions",
      value: "234",
      trend: { value: 5, isPositive: true },
      icon: <ActivitySquare className="h-4 w-4" />,
    },
  ];

  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
      {metrics.map((metric, index) => (
        <MetricCard key={index} {...metric} />
      ))}
    </div>
  );
};
