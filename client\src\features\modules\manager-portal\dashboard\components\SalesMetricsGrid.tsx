"use client";
import { MetricCard } from "../../../shared/stats/MetricCard";
import { motion } from "framer-motion";
import { DollarSign, Ticket, Calendar, TrendingUp } from "lucide-react";

export const SalesMetricsGrid = () => {
  // Dummy data for metrics
  const metrics = [
    {
      title: "Total Revenue",
      value: "$15,234",
      trend: { value: 12, isPositive: true },
      icon: <DollarSign className="h-4 w-4" />,
    },
    {
      title: "Active Events",
      value: "8",
      trend: { value: 3, isPositive: true },
      icon: <Calendar className="h-4 w-4" />,
    },
    {
      title: "Tickets Sold",
      value: "1,234",
      trend: { value: 8, isPositive: true },
      icon: <Ticket className="h-4 w-4" />,
    },
    {
      title: "Conversion Rate",
      value: "68%",
      trend: { value: 5, isPositive: true },
      icon: <TrendingUp className="h-4 w-4" />,
    },
  ];

  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
      {metrics.map((metric, index) => (
        <MetricCard
          key={index}
          title={metric.title}
          value={metric.value}
          trend={metric.trend}
          icon={metric.icon}
        />
      ))}
    </div>
  );
};
