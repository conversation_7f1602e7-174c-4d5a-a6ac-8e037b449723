// client/src/features/sales/components/PurchaseAccordion.tsx
// Component to display the details of a single purchase (checkout session)
// within an accordion item in the manager's sales overview.

import React, { useState } from "react";
import {
  CheckoutSessionDetailDTO,
  PurchasedItemDetailDTO,
  BillingAddressDTO,
} from "../types/sales.types";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { formatCurrency, formatDate } from "@/utils/format"; // Assuming you have these
import {
  Upload,
  MessageCircle,
  UserCircle,
  Home,
  ShoppingBag,
  AlertCircle,
  Info,
  DollarSign,
  Edit3,
  ChevronDown,
  Mail,
  Phone,
} from "lucide-react";
import { useUploadTicketTemplate } from "../hooks/useUploadTicketTemplate"; // We'll adapt this
import { toast } from "sonner";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"; // For upload dialog
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { ScrollArea } from "@/components/ui/scroll-area";
import { MessageReplyModal } from './MessageReplyModal';
// 🆕 NEW: Import messaging hooks
import { useManagerBuyerConversation, useUnreadMessageCount } from '@/features/messaging/hooks/useMessaging';

interface PurchaseAccordionProps {
  session: CheckoutSessionDetailDTO;
  eventId: string; // Needed for ticket upload context
  onMessagesClick?: (sessionId: string) => void; // For future messaging integration
}

export const PurchaseAccordion: React.FC<PurchaseAccordionProps> = ({
  session,
  eventId,
  onMessagesClick,
}) => {
  const { uploadTemplate, isUploading } = useUploadTicketTemplate();
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [uploadType, setUploadType] = useState<"template" | "batch">(
    "template"
  ); // 'template' might mean individual ticket for this session now
  const [file, setFile] = useState<File | null>(null);
  
  // 🆕 NEW: Use messaging hooks instead of basic state
  const {
    conversation,
    messages,
    unreadCount,
    hasMessages,
    isModalOpen,
    setIsModalOpen,
    sendReply,
    isLoading: isConversationLoading,
    needsAttention,
  } = useManagerBuyerConversation(session.sessionId);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const handleUpload = async () => {
    if (!file) {
      toast.error("Please select a file to upload.");
      return;
    }

    // Adapt this part based on how your backend handles per-session ticket uploads
    try {
      await uploadTemplate({
        eventId: eventId, // Event context
        checkoutSessionId: session.sessionId, // Specific session
        file,
        type: uploadType, // This 'type' might be re-interpreted by backend for session-specific uploads
      });
      toast.success(
        `Ticket(s) for session ${session.sessionId} uploaded successfully.`
      );
      setFile(null);
      setIsUploadDialogOpen(false);
      // Optionally, you might want to refetch sales data or update session.ticketUploadStatus
    } catch (error) {
      console.error(`😥 Upload error for session ${session.sessionId}:`, error);
      toast.error("Failed to upload ticket file. Please try again.");
    }
  };

  const totalItemsQuantity = session.items.reduce(
    (sum, item) => sum + item.quantity,
    0
  );

  // Format purchase date nicely
  const purchaseDate = new Date(session.purchaseDate);
  const formattedDate = formatDate(session.purchaseDate);

  // Get time in 12-hour format
  const formattedTime = purchaseDate.toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  });

  // Status badge color mapping
  const statusColorMap = {
    COMPLETED:
      "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400",
    PENDING:
      "bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400",
    default: "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300",
  };

  const getStatusColor = (status: string) => {
    return (
      statusColorMap[status as keyof typeof statusColorMap] ||
      statusColorMap.default
    );
  };

  // Get color class for ticket status badge
  const getTicketStatusColor = (status?: string) => {
    switch (status) {
      case "uploaded":
        return "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400";
      case "failed":
        return "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400";
      default:
        return "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300";
    }
  };

  // Get display text for ticket status
  const getTicketStatusText = (status?: string) => {
    switch (status) {
      case "uploaded":
        return "Ticket Uploaded";
      case "failed":
        return "Upload Failed";
      default:
        return "Ticket Pending";
    }
  };

  // 🆕 NEW: Generate static display names (no more type errors)
  const getBuyerDisplayName = () => {
    const shortId = session.sessionId.substring(0, 8);
    return `Buyer #${shortId}`;
  };

  const getEventDisplayName = () => {
    return `Event ${eventId || 'Unknown'}`;
  };

  // 🆕 NEW: Handle message click with proper hook
  const handleMessageClick = () => {
    setIsModalOpen(true);
  };

  return (
    <Accordion type="single" collapsible className="w-full">
      <AccordionItem
        value="purchase-details"
        className="border rounded-lg overflow-hidden shadow-sm"
      >
        <AccordionTrigger className="px-4 py-3 hover:bg-slate-100 dark:hover:bg-slate-800/50 transition-colors">
          <div className="flex flex-col sm:flex-row w-full justify-between items-start sm:items-center gap-2 text-left">
            <div className="flex flex-col">
              <div className="font-medium text-sm">
                Purchase ID: {session.sessionId}
              </div>
              <div className="text-xs text-muted-foreground">
                {formattedDate} at {formattedTime}
              </div>
            </div>
            <div className="flex flex-row items-center gap-2">
              {/* 🆕 NEW: Message indicator badge */}
              {hasMessages && unreadCount > 0 && (
                <Badge 
                  variant="default" 
                  className="bg-orange-500 text-white animate-pulse"
                >
                  {unreadCount} new message{unreadCount > 1 ? 's' : ''}
                </Badge>
              )}
              
              {/* Ticket Status Badge */}
              <Badge
                variant={
                  session.ticketUploadStatus === "uploaded"
                    ? "default"
                    : "outline"
                }
                className={getTicketStatusColor(session.ticketUploadStatus)}
              >
                {getTicketStatusText(session.ticketUploadStatus)}
              </Badge>
            </div>
          </div>
        </AccordionTrigger>
        <AccordionContent className="pb-1">
          <div className="p-4 space-y-6">
            {/* Quick Purchase Summary - Mobile Only */}
            <div className="md:hidden p-3 bg-slate-50 dark:bg-slate-900/50 rounded-md">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <p className="text-xs text-muted-foreground">Items</p>
                  <p className="text-sm font-medium">
                    {totalItemsQuantity} ticket(s)
                  </p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Your Payout</p>
                  <p className="text-sm font-medium text-green-600 dark:text-green-400">
                    {formatCurrency(session.managerPayout, session.currency)}
                  </p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Buyer & Billing Info Card */}
              <Card className="shadow-sm overflow-hidden">
                <CardHeader className="p-4 pb-2 space-y-1">
                  <CardTitle className="text-base font-semibold flex items-center">
                    <UserCircle className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400" />
                    Buyer Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-4 pt-0 text-sm space-y-3">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    <div>
                      <p className="text-xs text-muted-foreground">Buyer ID</p>
                      <p className="font-mono text-xs break-all">
                        {session.buyerInfo.userId}
                      </p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">
                        Purchase Date
                      </p>
                      <p className="text-sm">
                        {formattedDate} {formattedTime}
                      </p>
                    </div>
                  </div>

                  {session.billingAddress ? (
                    <div className="pt-2 mt-2 border-t">
                      <p className="text-xs font-medium text-muted-foreground mb-2 flex items-center">
                        <Home className="h-3 w-3 mr-1 text-muted-foreground" />
                        Billing Address
                      </p>

                      {/* Name and Email if available */}
                      {session.billingAddress.name && (
                        <p className="text-sm font-medium mb-1">
                          {session.billingAddress.name}
                        </p>
                      )}

                      {session.billingAddress.email && (
                        <p className="text-xs text-muted-foreground mb-2 flex items-center">
                          <Mail className="h-3 w-3 mr-1" />
                          {session.billingAddress.email}
                        </p>
                      )}

                      <address className="not-italic text-sm text-muted-foreground">
                        {session.billingAddress.addressLine1}
                        <br />
                        {session.billingAddress.addressLine2 && (
                          <>
                            {session.billingAddress.addressLine2}
                            <br />
                          </>
                        )}
                        {session.billingAddress.city},{" "}
                        {session.billingAddress.state}{" "}
                        {session.billingAddress.postalCode}
                        <br />
                        {session.billingAddress.country}
                      </address>
                    </div>
                  ) : (
                    <p className="italic text-xs text-muted-foreground">
                      No billing address provided.
                    </p>
                  )}
                </CardContent>
              </Card>

              {/* Manager Payout Card */}
              <Card className="shadow-sm">
                <CardHeader className="p-4 pb-2 space-y-1">
                  <CardTitle className="text-base font-semibold flex items-center">
                    <DollarSign className="h-5 w-5 mr-2 text-green-600 dark:text-green-400" />
                    Your Earnings
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-4 pt-2 flex flex-col items-center justify-center text-center">
                  {/* Ticket Upload Status */}
                  <div className="w-full flex justify-between items-center mb-4 text-sm">
                    <span className="text-muted-foreground">Ticket Status:</span>
                    <Badge 
                      variant={session.ticketUploadStatus === 'uploaded' ? 'default' : 'outline'}
                      className={`${getTicketStatusColor(session.ticketUploadStatus)} px-3 py-1`}
                    >
                      {getTicketStatusText(session.ticketUploadStatus)}
                    </Badge>
                  </div>

                  {/* Big Green Circle for Payout */}
                  <div className="my-3">
                    <div 
                      className="w-36 h-36 sm:w-40 sm:h-40 rounded-full 
                             bg-green-50 dark:bg-green-900/30 
                             border-2 border-green-500 dark:border-green-400 
                             flex flex-col items-center justify-center 
                             shadow-md"
                    >
                      <span className="text-xs text-green-700 dark:text-green-300 font-medium">
                        YOUR PAYOUT
                      </span>
                      <span className="text-2xl sm:text-3xl font-bold text-green-600 dark:text-green-500 mt-1 break-all px-2">
                        {formatCurrency(session.managerPayout, session.currency)}
                      </span>
                    </div>
                  </div>
                  
                  <p className="text-xs text-muted-foreground mt-3">
                    This is the amount you&apos;ll receive for this purchase after event completion and confirmation.
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Purchased Items Section */}
            <div>
              <h4 className="text-sm font-semibold mb-3 flex items-center">
                <ShoppingBag className="h-4 w-4 mr-2 text-purple-600 dark:text-purple-400" />
                Purchased Items ({totalItemsQuantity})
              </h4>

              {/* Table for larger screens */}
              <div className="hidden sm:block rounded-md border overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-slate-50 dark:bg-slate-800/50">
                      <TableHead>Item Name</TableHead>
                      <TableHead className="hidden md:table-cell">
                        Details
                      </TableHead>
                      <TableHead className="text-center">Qty</TableHead>
                      <TableHead className="text-right">Listed Price (ea.)</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {session.items.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">
                          {item.name || `Ticket ${index + 1}`}
                        </TableCell>
                        <TableCell className="hidden md:table-cell text-sm">
                          {item.section ? `Section ${item.section}` : ""}
                          {item.row ? `, Row ${item.row}` : ""}
                          {item.seat ? `, Seat ${item.seat}` : ""}
                          {item.ticketFormat ? ` (${item.ticketFormat})` : ""}
                        </TableCell>
                        <TableCell className="text-center">
                          {item.quantity}
                        </TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(item.price, session.currency)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Cards for mobile screens */}
              <div className="sm:hidden space-y-2">
                {session.items.map((item, index) => (
                  <Card key={index} className="shadow-sm">
                    <CardContent className="p-3">
                      <div className="flex justify-between items-start mb-2">
                        <div className="font-medium">
                          {item.name || `Ticket ${index + 1}`}
                        </div>
                        <Badge variant="outline">Qty: {item.quantity}</Badge>
                      </div>

                      <div className="text-xs text-muted-foreground mb-2">
                        {item.section ? `Section ${item.section}` : ""}
                        {item.row ? `, Row ${item.row}` : ""}
                        {item.seat ? `, Seat ${item.seat}` : ""}
                        {item.ticketFormat ? ` (${item.ticketFormat})` : ""}
                      </div>

                      <div className="flex justify-between text-sm mt-1">
                        <span>Price per ticket:</span>
                        <span>
                          {formatCurrency(item.price, session.currency)}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Action buttons */}
            <div className="flex flex-col sm:flex-row justify-end gap-2 pt-2 border-t">
              {/* Upload Tickets Button */}
              <Dialog
                open={isUploadDialogOpen}
                onOpenChange={setIsUploadDialogOpen}
              >
                <DialogTrigger asChild>
                  <Button variant="secondary" className="w-full sm:w-auto">
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Tickets
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>Upload Tickets</DialogTitle>
                    <DialogDescription>
                      Upload tickets for order #
                      {session.sessionId.substring(0, 8)}...
                    </DialogDescription>
                  </DialogHeader>

                  <Tabs
                    defaultValue="template"
                    className="w-full mt-2"
                    onValueChange={(value) =>
                      setUploadType(value as "template" | "batch")
                    }
                  >
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger value="template">Single Ticket</TabsTrigger>
                      <TabsTrigger value="batch">Batch Upload</TabsTrigger>
                    </TabsList>
                    <TabsContent value="template" className="mt-4 space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="template-file">
                          Select Ticket PDF or Image
                        </Label>
                        <Input
                          id="template-file"
                          type="file"
                          accept=".pdf,.jpg,.jpeg,.png"
                          onChange={handleFileChange}
                        />
                        <p className="text-xs text-muted-foreground">
                          For a single ticket for this purchase. Accepted
                          formats: PDF, JPG, PNG.
                        </p>
                      </div>
                    </TabsContent>
                    <TabsContent value="batch" className="mt-4 space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="batch-file">Select ZIP File</Label>
                        <Input
                          id="batch-file"
                          type="file"
                          accept=".zip"
                          onChange={handleFileChange}
                        />
                        <p className="text-xs text-muted-foreground">
                          Upload multiple ticket files in a ZIP archive. Each
                          file should be named with the ticket ID or seat
                          information.
                        </p>
                      </div>
                    </TabsContent>
                  </Tabs>

                  <DialogFooter className="mt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsUploadDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="button"
                      onClick={handleUpload}
                      disabled={isUploading || !file}
                    >
                      {isUploading ? (
                        <>Uploading...</>
                      ) : (
                        <>
                          Upload{" "}
                          {uploadType === "template" ? "Ticket" : "Tickets"}
                        </>
                      )}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              {/* 🆕 UPGRADED: Message Buyer Button with indicators */}
              <Button
                variant="outline"
                className={`w-full sm:w-auto relative ${
                  needsAttention 
                    ? 'border-orange-300 text-orange-700 hover:bg-orange-50' 
                    : ''
                }`}
                onClick={handleMessageClick}
                disabled={isConversationLoading}
              >
                <MessageCircle className="h-4 w-4 mr-2" />
                {hasMessages ? 'View Messages' : 'Message Buyer'}
                
                {/* Unread count indicator */}
                {unreadCount > 0 && (
                  <Badge 
                    className="absolute -top-1 -right-1 h-5 w-5 text-xs p-0 flex items-center justify-center bg-red-500 text-white border-2 border-white"
                  >
                    {unreadCount > 9 ? '9+' : unreadCount}
                  </Badge>
                )}
                
                {/* Attention indicator */}
                {needsAttention && unreadCount === 0 && (
                  <div className="absolute -top-1 -right-1 h-3 w-3 bg-orange-500 rounded-full border-2 border-white animate-pulse" />
                )}
              </Button>
            </div>
          </div>
        </AccordionContent>
      </AccordionItem>

      {/* 🆕 UPGRADED: Message Reply Modal with real messaging */}
      <MessageReplyModal
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        ticketId={session.sessionId}
        buyerName={getBuyerDisplayName()}
        eventName={getEventDisplayName()}
        // Pass the messaging data from the hook
        conversation={conversation}
        messages={messages}
        onSendMessage={sendReply}
        isLoading={isConversationLoading}
      />
    </Accordion>
  );
};
