-- CreateEnum
CREATE TYPE "EventCategory" AS ENUM ('SPORTS', 'MUSIC', 'ARTS');

-- CreateTable
CREATE TABLE "PriorityEvent" (
    "id" TEXT NOT NULL,
    "eventId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "category" "EventCategory" NOT NULL,
    "source" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "venue" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "country" TEXT NOT NULL,
    "image" TEXT,
    "isPopular" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "viewCount" INTEGER NOT NULL DEFAULT 0,
    "clickCount" INTEGER NOT NULL DEFAULT 0,
    "rawEventData" JSONB NOT NULL,
    "addedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PriorityEvent_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "PriorityEvent_category_idx" ON "PriorityEvent"("category");

-- CreateIndex
CREATE INDEX "PriorityEvent_isPopular_idx" ON "PriorityEvent"("isPopular");

-- CreateIndex
CREATE INDEX "PriorityEvent_date_idx" ON "PriorityEvent"("date");

-- CreateIndex
CREATE UNIQUE INDEX "PriorityEvent_eventId_source_key" ON "PriorityEvent"("eventId", "source");
