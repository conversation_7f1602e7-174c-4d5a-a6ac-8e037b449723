/**
 * AdminProfile Component
 * 
 * Profile view for users with admin role.
 * Extends manager profile with administration-specific features.
 */

import React from 'react';
import { ProfileComponentProps } from '../../types/profile.types';
import { ProfileHeader } from '../common/ProfileHeader';
import { ProfileInfo } from '../common/ProfileInfo';
import { ProfileActions } from '../common/ProfileActions';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Shield, Settings, Database, Activity,
  User, UserPlus, Server
} from 'lucide-react';
import { useSession } from 'next-auth/react';
import { AdminDashboardSummary } from './components/AdminDashboardSummary';


import { AdminSystemTab } from './components/AdminSystemTab';
import { AdminUsersTab } from './components/AdminUsersTab';
import { AdminEventsTab } from './components/AdminEventsTab';
import { AdminActivityTab } from './components/AdminActivityTab';

export function AdminProfile({ profile, isEditable = false }: ProfileComponentProps) {
  const { data: session } = useSession();
  const isCurrentUser = session?.user?.email === profile.email;

  return (
    <div className="container max-w-4xl mx-auto px-4 py-8 space-y-6">
      {/* Profile Header with Avatar */}
      <ProfileHeader profile={profile} isEditable={isEditable} />
      
      {/* Profile Actions */}
      <div className="w-full">
        <ProfileActions 
          profile={profile} 
          isCurrentUser={isCurrentUser}
          isFollowing={false}
        />
      </div>
      
      {/* Admin Dashboard Summary - Only shown for the admin's own profile */}
      {isCurrentUser && (
        <AdminDashboardSummary />
      )}
      
      {/* Main Content Area with Tabs */}
      <Tabs defaultValue="about" className="w-full">
        <TabsList className="grid grid-cols-5 max-w-2xl">
          <TabsTrigger value="about">
            <User className="h-4 w-4 mr-2" />
            About
          </TabsTrigger>
          <TabsTrigger value="system">
            <Server className="h-4 w-4 mr-2" />
            System
          </TabsTrigger>
          <TabsTrigger value="users">
            <UserPlus className="h-4 w-4 mr-2" />
            Users
          </TabsTrigger>
          <TabsTrigger value="events">
            <Settings className="h-4 w-4 mr-2" />
            Events
          </TabsTrigger>
          <TabsTrigger value="activity">
            <Activity className="h-4 w-4 mr-2" />
            Activity
          </TabsTrigger>
        </TabsList>
        
        {/* About Tab */}
        <TabsContent value="about" className="space-y-4 mt-6">
          <ProfileInfo profile={profile} isEditable={isEditable} />
        </TabsContent>
        
        {/* System Tab */}
        <TabsContent value="system" className="space-y-4 mt-6">
          <AdminSystemTab isCurrentUser={isCurrentUser} />
        </TabsContent>
        
        {/* Users Tab */}
        <TabsContent value="users" className="space-y-4 mt-6">
          <AdminUsersTab isCurrentUser={isCurrentUser} />
        </TabsContent>
        
        {/* Events Tab */}
        <TabsContent value="events" className="space-y-4 mt-6">
          <AdminEventsTab isCurrentUser={isCurrentUser} />
        </TabsContent>
        
        {/* Activity Tab */}
        <TabsContent value="activity" className="space-y-4 mt-6">
          <AdminActivityTab isCurrentUser={isCurrentUser} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Add debugging if needed
console.log('👨‍💻 AdminProfile loaded');
