import React from 'react';
import { calculateEstimatedWaitTime, formatWaitTime } from '../../helpers/queueHelpers';

type QueuePositionIndicatorProps = {
  position?: number;
  totalWaiting?: number;
  className?: string;
  showEstimatedTime?: boolean;
};

/**
 * A visual indicator of the user's position in the queue
 * Shows a progress bar and position information
 */
export const QueuePositionIndicator: React.FC<QueuePositionIndicatorProps> = ({
  position,
  totalWaiting,
  className = '',
  showEstimatedTime = true
}) => {
  if (!position) return null;
  
  // Calculate progress percentage (at least 5%, at most 95%)
  const progressPercentage = totalWaiting 
    ? Math.max(5, Math.min(95, 100 - ((position / totalWaiting) * 100)))
    : 5; // Default to minimal progress if no total
    
  // Calculate estimated wait time
  const estimatedWaitMinutes = calculateEstimatedWaitTime(position);
  const waitTimeText = formatWaitTime(estimatedWaitMinutes);
  
  return (
    <div className={`w-full ${className}`}>
      <div className="flex justify-between mb-1 text-sm">
        <span className="text-blue-700">Position {position}</span>
        {totalWaiting && (
          <span className="text-blue-500">Total waiting: {totalWaiting}</span>
        )}
      </div>
      
      <div className="w-full bg-blue-100 rounded-full h-2.5 mb-2">
        <div
          className={`bg-blue-600 h-2.5 rounded-full transition-all duration-500 ease-in-out queue-progress-bar`}
          data-progress={progressPercentage}
        />
      </div>
      
      <div className="flex justify-between text-xs text-blue-600">
        <span>You are here</span>
        <span>Checkout</span>
      </div>
      
      {showEstimatedTime && (
        <div className="mt-3 text-sm text-blue-700">
          <span className="font-medium">Estimated wait time:</span> {waitTimeText}
        </div>
      )}
    </div>
  );
};
