// client/src/features/billing-address/components/BillingAddressSelection.tsx
import React, { useState, useEffect } from 'react';
import { useBillingAddresses } from '../hooks/useBillingAddresses';
import { BillingAddress } from '../types/billing-address.types';
import {
  RadioGroup,
  RadioGroupItem,
} from "@/components/ui/radio-group";
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from '@/components/ui/button';
import { AlertCircle, PlusCircle, Home, CheckCircle } from 'lucide-react';
import { formatCountryCode } from '@/utils/country-codes';
import { Badge } from '@/components/ui/badge';

interface BillingAddressSelectionProps {
  onAddressSelect: (addressId: string | undefined) => void;
  onAddNewAddress?: () => void;
  // currentUserId prop seems unused in the provided snippet, can be removed if not needed elsewhere
}

export const BillingAddressSelection: React.FC<BillingAddressSelectionProps> = ({
  onAddressSelect,
  onAddNewAddress,
}) => {
  const { addresses, isLoading, isError, error } = useBillingAddresses();
  const [selectedAddressId, setSelectedAddressId] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (isLoading) return; // Don't do anything while loading

    if (addresses && addresses.length > 0) {
      const currentSelectionStillValid = addresses.some(addr => addr.id === selectedAddressId);
      
      // Only update if no valid selection or current selection is no longer in the list
      if (!selectedAddressId || !currentSelectionStillValid) {
        const defaultAddress = addresses.find(addr => addr.isDefault);
        const newSelection = defaultAddress ? defaultAddress.id : addresses[0].id;
        
        // Update local state only if it's different to avoid potential loops with parent
        if (selectedAddressId !== newSelection) {
            setSelectedAddressId(newSelection);
            onAddressSelect(newSelection); // Inform parent about the initial/default selection
        }
      }
    } else if (addresses && addresses.length === 0) {
      if (selectedAddressId !== undefined) {
        setSelectedAddressId(undefined);
        onAddressSelect(undefined); // Inform parent that no address is selected
      }
    }
  // Dependency array: re-run if addresses list changes or loading state finishes.
  // onAddressSelect is now memoized in parent, so it's stable.
  // selectedAddressId is included to correctly handle cases where addresses are re-fetched and the current selection might become invalid.
  }, [addresses, isLoading, onAddressSelect, selectedAddressId]); 

  const handleSelectionChange = (value: string) => {
    // This function is called by RadioGroup's onValueChange
    setSelectedAddressId(value);
    onAddressSelect(value);
  };

  if (isLoading) {
    return (
      <div className="space-y-2 p-1"> {/* Reduced padding for loading state */}
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-4 text-red-500 bg-red-50 border border-red-200 rounded-md flex items-center">
        <AlertCircle className="h-5 w-5 mr-2" />
        Error: {error?.message || "Could not load addresses."}
      </div>
    );
  }

  return (
    <div className="w-full space-y-3">
      {addresses.length === 0 ? (
        <div className="text-center p-4 border rounded-md bg-slate-50">
          <Home className="h-8 w-8 mx-auto text-slate-400 mb-2" />
          <p className="text-sm text-slate-600 mb-3">No billing addresses found.</p>
          {onAddNewAddress && (
            <Button variant="outline" size="sm" onClick={onAddNewAddress} className="w-full">
              <PlusCircle className="h-4 w-4 mr-2" />
              Add New Address
            </Button>
          )}
        </div>
      ) : (
        <ScrollArea className="h-[180px] pr-3 -mr-3 border rounded-md"> {/* Adjusted height and added border */}
          <RadioGroup 
            value={selectedAddressId} 
            onValueChange={handleSelectionChange} // This will set selectedAddressId
            className="p-3 space-y-2" // Added padding inside scroll area
          >
            {addresses.map((address) => (
              <Label 
                key={address.id}
                htmlFor={`addr-sel-${address.id}`} 
                className={`relative p-3 pr-4 rounded-md border cursor-pointer transition-colors hover:bg-slate-50 ${
                  selectedAddressId === address.id ? 'bg-blue-50 border-blue-300 ring-1 ring-blue-400' : 'bg-white border-slate-200'
                }`}
              >
                {/* Position Default badge at top right */}
                {address.isDefault && (
                  <Badge 
                    variant="outline" 
                    className="absolute top-1 right-1 border-green-600 text-green-700 bg-green-50 text-xs"
                  >
                    <CheckCircle className="h-3 w-3 mr-1" /> Default
                  </Badge>
                )}
                
                <div className="flex flex-col">
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem 
                      value={address.id} 
                      id={`addr-sel-${address.id}`}
                    />
                    <div className="space-y-1">
                      {/* Add name if available */}
                      {address.name && (
                        <span className="block font-medium text-sm text-slate-700">
                          {address.name}
                        </span>
                      )}
                      <span className="font-medium text-sm text-slate-700">
                        {address.addressLine1}
                      </span>
                      {/* City and country now positioned directly below address line 1 */}
                      <span className="block text-xs text-slate-500">
                        {address.city}, {formatCountryCode(address.country)}
                      </span>
                    </div>
                  </div>
                </div>
              </Label>
            ))}
          </RadioGroup>
        </ScrollArea>
      )}
      
      {addresses.length > 0 && onAddNewAddress && (
        <div className="mt-3">
          <Button variant="outline" size="sm" className="w-full" onClick={onAddNewAddress}>
            <PlusCircle className="h-4 w-4 mr-2" />
            Add New Address
          </Button>
        </div>
      )}
    </div>
  );
};
