// Component for displaying and managing event approval requests
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { ApprovalStatus, PendingEventApproval } from "../../types/approval.types";
import { toast } from "sonner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useApproveEvent, useRejectEvent } from "../../hooks/useApprovalHooks";
import { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Badge } from "@/components/ui/badge";
import { ChevronDown, ChevronUp, Layers } from "lucide-react";

interface ApprovalCardProps {
  approval: PendingEventApproval;
  status: ApprovalStatus;
  onApprove: (id: string) => void;
  onReject: (id: string) => void;
  openModal: (event: PendingEventApproval) => void;
}

export const ApprovalCard: React.FC<ApprovalCardProps> = ({
  approval,
  status,
  onApprove,
  onReject,
  openModal,
}) => {
  const approveEventMutation = useApproveEvent();
  const rejectEventMutation = useRejectEvent();
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);
  const [rejectionReason, setRejectionReason] = useState("");
  // New state for inventory section collapse
  const [isInventoryOpen, setIsInventoryOpen] = useState(false);

  const handleApprove = async () => {
    try {
      await approveEventMutation.mutateAsync({
        eventId: approval.id,
        notes: "Approved by admin"
      });
      toast.success("Event approved successfully");
      onApprove(approval.id);
    } catch (error) {
      console.error("Error approving event:", error);
      toast.error("Failed to approve event");
    }
  };

  const openRejectDialog = () => {
    setIsRejectDialogOpen(true);
  };

  const closeRejectDialog = () => {
    setIsRejectDialogOpen(false);
    setRejectionReason("");
  };

  const handleReject = async () => {
    if (!rejectionReason.trim()) {
      toast.error("Please provide a reason for rejection");
      return;
    }

    try {
      await rejectEventMutation.mutateAsync({
        eventId: approval.id,
        notes: rejectionReason
      });
      toast.success("Event rejected successfully");
      onReject(approval.id);
      closeRejectDialog();
    } catch (error) {
      console.error("Error rejecting event:", error);
      toast.error("Failed to reject event");
    }
  };

  // Calculate total inventory
  const totalInventoryItems = approval.inventory.reduce((sum, item) => sum + item.quantity, 0);

  // Helper function to get status badge
  const getStatusBadge = () => {
    switch (status) {
      case "APPROVED":
        return <Badge variant="default" className="bg-green-500 text-white">Approved</Badge>;
      case "REJECTED":
        return <Badge variant="destructive">Rejected</Badge>;
      case "PENDING":
        return <Badge variant="outline" className="border-yellow-500 text-yellow-600">Pending Review</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  return (
    <div className="p-4 border border-b-2 rounded-lg shadow hover:shadow-md transition-shadow">
      {/* Updated header section with status badge */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 mb-4">
        <div>
          <h3 className="font-medium text-lg">{approval.name}</h3>
          <p className="text-sm text-gray-500 mt-1">
            Manager ID: {approval.managerId}
          </p>
        </div>
        <div className="flex gap-2">
          {getStatusBadge()}
          <Badge variant="outline" className="whitespace-nowrap">
            {approval.category}
          </Badge>
          <Button 
            variant="outline" 
            size="sm" 
            className="whitespace-nowrap"
            onClick={() => openModal(approval)}
          >
            View Details
          </Button>
        </div>
      </div>

      {/* Responsive grid layout for event details */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
        <div>
          <p className="text-sm text-gray-500">Date</p>
          <p>{format(new Date(approval.date), 'PPP')}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Location</p>
          <p className="truncate">{approval.venue}, {approval.city}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Country</p>
          <p>{approval.country}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Source</p>
          <p>{approval.source}</p>
        </div>
      </div>

      {/* Show approval notes for approved/rejected events */}
      {(status === "APPROVED" || status === "REJECTED") && approval.approvalNotes && (
        <div className="mb-4 p-3 bg-gray-50 rounded-md">
          <p className="text-sm text-gray-600">
            <strong>Admin Notes:</strong> {approval.approvalNotes}
          </p>
          {approval.approvedAt && (
            <p className="text-xs text-gray-500 mt-1">
              {status === "APPROVED" ? "Approved" : "Rejected"} on {format(new Date(approval.approvedAt), 'PPP')}
            </p>
          )}
        </div>
      )}

      {/* New Collapsible Inventory Summary */}
      <Collapsible
        open={isInventoryOpen}
        onOpenChange={setIsInventoryOpen}
        className="mb-4 border rounded-md"
      >
        <CollapsibleTrigger asChild>
          <Button 
            variant="ghost" 
            className="flex w-full justify-between p-3"
          >
            <div className="flex items-center gap-2">
              <Layers className="h-4 w-4" />
              <span>Inventory Summary ({totalInventoryItems} items)</span>
            </div>
            {isInventoryOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <div className="px-3 pb-3">
            <ScrollArea className="h-32 sm:h-48 rounded-md border">
              <div className="overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Qty</TableHead>
                      <TableHead>Section</TableHead>
                      <TableHead>Row</TableHead>
                      <TableHead>Price</TableHead>
                      <TableHead>Fee</TableHead>
                      <TableHead>Selling</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {approval.inventory.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>{item.section}</TableCell>
                        <TableCell>{item.row}</TableCell>
                        <TableCell>${item.listPrice}</TableCell>
                        <TableCell>
                          ${item.serviceFee ? item.serviceFee : 0}
                        </TableCell>
                        <TableCell>{item.sellingPreference}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </ScrollArea>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Conditional button rendering based on status */}
      {status === "PENDING" ? (
        <div className="flex flex-col xs:flex-row gap-2 sm:justify-end">
          <Button
            variant="outline"
            className="w-full xs:w-auto"
            onClick={openRejectDialog}
            disabled={approveEventMutation.isPending || rejectEventMutation.isPending}
          >
            Reject
          </Button>
          <Button
            variant="default"
            className="w-full xs:w-auto"
            onClick={handleApprove}
            disabled={approveEventMutation.isPending || rejectEventMutation.isPending}
          >
            {approveEventMutation.isPending ? "Approving..." : "Approve"}
          </Button>
        </div>
      ) : (
        <div className="flex flex-col xs:flex-row gap-2 sm:justify-end">
          <Button
            variant="outline"
            className="w-full xs:w-auto"
            onClick={() => openModal(approval)}
          >
            View Full Details
          </Button>
          {status === "APPROVED" && (
            <Button
              variant="default"
              className="w-full xs:w-auto bg-green-600 hover:bg-green-700"
              disabled
            >
              ✓ Approved
            </Button>
          )}
          {status === "REJECTED" && (
            <Button
              variant="destructive"
              className="w-full xs:w-auto"
              disabled
            >
              ✗ Rejected
            </Button>
          )}
        </div>
      )}

      {/* Rejection Dialog */}
      <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Reject Event</DialogTitle>
            <DialogDescription>
              Please provide a reason for rejecting this event.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <Textarea
              placeholder="Explain the reason for rejection..."
              className="min-h-[100px]"
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              required
            />
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={closeRejectDialog}>
              Cancel
            </Button>
            <Button 
              type="button" 
              variant="destructive" 
              onClick={handleReject}
              disabled={!rejectionReason.trim() || rejectEventMutation.isPending}
            >
              {rejectEventMutation.isPending ? "Rejecting..." : "Reject Event"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};