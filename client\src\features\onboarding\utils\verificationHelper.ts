// 🆕 NEW: Shared verification logic helper
"use client";

import { ProfileData } from '@/features/profile/types/profile.types';

export interface VerificationStatus {
  isEmailVerified: boolean;
  isMobileVerified: boolean;
  isProfileComplete: boolean;
  verificationSummary: string;
}

/**
 * Shared verification logic used by both ProfileHeader and Onboarding
 * Same logic as ProfileHeader.tsx
 */
export const getVerificationStatus = (profile: ProfileData | null): VerificationStatus => {
  if (!profile) {
    return {
      isEmailVerified: false,
      isMobileVerified: false,
      isProfileComplete: false,
      verificationSummary: 'Profile not loaded'
    };
  }

  // 🎯 Same logic as ProfileHeader.tsx
  const isEmailVerified = !!profile.emailVerified;
  const isMobileVerified = !!profile.mobileVerified;
  
  // Profile is complete when BOTH email and mobile are verified
  const isProfileComplete = isEmailVerified && isMobileVerified;
  
  // Create summary for display
  const emailStatus = isEmailVerified ? '✅ Email' : '❌ Email';
  const mobileStatus = isMobileVerified ? '✅ Mobile' : '❌ Mobile';
  const verificationSummary = `${emailStatus} ${mobileStatus}`;

  return {
    isEmailVerified,
    isMobileVerified,
    isProfileComplete,
    verificationSummary
  };
};