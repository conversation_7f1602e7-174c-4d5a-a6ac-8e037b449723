/**
 * Types for subscription functionality
 */

export enum SubscriptionPlanType {
  MONTHLY = 'MONTHLY',
  VIP = 'VIP'
}

export interface PlanBenefit {
  title: string;
  description?: string;
  included: boolean;
}

export interface SubscriptionPlan {
  id: SubscriptionPlanType;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  benefits: PlanBenefit[];
  featured?: boolean;
}

export interface SubscriptionStatus {
  active: boolean;
  planType: SubscriptionPlanType | null;
  currentPeriodEnd?: string;
  cancelAtPeriodEnd?: boolean;
  trialEnd?: string | null;
}

export interface SubscriptionResponse {
  success: boolean;
  message?: string;
  data?: {
    status: SubscriptionStatus;
  };
  error?: string;
}

export interface CreateCheckoutResponse {
  success: boolean;
  url?: string;
  error?: string;
}