"use client"

import React, { useEffect } from 'react';
import { useSession } from 'next-auth/react'; // for redirection only
import { useRouter } from 'next/navigation';
import { AdminProfile } from './components/admin/AdminProfile';
import { ManagerProfile } from './components/manager/ManagerProfile';
import { useProfileQuery } from './hooks/useProfileQuery';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, Loader2 } from 'lucide-react';
import { ProfileData } from './types/profile.types';
import { VisitorProfile } from './components/visitor/VisitorProfile';

interface ProfilePageProps {
  expectedRole: 'ADMIN' | 'MANAGER' | 'VISITOR';
}

// Default profile data for missing fields (used only as fallback)
const defaultProfileData: Partial<ProfileData> = {
  fullName: '[No Name Set]',
  email: '<EMAIL>',
  mobile: '******-567-8900',
  bio: 'This is a default bio. Please update your profile.',
  headline: 'New User',
  location: 'City, Country',
  website: 'https://example.com',
  company: 'Company Name Ltd.',
  jobTitle: 'Position Title',
  industry: 'Industry Sector',
  skills: ['Skill 1', 'Skill 2', 'Skill 3'],
  socialLinks: {
    twitter: 'https://twitter.com/',
    linkedin: 'https://linkedin.com/in/',
    github: 'https://github.com/',
  },
};

export const ProfilePage: React.FC<ProfilePageProps> = ({ expectedRole }) => {
  const router = useRouter();
  const { data: session, status: sessionStatus } = useSession();
  
  // Fetch profile from DB via API
  const {
    profile,
    isLoading,
    isError,
    error,
    refetch
  } = useProfileQuery();

  useEffect(() => {
    if (sessionStatus === 'unauthenticated') {
      console.log("🔒 Unauthenticated: Redirecting");
      // router.push('/auth/signin');
      return;
    } else if (session?.user?.role && session.user.role !== expectedRole) {
      console.log('🚫 Access denied - redirecting');
      // router.push('/');
    }
  }, [session, sessionStatus, expectedRole, router]);

  if (isLoading || sessionStatus === 'loading') {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Loading profile information...</p>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="container max-w-4xl py-8">
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {error instanceof Error ? error.message : 'Failed to load profile data'}
          </AlertDescription>
        </Alert>
        <div className="flex justify-center">
          <Button onClick={() => refetch()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  // Merge fetched profile with defaults (fetched data takes higher priority)
  const profileData: ProfileData = {
    ...defaultProfileData,
    ...profile,
    socialLinks: {
      ...(defaultProfileData.socialLinks || {}),
      ...(profile?.socialLinks || {})
    }
  } as ProfileData;

  if (sessionStatus === 'authenticated' && profileData.role !== expectedRole) {
    return (
      <div className="container max-w-4xl py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>
            You do not have permission to view this profile page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <>
      {expectedRole === 'ADMIN' && (
        <AdminProfile profile={profileData} />
      )}
      {expectedRole === 'MANAGER' && (
        <ManagerProfile profile={profileData} />
      )}
      {expectedRole === 'VISITOR' && (
        <VisitorProfile profile={profileData} />
      )}
    </>
  );
};
