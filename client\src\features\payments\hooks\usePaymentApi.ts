/**
 * Payment API hooks using TanStack React Query
 */
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { api } from "@/apiAxios/axios";
import { toast } from "sonner"; // Assuming you're using sonner for toast notifications
import {
  CreatePaymentIntentRequest,
  PaymentIntentResponse,
  PaymentHistoryParams,
  PaymentHistoryResponse,
  CreateSubscriptionCheckoutRequest,
  CreateSubscriptionCheckoutResponse,
  PaymentRecord,
} from "../types/payment.types";

// Payment Query Keys
export const paymentKeys = {
  all: ["payments"] as const,
  history: () => [...paymentKeys.all, "history"] as const,
  historyWithParams: (params?: PaymentHistoryParams) =>
    [...paymentKeys.history(), params] as const,
  details: (id: string) => [...paymentKeys.all, "details", id] as const,
};

/**
 * Hook for creating a payment intent
 */
export function useCreatePaymentIntent() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      request: CreatePaymentIntentRequest
    ): Promise<PaymentIntentResponse> => {
      console.log("💰 Creating payment intent for session:", request.sessionId);
      const response = await api.post<PaymentIntentResponse>(
        "/api/v1/payments/stripe/create-intent-from-checkout",
        request
      );
      return response.data;
    },
    onSuccess: (data) => {
      console.log("✅ Payment intent created:", data.paymentIntentId);
      // No need to invalidate queries here since a new payment doesn't affect
      // existing history yet until it's completed (handled by webhook)
    },
    onError: (error: any) => {
      console.error("❌ Failed to create payment intent:", error);
      toast.error("Payment initialization failed", {
        description:
          error.response?.data?.message || error.message || "Please try again",
      });
    },
  });
}

/**
 * Hook for fetching payment history
 */
export function usePaymentHistory(params?: PaymentHistoryParams) {
  return useQuery({
    queryKey: paymentKeys.historyWithParams(params),
    queryFn: async (): Promise<PaymentHistoryResponse> => {
      // Convert params to query string
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append("page", params.page.toString());
      if (params?.limit) queryParams.append("limit", params.limit.toString());
      if (params?.sortBy) queryParams.append("sortBy", params.sortBy);
      if (params?.sortDirection)
        queryParams.append("sortDirection", params.sortDirection);
      if (params?.status) queryParams.append("status", params.status);

      const query = queryParams.toString();
      const url = `/api/v1/payments/history${query ? `?${query}` : ""}`;

      console.log("🔍 Fetching payment history");
      const response = await api.get<PaymentHistoryResponse>(url);
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false, // Added this option to prevent unnecessary refetches
  });
}

/**
 * Hook for fetching a single payment record
 */
export function usePaymentDetails(paymentId: string) {
  return useQuery({
    queryKey: paymentKeys.details(paymentId),
    queryFn: async (): Promise<PaymentRecord> => {
      console.log(`🔍 Fetching payment details for: ${paymentId}`);
      const response = await api.get(`/api/v1/payments/history/${paymentId}`);
      return response.data.data.payment;
    },
    enabled: !!paymentId,
  });
}

/**
 * Hook for creating a subscription checkout
 */
export function useCreateSubscriptionCheckout() {
  return useMutation({
    mutationFn: async (
      request: CreateSubscriptionCheckoutRequest
    ): Promise<CreateSubscriptionCheckoutResponse> => {
      console.log(
        "📅 Creating subscription checkout for plan:",
        request.planType
      );
      const response = await api.post<CreateSubscriptionCheckoutResponse>(
        "/api/v1/payments/stripe/create-subscription-checkout",
        request
      );
      return response.data;
    },
    onSuccess: (data) => {
      console.log("✅ Subscription checkout created");
      if (data.url) {
        // Redirect to Stripe Checkout
        window.location.href = data.url;
      }
    },
    onError: (error: any) => {
      console.error("❌ Failed to create subscription checkout:", error);
      toast.error("Subscription setup failed", {
        description:
          error.response?.data?.message || error.message || "Please try again",
      });
    },
  });
}

/**
 * Hook for creating a customer portal session
 */
export function useCreateCustomerPortal() {
  return useMutation({
    mutationFn: async (
      returnUrl: string
    ): Promise<{ success: boolean; url?: string; error?: string }> => {
      console.log("👤 Creating customer portal session");
      const response = await api.post(
        "/api/v1/payments/stripe/create-customer-portal",
        {
          returnUrl,
        }
      );
      return response.data;
    },
    onSuccess: (data) => {
      console.log("✅ Customer portal created");
      if (data.url) {
        // Redirect to Stripe Customer Portal
        window.location.href = data.url;
      }
    },
    onError: (error: any) => {
      console.error("❌ Failed to create customer portal:", error);
      toast.error("Billing portal access failed", {
        description:
          error.response?.data?.message || error.message || "Please try again",
      });
    },
  });
}
