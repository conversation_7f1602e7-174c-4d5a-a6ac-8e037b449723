/**
 * Demand Detection Service Module
 *
 * Monitors event demand using Redis counters (views, checkouts) and inventory levels.
 * Calculates a demand score and triggers queue activation/deactivation based on
 * configured thresholds.
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { redisClient } from '@/config/redis'; // Import centralized Redis client
import { QueueService } from './queue.service'; // Service to manage queue state
// TODO: //Replace with actual Event service if available for inventory lookup
// import { EventLookupService } from '@/features/events/services/eventLookup.service';
import {
  // Remove DemandDetectionConfig import as it's no longer an object here
  EventCounters,
  DemandCalculationResult,
  DemandDetectionServiceInterface
} from '../types/demandDetection.types';
// Import the specific constants from the new config file
import {
    QUEUE_ACTIVATION_THRESHOLD,
    QUEUE_DEACTIVATION_THRESHOLD,
    QUEUE_CHECKOUT_WEIGHT,
    QUEUE_COUNTER_EXPIRY_SECONDS,
    QUEUE_MIN_ACTIVE_SECONDS
} from '../config/demand.config'; // Adjust path if needed
import { NODE_ENV } from '@/constants';
import ApiError from '@/utils/ApiError';

// Initialize Prisma Client
const prisma = new PrismaClient();
// Initialize QueueService (no changes needed here)
const queueService = new QueueService();

// --- Redis Key Prefixes --- (No changes needed here)
const VIEW_COUNTER_KEY = (eventId: string) => `demand:views:${eventId}`;
const CHECKOUT_COUNTER_KEY = (eventId: string) => `demand:checkouts:${eventId}`;
// Note: QUEUE_ACTIVATED_AT_KEY potentially could use QUEUE_MIN_ACTIVE_SECONDS or QUEUE_COUNTER_EXPIRY_SECONDS
const QUEUE_ACTIVATED_AT_KEY = (eventId: string) => `demand:queue_activated:${eventId}`;


export class DemandDetectionService implements DemandDetectionServiceInterface {

  /**
   * Increments the view counter for an event in Redis.
   * Sets an expiry to automatically clear old counts.
   * @param eventId - The ID of the event being viewed.
   */
  async incrementViewCounter(eventId: string): Promise<void> {
    if (!eventId) {
      console.warn("⚠️ [DemandDetectionService] Attempted to increment view counter without eventId.");
      return;
    }
    if (!redisClient.isOpen) {
      console.error("❌ [DemandDetectionService] Redis not connected. Cannot increment view counter.");
      return;
    }
    const key = VIEW_COUNTER_KEY(eventId);
    try {
      const pipeline = redisClient.multi();
      pipeline.incr(key);
      // Use the imported constant for expiry
      pipeline.expire(key, QUEUE_COUNTER_EXPIRY_SECONDS);
      await pipeline.exec();

      // Trigger check (no changes needed here)
      this.checkAndTriggerQueue(eventId).catch(err => {
        console.error(`Failed during auto-trigger check after view: ${err.message}`);
      });

    } catch (error) {
      console.error(`❌ [DemandDetectionService] Failed to increment view counter for event ${eventId}:`, error);
    }
  }

  /**
   * Increments the checkout attempt counter for an event in Redis.
   * Sets an expiry to automatically clear old counts.
   * @param eventId - The ID of the event for which checkout was attempted.
   */
  async incrementCheckoutCounter(eventId: string): Promise<void> {
    if (!eventId) {
      console.warn("⚠️ [DemandDetectionService] Attempted to increment checkout counter without eventId.");
      return;
    }
     if (!redisClient.isOpen) {
      console.error("❌ [DemandDetectionService] Redis not connected. Cannot increment checkout counter.");
      return;
    }
    const key = CHECKOUT_COUNTER_KEY(eventId);
    try {
      const pipeline = redisClient.multi();
      pipeline.incr(key);
      // Use the imported constant for expiry
      pipeline.expire(key, QUEUE_COUNTER_EXPIRY_SECONDS);
      await pipeline.exec();

       // Trigger check (no changes needed here)
       this.checkAndTriggerQueue(eventId).catch(err => {
         console.error(`Failed during auto-trigger check after checkout: ${err.message}`);
       });

    } catch (error) {
      console.error(`❌ [DemandDetectionService] Failed to increment checkout counter for event ${eventId}:`, error);
    }
  }

  // --- getEventCounters method remains the same ---
  async getEventCounters(eventId: string): Promise<EventCounters> {
     if (!eventId) return { viewCount: 0, checkoutCount: 0 };
     if (!redisClient.isOpen) {
        console.error("❌ [DemandDetectionService] Redis not connected. Cannot get event counters.");
        return { viewCount: 0, checkoutCount: 0 };
     }
    const viewKey = VIEW_COUNTER_KEY(eventId);
    const checkoutKey = CHECKOUT_COUNTER_KEY(eventId);
    try {
      const [viewCountStr, checkoutCountStr] = await redisClient.mGet([viewKey, checkoutKey]);
      return {
        viewCount: Number(viewCountStr) || 0,
        checkoutCount: Number(checkoutCountStr) || 0
      };
    } catch (error) {
      console.error(`❌ [DemandDetectionService] Failed to get event counters for event ${eventId}:`, error);
      return { viewCount: 0, checkoutCount: 0 };
    }
  }

  // --- getAvailableInventory method remains the same ---
  private async getAvailableInventory(eventId: string): Promise<number> {
    try {
      console.log(`ℹ️ [DemandDetectionService] Fetching available inventory for event: ${eventId}`);
    
      // 1. Get the manager event with its inventory
      const managerEvent = await prisma.managerEvent.findUnique({
        where: { id: eventId },
        select: { 
          inventory: true,
          // Optional: Include other fields if needed for debugging
          name: true 
        }
      });

      if (!managerEvent?.inventory) {
        console.warn(`⚠️ [DemandDetectionService] No inventory data found for event ${eventId}.`);
        return 0;
      }

      // 2. Parse and sum the base inventory from the event
      let totalBaseInventory = 0;
      if (Array.isArray(managerEvent.inventory)) {
        managerEvent.inventory.forEach((item: any) => {
          // Make sure quantity is a valid number
          if (typeof item === 'object' && item !== null && typeof item.quantity === 'number') {
            totalBaseInventory += item.quantity;
          }
        });
      } else {
        console.warn(`⚠️ [DemandDetectionService] Unexpected inventory structure for event ${eventId}.`);
        return 0;
      }

      // 3. Get all active/pending checkout sessions for this event
      const now = new Date();
      const activeSessions = await prisma.checkoutSession.findMany({
        where: {
          eventId,
          status: { in: ['PENDING', 'ACTIVE'] }, // Only consider sessions not completed/cancelled
          expiresAt: { gt: now } // Only consider unexpired sessions
        },
        select: {
          id: true,
          items: true, // This is where the reserved items are stored
        }
      });

      // 4. Calculate the total reserved quantity across all active sessions
      let totalReserved = 0;
      for (const session of activeSessions) {
        try {
          // Parse items JSON if needed (depends on how it's stored)
          const items = typeof session.items === 'string' 
            ? JSON.parse(session.items) 
            : session.items;
        
          // Sum up quantities from all items in this session
          if (Array.isArray(items)) {
            for (const item of items) {
              if (typeof item.quantity === 'number') {
                totalReserved += item.quantity;
              }
            }
          }
        } catch (error) {
          console.error(`❌ [DemandDetectionService] Error parsing items for session ${session.id}:`, error);
          // Continue with next session even if this one fails
        }
      }

      // 5. Calculate final available inventory (ensure non-negative)
      const availableInventory = Math.max(0, totalBaseInventory - totalReserved);
    
      console.log(`ℹ️ [DemandDetectionService] Inventory for event ${eventId}: Base=${totalBaseInventory}, Reserved=${totalReserved}, Available=${availableInventory}`);
    
      return availableInventory;
    } catch (error) {
      console.error(`❌ [DemandDetectionService] Error calculating available inventory for event ${eventId}:`, error);
      return 0; // Default to 0 in case of error
    }
  }

  /**
   * Calculates the demand score for an event based on recent activity and inventory.
   * @param eventId - The ID of the event.
   * @returns The calculated demand score and related data.
   */
  async calculateDemandScore(eventId: string): Promise<DemandCalculationResult> {
    if (!eventId) {
        throw new ApiError(400, "Event ID required to calculate demand score.");
    }
    const counters = await this.getEventCounters(eventId);
    const availableInventory = await this.getAvailableInventory(eventId);

    // Use imported constant for the weight
    const score = (counters.viewCount + (counters.checkoutCount * QUEUE_CHECKOUT_WEIGHT)) / Math.max(availableInventory, 1);

    const result: DemandCalculationResult = {
      eventId,
      score: parseFloat(score.toFixed(2)),
      // Use imported constant for the threshold check
      isHighDemand: score > QUEUE_ACTIVATION_THRESHOLD,
      counters,
      availableInventory
    };

    if (NODE_ENV === 'development') {
       console.log(`📊 [DemandDetectionService] Demand Score Calculated for ${eventId}:`, result);
    }
    return result;
  }

  /**
   * Checks the demand score and activates the queue if the activation threshold is met.
   * @param eventId - The ID of the event to check.
   * @returns True if queue activation was triggered, false otherwise.
   */
  async checkAndTriggerQueue(eventId: string): Promise<boolean> {
    if (!eventId) return false;
    try {
      const currentQueue = await prisma.queue.findFirst({
        where: { eventId },
        select: { isActive: true }
      });

      if (currentQueue?.isActive) {
        return false;
      }

      // Calculate score using the method which now uses imported constants
      const { score, isHighDemand } = await this.calculateDemandScore(eventId);

      // isHighDemand already incorporates the QUEUE_ACTIVATION_THRESHOLD
      if (isHighDemand) {
        console.log(`🚀 [DemandDetectionService] Activation threshold EXCEEDED for event ${eventId} (Score: ${score}). Triggering activation.`);

        // Determine expiry for the activation timestamp key
        // Use the longer of MIN_ACTIVE_DURATION or COUNTER_EXPIRY to ensure the key persists long enough
        const activationKeyExpiry = Math.max(QUEUE_MIN_ACTIVE_SECONDS, QUEUE_COUNTER_EXPIRY_SECONDS);

        // Use set with EX option
        await redisClient.set(
            QUEUE_ACTIVATED_AT_KEY(eventId),
            Date.now().toString(),
            { EX: activationKeyExpiry }
        );

        queueService.activateQueue(eventId).catch(err => { // No changes needed here
           console.error(`🚨 [DemandDetectionService] Error during async queue activation for ${eventId}:`, err);
        });
        return true;
      }
    } catch (error) {
      console.error(`❌ [DemandDetectionService] Error during checkAndTriggerQueue for event ${eventId}:`, error);
    }
    return false;
  }

  /**
   * Checks the demand score for an active queue and deactivates it if conditions are met.
   * @param eventId - The ID of the event queue to check for deactivation.
   * @returns True if queue deactivation was triggered, false otherwise.
   */
  async checkAndDeactivateQueue(eventId: string): Promise<boolean> {
    if (!eventId) return false;
    try {
      const queue = await prisma.queue.findFirst({ // Use findFirst
        where: { eventId },
        select: { isActive: true, activatedAt: true }
      });

      if (!queue || !queue.isActive) {
        return false;
      }

      const now = new Date();
      // Use imported constant for minimum duration check
      if (queue.activatedAt && QUEUE_MIN_ACTIVE_SECONDS > 0) {
          const activeDuration = (now.getTime() - queue.activatedAt.getTime()) / 1000;
          // Use imported constant
          if (activeDuration < QUEUE_MIN_ACTIVE_SECONDS) {
              console.log(`⏳ [DemandDetectionService] Queue ${eventId} active for only ${activeDuration.toFixed(0)}s. Minimum duration (${QUEUE_MIN_ACTIVE_SECONDS}s) not met. Skipping deactivation check.`);
              return false;
          }
      }

      const { score } = await this.calculateDemandScore(eventId); // Uses imported constants internally now

      // Use imported constant for the deactivation threshold check
      if (score < QUEUE_DEACTIVATION_THRESHOLD) {
        console.log(`📉 [DemandDetectionService] Deactivation threshold MET for event ${eventId} (Score: ${score}). Triggering deactivation.`);
        queueService.deactivateQueue(eventId).catch(err => { // No changes needed here
           console.error(`🚨 [DemandDetectionService] Error during async queue deactivation for ${eventId}:`, err);
        });
        return true;
      }
    } catch (error) {
       console.error(`❌ [DemandDetectionService] Error during checkAndDeactivateQueue for event ${eventId}:`, error);
    }
    return false;
  }
}
