// client/src/hooks/useDelayedData.ts
import { useState, useEffect } from 'react';

/**
 * Custom hook to delay the return of data, useful for testing loading states.
 */
export const useDelayedData = <T>(data: T, delay: number): T | undefined => {
  const [delayedData, setDelayedData] = useState<T | undefined>(undefined);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDelayedData(data);
    }, delay);

    return () => clearTimeout(timeoutId);
  }, [data, delay]);

  return delayedData;
};
