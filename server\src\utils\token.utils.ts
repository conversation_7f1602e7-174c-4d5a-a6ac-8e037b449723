import crypto from 'crypto';

export class TokenUtils {
  /**
   * Generates a secure random token for password reset
   * @returns {Object} Object containing original and hashed token
   */
  static generateResetToken() {
    // Generate a random token
    const resetToken = crypto.randomBytes(32).toString('hex');
    
    // Hash the token for storage
    const hashedToken = crypto
      .createHash('sha256')
      .update(resetToken)
      .digest('hex');

    return {
      originalToken: resetToken, // Send to user via email
      hashedToken,              // Store in database
    };
  }

   /**
   * Verifies if a reset token is valid
   * @param providedToken - Token provided by user
   * @param hashedTokenFromDb - Hashed token from database
   * @returns {boolean} Whether the token is valid
   */
   static verifyResetToken(providedToken: string, hashedTokenFromDb: string): boolean {
    const hashedProvidedToken = crypto
      .createHash('sha256')
      .update(providedToken)
      .digest('hex');

    return hashedProvidedToken === hashedTokenFromDb;
  }
}