import { InventoryItem } from "@/features/event-listing/types/eventListing";

export const formatSeats = (item: InventoryItem): string => {
    switch (item.seatingType) {
      case 'GA': return 'General Admission'; // Make it clearer
      case 'Consecutive':
        return item.lowSeatNumber ? `Seats ${item.lowSeatNumber} - ${item.lowSeatNumber + item.quantity - 1}` : 'N/A';
      case 'Odd-even': {
        if (!item.lowSeatNumber) return 'N/A';
        const start = item.lowSeatNumber;
        const isOdd = start % 2 !== 0;
        const lastSeat = start + (item.quantity - 1) * 2;
        return `Seats ${start} - ${lastSeat} (${isOdd ? 'Odd' : 'Even'})`;
      }
      default: return 'N/A';
    }
}