{"name": "client", "version": "0.1.0", "private": true, "packageManager": "pnpm@10.0.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "vercel-build": "next build", "clean": "rimraf .next", "start:prod": "pnpm run clean && next build && node .next/standalone/server.js", "test:prod": "cross-env NODE_ENV=production pnpm run clean && next build && node .next/standalone/server.js", "analyze": "ANALYZE=true next build"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.4", "@radix-ui/react-visually-hidden": "^1.1.2", "@reduxjs/toolkit": "^2.3.0", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^7.2.0", "@tanstack/react-query": "^5.59.19", "@tanstack/react-table": "^8.21.3", "@types/lodash": "^4.17.16", "axios": "^1.7.7", "bcryptjs": "^3.0.2", "chart.js": "^4.4.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "embla-carousel-autoplay": "^8.3.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^11.11.9", "lodash": "^4.17.21", "luxon": "^3.5.0", "next": "15.0.1", "next-auth": "^4.24.10", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.2", "react-phone-input-2": "^2.15.1", "react-query": "^3.39.3", "react-redux": "^9.1.2", "recharts": "^2.15.0", "redux-persist": "^6.0.0", "sharp": "^0.33.5", "sonner": "^1.7.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.23.8"}, "devDependencies": {"@babel/core": "^7.25.8", "@babel/eslint-parser": "^7.25.8", "@babel/preset-env": "^7.25.8", "@babel/preset-react": "^7.25.9", "@tanstack/react-query-devtools": "^5.59.19", "@types/luxon": "^3.4.2", "@types/node": "^20.16.14", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/react-query": "^1.2.9", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.11.0", "@typescript-eslint/parser": "^8.11.0", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "eslint": "^8.57.1", "eslint-config-next": "15.0.0", "lucide-react": "^0.436.0", "postcss": "^8.4.47", "rimraf": "^6.0.1", "tailwindcss": "^3.4.14", "tw-colors": "^3.3.2", "typescript": "^5.6.3"}, "engines": {"pnpm": ">=9.0.0"}}