"use client";
import { TrendingUp } from "lucide-react";
import { BentoBox } from "../../../shared/widgets/BentoBox";

export const RevenueChartBento = () => {
  return (
    <BentoBox
      title="Revenue Analytics"
      className="col-span-2 row-span-2"
      header={<TrendingUp className="h-5 w-5 text-primary" />}
    >
      <div className="space-y-4">
        {/* Placeholder for actual chart implementation */}
        <div className="h-[300px] rounded-lg bg-accent/50 flex items-center justify-center">
          <p className="text-muted-foreground">Revenue Chart Placeholder</p>
        </div>

        {/* Summary stats */}
        <div className="grid grid-cols-3 gap-4">
          {[
            { label: "Daily", value: "$1,234" },
            { label: "Weekly", value: "$8,456" },
            { label: "Monthly", value: "$32,789" },
          ].map((stat, index) => (
            <div key={index} className="text-center">
              <p className="text-sm text-muted-foreground">{stat.label}</p>
              <p className="text-lg font-semibold">{stat.value}</p>
            </div>
          ))}
        </div>
      </div>
    </BentoBox>
  );
};
