"use client";
import { Activity } from "lucide-react";
import { BentoBox } from "../../../shared/widgets/BentoBox";

export const UserActivityBento = () => {
  const activities = [
    {
      id: "1",
      userType: "visitor",
      userName: "<PERSON> Doe",
      action: "Purchased Tickets",
      event: "Summer Festival",
      timestamp: "2 minutes ago",
    },
    {
      id: "2",
      userType: "manager",
      userName: "Sarah Manager",
      action: "Created Event",
      event: "Tech Conference 2024",
      timestamp: "15 minutes ago",
    },
    {
      id: "3",
      userType: "visitor",
      userName: "<PERSON> Smith",
      action: "Requested Refund",
      event: "Sports Championship",
      timestamp: "1 hour ago",
    },
  ];

  return (
    <BentoBox
      title="Live Activity Feed"
      className="col-span-2 row-span-2"
      header={<Activity className="h-5 w-5 text-primary" />}
    >
      <div className="space-y-4 mt-4">
        {activities.map((activity) => (
          <div
            key={activity.id}
            className="flex items-center justify-between p-3 rounded-lg bg-accent/50 hover:bg-accent/70 transition-colors"
          >
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <span
                  className={`px-2 py-1 rounded-full text-xs ${
                    activity.userType === "visitor"
                      ? "bg-blue-100 text-blue-700"
                      : "bg-green-100 text-green-700"
                  }`}
                >
                  {activity.userType}
                </span>
                <h4 className="font-medium">{activity.userName}</h4>
              </div>
              <p className="text-sm text-muted-foreground">
                {activity.action} - {activity.event}
              </p>
            </div>
            <span className="text-xs text-muted-foreground">
              {activity.timestamp}
            </span>
          </div>
        ))}
      </div>
    </BentoBox>
  );
};
