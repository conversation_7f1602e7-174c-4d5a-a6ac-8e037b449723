'use client';

import { useEffect } from 'react';
import { useGetPriorityEventsQuery } from '@/state/api';
import { useAppDispatch } from '@/app/redux';
import { setPriorityEvents, setPriorityEventsError, setPriorityEventsLoading } from '@/state/priorityEventsSlice';

export const InitialDataProvider = ({ children }: { children: React.ReactNode }) => {
  const dispatch = useAppDispatch();
  const { data: priorityEvents, isLoading, isError } = useGetPriorityEventsQuery();

  useEffect(() => {
    dispatch(setPriorityEventsLoading());
    
    if (priorityEvents) {
      dispatch(setPriorityEvents({ events: priorityEvents.data }));
    }
    
    if (isError) {
      dispatch(setPriorityEventsError('Failed to fetch priority events'));
    }
  }, [priorityEvents, isError, dispatch]);

  return <>{children}</>;
};
