import winston from 'winston';
import { LOGGER_CONFIG } from '../config';
import { fileFormatter } from '../formatters';

export const fileTransports = [
  // Error logs
  new winston.transports.File({
    filename: LOGGER_CONFIG.files.error,
    level: 'error',
    format: fileFormatter,
    maxsize: LOGGER_CONFIG.maxFileSize,
    maxFiles: LOGGER_CONFIG.maxFiles
  }),
  
  // Combined logs
  new winston.transports.File({
    filename: LOGGER_CONFIG.files.combined,
    format: fileFormatter,
    maxsize: LOGGER_CONFIG.maxFileSize,
    maxFiles: LOGGER_CONFIG.maxFiles
  })
];