import { User } from '@prisma/client';

export interface OAuthProfile {
  id: string;
  email: string;
  name?: string;
  image?: string;
  provider: string;
}

export interface OAuthAccount {
  provider: string;
  type: string;
  providerAccountId: string;
  access_token: string;
  token_type: string;
  expires_at?: number;
}

export interface OAuthUserData {
  profile: OAuthProfile;
  account: OAuthAccount;
}
