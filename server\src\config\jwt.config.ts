import dotenv from 'dotenv';
dotenv.config();

import jwt from 'jsonwebtoken';
import { User } from '@prisma/client';

// Token configuration for different scenarios
export const JWT_CONFIG = {
  TOKEN_EXPIRY: '5d', // 5 days token lifetime
  COOKIE_OPTIONS: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict' as const,
    maxAge: 5 * 24 * 60 * 60 * 1000 // 5 days in milliseconds
  }
};

// Enhanced token generation with user metadata
export const generateToken = (user: User) => {
  if (!process.env.JWT_SECRET) {
    throw new Error('JWT_SECRET is not defined');
  }

  // Create token with essential user data
  return jwt.sign(
    { 
      userId: user.id, 
      email: user.email, 
      role: user.role,
      // Add any additional claims needed for your application
    },
    process.env.JWT_SECRET,
    { expiresIn: JWT_CONFIG.TOKEN_EXPIRY }
  );
};

// Token verification utility
export const verifyToken = (token: string) => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET!);
  } catch (error) {
    throw new Error('Invalid token');
  }
};
