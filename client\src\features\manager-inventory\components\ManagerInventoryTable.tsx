// Main component to display the manager's event inventory in a table.
import React, { useState, useMemo } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { format } from 'date-fns';
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";

import { useManagerInventoryQuery } from '../hooks/useManagerInventoryQuery';
import { ManagerInventoryItem } from '../types/inventory.types';
import { ApprovalStatusBadge } from './shared/ApprovalStatusBadge';
import { EventStatusBadge } from './shared/EventStatusBadge';
import { ManagerInventoryActions } from './ManagerInventoryActions';
import { ManagerInventoryFilter } from './ManagerInventoryFilter';
import { EventDetailsModal } from './EventDetailsModal';
import { EditInventoryModal } from './EditInventoryModal';
import { InventoryEventCard } from './InventoryEventCard';

export const ManagerInventoryTable: React.FC = () => {
  // ✨ Destructure mutation functions and states from the hook ✨
  const {
    inventoryItems,

    isLoading,
    isError,
    error,




    toggleActive,
    isToggling,
    deleteEvent,
    isDeleting,
    updateInventory, 
    isUpdatingInventory 
  } = useManagerInventoryQuery();

  const [filterText, setFilterText] = useState('');
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedItemForView, setSelectedItemForView] = useState<ManagerInventoryItem | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [itemToEdit, setItemToEdit] = useState<ManagerInventoryItem | null>(null);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPageDesktop = 15; // Show 10 items per page on desktop
  const itemsPerPageMobile = 5;   // Show 5 items per page on mobile

  // Filter items based on search text
  const filteredItems = useMemo(() => {
    if (!filterText) return inventoryItems;
    const lowerCaseFilter = filterText.toLowerCase();
    return inventoryItems.filter(item =>
      item.name.toLowerCase().includes(lowerCaseFilter) ||
      item.venue.toLowerCase().includes(lowerCaseFilter) ||
      item.city.toLowerCase().includes(lowerCaseFilter)
    );
  }, [inventoryItems, filterText]);

  // Calculate pagination for desktop view
  const totalPagesDesktop = Math.ceil(filteredItems.length / itemsPerPageDesktop);
  const paginatedItemsDesktop = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPageDesktop;
    console.log(`📄 Desktop Slice: Page ${currentPage}, Start ${startIndex}, Items ${itemsPerPageDesktop}`);
    return filteredItems.slice(startIndex, startIndex + itemsPerPageDesktop);
  }, [filteredItems, currentPage, itemsPerPageDesktop]);

  // Calculate pagination for mobile view
  const totalPagesMobile = Math.ceil(filteredItems.length / itemsPerPageMobile);
  const paginatedItemsMobile = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPageMobile;
    console.log(`📱 Mobile Slice: Page ${currentPage}, Start ${startIndex}, Items ${itemsPerPageMobile}`);
    return filteredItems.slice(startIndex, startIndex + itemsPerPageMobile);
  }, [filteredItems, currentPage, itemsPerPageMobile]);

  // Handle page changes
  const goToNextPage = () => {
    // The button's disabled state handles the upper limit. Just increment.
    setCurrentPage(prev => prev + 1);
    console.log('➡️ Going to next page. New target page state:', currentPage + 1);
  };

  const goToPrevPage = () => {
    // Lower limit is always 1.
    setCurrentPage(prev => Math.max(prev - 1, 1));
    console.log('⬅️ Going to previous page. New target page state:', Math.max(currentPage - 1, 1));
  };

  // Reset to page 1 when filter changes
  const handleFilterChange = (text: string) => {
    setFilterText(text);
    setCurrentPage(1); // Reset to first page when filtering
    console.log('🔄 Filter changed, resetting to page 1');
  };

  const handleOpenViewModal = (item: ManagerInventoryItem) => {
    setSelectedItemForView(item);
    setIsViewModalOpen(true);
  };
  
  const handleCloseViewModal = () => {
    setIsViewModalOpen(false);

    setTimeout(() => setSelectedItemForView(null), 150);
  };

  const handleOpenEditModal = (item: ManagerInventoryItem) => {
    console.log("✏️ Editing inventory for:", item.name, item.id);


    setItemToEdit(item);
    setIsEditModalOpen(true);
  };
  
  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);

    setTimeout(() => setItemToEdit(null), 150);
  };

  // Skeleton loaders
  const renderTableSkeleton = (count = 5) => (
    Array.from({ length: count }).map((_, index) => (
      <TableRow key={`skeleton-${index}`}>
        <TableCell><Skeleton className="h-5 w-40" /></TableCell>
        <TableCell className="hidden sm:table-cell"><Skeleton className="h-5 w-24" /></TableCell>
        <TableCell className="hidden lg:table-cell"><Skeleton className="h-5 w-32" /></TableCell>
        <TableCell className="hidden md:table-cell"><Skeleton className="h-5 w-20" /></TableCell>
        <TableCell className="hidden md:table-cell"><Skeleton className="h-5 w-20" /></TableCell>
        <TableCell><Skeleton className="h-5 w-20" /></TableCell>
        <TableCell><Skeleton className="h-5 w-8" /></TableCell>
        <TableCell><Skeleton className="h-8 w-8" /></TableCell>
      </TableRow>
    ))
  );

  const renderCardSkeleton = (count = 5) => (
    Array.from({ length: count }).map((_, index) => (
      <div key={`card-skeleton-${index}`} className="border rounded-lg overflow-hidden shadow-sm p-4 space-y-3">
        <div className="flex justify-between">
          <Skeleton className="h-6 w-40" />
          <Skeleton className="h-8 w-8 rounded-full" />
        </div>
        <div className="space-y-2">
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-4 w-36" />
          <Skeleton className="h-4 w-24" />
        </div>
        <div className="pt-3 border-t flex justify-between">
          <Skeleton className="h-5 w-24" />
          <Skeleton className="h-5 w-16" />
        </div>
      </div>
    ))
  );

  // Pagination Controls Component
  const PaginationControls = ({ totalPages }: { totalPages: number }) => {
    const actualTotalPages = totalPages || 1; // Ensure totalPages is at least 1 for display
    console.log(`🕹️ Pagination Controls Render: Current Page ${currentPage}, Total Pages ${actualTotalPages}`);
    return (
      <div className="flex items-center justify-between mt-4 px-2 py-2 border-t">
        <p className="text-sm text-muted-foreground">
          Page {currentPage} of {actualTotalPages}
        </p>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={goToPrevPage}
            disabled={currentPage === 1 || actualTotalPages === 0}
          >
            <ChevronLeft className="h-4 w-4" />
            <span className="sr-only">Previous Page</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={goToNextPage}
            disabled={currentPage === actualTotalPages || actualTotalPages === 0}
          >
            <ChevronRight className="h-4 w-4" />
            <span className="sr-only">Next Page</span>
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4 min-h-screen">
      <div className="flex justify-end px-1">
        <ManagerInventoryFilter filterText={filterText} onFilterChange={handleFilterChange} />
      </div>

      {/* Desktop Table View */}
      <div className="hidden sm:block border rounded-lg overflow-hidden shadow-sm">
        <div className="overflow-x-auto">
          <Table className="min-w-full">
            <TableHeader>
              <TableRow className="bg-muted/50 hover:bg-muted/80">
                <TableHead className="w-[250px] sm:w-[300px] font-semibold">Event Name</TableHead>
                <TableHead className="w-[130px] font-semibold">Date</TableHead>
                <TableHead className="hidden lg:table-cell w-[200px] font-semibold">Venue</TableHead>
                <TableHead className="hidden md:table-cell w-[120px] font-semibold">City</TableHead>
                <TableHead className="hidden md:table-cell w-[110px] font-semibold">Status</TableHead>
                <TableHead className="w-[110px] font-semibold">Approval</TableHead>
                <TableHead className="w-[60px] font-semibold">Active</TableHead>
                <TableHead className="w-[50px] text-right font-semibold">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (

                renderTableSkeleton(itemsPerPageDesktop)
              ) : isError ? (
                <TableRow><TableCell colSpan={8} className="text-center text-destructive py-8 h-64 align-middle">Error: {error?.message || 'Unknown error'}</TableCell></TableRow>
              ) : filteredItems.length === 0 ? (
                <TableRow><TableCell colSpan={8} className="text-center text-muted-foreground py-8 h-64 align-middle">{filterText ? 'No matches found.' : 'No events listed.'}</TableCell></TableRow>
              ) : (

                paginatedItemsDesktop.map((item: ManagerInventoryItem) => (
                  <TableRow key={item.id} className={`hover:bg-muted/50 ${!item.isActive ? 'opacity-70' : ''}`}>
                    <TableCell className="font-medium truncate max-w-[250px] sm:max-w-[300px]" title={item.name}>
                      {item.name}
                    </TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {format(item.date, 'MMM d, yyyy')}
                    </TableCell>
                    <TableCell className="hidden lg:table-cell text-sm text-muted-foreground truncate max-w-[200px]" title={item.venue}>
                      {item.venue}
                    </TableCell>
                    <TableCell className="hidden md:table-cell text-sm text-muted-foreground">
                      {item.city}
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      <EventStatusBadge status={item.status} isActive={item.isActive} />
                    </TableCell>
                    <TableCell>
                      <ApprovalStatusBadge status={item.approvalStatus} />
                    </TableCell>
                    <TableCell>
                      <span className={`text-xs font-semibold px-2 py-0.5 rounded-full ${item.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                        {item.isActive ? 'Yes' : 'No'}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <ManagerInventoryActions
                        item={item}
                        toggleActive={toggleActive}
                        isToggling={isToggling}
                        deleteEvent={deleteEvent}
                        isDeleting={isDeleting}
                        onViewDetails={handleOpenViewModal}
                        onEditInventory={handleOpenEditModal}
                      />
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Desktop pagination */}
        {!isLoading && !isError && filteredItems.length > 0 && (
          <PaginationControls totalPages={totalPagesDesktop} />
        )}
      </div>

      {/* Mobile Card View */}
      <div className="sm:hidden">
        <div className="space-y-3">
          {isLoading ? (

            renderCardSkeleton(itemsPerPageMobile)
          ) : isError ? (
            <div className="text-center text-destructive py-8 border rounded-md h-64 flex items-center justify-center">
              Error: {error?.message || 'Unknown error'}
            </div>
          ) : filteredItems.length === 0 ? (
            <div className="text-center text-muted-foreground py-8 border rounded-md h-64 flex items-center justify-center">
              {filterText ? 'No matches found.' : 'No events listed.'}
            </div>
          ) : (

            paginatedItemsMobile.map((item: ManagerInventoryItem) => (
              <InventoryEventCard
                key={item.id}
                item={item}
                toggleActive={toggleActive}
                isToggling={isToggling}
                deleteEvent={deleteEvent}
                isDeleting={isDeleting}
                onViewDetails={handleOpenViewModal}
                onEditInventory={handleOpenEditModal}
              />
            ))
          )}

          {/* Mobile pagination */}
          {!isLoading && !isError && filteredItems.length > 0 && (
            <PaginationControls totalPages={totalPagesMobile} />
          )}
        </div>
      </div>

      {/* Modals */}
      <EventDetailsModal
        item={selectedItemForView}
        isOpen={isViewModalOpen}
        onClose={handleCloseViewModal}
      />
      {itemToEdit && (
        <EditInventoryModal
          item={itemToEdit}
          isOpen={isEditModalOpen}
          onClose={handleCloseEditModal}
          updateInventory={updateInventory}
          isUpdatingInventory={isUpdatingInventory}
        />
      )}
    </div>
  );
};
