// server/src/features/sales/services/sales.service.ts
// Service layer for handling manager sales overview logic with detailed checkout sessions

import { prisma } from "@/lib/prisma";
import ApiError from "@/utils/ApiError";
import { CheckoutSessionStatus, Prisma } from "@prisma/client";
import { 
    ManagerSalesDetailedResponse, 
    ManagerEventSalesDetailsDTO,
    BuyerInfoDTO,
    PurchasedItemDetailDTO,
    CheckoutSessionDetailDTO,
    BillingAddressDTO
} from "../types/sales.types";

// --- Constants ---
const MANAGER_PAYOUT_RATE = 0.90; // Manager keeps 90% (Platform takes 10%)

// --- Interfaces for JSON parsing ---

// Structure expected in CheckoutSession.items
interface CheckoutSessionItemForSales {
    inventoryId: string;
    quantity: number;
    price: number;
    subtotal: number;
    name?: string;
    section?: string;
    row?: string | number;
    seat?: string;
    attributes?: string[];
    ticketFormat?: string;
}

// Structure expected *within* the ManagerEvent.inventory JSON array
interface ManagerInventoryItemDetail {
    id: string;
    listPrice: number;
    quantity: number;
    section?: string;
    row?: number | string;
}

// Basic structure of billing address stored in checkout session
interface StoredBillingAddress {
    id?: string;
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
    name: string;
    email: string;
}

export class SalesService {

    /**
     * Parses items JSON from CheckoutSession.
     */
    private static parseCheckoutItems(itemsJson: Prisma.JsonValue): CheckoutSessionItemForSales[] {
      try {
        if (!Array.isArray(itemsJson)) {
          console.warn("🛒 Checkout items is not an array:", itemsJson);
          return [];
        }
        return itemsJson as unknown as CheckoutSessionItemForSales[];
      } catch (error) {
        console.error("❌ Error parsing checkout session items:", error);
        return [];
      }
    }

    /**
     * Parses inventory JSON from ManagerEvent.
     */
    private static parseManagerInventory(inventoryJson: Prisma.JsonValue | null): ManagerInventoryItemDetail[] {
      try {
        if (!inventoryJson || !Array.isArray(inventoryJson)) {
          console.warn("📦 Manager inventory is not an array or is null:", inventoryJson);
          return [];
        }
        return inventoryJson as unknown as ManagerInventoryItemDetail[];
      } catch (error) {
        console.error("❌ Error parsing manager inventory items:", error);
        return [];
      }
    }

    /**
     * Parses billing address JSON from CheckoutSession.
     */
    private static parseBillingAddress(addressJson: Prisma.JsonValue | null): BillingAddressDTO | undefined {
      try {
        if (!addressJson || typeof addressJson !== 'object') {
          return undefined;
        }
      
        const address = addressJson as unknown as StoredBillingAddress;
      
        // Basic validation to ensure we have the required fields
        if (!address.addressLine1 || !address.city || !address.state || !address.postalCode || !address.country) {
          console.warn("🏠 Incomplete billing address data:", address);
          return undefined;
        }
      
        return {
          id: address.id,
          addressLine1: address.addressLine1,
          addressLine2: address.addressLine2,
          city: address.city,
          state: address.state,
          postalCode: address.postalCode,
          country: address.country,
          name: address.name ,
          email: address.email 
        };
      } catch (error) {
        console.error("❌ Error parsing billing address:", error);
        return undefined;
      }
    }

    /**
     * Calculates the total payout for a manager from a single checkout session based on listPrice.
     * @param sessionItems - Parsed items from the CheckoutSession.
     * @param eventInventoryMap - Map of inventoryId -> listPrice for the specific event.
     * @returns The calculated payout for the manager for this session.
     */
    private static calculateManagerPayoutForSession(
      sessionItems: CheckoutSessionItemForSales[],
      eventInventoryMap: Map<string, number> // Map<inventoryId, listPrice>
    ): number {
      let sessionPayout = 0;
      for (const item of sessionItems) {
        const listPrice = eventInventoryMap.get(item.inventoryId);
        if (listPrice !== undefined && listPrice !== null) {
          // Calculate payout for this item: listPrice * quantity * payoutRate
          sessionPayout += listPrice * item.quantity * MANAGER_PAYOUT_RATE;
        } else {
          console.warn(`⚠️ Could not find listPrice for inventoryId ${item.inventoryId} in event inventory during payout calculation.`);
          // Fallback to the price stored in the checkout session, less accurate but better than zero
          sessionPayout += item.price * item.quantity * MANAGER_PAYOUT_RATE;
        }
      }
      return sessionPayout;
    }
  
    /**
     * Converts CheckoutSession item to PurchasedItemDetailDTO
     */
    private static mapToPurchasedItemDTO(item: CheckoutSessionItemForSales): PurchasedItemDetailDTO {
      return {
        inventoryId: item.inventoryId,
        quantity: item.quantity,
        name: item.name || `Ticket ID: ${item.inventoryId}`,
        price: item.price,
        subtotal: item.subtotal || (item.price * item.quantity),
        section: item.section,
        row: item.row,
        seat: item.seat,
        attributes: item.attributes,
        ticketFormat: item.ticketFormat
      };
    }

    /**
     * Fetches detailed sales data for events managed by a specific manager,
     * including individual checkout sessions for each event.
     * 
     * @param managerId - The ID of the manager.
     * @param page - The current page number (1-based).
     * @param limit - The number of items per page.
     * @returns Promise<ManagerSalesDetailedResponse>
     */
    static async getManagerSalesOverview(
      managerId: string, 
      page: number = 1, 
      limit: number = 10
    ): Promise<ManagerSalesDetailedResponse> {
      console.log(`📊 [SalesService] Fetching detailed sales for manager: ${managerId}, Page: ${page}, Limit: ${limit}`);
      const skip = (page - 1) * limit;

      try {
        // 1. Find all ManagerEvents managed by this manager, including inventory
        const managedEvents = await prisma.managerEvent.findMany({
          where: { managerId: managerId },
          select: {
            id: true,
            name: true,
            date: true,
            venue: true,
            city: true,
            country: true,
            image: true,
            inventory: true,
          }
        });

        if (managedEvents.length === 0) {
          console.log(`ℹ️ [SalesService] Manager ${managerId} manages no events.`);
          return { 
            data: [], 
            pagination: { 
              currentPage: 1, 
              totalPages: 0, 
              totalItems: 0, 
              hasNextPage: false, 
              hasPrevPage: false 
            } 
          };
        }

        const managedEventIds = managedEvents.map(event => event.id);

        // Create a lookup map for event details and inventory list prices
        const eventDetailsAndInventoryMap = new Map<string, {
          details: Omit<typeof managedEvents[0], 'inventory'>;
          inventoryMap: Map<string, number>; // Map<inventoryId, listPrice>
        }>();

        managedEvents.forEach(event => {
          const inventoryItems = this.parseManagerInventory(event.inventory);
          const inventoryMap = new Map<string, number>();
        
          inventoryItems.forEach(invItem => {
            if (typeof invItem.listPrice === 'number' && !isNaN(invItem.listPrice)) {
              inventoryMap.set(invItem.id, invItem.listPrice);
            } else {
              console.warn(`❓ Invalid or missing listPrice for inventory item ${invItem.id} in event ${event.id}`);
            }
          });
        
          const { inventory, ...details } = event;
          eventDetailsAndInventoryMap.set(event.id, { details, inventoryMap });
        });

        // 2. Find all *completed* CheckoutSessions related to these events
        const completedSessions = await prisma.checkoutSession.findMany({
          where: {
            eventId: { in: managedEventIds },
            status: CheckoutSessionStatus.COMPLETED,
          },
          include: {
            user: {
              select: {
                id: true,
                // Include other fields only if needed and privacy-compliant
              }
            }
          },
          orderBy: {
            completedAt: 'desc',
          }
        });

        if (completedSessions.length === 0) {
          console.log(`ℹ️ [SalesService] No completed sales found for events managed by ${managerId}.`);
          return { 
            data: [], 
            pagination: { 
              currentPage: 1, 
              totalPages: 0, 
              totalItems: 0, 
              hasNextPage: false, 
              hasPrevPage: false 
            } 
          };
        }

        // 3. Group sessions by eventId and convert to DTOs
        const eventSessionsMap = new Map<string, {
          totalTicketsSold: number;
          totalManagerPayout: number;
          currency: string;
          sessions: CheckoutSessionDetailDTO[];
        }>();

        for (const session of completedSessions) {
          if (!session.eventId) {
            console.warn(`⚠️ Skipping session ${session.id} due to missing eventId.`);
            continue;
          }

          const eventData = eventDetailsAndInventoryMap.get(session.eventId);
          if (!eventData) {
            console.warn(`⚠️ Could not find event details for eventId ${session.eventId} linked to session ${session.id}.`);
            continue;
          }

          // Parse session items
          const sessionItems = this.parseCheckoutItems(session.items);
          const ticketsInSession = sessionItems.reduce((sum, item) => sum + (item.quantity || 0), 0);
        
          // Calculate manager payout for this session
          const managerPayoutForSession = this.calculateManagerPayoutForSession(
            sessionItems, 
            eventData.inventoryMap
          );

          // Parse billing address if present
          const billingAddress = this.parseBillingAddress(session.billingAddress);

          // Create buyer info
          const buyerInfo: BuyerInfoDTO = {
            userId: session.userId
            // Only add these if truly needed and privacy-compliant:
            // fullName: session.user?.fullName,
            // email: session.user?.email
          };

          // Map session items to DTOs
          const itemDTOs: PurchasedItemDetailDTO[] = sessionItems.map(item => 
            this.mapToPurchasedItemDTO(item)
          );

          // Create the checkout session detail DTO
          const sessionDetailDTO: CheckoutSessionDetailDTO = {
            sessionId: session.id,
            status: session.status,
            purchaseDate: session.completedAt?.toISOString() || session.updatedAt.toISOString(),
            buyerInfo,
            billingAddress,
            items: itemDTOs,
            totalAmountPaid: session.total,
            subtotal: session.subtotal,
            serviceFee: session.serviceFee,
            tax: session.tax || undefined,
            currency: session.currency,
            managerPayout: managerPayoutForSession,
            ticketUploadStatus: 'pending', // Placeholder - would be determined by ticket upload status logic
          };

          // Add to or create entry in the sessions map
          const eventSessionData = eventSessionsMap.get(session.eventId) || {
            totalTicketsSold: 0,
            totalManagerPayout: 0,
            currency: session.currency,
            sessions: []
          };

          eventSessionData.totalTicketsSold += ticketsInSession;
          eventSessionData.totalManagerPayout += managerPayoutForSession;
          eventSessionData.sessions.push(sessionDetailDTO);
        
          eventSessionsMap.set(session.eventId, eventSessionData);
        }

        // 4. Build final event sales details DTOs
        const allEventSalesData: ManagerEventSalesDetailsDTO[] = [];

        for (const [eventId, sessionData] of eventSessionsMap.entries()) {
          const eventDetails = eventDetailsAndInventoryMap.get(eventId)?.details;
        
          if (!eventDetails) {
            console.warn(`⚠️ Missing event details for eventId ${eventId} when building final DTOs.`);
            continue;
          }

          allEventSalesData.push({
            eventId,
            eventName: eventDetails.name,
            eventDate: eventDetails.date.toISOString(),
            eventVenue: eventDetails.venue,
            eventCity: eventDetails.city || undefined,
            eventCountry: eventDetails.country || undefined,
            totalTicketsSold: sessionData.totalTicketsSold,
            totalRevenue: sessionData.totalManagerPayout,
            currency: sessionData.currency,
            eventImageUrl: eventDetails.image,
            sessions: sessionData.sessions
          });
        }

        // 5. Sort by event date (descending - most recent first) and apply pagination
        const sortedEventSalesData = allEventSalesData.sort(
          (a, b) => new Date(b.eventDate).getTime() - new Date(a.eventDate).getTime()
        );

        const totalItems = sortedEventSalesData.length;
        const paginatedEventSalesData = sortedEventSalesData.slice(skip, skip + limit);
        const totalPages = Math.ceil(totalItems / limit);

        // 6. Return the final response
        return {
          data: paginatedEventSalesData,
          pagination: {
            currentPage: page,
            totalPages,
            totalItems,
            hasNextPage: page < totalPages,
            hasPrevPage: page > 1
          }
        };
      } catch (error) {
        console.error(`❌ [SalesService] Error fetching detailed sales for manager ${managerId}:`, error);
        if (error instanceof ApiError) throw error;
        throw new ApiError(500, "Could not retrieve sales data.");
      }
    }
}
