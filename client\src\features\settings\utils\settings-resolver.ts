import dynamic from 'next/dynamic'
import { FC } from 'react'

// Define allowed roles using a union type
type AllowedRoles = 'ADMIN' | 'MANAGER' | 'VISITOR'

// Define the components mapping with specific roles
const settingsComponents: Record<AllowedRoles, FC> = {
  ADMIN: dynamic(() => 
    import('../components/AdminSettings').then(mod => mod.AdminSettings)
  ) as FC,
  MANAGER: dynamic(() => 
    import('../components/ManagerSettings').then(mod => mod.ManagerSettings)
  ) as FC,
  VISITOR: dynamic(() => 
    import('../components/VisitorSettings').then(mod => mod.VisitorSettings)
  ) as FC,
}

export function getRoleSpecificSettingsComponent(role: string): FC {
  // Convert role to uppercase and assert it as AllowedRoles
  const upperRole = role as AllowedRoles
  // Return the component for the role or fallback to visitor settings
  return settingsComponents[upperRole] || settingsComponents.VISITOR
}