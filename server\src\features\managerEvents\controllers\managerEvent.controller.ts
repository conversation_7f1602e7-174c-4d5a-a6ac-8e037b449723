import { Request, Response } from "express";
import { CreateManagerEventPayload } from "../types/managerEvent.types";
import { ManagerEventService } from "../services/managerEvent.service";
import { asyncHandler } from "@/utils/asyncHandler";

/**
 * Controller for handling manager event inventory endpoints.
 */
export class ManagerEventController {
  /**
   * Create a new Manager Event (inventory event) record.
   */
  static create = asyncHandler(async (req: Request, res: Response) => {
    try {
      const payload: CreateManagerEventPayload = req.body;
      
      console.log("📥 Incoming create manager event request");
      console.log("⚙️ Processing manager event creation...");
      
      const createdEvent = await ManagerEventService.createManagerEvent(payload);

      console.log("✅ Manager event created successfully");

      res.status(201).json({
        success: true,
        message: "Inventory event created successfully",
        data: createdEvent,
      });
    } catch (error: any) {
      console.error("❌ Error creating manager event:", error);
      
      // Check for specific error code and return appropriate response
      if (error.code === 'P2003') {
        return res.status(400).json({
          success: false,
          message: "Invalid manager ID or manager not found",
        });
      }

      // Generic error response
      res.status(500).json({
        success: false,
        message: error.message || "Internal server error",
      });
    }
  });
}