"use client"
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { 
  Card,
  CardContent
} from "@/components/ui/card";
import type { VisitorFilters as FilterType } from '../types/visitor-management.types';

interface VisitorFiltersProps {
  filters: FilterType;
  onFilterChange: (filters: FilterType) => void;
}

export const VisitorFilters = ({ filters, onFilterChange }: VisitorFiltersProps) => {
  return (
    <Card className="mb-6">
      <CardContent className="pt-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search visitors..."
              className="pl-8"
              value={filters.search}
              onChange={(e) => onFilterChange({ ...filters, search: e.target.value })}
            />
          </div>
          
          <Select
            value={filters.status}
            onValueChange={(value) => onFilterChange({ ...filters, status: value as any })}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="suspended">Suspended</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            onClick={() => onFilterChange({
              search: "",
              status: "all",
              dateRange: undefined
            })}
          >
            Reset Filters
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
