import { useAuth } from "@/features/auth/hooks/useAuth";
import { usePermissions } from "@/utils/permissions/hooks/usePermissions";
import { useNavigation } from "./useNavigation";
import { UserRole } from "@/utils/permissions/types";

export const useRoleNavigation = () => {
  const { session } = useAuth();
  const { hasPermission, userRole } = usePermissions();
  const { navigationItems } = useNavigation();

  const roleBasePaths: Record<UserRole, string> = {
    VISITOR: "/visitor",
    MANAGER: "/manager",
    ADMIN: "/admin"
  };

  const getRolePath = (role: UserRole, feature: string = 'dashboard') => {
    return `${roleBasePaths[role]}/${feature}`;
  };

  return {
    isAuthenticated: !!session,
    navigationItems,
    basePath: roleBasePaths[userRole],
    getRolePath,
    userRole,
    hasPermission,
  };
};

/**
 * 
 !! is a double negation operator that converts a value to boolean
 For example: !!undefined = false, !!null = false, !!"" = false, !!0 = false
 Any other value will be true: !!"hello" = true, !!1 = true, !!{} = true
 */
