/**
 * Service for coupon operations within the checkout feature
 */

import { PrismaClient, CheckoutSessionStatus } from "@prisma/client";
import { toPrisma<PERSON>son } from "@/utils/prismaHelpers";
import { CouponDiscount } from "../types/coupon.types";
import ApiError from "@/utils/ApiError";

const prisma = new PrismaClient();

export class CouponService {
  /**
   * Apply a coupon to a checkout session
   * 
   * @param sessionId - The ID of the checkout session
   * @param couponCode - The coupon code to apply
   * @param userId - The ID of the authenticated user
   * @returns The updated session with coupon applied
   * @throws ApiError if session not found, unauthorized, or coupon invalid
   */
  static async applyCoupon(
    sessionId: string,
    couponCode: string,
    userId: string
  ) {
    try {
      console.log(`🔍 Finding session to apply coupon: ${sessionId}`);

      // Step 1: Find the session
      const session = await prisma.checkoutSession.findUnique({
        where: { id: sessionId },
      });

      if (!session) {
        console.log(`❌ Session not found: ${sessionId}`);
        throw ApiError.notFound("Checkout session not found");
      }

      // Verify the session belongs to the user
      if (session.userId !== userId) {
        console.log(`❌ Session belongs to different user: ${sessionId}`);
        throw ApiError.forbidden("You don't have permission for this session");
      }

      // Step 2: Check if session is in a valid state
      if (session.status !== CheckoutSessionStatus.RESERVED) {
        console.log(`❌ Cannot apply coupon to session with status: ${session.status}`);
        throw ApiError.badRequest(`Cannot apply coupon to session with status ${session.status}`);
      }
      
      // Special case: If empty coupon code is provided, remove any existing coupon
      if (couponCode.trim() === '') {
        console.log('🧹 Removing existing coupon from session');
        
        // Calculate new total (removing any existing coupon discount)
        let existingDiscount = 0;
        try {
          const existingCoupon = session.couponDiscount as any;
          if (existingCoupon && existingCoupon.discountAmount) {
            existingDiscount = existingCoupon.discountAmount;
          }
        } catch (e) {
          console.log('⚠️ Error parsing existing coupon discount:', e);
        }
        
        const newTotal = session.total + existingDiscount;
        
        // Update session to remove coupon
        const updatedSession = await prisma.checkoutSession.update({
          where: { id: sessionId },
          data: {
            couponDiscount: 0,
            total: newTotal,
            updatedAt: new Date(),
          },
        });
        
        return {
          success: true,
          message: "Coupon removed successfully",
          data: {
            isValid: true,
            session: updatedSession
          }
        };
      }

      // Step 3: Validate the coupon code
      // For demo purposes, we'll use hardcoded coupon codes
      const validCoupons: Record<string, {
        id: string;
        description: string;
        type: 'PERCENTAGE' | 'FIXED';
        value: number;
      }> = {
        'WELCOME10': {
          id: 'welcome10',
          description: 'Welcome discount: 10% off',
          type: 'PERCENTAGE',
          value: 10
        },
        'SUMMER2023': {
          id: 'summer2023',
          description: '$15 off summer promotion',
          type: 'FIXED',
          value: 15
        },
        'SAVE5': {
          id: 'save5',
          description: '$5 discount',
          type: 'FIXED',
          value: 5
        }
      };
      
      const coupon = validCoupons[couponCode.toUpperCase()];
      
      if (!coupon) {
        console.log(`❌ Invalid coupon code: ${couponCode}`);
        throw ApiError.badRequest("Invalid coupon code");
      }

      // Step 4: Calculate the discount
      const couponDiscount: CouponDiscount = {
        couponId: coupon.id,
        couponCode: couponCode.toUpperCase(),
        description: coupon.description,
        discountType: coupon.type,
        discountValue: coupon.value,
        discountAmount: 0 // Will calculate below
      };

      // Calculate actual discount amount based on type
      if (coupon.type === 'PERCENTAGE') {
        couponDiscount.discountAmount = session.subtotal * (coupon.value / 100);
      } else { // FIXED
        couponDiscount.discountAmount = coupon.value;
      }

      // Ensure discount doesn't exceed the subtotal
      const effectiveDiscount = Math.min(couponDiscount.discountAmount, session.subtotal);
      couponDiscount.discountAmount = effectiveDiscount;

      // Step 5: Calculate new total, accounting for any applied points
      let pointsDiscount = 0;
      try {
        const appliedPoints = session.appliedPoints as any;
        if (appliedPoints && appliedPoints.discountAmount) {
          pointsDiscount = appliedPoints.discountAmount;
        }
      } catch (e) {
        console.log('⚠️ Error parsing applied points:', e);
      }
      
      const newTotal = session.subtotal + session.serviceFee + (session.tax || 0) - effectiveDiscount - pointsDiscount;

      // Step 6: Update the session with the coupon
      console.log(`📝 Updating session with coupon: ${sessionId}`);
      
      const updatedSession = await prisma.checkoutSession.update({
        where: { id: sessionId },
        data: {
          couponDiscount: toPrismaJson(couponDiscount),
          total: newTotal,
          updatedAt: new Date(),
        },
      });
      
      console.log(`✅ Session updated with coupon: ${sessionId}`);

      return {
        success: true,
        message: "Coupon applied successfully",
        data: {
          isValid: true,
          session: updatedSession
        }
      };
    } catch (error) {
      // If it's already an ApiError, rethrow it
      if (error instanceof ApiError) {
        throw error;
      }
      
      // Otherwise, log and convert to an ApiError
      console.error("💥 Error applying coupon:", error);
      throw ApiError.internal("Failed to apply coupon");
    }
  }
}