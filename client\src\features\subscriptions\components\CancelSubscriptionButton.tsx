import { useState } from "react";
import { Loader2, AlertCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";

interface CancelSubscriptionButtonProps {
  onCancel: (immediately?: boolean) => void;
  isCanceling: boolean;
  currentPlanName: string;
  endDate: string;
}

export function CancelSubscriptionButton({
  onCancel,
  isCanceling,
  currentPlanName,
  endDate,
}: CancelSubscriptionButtonProps) {
  const [open, setOpen] = useState(false);

  const handleCancel = () => {
    onCancel(false); // Cancel at period end
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="text-destructive">
          Cancel Subscription
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Cancel Your {currentPlanName} Subscription</DialogTitle>
          <DialogDescription>
            Are you sure you want to cancel your subscription? You&apos;ll maintain access until the end of your billing period.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>You will lose your premium benefits</AlertTitle>
            <AlertDescription className="mt-2">
              <ul className="list-disc pl-5 space-y-1">
                <li>Priority queue access</li>
                <li>Discounted service fees</li>
                {currentPlanName === "VIP" && <li>VIP exclusive events</li>}
              </ul>
            </AlertDescription>
          </Alert>

          <p className="text-sm text-muted-foreground">
            Your subscription will remain active until {endDate}. After that, you&apos;ll be downgraded to a free account.
          </p>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Keep Subscription
          </Button>
          <Button
            variant="destructive"
            onClick={handleCancel}
            disabled={isCanceling}
          >
            {isCanceling ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Canceling...
              </>
            ) : (
              "Confirm Cancellation"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}