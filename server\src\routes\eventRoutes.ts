import { Router } from "express";
import {
  getUpcomingEvents,
  getUpcomingPopularEvents,
  getUpcomingInternationalEvents,
  getUpcomingNationalEvents,
  getEventById,
} from "../controllers/eventsController";


const router = Router();

router.get("/", getUpcomingEvents);
router.get("/popular", getUpcomingPopularEvents);
router.get("/international", getUpcomingInternationalEvents);
router.get("/national", getUpcomingNationalEvents);
router.get("/:id", getEventById);

export default router;