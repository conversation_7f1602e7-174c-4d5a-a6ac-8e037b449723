// Ticketmaster API adapter to fetch data from the Ticketmaster API
// This adapter translates the OpenCTX query to Ticketmaster API compatible queries

import axios from 'axios';
import { OpenCTXRequest, Context } from '../types';

// Configure the Ticketmaster API URL and API key from environment variables
const TICKETMASTER_API_URL = 'https://app.ticketmaster.com/discovery/v2/events';

// Function to fetch data from Ticketmaster API
async function fetchFromTicketmaster<T>(query: OpenCTXRequest): Promise<T[]> {
  const { filters, sort, pagination, query: searchQuery } = query;
   // Start with a base query for the TM API, and include api key from .env file
  let tmApiParams: any = {
      apikey: process.env.TICKETMASTER_API_KEY,
      locale: '*',
  };
    // Handle search query (if provided)
    if (searchQuery) {
      tmApiParams.keyword = searchQuery
  }

  // Handle filters (if provided)
  if (filters) {
      filters.forEach(filter => {
          if ('value' in filter) {
            tmApiParams[filter.field] = filter.value;
          } else if ('values' in filter) {
              tmApiParams[filter.field] = filter.values.join(',')
          }
      })
    }

  // Handle sorting (if provided)
    if (sort) {
        tmApiParams.sort = `${sort.field},${sort.order || 'asc'}`;
    }
    // Handle pagination (if provided)
      if (pagination) {
          tmApiParams.page = pagination.page - 1;
          tmApiParams.size = pagination.size;
    }

    try {
      const response = await axios.get(TICKETMASTER_API_URL, {
        params: tmApiParams,
      });
      return response.data._embedded?.events || [];
  } catch (error) {
      console.error('Error fetching data from Ticketmaster API:', error);
        throw new Error('Failed to fetch data from Ticketmaster API.');
      }
}

// Function to transform Ticketmaster data to MCP context and send to client side for display
function transformToContext<T>(events: T[]): Context[] {
  return events.map((event: any) => ({
    type: 'event',
    text: `Event: ${event.name}, Venue: ${event._embedded?.venues?.[0]?.name}, Date: ${event.dates?.start?.localDate}`,
    metadata: {
      source: 'ticketmaster',
      id: event.id,
      name: event.name,
      venue: event._embedded?.venues?.[0]?.name,
      date: event.dates?.start?.localDate,
      image: event.images?.[0]?.url,
      city: event._embedded?.venues?.[0]?.city?.name, // types.ts add city
      // Include all necessary data in metadata
      rawEvent: event // Store complete event data
    },
  }));
}

// Updated tmApiAdapter to use fetchFromTicketmaster and transformToContext functions
export const tmApiAdapter = {
  fetch: async <T>(query: OpenCTXRequest): Promise<Context[]> => {
   const events = await fetchFromTicketmaster<T>(query);
    return transformToContext(events)
  },
};
