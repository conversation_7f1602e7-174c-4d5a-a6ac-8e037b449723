export type OAuthProvider = "google" | "github" | "apple" | "facebook";

export interface OAuthButtonProps {
  provider: OAuthProvider;
  className?: string;
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

export interface OAuthResponse {
  user: {
    id: string;
    email: string;
    name?: string;
    image?: string;
  };
  account: {
    provider: string;
    providerAccountId: string;
    access_token: string;
    token_type: string;
  };
}
