/**
 * Navbar Component
 *
 * A responsive navigation bar component that provides various functionalities:
 * - Sidebar toggle control
 * - Search functionality
 * - Language and currency selection
 * - Dark mode toggle
 * - Notifications display
 * - User profile menu
 *
 * The component uses Redux for state management and custom hooks for language
 * and currency handling.
 */

"use client";

import {
  Menu,
  User,
  Search,
  Sun,
  Moon,
  Bell,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useState } from "react";
// import Image from "next/image";
import { useCurrency, Currency } from "@/hooks/useCurrency";
import { useLanguage, Language } from "@/hooks/useLanguage";
import CustomDropdown from "@/components/CustomDropdown";
import { useAppSelector, useAppDispatch } from "@/app/redux";
import { setIsDarkMode, setIsSidebarCollapsed } from "@/state";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { AuthModal } from "@/features/auth/components/credential/AuthModal";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";

const Navbar = () => {
  // --------------- State and Hooks -------------------
  const { session, logout } = useAuth();
  const { data: sessionData } = useSession();
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);

  const { language, changeLanguage, languages } = useLanguage();
  const { currency, changeCurrency, currencies } = useCurrency();
  const dispatch = useAppDispatch();
// same output : expires , token, email , id , role
//   console.log("checking session details - navbar- useAuth☠️",session)
// console.log ("useSession 🌻 ",sessionData)
const router = useRouter();


  // Redux state selectors
  const isSidebarCollapsed = useAppSelector(
    (state) => state.global.isSidebarCollapsed
  );
  const isDarkMode = useAppSelector((state) => state.global.isDarkMode);
  const isUserLoggedIn = false; // TODO: Implement authentication logic

  // --------------- Event Handlers -------------------
  const toggleSidebar = () => {
    dispatch(setIsSidebarCollapsed(!isSidebarCollapsed));
  };

  const toggleDarkMode = () => {
    dispatch(setIsDarkMode(!isDarkMode));
  };

  // --------------- Data Transformations -------------------
  // Transform language and currency data for dropdown components
  const languageOptions = Object.entries(languages).map(([code, name]) => ({
    value: code,
    label: name,
  }));
  const currencyOptions = Object.entries(currencies).map(([code, symbol]) => ({
    value: code,
    label: `${code} (${symbol})`,
  }));

   // If user is not logged in, provide fallback route (e.g. login or home)
  const profilePath = session?.user?.role
  ? `/${session?.user?.role.toLowerCase()}/profile`
  : null;

  // Navigation items for the profile dropdown
  const profileNavItems = session
   ? [
      { label: "Profile", action: () => window.location.href = profilePath || '/' },
      { label: "Settings", action: () => {} },
      { label: "Logout", action: () => logout() },
    ]
   : [{ label: "Sign In", action: () => setIsAuthModalOpen(true) }];

  // --------------- Component Render -------------------
  return (
    <>
      <nav
        className={`flex items-center justify-between p-4 border-b transition-all duration-300 ease-in-out
        
  `}
      >
        {/* Left Section: Sidebar Toggle and Search */}
        <div className="flex items-center  justify-between p-4">
          <Button
            className="font-bold "
            variant="ghost"
            size="icon"
            onClick={toggleSidebar}
          >
            {isSidebarCollapsed ? (
              <ChevronRight className="h-6 w-6" />
            ) : (
              <ChevronLeft className="h-6 w-6" />
            )}
          </Button>
          <div className="relative">
            <Input
              type="text"
              placeholder="Find events and book seats..."
              className="pl-10 pr-4 py-2 rounded-full cursor-pointer"
              onClick={() => router.push('/events')}
              onFocus={() => router.push('/events')}
              readOnly
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          </div>
        </div>

        {/* Middle Section: Language and Currency Dropdowns (Hidden on Mobile) */}
        <div className="items-center space-x-4 hidden md:flex">
          <CustomDropdown
            options={languageOptions}
            value={language}
            onChange={(value: string) => changeLanguage(value as Language)}
          />
          <CustomDropdown
            options={currencyOptions}
            value={currency}
            onChange={(value: string) => changeCurrency(value as Currency)}
          />
        </div>

        {/* Right Section: Notifications, Dark Mode Toggle, and Profile */}
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Button variant="ghost" size="icon">
              <Bell className="h-5 w-5" />
              <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                3
              </span>
            </Button>
          </div>
          <Button variant="ghost" size="icon" onClick={toggleDarkMode}>
            {isDarkMode ? (
              <Sun className="h-5 w-5" />
            ) : (
              <Moon className="h-5 w-5" />
            )}
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center space-x-2">
                {session?.user ? (
                  <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
                    <span className="text-white font-medium">
                      {session.user.email?.charAt(0).toUpperCase()}
                    </span>
                  </div>
                ) : (
                  <User className="w-8 h-8 p-1 rounded-full bg-gray-200" />
                )}
                <span className="hidden md:inline">
                  {session?.user?.email || "Guest"}
                </span>
              </Button>
            </DropdownMenuTrigger>

            <DropdownMenuContent>
              {profileNavItems?.map((item) => (
                <DropdownMenuItem key={item.label} onClick={item.action}>
                  {item.label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </nav>
      {/* //* --------------- Auth Modal -----signup and sigin-------------- */}
      {!session?.user && (
        <AuthModal open={isAuthModalOpen} onOpenChange={setIsAuthModalOpen} />
      )}
    </>
  );
};

export default Navbar;
