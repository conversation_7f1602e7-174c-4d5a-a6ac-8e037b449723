import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { authApi } from "@/features/auth/api/credentialApi";


// import GoogleProvider from "next-auth/providers/google";
// import GithubProvider from "next-auth/providers/github";

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { type: "email" },
        password: { type: "password" },
        action: { type: "text" }, // Identifies login vs register
        userData: { type: "text" },
        token: { type: "text" },
      },
      async authorize(credentials) {
      if (!credentials?.email || !credentials?.password) {
        console.log("❌ Missing credentials");
          throw new Error("Missing credentials");
        }

        try {
          // Handle registration flow
          if (credentials.action === "register" && credentials.userData) {
            const userData = JSON.parse(credentials.userData);
            return {
              id: userData.id,
              email: userData.email,
              role: userData.role,
              token: credentials.token,
            };
          }

          // Handle login flow
          const response = await authApi.login({
            email: credentials.email,
            password: credentials.password,
          });

          return {
            id: response.user.id,
            email: response.user.email,
            role: response.user.role,
            token: response.token,
          };
        } catch (error) {
          throw new Error(
            error instanceof Error ? error.message : "Authentication failed"
          );
        }
      },
    }),

    // Added OAuth providers
    // GoogleProvider({
    //   clientId: process.env.GOOGLE_CLIENT_ID!,
    //   clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    //   authorization: {
    //     params: {
    //       prompt: "consent",
    //       access_type: "offline",
    //       response_type: "code"
    //     }
    //   }
    // }),
    // GithubProvider({
    //   clientId: process.env.GITHUB_ID!,
    //   clientSecret: process.env.GITHUB_SECRET!,
    // })
  ],

  callbacks: {
    // JWT callback to handle token creation
    async jwt({ token, user }) {
      // console.log("JWT Callback running", { user: user?.email });
      if (user) {
        token.id = user.id;
        token.email = user.email;
        token.role = user.role;
        token.token = user.token;
      }
      return token;
    },

    // Session callback to expose token to client
    async session({ session, token }) {
      // console.log("Session Callback running", { user: session?.user?.email });
      session.user.id = token.id as string;
      session.user.role = token.role as string;
      session.token = token.token as string;
      return session;
    },




    //?-------testing ----google,apple provider

    async signIn({ user, account, profile }) {
      // Allow credential auth to proceed as normal
      if (account?.type === "credentials") return true;

      // For OAuth providers
      // if (account?.provider === "google" || account?.provider === "github") {
      //   try {
      //     // Sync with your Express backend
      //     const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/oauth/sync`, {
      //       method: "POST",
      //       headers: { "Content-Type": "application/json" },
      //       body: JSON.stringify({ user, account, profile }),
      //     });
      //     return response.ok;
      //   } catch (error) {
      //     console.error("OAuth sync failed:", error);
      //     return false;
      //   }
      // }
      return true;
    },
  },

  session: {
    strategy: "jwt",
    maxAge: 5 * 24 * 60 * 60, // 5 days matching backend token expiry

    /*
    Server token expires first (30s):
        API calls will return 401
        Axios interceptor catches 401
         

   Client session expires first (60s):
        NextAuth triggers automatic logout
        Session becomes null and token is cleared from cookies
        UI updates to logged-out state 
        
    */

        

  },
 

    // These are crucial
    debug: true,
    secret: process.env.NEXTAUTH_SECRET,
    pages: {
      // Add custom error page if needed
      error: '/auth/error', 
      // If you don't have a custom error page, comment out or remove this 'pages' section
    },


};
