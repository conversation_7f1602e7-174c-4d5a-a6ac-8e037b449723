import { ProtectedRoute } from "@/components/roleAuthProtection/ProtectedRoute";
import { VisitorManagement } from "@/features/modules/admin-portal/visitor-management/VisitorManagement";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Visitor Management | Admin Dashboard",
  description: "Manage and monitor visitor activities across the platform",
};

export default function VisitorManagementPage() {
  return (
    <ProtectedRoute allowedRoles={["ADMIN"]}>
      <VisitorManagement />
    </ProtectedRoute>
  );
}
