// This file defines the API slice for Redux Toolkit Query
// It includes endpoints for fetching various types of events, searching events,
// and managing priority events

import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { TmEvent } from "@/types";
import { ApiResponse, Context } from "@/types/openctx.types";
import {
  BulkPriorityEventPayload,
  CreatePriorityEventPayload,
  PriorityEventsApiResponse,
} from "@/features/settings/components/PriorityEvents/types/priority-events.types";
import { QueueStatusResponse, JoinQueueResponse } from "@/features/unified-events/types/queue.types";

export const api = createApi({
  baseQuery: fetchBaseQuery({ baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL }),
  reducerPath: "api/v1",
  tagTypes: [
    "Events",
    // "PopularEvents",
    // "InternationalEvents",
    // "NationalEvents",
    "SearchEvents",
    "PriorityEvents",
    "EventListings",
    "ManagerEvents",
   
  ],
  endpoints: (builder) => ({
    getEvents: builder.query<TmEvent[], void>({
      query: () => "api/v1/events",
      providesTags: ["Events"],
    }),
    // getPopularEvents: builder.query<TmEvent[], void>({
    //   query: () => "api/v1/events/popular",
    //   providesTags: ["PopularEvents"],
    // }),
    // getInternationalEvents: builder.query<TmEvent[], void>({
    //   query: () => "api/v1/events/international",
    //   providesTags: ["InternationalEvents"],
    // }),
    // getNationalEvents: builder.query<TmEvent[], void>({
    //   query: () => "api/v1/events/national",
    //   providesTags: ["NationalEvents"],
    // }),
    searchEvents: builder.query<ApiResponse<Context>, string>({
      query: (searchTerm) =>
        `api/v1/openctx/search?protocol=openctx/v1&entity=events&query=${searchTerm}`,
      providesTags: ["SearchEvents"],
    }),
    // getPriorityEvents: builder.query<PriorityEventsApiResponse, void>({
    getPriorityEvents: builder.query<PriorityEventsApiResponse, void>({
      query: () => "api/v1/priority-events",
      transformResponse: (response: PriorityEventsApiResponse) => response,
      providesTags: ["PriorityEvents"],
    }),
    createPriorityEvent: builder.mutation<
      PriorityEventsApiResponse,
      CreatePriorityEventPayload
    >({
      query: (data) => ({
        url: "api/v1/priority-events",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["PriorityEvents"],
    }),
    createBulkPriorityEvents: builder.mutation<
      PriorityEventsApiResponse,
      BulkPriorityEventPayload
    >({
      query: (data) => ({
        url: "api/v1/priority-events/bulk",
        method: "POST",
        body: { events: data },
      }),
      invalidatesTags: ["PriorityEvents"],
    }),
    deletePriorityEvent: builder.mutation({
      query: (id) => ({
        url: `api/v1/priority-events/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["PriorityEvents"],
    }),
    togglePopularStatus: builder.mutation({
      query: (id) => ({
        url: `api/v1/priority-events/${id}/toggle`,
        method: "PUT",
      }),
      invalidatesTags: ["PriorityEvents"],
    }),
    createEventListing: builder.mutation<any, any>({ // Using any for now
      query: (data) => ({
        url: "api/v1/manager-events",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["ManagerEvents"], 
    }),
 
  }),
});

export const {
  useGetEventsQuery,
  // useGetPopularEventsQuery,
  // useGetInternationalEventsQuery,
  // useGetNationalEventsQuery,
  useSearchEventsQuery,
  useGetPriorityEventsQuery,
  useCreatePriorityEventMutation,
  useCreateBulkPriorityEventsMutation,
  useDeletePriorityEventMutation,
  useTogglePopularStatusMutation,
  useCreateEventListingMutation,

} = api;