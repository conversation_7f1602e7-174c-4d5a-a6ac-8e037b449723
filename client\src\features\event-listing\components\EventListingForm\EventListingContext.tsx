// EventListingContext and Provider for managing event listing state and navigation

"use client"; // Enable client-side rendering for Next.js 13+

import React, { createContext, useState, useContext, useCallback } from 'react';
import {
    EventListingContextProps,
    EventListingData,
    InventoryItem,
} from '../../types/eventListing';

const initialState: EventListingData = {
    selectedEvent: null,
    inventory: [],
    tempInventoryItem: undefined, // Initialize tempInventoryItem
    purchaseOrder: {
        exchange: "",
        market: "",
        price: 0,
        quantity: 0,
        generateDraft: false,
    },
};

const EventListingContext = createContext<
    EventListingContextProps | undefined
>(undefined);

export const EventListingProvider: React.FC<{ children: React.ReactNode }> = ({
    children,
}) => {
    const [currentStep, setCurrentStep] = useState<number>(1);
    const [eventListingData, setEventListingData] =
        useState<EventListingData>(initialState);

    const totalSteps = 3;

    const goToNextStep = () => {
        setCurrentStep((prevStep) => prevStep + 1);
    };

    const goToPreviousStep = () => {
        setCurrentStep((prevStep) => prevStep - 1);
    };

    const goToStep = (step: number) => {
        setCurrentStep(step);
    };

    // Add functions to add, update, and delete inventory items
      // Function to handle editing an inventory item
      const handleEditInventory = useCallback((item: InventoryItem) => {
        setEventListingData((prevData) => ({
          ...prevData,
          tempInventoryItem: { ...item }, // Create a copy of the item to edit
        }));
        goToStep(2); // Navigate to the "Add Inventory" step
      }, []);

    const handleDeleteInventory = useCallback((itemId: string) => {
        setEventListingData((prevData) => ({
            ...prevData,
            inventory: prevData.inventory.filter((item) => item.id !== itemId), // Remove item by ID
        }));
    }, []);

    const contextValue: EventListingContextProps = {
        currentStep,
        goToNextStep,
        goToPreviousStep,
        goToStep,
        eventListingData,
        setEventListingData,
        totalSteps,
        handleEditInventory, // Add to context
        handleDeleteInventory, // Add to context
    };

    return (
        <EventListingContext.Provider value={contextValue}>
            {children}
        </EventListingContext.Provider>
    );
};

// Custom hook to consume the context
export const useEventListing = () => {
    const context = useContext(EventListingContext);
    if (!context) {
        throw new Error(
            'useEventListing must be used within an EventListingProvider'
        );
    }
    return context;
};
