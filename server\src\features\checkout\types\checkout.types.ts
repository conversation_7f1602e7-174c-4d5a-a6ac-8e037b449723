/**
 * Checkout types for the backend
 */

export interface CreateReservationRequest {
  eventId: string;
  items: CheckoutItemRequest[];
  billingAddressId?: string; // Optional billing address ID
}

export interface CheckoutItemRequest {
  inventoryId: string;
  quantity: number;
}

//Todo: // modifiy overtime - CheckoutItem represents an item in the checkout/sessionId page 
export interface CheckoutItem extends CheckoutItemRequest {
  name: string;
  price: number;
  subtotal: number;
  section?: string;
  row?: number | string;
  seat: string;
  attributes?: string[];
  ticketFormat?: string;
}

export interface CreateReservationResponse {
  success: boolean;
  message?: string;
  data?: {
    session: any;
    unavailableItems?: CheckoutItemRequest[];
  };
  error?: string;
}

export interface ApplyCouponRequest {
  sessionId: string;
  couponCode: string;
}

export interface ApplyCouponResponse {
  success: boolean;
  message?: string;
  data: {
    isValid: boolean;
    error?: string;
    session?: {
      id: string;
      couponDiscount: {
        code: string;
        discountAmount: number;
      };
      total: number;
    };
  };
}

export interface ApplyPointsRequest {
  sessionId: string;
  pointsToApply: number;
}

export interface ApplyPointsResponse {
  success: boolean;
  message?: string;
  data: {
    success: boolean;
    error?: string;
    session?: {
      id: string;
      appliedPoints: {
        points: number;
        discountAmount: number;
      };
      total: number;
    };
  };
}

export interface RefreshSessionRequest {
  sessionId: string;
}

export interface RefreshSessionResponse {
  success: boolean;
  message?: string;
  data: {
    session?: {
      id: string;
      expiresAt: string;
    };
    error?: string;
  };
}

// NEW: Interface for updating checkout session status
export interface UpdateSessionStatusRequest {
  status: string; // Using string type since we validate against CheckoutSessionStatus enum in the controller
}

export interface UpdateSessionStatusResponse {
  success: boolean;
  message?: string;
  data?: {
    session?: {
      id: string;
      status: string;
    };
  };
  error?: string;
}

// Interface for getting checkout session details
export interface GetSessionResponse {
  success: boolean;
  message?: string;
  data?: any & {
    // Add event details fields
    eventName?: string;
    eventDate?: Date | string ;
    eventVenue?: string;
    eventCity?: string;
    eventCountry?: string;
    eventImage?: string | null;
  };
  error?: string;
}

export interface CreateCheckoutSessionRequest {
  eventId: string;
  items: CheckoutItemRequest[];
  billingAddressId?: string; // New field
  couponCode?: string;
  pointsToApply?: number;
}

// Example of what might be stored in CheckoutSession.billingAddress (JSON field)
// This is not strictly part of this file but for context
export interface StoredBillingAddress {
    id: string; // original billing address ID
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
}