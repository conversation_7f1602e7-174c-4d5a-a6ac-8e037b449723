import "next-auth";

// Extend NextAuth types
declare module "next-auth" {
  interface User {
    id: string;
    email: string;
    role: string;
    name?: string;

    token: string;
  }

  interface Session {
    user: User & {
      role: string;
    };

    token: string;
  }
}

// Request types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials extends LoginCredentials {
  role: string;
  fullName: string;
  mobile: string;
}


export interface ResetPasswordCredentials {
  token: string;
  newPassword: string;
}

export interface ForgotPasswordCredentials {
  email: string;
}

// Update AuthResponse to include reset password responses
export interface AuthResponse {
  user: {
    id: string;
    email: string;
    role: string;
    fullName: string;
  };
  token: string;
  message?: string; // For reset password responses
}
