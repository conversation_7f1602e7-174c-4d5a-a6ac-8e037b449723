'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { ErrorDisplay } from "@/components/shared/ErrorDisplay"

export default function SettingsError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  const router = useRouter()

  // Handle specific error types for settings
  const getErrorMessage = (error: Error) => {
    if (error.message.includes('priority')) {
      return "Unable to load priority events. Please try again."
    }
    if (error.message.includes('permission')) {
      return "You don't have permission to access these settings."
    }
    return "There was an error loading the settings page."
  }

  return (
    <ErrorDisplay 
      title="Settings Error"
      message={getErrorMessage(error)}
      actions={
        <>
          <Button onClick={() => reset()} variant="default">
            Retry Settings
          </Button>
          <Button onClick={() => router.push('/admin/dashboard')} variant="outline">
            Go to Dashboard
          </Button>
        </>
      }
      className="container mx-auto py-6 px-4"
    />
  )
}
