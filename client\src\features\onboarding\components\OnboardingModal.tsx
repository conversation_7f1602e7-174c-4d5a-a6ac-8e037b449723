// 🔄 Updated modal with role-based navigation - Debug logs removed
"use client";

import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle2,
  Circle,
  ArrowRight,
  Sparkles,
  X,
  Info,
  User,
  Settings,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useRouter } from "next/navigation";
import { useOnboardingCheck } from "../hooks/useOnboardingCheck";

interface OnboardingModalProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export const OnboardingModal: React.FC<OnboardingModalProps> = ({
  open: externalOpen,
  onOpenChange: externalOnOpenChange,
}) => {
  const router = useRouter();
  const {
    onboardingStatus,
    onboardingSteps,
    userRole,
    profilePath,
    settingsPath,
    
   
  } = useOnboardingCheck();

  // Internal state management with external control support
  const [internalOpen, setInternalOpen] = React.useState(false);

  // Use external control if provided, otherwise use internal state
  const isOpen = externalOpen !== undefined ? externalOpen : internalOpen;
  const setIsOpen = externalOnOpenChange || setInternalOpen;

  // Auto-show logic for when no external control is provided
  React.useEffect(() => {
    if (externalOpen === undefined) {
      setInternalOpen(onboardingStatus.shouldShowModal);
    }
  }, [onboardingStatus.shouldShowModal, externalOpen]);

  const handleAction = (href: string, stepId: string) => {
    // Mark guidance step as "will be completed" for demo purposes
    if (stepId === "profile") {
      // markProfileGuidanceCompleted(); // Uncomment to auto-mark as complete
    } else if (stepId === "address") {
      // markAddressGuidanceCompleted(); // Uncomment to auto-mark as complete
    }

    setIsOpen(false);
    router.push(href);
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  const progressPercentage =
    (onboardingStatus.completedSteps / onboardingStatus.totalSteps) * 100;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent
        className={cn(
          "sm:max-w-[520px] p-0 overflow-hidden",
          "bg-gradient-to-br from-background via-background to-accent/5",
          "border border-border/50 shadow-2xl"
        )}
      >
        {/* Header with gradient background */}
        <DialogHeader
          className={cn(
            "relative p-6 pb-4",
            "bg-gradient-to-r from-primary/10 via-primary/5 to-transparent",
            "border-b border-border/30"
          )}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div
                className={cn(
                  "flex items-center justify-center w-10 h-10 rounded-full",
                  "bg-gradient-to-br from-primary to-primary/80",
                  "shadow-lg"
                )}
              >
                <Sparkles className="w-5 h-5 text-primary-foreground" />
              </div>
              <div>
                <DialogTitle className="text-xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text">
                  {onboardingStatus.currentStep === "complete"
                    ? "Setup Complete!"
                    : "Complete Your Setup"}
                </DialogTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  {onboardingStatus.currentStep === "complete"
                    ? `Welcome ${userRole}! You're all set to explore Fanseatmaster`
                    : `Get the most out of Fanseatmaster as ${userRole}`}
                </p>
              </div>
            </div>
            {/* <Button
              variant="ghost"
              size="icon"
              onClick={handleClose}
              className="h-8 w-8 hover:bg-background/80"
            >
              <X className="h-4 w-4" />
            </Button> */}
          </div>

          {/* Progress section */}
          <div className="mt-4 space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-foreground">
                {onboardingStatus.completedSteps} of{" "}
                {onboardingStatus.totalSteps} completed
              </span>
              <Badge
                variant={
                  onboardingStatus.currentStep === "complete"
                    ? "default"
                    : "secondary"
                }
                className="font-semibold"
              >
                {Math.round(progressPercentage)}%
              </Badge>
            </div>
            <Progress
              value={progressPercentage}
              className="h-2 bg-secondary/50"
            />
          </div>

          {/* Info banner with role-based paths */}
          <div className="mt-3 flex items-center gap-2 p-2 bg-blue-50/50 dark:bg-blue-950/20 rounded-md border border-blue-200/30 dark:border-blue-800/30">
            <Info className="w-4 h-4 text-blue-600 dark:text-blue-400 flex-shrink-0" />
            <p className="text-xs text-blue-700 dark:text-blue-300">
              This guidance helps you navigate platform features and settings.
            </p>
          </div>
        </DialogHeader>

        {/* Steps content */}
        <div className="p-6 pt-2 space-y-4">
          <AnimatePresence mode="wait">
            {onboardingSteps.map((step, index) => (
              <motion.div
                key={step.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className={cn(
                  "flex items-center gap-4 p-4 rounded-lg transition-all",
                  "border border-border/30",
                  step.isComplete
                    ? "bg-emerald-50/50 dark:bg-emerald-950/20 border-emerald-200/50 dark:border-emerald-800/30"
                    : "bg-background/50 hover:bg-accent/20"
                )}
              >
                {/* Step indicator */}
                <div className="flex-shrink-0">
                  {step.isComplete ? (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{
                        type: "spring",
                        stiffness: 500,
                        damping: 30,
                      }}
                    >
                      <CheckCircle2 className="w-6 h-6 text-emerald-600 dark:text-emerald-400" />
                    </motion.div>
                  ) : (
                    <Circle className="w-6 h-6 text-muted-foreground" />
                  )}
                </div>

                {/* Step content */}
                <div className="flex-grow">
                  <h4
                    className={cn(
                      "font-semibold text-sm flex items-center gap-2",
                      step.isComplete
                        ? "text-emerald-700 dark:text-emerald-300"
                        : "text-foreground"
                    )}
                  >
                    {step.id === "profile" && <User className="w-4 h-4" />}
                    {step.id === "address" && <Settings className="w-4 h-4" />}
                    {step.title}
                  </h4>
                  <p className="text-xs text-muted-foreground mt-1">
                    {step.description}
                  </p>
                </div>

                {/* Action button */}
                {step.action && (
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      size="sm"
                      onClick={() => handleAction(step.action!.href, step.id)}
                      className="gap-2 font-medium"
                    >
                      {step.action.label}
                      <ArrowRight className="w-3 h-3" />
                    </Button>
                  </motion.div>
                )}
              </motion.div>
            ))}
          </AnimatePresence>

          {/* Footer actions */}
          <div className="flex items-center justify-center pt-4 border-t border-border/30">
            {onboardingStatus.currentStep === "complete" ? (
              <p className="text-xs text-muted-foreground text-center">
                🎉 All set! You can access this guide anytime from the sidebar.
              </p>
            ) : (
              <p className="text-xs text-muted-foreground text-center">
                Complete these steps to unlock all platform features.
              </p>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
