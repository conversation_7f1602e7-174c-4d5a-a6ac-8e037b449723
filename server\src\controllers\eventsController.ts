import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";
import { DateTime } from "luxon";

const prisma = new PrismaClient();

type EventFilter = {
  isPopular?: boolean;
  category?: { has: string };
};

const getFilteredEvents = async (filter: EventFilter) => {
  const now = DateTime.utc().startOf("day");
  return prisma.event.findMany({
    where: {
      ...filter,
      date: {
        gte: now.toJSDate(),
      },
    },
    orderBy: {
      date: "asc",
    },
  });
};

// Remove formatEventDates to send raw date and timezone to frontend
export const getUpcomingEvents = async (_req: Request, res: Response) => {
  try {
    const events = await getFilteredEvents({});
    res.json(events);
  } catch (error) {
    res.status(500).json({ error: "Error fetching upcoming events" });
  }
};

export const getUpcomingPopularEvents = async (_req: Request, res: Response) => {
  try {
    const events = await getFilteredEvents({ isPopular: true });
    res.json(events);
  } catch (error) {
    res.status(500).json({ error: "Error fetching upcoming popular events" });
  }
};

export const getUpcomingInternationalEvents = async (_req: Request, res: Response) => {
  try {
    const events = await getFilteredEvents({
      category: { has: "International" },
    });
    res.json(events);
  } catch (error) {
    res.status(500).json({ error: "Error fetching upcoming international events" });
  }
};

export const getUpcomingNationalEvents = async (_req: Request, res: Response) => {
  try {
    const events = await getFilteredEvents({ category: { has: "Indian" } });
    res.json(events);
  } catch (error) {
    res.status(500).json({ error: "Error fetching upcoming national events" });
  }
};

export const getEventById = async (req: Request, res: Response) => {
  try {
    const event = await prisma.event.findUnique({
      where: { id: req.params.id },
    });
    if (event) {
      res.json(event);
    } else {
      res.status(404).json({ error: "Event not found" });
    }
  } catch (error) {
    res.status(500).json({ error: "Error fetching event" });
  }
};
