
import { useState } from 'react';

// export type Currency = 'USD' | 'EUR' | 'INR';
export type Currency = 'USD';

const currencies: Record<Currency, string> = {
  USD: '$',
  // EUR: '€',
  // INR: '₹',
};

export const useCurrency = () => {
  const [currency, setCurrency] = useState<Currency>('USD');

  const changeCurrency = (newCurrency: Currency) => {
    setCurrency(newCurrency);
    // Here you can add logic to change the app's currency
  };

  return { currency, changeCurrency, currencies };
};
