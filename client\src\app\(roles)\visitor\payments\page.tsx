/**
 * Visitor Payment History Page
 * 
 * Displays the logged-in visitor's transaction history and status
 * messages after completing a checkout.
 */
'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { usePaymentHistory } from '@/features/payments/hooks/usePaymentApi';
// import { PaymentHistoryTable } from '@/features/payments/components/PaymentHistoryTable';
import { TransactionStatusBanner } from '@/features/payments/components/TransactionStatusBanner';
import { LoadingSpinner } from '@/components/shared/LoadingSpinner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { RefreshCw, Wallet } from 'lucide-react';
import { VisitorPaymentHistoryTable } from '@/features/payments/components/VisitorPaymentHistoryTable';

export default function VisitorPaymentHistoryPage() {
  // Get query parameters for post-checkout status
  const searchParams = useSearchParams();
  const status = searchParams.get('status');
  const paymentId = searchParams.get('paymentId'); // Corrected param name based on checkout redirect
  const errorMessage = searchParams.get('error');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10); // Or make this configurable

  // Fetch payment history using the hook
  const {
    data: historyResponse,
    isLoading,
    isError,
    error,
    refetch,
    isRefetching
  } = usePaymentHistory({
    page: currentPage,
    limit: pageSize,
    sortBy: 'processedAt',
    sortDirection: 'desc'
  });

  // --- Prepare data for the status banner ---
  const [bannerDetails, setBannerDetails] = useState<{
    title: string;
    message: string;
    transactionId?: string;
    amount?: string;
    type: 'success' | 'error';
  } | null>(null);

  useEffect(() => {
    // This effect runs once when the page loads or params change
    if (status === 'success' && paymentId) {
        setBannerDetails({
            type: 'success',
            title: 'Payment Successful',
            message: 'Your payment has been processed. Details will appear below shortly.',
            transactionId: paymentId,
        });
        // Auto-refresh after a short delay to fetch the new payment record
        const timer = setTimeout(() => refetch(), 2500);
        return () => clearTimeout(timer);

    } else if (status === 'error' && errorMessage) {
        setBannerDetails({
            type: 'error',
            title: 'Payment Failed',
            message: decodeURIComponent(errorMessage),
        });
    } else {
        setBannerDetails(null); // Clear banner if no relevant params
    }
    // Intentionally run only when status/id/error changes
  }, [status, paymentId, errorMessage, refetch]);
  // --- End banner logic ---

  // Handle page change for pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle manual refresh
  const handleRefresh = () => {
    refetch();
  };

  // Calculate total pages for pagination
  const totalPages = historyResponse?.data
    ? Math.ceil(historyResponse.data.total / pageSize)
    : 0;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6 flex-wrap gap-4">
        <div>
            <h1 className="text-3xl font-bold mb-1">My Payments</h1>
            <p className="text-muted-foreground">View your personal transaction history.</p>
        </div>
        <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading || isRefetching}
            aria-label="Refresh payment history"
          >
            {isRefetching || isLoading ? <LoadingSpinner /> : <RefreshCw className="h-4 w-4 mr-2" />}
            Refresh
        </Button>
      </div>


      {/* Transaction Status Banner - Renders based on state set from URL params */}
      {bannerDetails && (
        <TransactionStatusBanner
          status={bannerDetails.type}
          title={bannerDetails.title}
          message={bannerDetails.message}
          transactionId={bannerDetails.transactionId}
          amount={bannerDetails.amount} // Amount might not be available immediately
          autoDismiss={bannerDetails.type === 'success'} // Auto-dismiss success message
          dismissAfter={10000} // 10 seconds
          className="mb-8"
        />
      )}

      {/* Main Content Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Wallet className="h-5 w-5 mr-2 text-primary" />
            Transaction History
          </CardTitle>
          <CardDescription>
            A record of all your purchases and payments made on Fanseatmaster.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isError ? (
            // Error state when fetching history
            <div className="text-center py-10 px-4 border rounded-md bg-destructive/10 text-destructive">
              <h3 className="font-semibold text-lg mb-2">Error Loading Payments</h3>
              <p className="text-sm mb-4">
                {error instanceof Error ? error.message : 'An unknown error occurred while fetching your history.'}
              </p>
              <Button onClick={handleRefresh} variant="destructive" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </div>
          ) : (
            // Render the table (handles its own loading/empty states)
            <VisitorPaymentHistoryTable
              payments={historyResponse?.data.payments || []}
              isLoading={isLoading || isRefetching}
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
              onRefresh={handleRefresh} // Pass refresh down if needed by table
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
