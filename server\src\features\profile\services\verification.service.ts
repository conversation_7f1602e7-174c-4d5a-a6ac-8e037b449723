/**
 * Verification Service
 * 
 * Handles email and mobile verification using OTP
 */
import { PrismaClient } from '@prisma/client';
import ApiError from '@/utils/ApiError';
import crypto from 'crypto';
import { EmailService } from '@/features/email/services/email.service';
import { SmsService } from '@/features/sms/services/sms.service';
import { EMAIL_CONFIG } from '@/features/email/config/email.config';
import { SMS_CONFIG } from '@/features/sms/config/sms.config';
import { NODE_ENV } from '@/constants';

const prisma = new PrismaClient();

export class VerificationService {
  /**
   * Generate a random OTP of specified length
   */
  static generateOTP(length = EMAIL_CONFIG.OTP.LENGTH): string {
    const min = Math.pow(10, length - 1);
    const max = Math.pow(10, length) - 1;
    return Math.floor(min + Math.random() * (max - min + 1)).toString();
  }

  /**
   * Initiate email verification by generating and sending OTP
   */
  static async initiateEmailVerification(userId: string, email: string): Promise<{ success: boolean }> {
    try {
      // Generate OTP
      const otp = this.generateOTP();
      const expiresAt = new Date(Date.now() + EMAIL_CONFIG.OTP.EXPIRY_MINUTES * 60 * 1000);
      
      // Get user details for email personalization
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { fullName: true }
      });
      
      // Store OTP in database
      await prisma.user.update({
        where: { id: userId },
        data: {
          resetPasswordToken: otp,
          resetPasswordExpires: expiresAt
        }
      });
      
      // Send OTP via email service
      await EmailService.sendVerificationOTP(
        email, 
        otp, 
        user?.fullName || 'User'
      );
      
      // Log in development mode
      if (NODE_ENV === 'development') {
        console.log(`✅ Email verification initiated for user ${userId}`);
        console.log(`📧 OTP: ${otp} (expires in ${EMAIL_CONFIG.OTP.EXPIRY_MINUTES} minutes)`);
      }
      
      return { success: true };
    } catch (error) {
      console.error('❌ Error initiating email verification:', error);
      throw ApiError.internal('Failed to initiate email verification');
    }
  }

  /**
   * Verify email with OTP
   */
  static async verifyEmail(userId: string, otp: string): Promise<{ success: boolean }> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId }
      });
      
      if (!user) {
        throw ApiError.notFound('User not found');
      }
      
      // Check if OTP exists and is valid
      if (!user.resetPasswordToken || user.resetPasswordToken !== otp) {
        throw ApiError.badRequest('Invalid verification code');
      }
      
      // Check if OTP is expired
      if (user.resetPasswordExpires && user.resetPasswordExpires < new Date()) {
        throw ApiError.badRequest('Verification code has expired');
      }
      
      // Mark email as verified
      await prisma.user.update({
        where: { id: userId },
        data: {
          emailVerified: new Date(),
          resetPasswordToken: null,
          resetPasswordExpires: null
        }
      });
      
      return { success: true };
    } catch (error) {
      console.error('❌ Error verifying email:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw ApiError.internal('Failed to verify email');
    }
  }

  /**
   * Initiate mobile verification by generating and sending OTP
   */
  static async initiateMobileVerification(userId: string, mobile: string): Promise<{ success: boolean }> {
    try {
      // Generate OTP
      const otp = this.generateOTP(SMS_CONFIG.OTP.LENGTH);
      const tokenHash = crypto.createHash('sha256').update(otp).digest('hex');
      const expiresAt = new Date(Date.now() + SMS_CONFIG.OTP.EXPIRY_MINUTES * 60 * 1000);
      
      // Store OTP in database (using resetPasswordToken temporarily)
      await prisma.user.update({
        where: { id: userId },
        data: {
          resetPasswordToken: tokenHash, 
          resetPasswordExpires: expiresAt
        }
      });
      
      // Send OTP via SMS service
      await SmsService.sendVerificationOTP(mobile, otp);
      
      // Log in development mode
      if (NODE_ENV === 'development') {
        console.log(`✅ Mobile verification initiated for user ${userId}`);
        console.log(`📱 OTP: ${otp} (expires in ${SMS_CONFIG.OTP.EXPIRY_MINUTES} minutes)`);
      }
      
      return { success: true };
    } catch (error) {
      console.error('❌ Error initiating mobile verification:', error);
      throw ApiError.internal('Failed to initiate mobile verification');
    }
  }

  /**
   * Verify mobile with OTP
   */
  static async verifyMobile(userId: string, otp: string): Promise<{ success: boolean }> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId }
      });
      
      if (!user) {
        throw ApiError.notFound('User not found');
      }
      
      // Hash the provided OTP to compare with stored hash
      const otpHash = crypto.createHash('sha256').update(otp).digest('hex');
      
      // Check if OTP exists and is valid
      if (!user.resetPasswordToken || user.resetPasswordToken !== otpHash) {
        throw ApiError.badRequest('Invalid verification code');
      }
      
      // Check if OTP is expired
      if (user.resetPasswordExpires && user.resetPasswordExpires < new Date()) {
        throw ApiError.badRequest('Verification code has expired');
      }
      
      // Mark mobile as verified
      await prisma.user.update({
        where: { id: userId },
        data: {
          mobileVerified: new Date(),
          resetPasswordToken: null,
          resetPasswordExpires: null
        }
      });
      
      return { success: true };
    } catch (error) {
      console.error('❌ Error verifying mobile:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw ApiError.internal('Failed to verify mobile');
    }
  }
}
