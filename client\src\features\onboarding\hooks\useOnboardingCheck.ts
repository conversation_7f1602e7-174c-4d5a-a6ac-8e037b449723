// 🔍 Debug version with detailed dependency tracking
"use client";

import { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useBillingAddresses } from '@/features/billing-address/hooks/useBillingAddresses';
import { useProfileQuery } from '@/features/profile/hooks/useProfileQuery';
import { getVerificationStatus } from '../utils/verificationHelper';
import { OnboardingStatus, OnboardingStep } from '../types/onboarding.types';

export const useOnboardingCheck = () => {
  const { data: session, status } = useSession();
  const { addresses } = useBillingAddresses();
  const { profile, isLoading: isProfileLoading } = useProfileQuery();
  
  // 🔍 DEBUG: Add refs to track previous values
  const prevDeps = useRef<any>({});
  const renderCount = useRef(0);
  
  const [onboardingStatus, setOnboardingStatus] = useState<OnboardingStatus>({
    isAuthenticated: false,
    isProfileComplete: false,
    isEmailVerified: false,
    isMobileVerified: false,
    isAddressComplete: false,
    currentStep: 'auth',
    completedSteps: 0,
    totalSteps: 3,
    shouldShowModal: false,
  });

  const [modalDismissed, setModalDismissed] = useState<boolean>(false);

  // Increment render count for debugging
  renderCount.current += 1;

  // Check if modal was dismissed recently
  useEffect(() => {
    const dismissed = localStorage.getItem('onboarding-modal-dismissed');
    const dismissedTime = dismissed ? parseInt(dismissed) : 0;
    const now = Date.now();
    const twentyFourHours = 24 * 60 * 60 * 1000;
    
    setModalDismissed(now - dismissedTime < twentyFourHours);
  }, []);

  // Get user role for dynamic paths
  const userRole = session?.user?.role || 'VISITOR';

  // 🔍 DEBUG: Main useEffect with detailed logging
  useEffect(() => {
    // 🔍 Current dependency values
    const currentDeps = {
      session: session,
      status: status,
      isProfileLoading: isProfileLoading,
      profile: profile,
      addresses: addresses,
      modalDismissed: modalDismissed
    };

    console.log(`🔍 [RENDER ${renderCount.current}] useOnboardingCheck useEffect triggered`);
    console.log('📊 Current dependency values:', {
      session: session ? 'exists' : 'null',
      sessionUserId: session?.user?.id || 'no-id',
      status: status,
      isProfileLoading: isProfileLoading,
      profile: profile ? 'exists' : 'null',
      profileId: profile?.id || 'no-profile-id',
      addresses: addresses ? `array[${addresses.length}]` : 'null',
      addressesRef: addresses === prevDeps.current.addresses ? 'SAME_REF' : 'DIFFERENT_REF',
      modalDismissed: modalDismissed
    });

    // 🔍 Check what changed from previous render
    if (prevDeps.current.session !== undefined) {
      console.log('🔄 Dependency changes:');
      Object.keys(currentDeps).forEach(key => {
        const current = currentDeps[key as keyof typeof currentDeps];
        const previous = prevDeps.current[key];
        if (current !== previous) {
          console.log(`  ❗ ${key}: CHANGED`, {
            from: previous === null ? 'null' : typeof previous === 'object' ? 'object' : previous,
            to: current === null ? 'null' : typeof current === 'object' ? 'object' : current,
            sameReference: current === previous
          });
          
          // Special handling for arrays
          if (key === 'addresses' && Array.isArray(current) && Array.isArray(previous)) {
            console.log(`    📋 Addresses details:`, {
              currentLength: current.length,
              previousLength: previous.length,
              currentItems: current.map(addr => addr.id),
              previousItems: previous.map(addr => addr.id),
              contentEqual: JSON.stringify(current) === JSON.stringify(previous)
            });
          }

          // Special handling for profile
          if (key === 'profile' && current && previous) {
            console.log(`    👤 Profile details:`, {
              currentId: (current as any)?.id,
              previousId: (previous as any)?.id,
              currentEmailVerified: (current as any)?.emailVerified,
              previousEmailVerified: (previous as any)?.emailVerified,
              contentEqual: JSON.stringify(current) === JSON.stringify(previous)
            });
          }
        }
      });
    }

    // Store current values for next comparison
    prevDeps.current = { ...currentDeps };

    // 🎯 Early return for loading states
    if (status === 'loading') {
      console.log('⏳ Exiting early: session loading');
      return;
    }
    
    const isAuthenticated = !!session?.user;
    console.log('🔐 Authentication status:', { isAuthenticated, userId: session?.user?.id });
    
    if (!isAuthenticated) {
      console.log('❌ User not authenticated, setting default onboarding status');
      setOnboardingStatus({
        isAuthenticated: false,
        isProfileComplete: false,
        isEmailVerified: false,
        isMobileVerified: false,
        isAddressComplete: false,
        currentStep: 'auth',
        completedSteps: 0,
        totalSteps: 3,
        shouldShowModal: false,
      });
      return;
    }

    if (isProfileLoading) {
      console.log('⏳ Exiting early: profile loading');
      return;
    }

    console.log('✅ Processing onboarding logic for authenticated user');

    // Calculate verification status
    const verificationStatus = getVerificationStatus(profile ?? null);
    const { isEmailVerified, isMobileVerified, isProfileComplete } = verificationStatus;
    
    console.log('📧 Verification status:', verificationStatus);
    
    // Use real API data for billing addresses
    const hasBillingAddresses = addresses && addresses.length > 0;
    const isAddressComplete = hasBillingAddresses;
    
    console.log('🏠 Address status:', { 
      addressCount: addresses?.length || 0, 
      isAddressComplete,
      addressesExists: !!addresses 
    });

    let currentStep: 'auth' | 'profile' | 'address' | 'complete' = 'auth';
    let completedSteps = 0;

    if (isAuthenticated) {
      completedSteps = 1;
      if (isProfileComplete) {
        completedSteps = 2;
        currentStep = 'address';
        if (isAddressComplete) {
          completedSteps = 3;
          currentStep = 'complete';
        }
      } else {
        currentStep = 'profile';
      }
    }

    const shouldShowModal = isAuthenticated && 
                           currentStep !== 'complete' && 
                           !modalDismissed;

    console.log('🎯 Final onboarding calculation:', {
      currentStep,
      completedSteps,
      shouldShowModal
    });

    console.log('📝 About to call setOnboardingStatus...');
    setOnboardingStatus({
      isAuthenticated,
      isProfileComplete,
      isEmailVerified,
      isMobileVerified,
      isAddressComplete,
      currentStep,
      completedSteps,
      totalSteps: 3,
      shouldShowModal,
    });
    console.log('✅ setOnboardingStatus called successfully');

  }, [session, status, isProfileLoading, profile, addresses, modalDismissed]);

  // Generate role-based paths
  const getProfilePath = () => `/${userRole.toLowerCase()}/profile`;
  const getSettingsPath = () => `/${userRole.toLowerCase()}/settings`;

  // 🎯 FIXED: Calculate steps inside function to get fresh verification status
  const getOnboardingSteps = (): OnboardingStep[] => {
    // Calculate fresh verification status when steps are requested
    const verificationStatus = getVerificationStatus(profile ?? null);
    
    return [
      {
        id: 'auth',
        title: 'Account Created',
        description: 'Welcome to Fanseatmaster! Your account is ready.',
        isComplete: onboardingStatus.isAuthenticated,
      },
      {
        id: 'profile',
        title: 'Complete Your Profile',
        description: `Verify your email and mobile number. ${verificationStatus.verificationSummary}`,
        isComplete: onboardingStatus.isProfileComplete,
        action: onboardingStatus.isAuthenticated && !onboardingStatus.isProfileComplete ? {
          label: 'Complete Profile',
          href: getProfilePath(),
        } : undefined,
      },
      {
        id: 'address',
        title: 'Add Billing Address',
        description: `Add your billing address for faster checkout. ${addresses ? `(${addresses.length} added)` : ''}`,
        isComplete: onboardingStatus.isAddressComplete,
        action: onboardingStatus.isProfileComplete && !onboardingStatus.isAddressComplete ? {
          label: 'Go to Settings',
          href: getSettingsPath(),
        } : undefined,
      },
    ];
  };

  const dismissModal = () => {
    localStorage.setItem('onboarding-modal-dismissed', Date.now().toString());
    setModalDismissed(true);
  };

  const resetGuidance = () => {
    localStorage.removeItem('onboarding-modal-dismissed');
    console.log('🔄 Onboarding guidance reset');
  };

  return {
    onboardingStatus,
    onboardingSteps: getOnboardingSteps(),
    userRole,
    profilePath: getProfilePath(),
    settingsPath: getSettingsPath(),
    billingAddressCount: addresses?.length || 0,
    dismissModal,
    resetGuidance,
  };
};