// 🔄 Updated types with proper modal props
export interface OnboardingStep {
  id: 'auth' | 'profile' | 'address';
  title: string;
  description: string;
  isComplete: boolean;
  action?: {
    label: string;
    href: string;
  };
}

export interface OnboardingStatus {
  isAuthenticated: boolean;
  isProfileComplete: boolean;
  isEmailVerified: boolean;
  isMobileVerified: boolean;
  isAddressComplete: boolean;
  currentStep: 'auth' | 'profile' | 'address' | 'complete';
  completedSteps: number;
  totalSteps: number;
  shouldShowModal: boolean;
}

// 🆕 NEW: Proper modal props interface
export interface OnboardingModalProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

// 🆕 NEW: Trigger component props
export interface OnboardingTriggerProps {
  variant?: 'button' | 'icon' | 'minimal';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showProgress?: boolean;
  children?: React.ReactNode;
}

// 🆕 NEW: Widget component props
export interface OnboardingWidgetProps {
  collapsed: boolean;
}