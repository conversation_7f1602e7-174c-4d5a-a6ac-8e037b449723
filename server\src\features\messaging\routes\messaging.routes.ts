/**
 * Messaging Routes
 * 
 * Defines all HTTP endpoints for the ticket messaging system.
 * Includes routes for visitors, managers, and admin oversight.
 */

import { Router } from 'express';
import { MessagingController } from '../controllers/messaging.controller';
import { authMiddleware } from '@/middleware/auth.middleware';

const router = Router();
const messagingController = new MessagingController();

// 🔧 APPLY AUTH MIDDLEWARE TO ALL ROUTES
router.use(authMiddleware);

// ============================================================================
// VISITOR & MANAGER ROUTES
// ============================================================================

/**
 * Send a new message in a ticket conversation
 * POST /api/v1/messaging/send
 */
router.post('/send', messagingController.sendMessage);

/**
 * Get conversation messages for a specific checkout session
 * GET /api/v1/messaging/conversation/:checkoutSessionId
 */
router.get('/conversation/:checkoutSessionId', messagingController.getConversation);

/**
 * Get user's conversations summary (for dashboard)
 * GET /api/v1/messaging/my-conversations
 */
router.get('/my-conversations', messagingController.getMyConversations);

/**
 * Mark a specific message as read
 * PATCH /api/v1/messaging/message/:messageId/read
 */
router.patch('/message/:messageId/read', messagingController.markMessageAsRead);

/**
 * Mark entire conversation as resolved
 * PATCH /api/v1/messaging/conversation/:checkoutSessionId/resolve
 */
router.patch('/conversation/:checkoutSessionId/resolve', messagingController.resolveConversation);

// ============================================================================
// ADMIN ROUTES - Require admin role validation
// ============================================================================

/**
 * Get all conversations for admin oversight
 * GET /api/v1/messaging/admin/conversations
 */
router.get('/admin/conversations', messagingController.getAdminConversations);

/**
 * Get conversation details for admin intervention
 * GET /api/v1/messaging/admin/conversation/:checkoutSessionId
 */
router.get('/admin/conversation/:checkoutSessionId', messagingController.getAdminConversationDetails);

/**
 * Send admin message to a conversation
 * POST /api/v1/messaging/admin/send
 */
router.post('/admin/send', messagingController.sendAdminMessage);

/**
 * Escalate a conversation for admin intervention
 * POST /api/v1/messaging/admin/escalate/:checkoutSessionId
 */
router.post('/admin/escalate/:checkoutSessionId', messagingController.escalateConversation);

/**
 * Get messaging system statistics for admin dashboard
 * GET /api/v1/messaging/admin/stats
 */
router.get('/admin/stats', messagingController.getMessagingStats);

// ============================================================================
// HEALTH CHECK ROUTE
// ============================================================================

/**
 * Health check for messaging system
 * GET /api/v1/messaging/health
 */
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Messaging system is operational',
    data: {
      timestamp: new Date().toISOString(),
      service: 'messaging',
      version: '1.0.0'
    }
  });
});

export default router;