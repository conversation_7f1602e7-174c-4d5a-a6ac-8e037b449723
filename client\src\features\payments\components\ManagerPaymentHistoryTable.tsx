/**
 * Manager Payment History Table
 * 
 * Displays event sales, payouts, or other financial data relevant to a manager.
 * NOTE: This component requires a specific data structure from the backend.
 */
'use client';

import { useState } from 'react';
// Adjust imports based on actual data structure and UI needs
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/shared/LoadingSpinner';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { MoreVertical, BarChart, TrendingUp, TrendingDown, Download } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

// Define a type for the manager's data (EXAMPLE - NEEDS TO MATCH BACKEND)
interface ManagerSalesRecord {
  id: string;
  eventId: string;
  eventName: string;
  date: string; // ISO String
  ticketsSold: number;
  grossRevenue: number;
  netPayout: number;
  payoutStatus: 'PENDING' | 'PROCESSING' | 'PAID' | 'FAILED';
  currency: string;
}

interface ManagerPaymentHistoryTableProps {
  salesData: ManagerSalesRecord[]; // Use the specific manager data type
  isLoading: boolean;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  // Add other necessary props like onRefresh if needed
  className?: string;
}

export function ManagerPaymentHistoryTable({
  salesData,
  isLoading,
  currentPage,
  totalPages,
  onPageChange,
  className,
}: ManagerPaymentHistoryTableProps) {

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount); // Assuming amount is already in correct units
  };

  const getPayoutStatusBadge = (status: 'PENDING' | 'PROCESSING' | 'PAID' | 'FAILED') => {
     const statusMap: Record<string, { variant: "default" | "destructive" | "outline" | "secondary" | "success" | "warning"; label: string }> = {
       PENDING: { variant: "outline", label: "Pending" },
       PROCESSING: { variant: "secondary", label: "Processing" },
       PAID: { variant: "success", label: "Paid" },
       FAILED: { variant: "destructive", label: "Failed" },
     };
     const config = statusMap[status] || { variant: "default", label: status };
     return <Badge variant={config.variant as any}>{config.label}</Badge>;
  };


  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-12 border rounded-md min-h-[200px]">
        <LoadingSpinner />
        <span className="ml-3 text-muted-foreground">Loading sales history...</span>
      </div>
    );
  }

  if (salesData.length === 0) {
    return (
      <div className="text-center p-8 border rounded-md bg-muted/20 min-h-[200px] flex flex-col justify-center items-center">
        <h3 className="font-medium text-lg mb-2">No Sales Data Found</h3>
        <p className="text-muted-foreground mb-4">There are no sales records for your events yet.</p>
        {/* Optional Refresh Button */}
      </div>
    );
  }

  return (
    <div className={cn("w-full", className)}>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Event</TableHead>
              <TableHead className="w-[100px]">Date</TableHead>
              <TableHead className="text-right w-[100px]">Tickets</TableHead>
              <TableHead className="text-right w-[120px]">Revenue</TableHead>
              <TableHead className="text-right w-[120px]">Net Payout</TableHead>
              <TableHead className="w-[120px]">Status</TableHead>
              <TableHead className="text-right w-[80px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {salesData.map((record) => (
              <TableRow key={record.id}>
                <TableCell className="font-medium">
                  {record.eventName}
                  <div className="text-xs text-muted-foreground">ID: {record.eventId}</div>
                </TableCell>
                <TableCell>{format(new Date(record.date), 'MMM d, yyyy')}</TableCell>
                <TableCell className="text-right">{record.ticketsSold}</TableCell>
                <TableCell className="text-right">{formatAmount(record.grossRevenue, record.currency)}</TableCell>
                <TableCell className="text-right font-medium">{formatAmount(record.netPayout, record.currency)}</TableCell>
                <TableCell>{getPayoutStatusBadge(record.payoutStatus)}</TableCell>
                 <TableCell className="text-right">
                   <DropdownMenu>
                     <DropdownMenuTrigger asChild>
                       <Button variant="ghost" size="icon" className="h-8 w-8">
                         <MoreVertical className="h-4 w-4" />
                         <span className="sr-only">Open menu</span>
                       </Button>
                     </DropdownMenuTrigger>
                     <DropdownMenuContent align="end">
                       <DropdownMenuLabel>Event Actions</DropdownMenuLabel>
                       <DropdownMenuSeparator />
                       <DropdownMenuItem disabled> {/* Link to event analytics */}
                         <BarChart className="h-4 w-4 mr-2" />
                         View Analytics
                       </DropdownMenuItem>
                       <DropdownMenuItem disabled> {/* Link to payout details */}
                         <TrendingUp className="h-4 w-4 mr-2" />
                         Payout Details
                       </DropdownMenuItem>
                       <DropdownMenuItem disabled> {/* Link to download report */}
                         <Download className="h-4 w-4 mr-2" />
                         Download Report
                       </DropdownMenuItem>
                     </DropdownMenuContent>
                   </DropdownMenu>
                 </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

       {/* Pagination */}
       {totalPages > 1 && (
         <Pagination className="mt-6">
           {/* ... Pagination Content (same as Visitor table) ... */}
            <PaginationContent>
             <PaginationItem>
               <PaginationPrevious
                 href="#"
                 onClick={(e) => {
                   e.preventDefault();
                   if (currentPage > 1) onPageChange(currentPage - 1);
                 }}
                 className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
               />
             </PaginationItem>
             {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
               <PaginationItem key={page}>
                 <PaginationLink
                   href="#"
                   onClick={(e) => {
                     e.preventDefault();
                     onPageChange(page);
                   }}
                   isActive={page === currentPage}
                 >
                   {page}
                 </PaginationLink>
               </PaginationItem>
             ))}
             <PaginationItem>
               <PaginationNext
                 href="#"
                 onClick={(e) => {
                   e.preventDefault();
                   if (currentPage < totalPages) onPageChange(currentPage + 1);
                 }}
                 className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
               />
             </PaginationItem>
           </PaginationContent>
         </Pagination>
       )}
    </div>
  );
}