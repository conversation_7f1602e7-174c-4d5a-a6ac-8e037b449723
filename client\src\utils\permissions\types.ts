
/**
 * Permission Type Definitions
 * Following CRUD (Create, Read, Update, Delete) pattern for consistency
 */

export type Permission =
  // Dashboard Permissions
  | "dashboard:read"    // View dashboard content
  | "dashboard:manage"  // Full dashboard control

  // User Management - CRUD operations
  | "users:create"      // Add new users
  | "users:read"        // View user details
  | "users:update"      // Modify user data
  | "users:delete"      // Remove users
  | "users:manage"      // Full user management access

  // Event Management - CRUD operations
  | "events:create"     // Ability to create new events
  | "events:read"       // Ability to view event details
  | "events:update"     // Modify event data
  | "events:delete"     // Remove events
  | "events:manage"     // Additional management capabilities (settings, configurations)
  | "events:publish"    // Ability to change event status
  | "events:settings"   // Access to event settings

  // Ticket Management - CRUD + specific actions
  | "tickets:create"    // Create new tickets
  | "tickets:read"      // View ticket details
  | "tickets:update"    // Modify ticket data
  | "tickets:delete"    // Remove tickets
  | "tickets:manage"    // Full ticket management
  | "tickets:book"      // Book tickets (specific action)
  | "tickets:cancel"    // Cancel tickets (specific action)

  // Analytics
  | "analytics:read"    // View analytics data
  | "analytics:export"  // Export analytics reports
  | "analytics:manage"  // Full analytics control

  // Settings
  | "settings:read"     // View settings
  | "settings:manage";  // Modify settings

/**
 * User Roles
 * Hierarchical structure from highest to lowest privileges
 */
export type UserRole = "ADMIN" | "MANAGER" | "VISITOR";

/**
 * Permission Configuration Interface
 * Used for routing and access control
 */
export interface PermissionConfig {
  permissions: Permission[];  // Required permissions
  routes: string[];          // Associated routes
}

/**
 * Resource Action Types
 * Generic actions that can be performed on resources
 */
export type ResourceAction = "create" | "read" | "update" | "delete" | "manage";

/**
 * Permission Check Result
 * Used for returning permission check results with context
 */
export interface PermissionCheckResult {
  hasPermission: boolean;
  requiredPermission: Permission;
  userRole: UserRole;
  reason?: string;
}

// Defining specific Operations Types for each feature
export interface EventOperations {
    canCreate: boolean;
    canUpdate: boolean;
    canDelete: boolean;
    canManage: boolean;
    canPublish?:boolean;
    canManageSettings?:boolean;
}

export interface UserOperations {
    canCreate: boolean;
    canRead: boolean;
    canUpdate: boolean;
    canDelete: boolean;
    canManage: boolean;
}

export interface TicketOperations {
    canCreate: boolean;
    canRead: boolean;
    canUpdate: boolean;
    canDelete: boolean;
    canManage: boolean;
    canBook:boolean;
    canCancel:boolean
}

export interface AnalyticsOperations {
    canRead: boolean;
    canExport: boolean;
    canManage: boolean;
}

export interface SettingsOperations{
    canRead: boolean;
    canManage: boolean;
}























// -----------------------------------------------------------------// /**
//  * Permission Type Definitions
//  * Following CRUD (Create, Read, Update, Delete) pattern for consistency
//  */

// export type Permission =
//   // Dashboard Permissions
//   | "dashboard:read"    // View dashboard content
//   | "dashboard:manage"  // Full dashboard control

//   // User Management - CRUD operations
//   | "users:create"      // Add new users
//   | "users:read"        // View user details
//   | "users:update"      // Modify user data
//   | "users:delete"      // Remove users
//   | "users:manage"      // Full user management access

//     // Event Management - Granular CRUD operations
//     | "events:create"     // Ability to create new events
//     | "events:read"       // Ability to view event details
//     | "events:update"     // Ability to modify events
//     | "events:delete"     // Ability to remove events
//     | "events:manage"     // Additional management capabilities (settings, configurations)
//     | "events:publish"    // Ability to change event status
//     | "events:settings"   // Access to event settings

//   // Ticket Management - CRUD + specific actions
//   | "tickets:create"    // Create new tickets
//   | "tickets:read"      // View ticket details
//   | "tickets:update"    // Modify ticket data
//   | "tickets:delete"    // Remove tickets
//   | "tickets:manage"    // Full ticket management
//   | "tickets:book"      // Book tickets (specific action)
//   | "tickets:cancel"    // Cancel tickets (specific action)

//   // Analytics
//   | "analytics:read"    // View analytics data
//   | "analytics:export"  // Export analytics reports
//   | "analytics:manage"  // Full analytics control

//   // Settings
//   | "settings:read"     // View settings
//   | "settings:manage";  // Modify settings

// /**
//  * User Roles
//  * Hierarchical structure from highest to lowest privileges
//  */
// export type UserRole = "ADMIN" | "MANAGER" | "VISITOR";

// /**
//  * Permission Configuration Interface
//  * Used for routing and access control
//  */
// export interface PermissionConfig {
//   permissions: Permission[];  // Required permissions
//   routes: string[];          // Associated routes
// }

// /**
//  * Resource Action Types
//  * Generic actions that can be performed on resources
//  */
// export type ResourceAction = "create" | "read" | "update" | "delete" | "manage";

// /**
//  * Permission Check Result
//  * Used for returning permission check results with context
//  */
// export interface PermissionCheckResult {
//   hasPermission: boolean;
//   requiredPermission: Permission;
//   userRole: UserRole;
//   reason?: string;
// }