import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { asyncHand<PERSON> } from '@/utils/asyncHandler';
import ApiError from '@/utils/ApiError';

/**
  * Type declaration to extend Express Request interface
  * This allows TypeScript to recognize the user and requestId properties we'll attach
  */
declare global {
    namespace Express {
        interface Request {
            user?: jwt.JwtPayload;
            requestId?: string; // Added this line
        }
    }
}

/**
  * Authentication Middleware
  * 
  * This middleware performs token-based authentication by:
  * 1. Extracting JWT token from request headers, cookies, or custom header
  * 2. Verifying token validity and signature
  * 3. Attaching decoded user information to request object
  * 
  * Uses asyncHandler for consistent error handling across middleware
  */
export const authMiddleware = asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      // Log request details for debugging purposes with requestId
      console.log(`🔐 [${req.requestId}] Auth Middleware - Checking authentication`);
          
      // Extract token from multiple possible locations
      const token =
        req.headers.authorization?.split(' ')[1] || 
        req.cookies?.token ||                       
        req.header('x-auth-token');                 
  
      // Log whether token was found in request
      console.log(`🎫 [${req.requestId}] Token present:`, !!token);
      
      if (!token) {
        console.log(`❌ [${req.requestId}] No token found in request`);
        throw new ApiError(401, 'Access denied. No token provided');
      }
  
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as jwt.JwtPayload;
        req.user = decoded;
        
        console.log(`✅ [${req.requestId}] Auth successful for user:`, decoded.userId);
        next();
      } catch (error) {
        console.error(`❌ [${req.requestId}] Token verification failed:`, error);
        throw new ApiError(401, 'Invalid token');
      }
    }
);











// import { Request, Response, NextFunction } from 'express';
// import jwt from 'jsonwebtoken';
// import { asyncHandler } from '@/utils/asyncHandler';
// import ApiError from '@/utils/ApiError';


// /**
//   * Type declaration to extend Express Request interface
//   * This allows TypeScript to recognize the user property we'll attach
//   */
// declare global {
//     namespace Express {
//         interface Request {
//             user?: jwt.JwtPayload
//         }
//     }
// }

// /**
//   * Authentication Middleware
//   * 
//   * This middleware performs token-based authentication by:
//   * 1. Extracting JWT token from request headers, cookies, or custom header
//   * 2. Verifying token validity and signature
//   * 3. Attaching decoded user information to request object
//   * 
//   * Uses asyncHandler for consistent error handling across middleware
//   */
// export const authMiddleware = asyncHandler(
//     async (req: Request, res: Response, next: NextFunction) => {
//       // Log request details for debugging purposes
//       console.log('🔐 Auth Middleware - Checking authentication');
//       // console.log('📝 Headers:', JSON.stringify({
//       //   authorization: req.headers.authorization,
//       //   cookie: req.headers.cookie
//       // }, null, 2));
          
//       // Extract token from multiple possible locations
//       // 1. Authorization header (Bearer token format)
//       // 2. Cookies object
//       // 3. Custom x-auth-token header
//       const token =
//         req.headers.authorization?.split(' ')[1] || // Bearer TOKEN format
//         req.cookies?.token ||                       // Cookie storage
//         req.header('x-auth-token');                 // Custom header
  
//       // Log whether token was found in request
//       console.log('🎫 Token present:', !!token);
      
//       // Return 401 Unauthorized if no token is provided
//       if (!token) {
//         console.log('❌ No token found in request');
//         throw new ApiError(401, 'Access denied. No token provided');
//       }
  
//       try {
//         // Verify token signature and expiration
//         // Uses JWT_SECRET from environment variables
//         const decoded = jwt.verify(token, process.env.JWT_SECRET!) as jwt.JwtPayload;
        
//         // Attach decoded user information to request object
//         // This makes user data available to subsequent route handlers
//         (req as any).user = decoded;
  
//         // Log successful authentication details
//         // console.log('✅ Auth successful:', { userId: decoded.userId, role: decoded.role });
        
//         // Proceed to the next middleware or route handler
//         next();
//       } catch (error) {
//         // Log and handle token verification errors
//         // This catches expired tokens, invalid signatures, etc.
//         console.error('❌ Token verification failed:', error);
//         throw new ApiError(401, 'Invalid token');
//       }
//     }
// );
