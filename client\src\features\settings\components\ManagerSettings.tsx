// ManagerSettings component for handling manager-specific settings
'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

// Define form schema using Zod
const formSchema = z.object({
  eventType: z.string(),
  manageableRegions: z.string(),
})

// Updated ManagerSettings component with form functionality
export function ManagerSettings() {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      eventType: 'all',
      manageableRegions: 'local',
    },
  })

  function onSubmit(values: z.infer<typeof formSchema>) {
    console.log(values)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="eventType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Event Type Management</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select event types to manage" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="all">All Events</SelectItem>
                  <SelectItem value="sports">Sports Only</SelectItem>
                  <SelectItem value="concerts">Concerts Only</SelectItem>
                </SelectContent>
              </Select>
              <FormDescription>
                Select which types of events you can manage
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="manageableRegions"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Manageable Regions</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select regions" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="local">Local Events</SelectItem>
                  <SelectItem value="regional">Regional Events</SelectItem>
                  <SelectItem value="national">National Events</SelectItem>
                </SelectContent>
              </Select>
              <FormDescription>
                Define your event management scope
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit">Save Manager Settings</Button>
      </form>
    </Form>
  )
}