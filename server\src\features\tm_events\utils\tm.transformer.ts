// Imports for TmApiEvent, TmEvent types and Prisma client
import { TmApiEvent, TmEvent } from "../types/tm.types";
import { Prisma } from "@prisma/client";

// TmEventTransformer class for transforming TmApiEvent to TmEvent
export class TmEventTransformer {
  static toDbModel(
    apiEvent: TmApiEvent
  ): Omit<TmEvent, "createdAt" | "updatedAt"> {
    const venue = apiEvent._embedded?.venues?.[0];
    const classification = apiEvent.classifications[0];
    const priceRange = apiEvent.priceRanges?.[0];

    return {
      id: apiEvent.id,
      name: apiEvent.name,
      type: apiEvent.type,
      url: apiEvent.url ?? null, // Apply nullish coalescing operator
      locale: apiEvent.locale ?? null, // Apply nullish coalescing operator

      // Images
      primaryImage: this.extractPrimaryImage(apiEvent.images),
      images: this.toJsonValue(apiEvent.images),

      // Dates and Status
      startDateTime: apiEvent.dates.start.dateTime
        ? new Date(apiEvent.dates.start.dateTime)
        : null,
      endDateTime: null,
      timezone: apiEvent.dates.timezone,
      status: this.toJsonValue(apiEvent.dates.status),

      // Classifications
      classifications: this.toJsonValue(apiEvent.classifications),
      segment: classification?.segment?.name || null,
      genre: classification?.genre?.name || null,
      subGenre: classification?.subGenre?.name || null,

      // Venue
      venue: this.toJsonValue(venue),
      venueName: venue?.name || null,
      venueCity: venue?.city?.name || null,
      venueState: venue?.state?.name || null,
      venueCountry: venue?.country?.name || null,

      // Pricing
      priceRanges: this.toJsonValue(apiEvent.priceRanges),
      priceRangeMin: priceRange?.min || null,
      priceRangeMax: priceRange?.max || null,
      currency: priceRange?.currency || null,

      // Additional Info
      sales: this.toJsonValue(apiEvent.sales),
      seatmap: this.toJsonValue(apiEvent.seatmap),
      ticketLimit: this.toJsonValue(apiEvent.ticketLimit),
      accessibility: this.toJsonValue(apiEvent.accessibility),
      promoter: this.toJsonValue(apiEvent.promoter),
      promoters: this.toJsonValue(apiEvent.promoters),
      products: this.toJsonValue(apiEvent.products),
      links: this.toJsonValue(apiEvent._links),
    };
  }

  private static extractPrimaryImage(
    images: TmApiEvent["images"]
  ): string | null {
    return images.find((img) => img.ratio === "16_9")?.url || null;
  }

  private static toJsonValue(data: any): Prisma.JsonValue | null {
    if (data === null || data === undefined) {
      return null;
    }
    return data as Prisma.JsonValue;
  }

  static transformBatch(
    apiEvents: TmApiEvent[]
  ): Omit<TmEvent, "createdAt" | "updatedAt">[] {
    return apiEvents.map((event) => this.toDbModel(event));
  }
}
