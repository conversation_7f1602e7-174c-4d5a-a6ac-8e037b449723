import { NavItem } from '../../types/navigation.types'
import { RoleBasedLink } from '../Link/RoleBasedLink'

interface SidebarItemProps {
  item: NavItem
  collapsed: boolean
}

export function SidebarItem({ item, collapsed }: SidebarItemProps) {
  return (
    <li className="relative">
      <RoleBasedLink item={item} collapsed={collapsed} />
    </li>
  )
}



















// import React from 'react';
// import { NavItem } from '@/features/navigation/types/navigation.types';
// import Link from 'next/link';
// import { usePathname } from 'next/navigation';
// import { cn } from '@/lib/utils';
// import { Badge } from '@/components/ui/badge';
// import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
// import { useAppSelector } from '@/app/redux';

// interface SidebarItemProps {
//   item: NavItem;
//   collapsed: boolean;
// }

// export const SidebarItem: React.FC<SidebarItemProps> = ({ item, collapsed }) => {
//   const pathname = usePathname();
//   const isActive = pathname === item.path;
//     const isSidebarCollapsed = useAppSelector((state) => state.global.isSidebarCollapsed);


//   return (
//     <TooltipProvider>
//       <li className="relative">
//         <Link
//           href={item.path}
//           className={cn(
//             'flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium transition-colors hover:bg-gray-100 dark:hover:bg-gray-800',
//             isActive
//               ? 'bg-gray-100 dark:bg-gray-800 text-indigo-500 dark:text-indigo-400'
//               : 'text-gray-600 dark:text-gray-300'
//           )}
//         >
//           <Tooltip delayDuration={200}>
//             <TooltipTrigger asChild>
//               <span className="flex-shrink-0">
//                 <item.icon className="h-4 w-4" />
//               </span>
//             </TooltipTrigger>
//             <TooltipContent
//               side="right"
//               className="dark:bg-gray-800 dark:text-gray-100"
//             >
//               {item.label}
//             </TooltipContent>
//           </Tooltip>
//           <span
//             className={cn(
//               'flex-1',
//               isSidebarCollapsed ? 'hidden' : 'block'
//             )}
//           >
//             {item.label}
//           </span>
//           {item.badges &&
//             Object.entries(item.badges).map(([key, badge]) => (
//               <Badge
//                 key={key}
//                 variant="secondary"
//                 className={`ml-auto ${
//                   isSidebarCollapsed ? 'hidden' : 'block'
//                 } bg-${badge.color}-500 text-white `}
//               >
//                 {badge.label}
//               </Badge>
//             ))}
//         </Link>
//       </li>
//     </TooltipProvider>
//   );
// };
















