import { Request, Response, NextFunction } from 'express';
import { asyncHand<PERSON> } from '@/utils/asyncHandler';
import { QueueService } from '../services/queue.service';
import ApiError from '@/utils/ApiError';

const queueService = new QueueService();

/**
 * Extract user ID from authenticated request
 */
const extractUserId = (req: Request): string => {
    const userId = (req as any).user?.userId;
    if (!userId) {
        throw new ApiError(401, 'User ID not found in authenticated request');
    }
    return userId;
};

export class QueueController {
    /**
     * Gets the queue status for a specific event, including user-specific details.
     */
    static getQueueStatus = asyncHandler(async (req: Request, res: Response) => {
        const { eventId } = req.params;
        if (!eventId) {
            throw new ApiError(400, 'Event ID parameter is required');
        }

        const userId = extractUserId(req); // Get user ID after auth middleware

        const status = await queueService.getQueueStatus(eventId, userId);

        res.status(200).json({
            success: true,
            message: 'Queue status retrieved successfully.',
            data: status,
        });
    });

    /**
     * Allows an authenticated user to join the queue for a specific event.
     */
    static joinQueue = asyncHandler(async (req: Request, res: Response) => {
        const { eventId } = req.body;
        if (!eventId) {
            throw new ApiError(400, 'Event ID is required in the request body');
        }

        const userId = extractUserId(req); // Get user ID after auth middleware

        // Use the method that automatically gets priority based on membership
        const joinResult = await queueService.joinQueueWithMembership(eventId, userId);

        // Determine appropriate status code based on result (e.g., 201 Created if new, 200 OK if already exists)
        // For simplicity, using 200 OK for now.
        res.status(200).json({
            success: true,
            message: joinResult.message, // Use message from the service
            data: {
                queueId: joinResult.queueId,
                userId: joinResult.userId,
                status: joinResult.status,
                position: joinResult.position,
                // estimatedWaitTime: joinResult.estimatedWaitTime // Uncomment if service provides this
            },
        });
    });
}
