// Hook for managing global search functionality
import { useState, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/app/redux';
import { setSelectedSearchEvent, setSearchQuery } from '@/state';
import { useSearchEventsQuery } from '@/state/api';
import { SearchState } from '../types/search.types';
import { Context } from '@/types/openctx.types';

export const useGlobalSearch = () => {
  const dispatch = useAppDispatch();
  const query = useAppSelector((state) => state.global.searchQuery || ''); // Add default empty string
  // Initialize isLoading to false in searchState
  const [searchState, setSearchState] = useState<SearchState>({
      query: '',
      results: [],
      isLoading: false
  });

  // State to store toggle status of nlp search
  const [isNlpSearchActive, setIsNlpSearchActive] = useState(false);

  // Toggle function to manage nlp state
  const toggleNlpSearch = () => {
      setIsNlpSearchActive(prev => !prev);
      // Reset search query when toggling to prevent stale searches
      dispatch(setSearchQuery(''));
  };

  const { data: apiResponse, isLoading, isError } = useSearchEventsQuery(query, {
      skip: !query || query.length < 3 || isNlpSearchActive // Add null check for query
  });

  // Function to update the search query and set the loading state
  const handleSearch = (newQuery: string) => {
      dispatch(setSearchQuery(newQuery));
      // Set isLoading to true when a new search is initiated
      setSearchState(prev => ({ ...prev, isLoading: true, query: newQuery }));
      console.log('Search initiated with query:', newQuery);
  };

  // Function to select event and dispatch to store
  const selectEvent = (ctxEvent: Context | null) => {
      dispatch(setSelectedSearchEvent(ctxEvent));
      console.log('Selected event:', ctxEvent);
  };

  useEffect(() => {
      // Set loading state to true when query changes and api call is pending
      if (isLoading) {
        setSearchState(prev => ({ ...prev, results: [], isLoading: true }));
        console.log('Loading search results...');
        return;
      }

      if (apiResponse?.data) {
          // Check if apiResponse.data is an array before processing
          if (Array.isArray(apiResponse.data)) {
        console.log("openctxApi response ", apiResponse.data);
            const contexts = apiResponse.data; // Removed the type assertion
            
            // First, separate manager events from other events
            const managerEvents = contexts.filter(ctx => 
              ctx.type === 'managerEvent' || ctx.metadata?.source === 'manager'
            );
            
            // Then process the remaining events
            const otherContexts = contexts.filter(ctx => 
              ctx.type !== 'managerEvent' && ctx.metadata?.source !== 'manager'
            );
            
            // Categorize regular results based on specific segments
            const categorized = otherContexts.reduce<Record<string, Context[]>>((acc, ctx) => {
                const segment = ctx.metadata?.rawEvent?.classifications?.[0]?.segment?.name;

                if (segment === 'Music' || segment === 'Sports' || segment === 'Arts & Theatre' ) {
                    if (!acc[segment]) {
                        acc[segment] = [];
                    }
                     acc[segment].push(ctx);
                }

              return acc;
            }, {});

            // Limit events to 10 per category
            const regularResults = Object.entries(categorized).map(([category, events]) => ({
                category,
                events: events.slice(0,10)
              }));
            
            // Add manager events as a special category at the top if any exist
            const results = managerEvents.length > 0 
              ? [{ category: 'Manager Events', events: managerEvents }, ...regularResults]
              : regularResults;
            
             // Set isLoading to false and update results
             setSearchState(prev => ({ ...prev, results, isLoading: false }));
              console.log('Search results loaded successfully:', results);
          }
          else {
            console.error('API did not return an array:', apiResponse);
            // Set isLoading to false and clear results
            setSearchState(prev => ({ ...prev, results: [], isLoading: false }));
          }
      } else if (isError) {
          // Set isLoading to false and clear results
          setSearchState(prev => ({ ...prev, results: [], isLoading: false }));
          console.error('Error fetching search results');
      }
  }, [apiResponse, isLoading, isError]);

  return {
      ...searchState,
      handleSearch,
      selectEvent,
      toggleNlpSearch, // returning toggle action
      isNlpSearchActive // returning toggle state
  };
};
