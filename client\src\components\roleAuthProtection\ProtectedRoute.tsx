"use client";

import { useRoleNavigation } from "@/features/navigation/hooks/useRoleNavigation";
import { UserRole } from "@/utils/permissions/types";
import { redirect } from "next/navigation";
import { useEffect } from "react";

interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles: UserRole[];
}

export const ProtectedRoute = ({
  children,
  allowedRoles,
}: ProtectedRouteProps) => {
  const { isAuthenticated, userRole, basePath } = useRoleNavigation();

  //TODO: there is no auth/login route, so redirect to home page or triger the login modal @Authmodal
  useEffect(() => {
    if (!isAuthenticated) {
      redirect("/auth/login");
    }

    if (!allowedRoles.includes(userRole)) {
      redirect(basePath);
    }
  }, [isAuthenticated, userRole, allowedRoles, basePath]);

  return <>{children}</>;
};
//
