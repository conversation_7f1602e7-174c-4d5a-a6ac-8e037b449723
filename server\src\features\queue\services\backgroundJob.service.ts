// Service to run background tasks related to the queue system, like checking for deactivation.

import cron from 'node-cron';
import { PrismaClient } from '@prisma/client';
import { DemandDetectionService } from './demandDetection.service';
import { QueueService } from './queue.service';
import { NODE_ENV } from '@/constants';
// Import centralized schedules
import { CRON_SCHEDULES, CRON_JOB_NAMES } from '@/config/cronJob.config';

const prisma = new PrismaClient();
const demandDetectionService = new DemandDetectionService();
const queueService = new QueueService();

// REMOVED: Configuration constants (CRON_SCHEDULE, BATCH_CHECK_INTERVAL) - now in cronJob.config.ts

class BackgroundJobService {
    private deactivationTask: cron.ScheduledTask | null = null;
    private batchProcessingTask: cron.ScheduledTask | null = null;

    /**
     * Task that checks if queues should be deactivated based on demand
     */
    private async checkDeactivationTask(): Promise<void> {
        if (NODE_ENV === 'development') {
            // Use job name from config for logging
            console.log(`⏳ [BackgroundJob] Running ${CRON_JOB_NAMES.queueDeactivation}...`);
        }

        try {
            // Find all currently active queues
            const activeQueues = await prisma.queue.findMany({
                where: { isActive: true },
                select: { eventId: true }
            });

            if (activeQueues.length === 0) {
                if (NODE_ENV === 'development') {
                    console.log('ℹ️ [BackgroundJob] No active queues found to check for deactivation.');
                }
                return;
            }

            if (NODE_ENV === 'development') {
                console.log(`🔍 [BackgroundJob] Checking ${activeQueues.length} active queues for deactivation.`);
            }

            // Check each queue for deactivation
            let deactivatedCount = 0;
            for (const queue of activeQueues) {
                try {
                    const deactivated = await demandDetectionService.checkAndDeactivateQueue(queue.eventId);
                    if (deactivated) deactivatedCount++;
                } catch (error) {
                    console.error(`❌ [BackgroundJob] Error checking deactivation for event ${queue.eventId}:`, 
                        error instanceof Error ? error.message : error);
                }
            }

            if (NODE_ENV === 'development') {
                console.log(`✅ [BackgroundJob] Deactivation check complete. Deactivated: ${deactivatedCount} queues.`);
            }
        } catch (error) {
            console.error('❌ [BackgroundJob] Critical error during the queue deactivation check:', error);
        }
    }

    /**
     * Task that processes batches of users in active queues
     * Moves users from WAITING to ACTIVE status based on priority
     */
    private async processBatchesTask(): Promise<void> {
        if (NODE_ENV === 'development') {
            console.log(`⏳ [BackgroundJob] Running ${CRON_JOB_NAMES.queueBatchProcessing} - Batch Processing Phase...`);
        }

        try {
            // Find all currently active queues
            const activeQueues = await prisma.queue.findMany({
                where: { isActive: true },
                select: { 
                    id: true,
                    eventId: true, 
                    batchSize: true 
                }
            });

            if (activeQueues.length === 0) {
                if (NODE_ENV === 'development') {
                    console.log('ℹ️ [BackgroundJob] No active queues found for batch processing.');
                }
                return;
            }

            if (NODE_ENV === 'development') {
                console.log(`🔍 [BackgroundJob] Processing batches for ${activeQueues.length} active queues.`);
            }

            // Process each queue independently
            let totalProcessed = 0;
            for (const queue of activeQueues) {
                try {
                    // Use the queue's configured batch size if available, otherwise use default
                    const batchSize = queue.batchSize || undefined; // undefined will use DEFAULT_BATCH_SIZE in QueueService
                    
                    // Process the next batch of users for this queue
                    const admittedCount = await queueService.processNextBatch(queue.eventId, batchSize);
                    
                    if (admittedCount > 0) {
                        totalProcessed += admittedCount;
                        console.log(`🎟️ [BackgroundJob] Admitted ${admittedCount} users for event ${queue.eventId}.`);
                    }
                } catch (error) {
                    console.error(`❌ [BackgroundJob] Error processing batch for event ${queue.eventId}:`, 
                        error instanceof Error ? error.message : error);
                }
            }

            if (NODE_ENV === 'development') {
                console.log(`✅ [BackgroundJob] Batch processing complete. Total users admitted: ${totalProcessed}.`);
            }
        } catch (error) {
            console.error('❌ [BackgroundJob] Critical error during batch processing:', error);
        }
    }

    /**
     * Also periodically check and expire active sessions that have reached their time limit
     */
    private async expireSessionsTask(): Promise<void> {
        if (NODE_ENV === 'development') {
            console.log(`⏳ [BackgroundJob] Running ${CRON_JOB_NAMES.queueBatchProcessing} - Session Expiry Phase...`);
        }

        try {
            const expiredCount = await queueService.expireActiveSessions();
            if (expiredCount > 0 && NODE_ENV === 'development') {
                console.log(`⏱️ [BackgroundJob] Expired ${expiredCount} active queue sessions.`);
            }
        } catch (error) {
            console.error('❌ [BackgroundJob] Error expiring queue sessions:', error);
        }
    }

    /**
     * Starts all background jobs
     */
    public start(): void {
        if (NODE_ENV === 'development') {
            console.log('🚀 [BackgroundJob] Starting QUEUE background job service...');
        }

        // Start deactivation check task using centralized schedule
        this.deactivationTask = cron.schedule(CRON_SCHEDULES.queueDeactivation, () => {
            this.checkDeactivationTask().catch(err => {
                console.error(`❌ [BackgroundJob] Unhandled error in ${CRON_JOB_NAMES.queueDeactivation} task:`, err);
            });
        });

        // Start batch processing & session expiry task using centralized schedule
        this.batchProcessingTask = cron.schedule(CRON_SCHEDULES.queueBatchProcessing, async () => {
            try {
                // First expire any queue sessions that have timed out
                await this.expireSessionsTask();
                // Then process waiting users into active status
                await this.processBatchesTask();
            } catch (err) {
                console.error(`❌ [BackgroundJob] Unhandled error in ${CRON_JOB_NAMES.queueBatchProcessing} task:`, err);
            }
        });

        if (NODE_ENV === 'development') {
            console.log('✅ [BackgroundJob] QUEUE background jobs started successfully.');
        }
    }

    /**
     * Stops all background jobs
     */
    public stop(): void {
        if (this.deactivationTask) {
            this.deactivationTask.stop();
            this.deactivationTask = null;
        }
        
        if (this.batchProcessingTask) {
            this.batchProcessingTask.stop();
            this.batchProcessingTask = null;
        }

        if (NODE_ENV === 'development') {
            console.log('🛑 [BackgroundJob] QUEUE background jobs stopped.');
        }
    }
}

// Create and export a singleton instance
export const backgroundJobService = new BackgroundJobService();
