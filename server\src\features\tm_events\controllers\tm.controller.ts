// TmEventController: Handles Ticketmaster event-related API endpoints

import { Request, Response, NextFunction } from 'express';
import { TmEventService } from '../services/tm.service';
import { TmEventQueryParams, CustomRequest } from '../types/tm.types';
import { validateQueryParams } from '../utils/tm.validator';
import { asyncHandler } from '@/utils/asyncHandler';

export class TmEventController {
  static getEvents = asyncHandler(
    async (req: CustomRequest, res: Response, next: NextFunction) => {
        const queryParams: TmEventQueryParams = req.query;
        
      // Validate query parameters
      validateQueryParams(queryParams);
      
        const { events, pagination } = await TmEventService.fetchAndCacheEvents(queryParams);

      res.setHeader('X-RateLimit-Remaining', req.rateLimit?.remaining || 0);
      res.setHeader('X-RateLimit-Reset', req.rateLimit?.resetTime || 0);

      res.status(200).json({
        success: true,
        message: 'Ticketmaster events fetched successfully',
        data: {
          events,
          pagination,
        },
      });
    }
  );
  static invalidateCache = asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
       const { query, page, size } = req.query as { query:string, page:string, size: string};
        await TmEventService.invalidateCache(query, page, size);
       res.status(200).json({
            success: true,
            message: "Cache invalidated successfully",
        });
   }
);
}
