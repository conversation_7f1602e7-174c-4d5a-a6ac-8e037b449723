// Defines the TypeScript types used within the manager inventory feature.

import { InventoryItem as SharedInventoryItem } from '@/features/event-listing/types/eventListing';

// Define string literal types based on expected backend enum values FOR FIELDS *OUTSIDE* inventory JSON
export type EventCategoryString = "SPORTS" | "MUSIC" | "ARTS" | "THEATER";
export type EventApprovalStatusString = "PENDING" | "APPROVED" | "REJECTED";

// Alias the imported type for clarity within this feature's context
export type InventoryDetailItem = SharedInventoryItem;

// Represents the full ManagerEvent data received from the API
export interface ManagerInventoryItem {
  id: string;
  eventId: string;
  priorityEventId?: string | null;
  name: string;
  category: EventCategoryString;
  source: string;
  date: Date; // Transformed in hook
  venue: string;
  city: string;
  country: string;
  image?: string | null;
  inventory: InventoryDetailItem[]; // Use the detailed type (imported structure)
  purchaseOrder?: any | null;
  rawEventData?: any | null;
  isActive: boolean;
  status: string;
  approvalStatus: EventApprovalStatusString;
  approvedBy?: string | null;
  approvedAt?: Date | null; // Transformed in hook
  approvalNotes?: string | null;
  createdAt: Date; // Transformed in hook
  updatedAt?: Date; // Transformed in hook
}

// Props for the main table component (no changes needed for view details)
export interface ManagerInventoryTableProps {}

// Props for the actions dropdown/buttons for each row
export interface ManagerInventoryActionsProps {
  item: ManagerInventoryItem;
  toggleActive: (payload: { eventId: string }) => void;
  isToggling: boolean;
  deleteEvent: (payload: { eventId: string }) => void;
  isDeleting: boolean;
  onViewDetails: (item: ManagerInventoryItem) => void;
  onEditInventory: (item: ManagerInventoryItem) => void;
}

// Props for the Details Modal component
export interface EventDetailsModalProps {
    item: ManagerInventoryItem | null; // The item to display, or null if closed
    isOpen: boolean;
    onClose: () => void;
}

// ✨ Props for the Edit Inventory Modal component ✨
export interface EditInventoryModalProps {
    item: ManagerInventoryItem | null; // The item whose inventory is being edited
    isOpen: boolean;
    onClose: () => void;
    // ✨ Add mutation function and state ✨
    updateInventory: (payload: { eventId: string; inventory: InventoryDetailItem[] }) => void;
    isUpdatingInventory: boolean;
}

// Props for the Approval Status Badge
export interface ApprovalStatusBadgeProps {
    status: EventApprovalStatusString;
}

// Props for the general Event Status Badge (if reused/adapted)
export interface EventStatusBadgeProps {
    status: string; // General status like "LISTED", "SOLD"
    isActive: boolean; // Pass isActive to potentially style the badge
}

// Props for the Filter component
export interface ManagerInventoryFilterProps {
    filterText: string;
    onFilterChange: (text: string) => void;
}
