/**
 * ProfileInfo Component
 * 
 * Displays detailed information about the user in a well-structured format.
 * Shows location, contact info, social links, and other key details.
 */

import React from 'react';
import { ProfileComponentProps } from '../../types/profile.types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  MapPin, Mail, Phone, Calendar, 
  Twitter, Linkedin, Github, Globe, 
  Pencil
} from 'lucide-react';
import { Separator } from '@/components/ui/separator';

export function ProfileInfo({ profile, isEditable = false }: ProfileComponentProps) {
  const joinDate = profile.joinedDate 
    ? new Date(profile.joinedDate).toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      })
    : 'Unknown';

    //adding console log for profile information
    console.log("Profile Info 💀💀:", profile);
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-xl">About</CardTitle>
        {isEditable && (
          <Button variant="ghost" size="sm" className="h-8 px-2">
            <Pencil className="h-4 w-4 mr-1" />
            <span className="text-sm">Edit</span>
          </Button>
        )}
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {/* Basic Info Section */}
          <div className="space-y-2">
            {profile.location && (
              <div className="flex items-center text-sm">
                <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                <span>{profile.location}</span>
              </div>
            )}
            
            <div className="flex items-center text-sm">
              <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
              <span>{profile.email || 'No email available'}</span>
            </div>
            
            {profile.mobileVerified && (
              <div className="flex items-center text-sm">
                <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                <span>{profile.mobile}</span>
              </div>
            )}
            
            <div className="flex items-center text-sm">
              <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
              <span>Joined {joinDate}</span>
            </div>
          </div>
          
          <Separator />
          
          {/* Social Links Section */}
          {profile.socialLinks && Object.values(profile.socialLinks).some(Boolean) && (
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Social Links</h3>
              <div className="flex flex-wrap gap-2">
                {profile.socialLinks.twitter && (
                  <Button variant="outline" size="sm" className="h-8" asChild>
                    <a href={profile.socialLinks.twitter} target="_blank" rel="noopener noreferrer">
                      <Twitter className="h-4 w-4 mr-1" />
                      <span className="text-xs">Twitter</span>
                    </a>
                  </Button>
                )}
                
                {profile.socialLinks.linkedin && (
                  <Button variant="outline" size="sm" className="h-8" asChild>
                    <a href={profile.socialLinks.linkedin} target="_blank" rel="noopener noreferrer">
                      <Linkedin className="h-4 w-4 mr-1" />
                      <span className="text-xs">LinkedIn</span>
                    </a>
                  </Button>
                )}
                
                {profile.socialLinks.github && (
                  <Button variant="outline" size="sm" className="h-8" asChild>
                    <a href={profile.socialLinks.github} target="_blank" rel="noopener noreferrer">
                      <Github className="h-4 w-4 mr-1" />
                      <span className="text-xs">GitHub</span>
                    </a>
                  </Button>
                )}
                
                {profile.socialLinks.website && (
                  <Button variant="outline" size="sm" className="h-8" asChild>
                    <a href={profile.socialLinks.website} target="_blank" rel="noopener noreferrer">
                      <Globe className="h-4 w-4 mr-1" />
                      <span className="text-xs">Website</span>
                    </a>
                  </Button>
                )}
              </div>
            </div>
          )}
          
          {/* Skills Section */}
          {profile.skills && profile.skills.length > 0 && (
            <>
              <Separator />
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Skills</h3>
                <div className="flex flex-wrap gap-1.5">
                  {profile.skills.map((skill, index) => (
                    <span 
                      key={index}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Add debugging if needed
console.log('📋 ProfileInfo loaded');
