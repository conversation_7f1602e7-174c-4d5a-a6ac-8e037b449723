import { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Loader2, Coins, CheckCircle, Info, X } from 'lucide-react';
import { AppliedPoints } from '../types/checkout.types';
import { cn } from '@/lib/utils';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface PointsSelectorProps {
  onApply: (points: number) => void;
  isApplying: boolean;
  appliedPoints?: AppliedPoints;
  userPointsBalance: number;
  orderTotal: number;
  className?: string;
}

export const PointsSelector: React.FC<PointsSelectorProps> = ({
  onApply,
  isApplying,
  appliedPoints,
  userPointsBalance = 0, // Ensure default value
  orderTotal = 0, // Ensure default value
  className
}) => {
  const [usePoints, setUsePoints] = useState(false);
  const [pointsToApply, setPointsToApply] = useState(0);
  // Flag to track if points have been applied through the button
  const [hasManuallyApplied, setHasManuallyApplied] = useState(false);
  
  // Ensure userPointsBalance is a number with default value
  const safeUserPointsBalance = typeof userPointsBalance === 'number' ? userPointsBalance : 0;
  
  // Maximum points that can be applied (lesser of user balance or orderTotal*100)
  const maxApplicablePoints = useMemo(() => {
    // Ensure we're working with valid numbers
    const safeOrderTotal = typeof orderTotal === 'number' ? orderTotal : 0;
    const maxForOrder = Math.ceil(safeOrderTotal * 100); // If 1 point = $0.01
    return Math.min(safeUserPointsBalance, maxForOrder);
  }, [safeUserPointsBalance, orderTotal]);
  
  // Dollar value of selected points (for display)
  const pointsValue = useMemo(() => {
    return parseFloat((pointsToApply * 0.01).toFixed(2));
  }, [pointsToApply]);
  
  // Reset points to apply when balance changes
  useEffect(() => {
    // Default to 50% of max applicable points
    setPointsToApply(Math.floor(maxApplicablePoints / 2));
  }, [maxApplicablePoints]);
  
  // When toggle changes
  useEffect(() => {
    if (!usePoints) {
      // If toggled off AND points were previously applied via button, send 0 points
      if (hasManuallyApplied) {
        onApply(0);
        setHasManuallyApplied(false);
      }
    }
  }, [usePoints, onApply, hasManuallyApplied]);
  
  // Apply points when button is clicked - this is now the ONLY place where onApply is called
  const handleApplyPoints = () => {
    if (usePoints && pointsToApply > 0 && !isApplying) {
      onApply(pointsToApply);
      setHasManuallyApplied(true);
    }
  };
  
  // Remove applied points
  const handleRemovePoints = () => {
    setUsePoints(false);
    onApply(0);
    setHasManuallyApplied(false);
  };
  
  // If no points balance, show empty state
  if (safeUserPointsBalance <= 0) {
    return (
      <div className={cn("space-y-2", className)}>
        <Label className="flex items-center">
          Credit Points
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="h-4 w-4 ml-2 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>
                <p>Earn points from purchases and referrals</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </Label>
        <div className="bg-muted/40 border rounded-md p-3 text-sm text-muted-foreground">
          <p>You don&apos;t have any credit points to apply to this order.</p>
        </div>
      </div>
    );
  }
  
  // If points are already applied, show the applied state
  if (appliedPoints && appliedPoints.pointsUsed > 0) {
    const effectiveRate = appliedPoints.refereeStatus?.discountMultiplier || 1.0;
    
    return (
      <div className={cn("space-y-2", className)}>
        <Label>Credit Points</Label>
        <div className="bg-green-50 border border-green-200 rounded-md p-3">
          <div className="flex justify-between items-center">
            <div className="flex items-center text-green-700">
              <CheckCircle className="h-5 w-5 mr-2" />
              <div>
                <p className="font-medium">{appliedPoints.pointsUsed.toLocaleString()} Points Applied</p>
                <p className="text-sm">
                  {effectiveRate < 1 ? 
                    `Applied at ${effectiveRate * 100}% value (referee bonus)` : 
                    'Applied at full value'}
                </p>
              </div>
            </div>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleRemovePoints}
              className="h-8 text-muted-foreground hover:text-destructive"
              disabled={isApplying}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <div className="mt-2 text-sm text-green-700 font-medium">
            Discount: ${appliedPoints.discountAmount.toFixed(2)}
          </div>
        </div>
      </div>
    );
  }
  
  // Otherwise, show the selector
  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <Label className="flex items-center">
          Use Credit Points
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="h-4 w-4 ml-2 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>
                <p>Apply your credit points for a discount</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </Label>
        <Switch
          checked={usePoints}
          onCheckedChange={setUsePoints}
          disabled={isApplying}
        />
      </div>
      
      {usePoints && (
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
            <div className="flex justify-between items-center">
              <div className="flex items-center text-blue-700">
                <Coins className="h-5 w-5 mr-2" />
                <p className="font-medium">
                  {safeUserPointsBalance.toLocaleString()} points available
                </p>
              </div>
            </div>
          </div>
          
          <div className="space-y-2 pt-2">
            <div className="flex justify-between text-sm">
              <span>Points to use:</span>
              <span>{pointsToApply.toLocaleString()} points</span>
            </div>
            
            <Slider
              value={[pointsToApply]}
              min={0}
              max={maxApplicablePoints}
              step={100}
              onValueChange={(value) => setPointsToApply(value[0])}
              disabled={isApplying}
            />
            
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>0</span>
              <span>{maxApplicablePoints.toLocaleString()}</span>
            </div>
            
            <div className="mt-2 text-sm font-medium">
              Discount value: ${pointsValue.toFixed(2)}
            </div>
            
            <Button
              onClick={handleApplyPoints}
              disabled={pointsToApply <= 0 || isApplying}
              className="mt-2 w-full"
            >
              {isApplying ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Coins className="h-4 w-4 mr-2" />
              )}
              Apply {pointsToApply.toLocaleString()} Points
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
