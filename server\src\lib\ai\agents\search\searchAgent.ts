// server/src/lib/ai/agents/search/searchAgent.ts
// Define the search agent for handling user search queries using AI

import { BaseAgent } from '../base/baseAgent';
import { searchTool } from '../../tools/search/searchTool';
import { searchPrompt } from './searchAgent.prompts';
import { OpenCTXRequest } from '@/lib/openctx/types';

// Define a class for the search agent
export class SearchAgent extends BaseAgent {
    static async performSearch(userQuery: string, openCTXFilters?: any): Promise<OpenCTXRequest> {
        try {
            const { response } = await BaseAgent.generateTextFromPrompt(
              searchPrompt,
              `User query: ${userQuery} Existing filters: ${JSON.stringify(openCTXFilters)}`,
              { search: searchTool }
            );

            // Check if the AI responded
            if (!response?.toolCalls) {
                throw new Error('No tool calls were made by AI')
            }

            // Extract the tool call details
            const toolCall = response.toolCalls[0];

            // Ensure the tool call is to "search"
            if (toolCall.toolName !== "search") {
                throw new Error(`Unexpected tool called: ${toolCall.toolName}`);
            }

            // Execute the search tool and get the data
            const data = await searchTool.execute(toolCall.args)

            return data;
        } catch (error: any) {
            console.error('Error in search agent:', error);
            throw new Error(`Error in search agent: ${error.message}`);
        }
    }
}