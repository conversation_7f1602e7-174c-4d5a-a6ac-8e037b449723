import React from 'react';
import { NavSection } from '@/features/navigation/types/navigation.types';
import { SidebarItem } from './SidebarItem';
import { cn } from '@/lib/utils';
import { useAppSelector } from '@/app/redux';

interface SidebarSectionProps {
  section: NavSection;
  collapsed: boolean;
}

export const SidebarSection: React.FC<SidebarSectionProps> = ({
  section,
  collapsed,
}) => {
  const isSidebarCollapsed = useAppSelector((state) => state.global.isSidebarCollapsed);

  return (
    <div className="relative">
      {section.title && (
        <div className="px-4 py-2 text-gray-500 text-xs uppercase font-bold">
          {section.title}
        </div>
      )}
      <ul className="space-y-1">
        {section.items.map((item) => (
          <SidebarItem key={item.label} item={item} collapsed={isSidebarCollapsed} />
        ))}
      </ul>
    </div>
  );
};
























// import { NavSection } from '../../types/navigation.types'
// import { SidebarItem } from './SidebarItem'
// // Added import for Tooltip components
// import {
//   Tooltip,
//   TooltipContent,
//   TooltipProvider,
//   TooltipTrigger,
// } from "@/components/ui/tooltip"

// interface SidebarSectionProps {
//   section: NavSection
//   collapsed: boolean
// }

// export const SidebarSection = ({ section, collapsed }: SidebarSectionProps) => {
//   return (
//     <div className="py-2">
//       {section.title && !collapsed && (
//         <h3 className="px-3 mb-2 text-xs font-semibold text-muted-foreground">
//           {section.title}
//         </h3>
//       )}
//       <div className="space-y-1">
//         {/* Added TooltipProvider and conditional rendering for collapsed state */}
//         <TooltipProvider delayDuration={0}>
//           {section.items.map((item) => (
//             collapsed ? (
//               <Tooltip key={item.path}>
//                 <TooltipTrigger asChild>
//                   <div>
//                     <SidebarItem
//                       item={item}
//                       collapsed={collapsed}
//                     />
//                   </div>
//                 </TooltipTrigger>
//                 <TooltipContent side="right" className="flex items-center">
//                   {item.label}
//                 </TooltipContent>
//               </Tooltip>
//             ) : (
//               <SidebarItem
//                 key={item.path}
//                 item={item}
//                 collapsed={collapsed}
//               />
//             )
//           ))}
//         </TooltipProvider>
//       </div>
     
//     </div>
//   )
// }