import { api } from "@/apiAxios/axios";
import {
  LoginCredentials,
  RegisterCredentials,
  AuthResponse,
  ResetPasswordCredentials,
} from "@/features/auth/types/credential.types";
import axios from "axios";

export const authApi = {
  register: async (credentials: RegisterCredentials): Promise<AuthResponse> => {
    try {
      console.log("Sending registration request:", credentials);
      const { data } = await api.post("/api/v1/auth/register", credentials);
      console.log("Registration successful:", data);
      return data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorMessage =
          error.response?.data?.message || "Registration failed";
        console.error("Registration error:", errorMessage);
        throw new Error(errorMessage);
      }
      throw error;
    }
  },

  // Login endpoint with enhanced error handling
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    try {
      console.log("Login attempt:", credentials); // Add debugging
      // const { data } = await api.post("/api/v1/auth/login", credentials);
      const { data } = await api.post("/api/v1/auth/login", credentials, {
        timeout: 10000, // 10 second timeout
      });
      console.log("Login response:", data); // Add debugging
      return data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.code === "ECONNABORTED") {
          throw new Error("Connection timeout. Please try again.");
        }
        if (error.response?.status === 401) {
          throw new Error("Invalid email or password");
        }
        if (error.response?.status === 404) {
          throw new Error("Service unavailable. Please try again later.");
        }
        throw new Error(error.response?.data?.message || "Login failed");
      }
      throw error;
    }
  },

  refreshToken: async (refreshToken: string): Promise<AuthResponse> => {
    const { data } = await api.post("/api/v1/auth/refresh-token", {
      refreshToken,
    });
    return data;
  },

  forgotPassword: async (
    email: string
  ): Promise<{ success: boolean; message: string }> => {
    try {
      console.log("Initiating password reset for:", email);
      const { data } = await api.post("/api/v1/auth/forgot-password", {
        email,
      });
      console.log("Password reset initiated:", data);
      return data;
    } catch (error) {
      console.error("Password reset request failed:", error);
      if (axios.isAxiosError(error)) {
        throw new Error(
          error.response?.data?.message || "Failed to send reset email"
        );
      }
      throw error;
    }
  },

  resetPassword: async ({
    token,
    newPassword,
  }: ResetPasswordCredentials): Promise<{
    success: boolean;
    message: string;
  }> => {
    try {
      console.log("Attempting password reset with token");
      const { data } = await api.post("/api/v1/auth/reset-password", {
        token,
        newPassword,
      });
      console.log("Password reset successful");
      return data;
    } catch (error) {
      console.error("Password reset failed:", error);
      if (axios.isAxiosError(error)) {
        throw new Error(
          error.response?.data?.message || "Failed to reset password"
        );
      }
      throw error;
    }
  },
  verifyResetToken: async (token: string): Promise<boolean> => {
    try {
      console.log("Verifying reset token:", token);
      const { data } = await api.post("/api/v1/auth/verify-reset-token", {
        token,
      });
      console.log("Token verification response:", data);
      return data.isValid;
    } catch (error) {
      console.error("Token verification failed:", error);
      if (axios.isAxiosError(error)) {
        throw new Error(error.response?.data?.message || "Invalid reset token");
      }
      throw error;
    }
  },
};
