/**
 * This module is responsible for seeding the database with initial data.
 * It uses Prisma ORM to interact with the database and reads seed data from JSON files.
 * The module provides functionality to clear existing data and populate the database with new data.
 */


// !--------https://app.ticketmaster.com/discovery/v2/events.json?apikey=********************************&size=10&locale=*
import { PrismaClient } from "@prisma/client";
import { TmEventTransformer } from "../src/features/tm_events/utils/tm.transformer";
import fs from "fs/promises";
import path from "path";

// Initialize Prisma client and set the data directory
const prisma = new PrismaClient();
const dataDirectory = path.join(__dirname, "seedData");

// Define types for better type safety
type ModelName = keyof Omit<PrismaClient, '$connect' | '$disconnect' | '$on' | '$transaction' | '$use'>;

interface SeedFile {
  name: string;
  model: ModelName;
}

// ---------------Seed Files Configuration-------------------
// Define the seed files and their corresponding Prisma models
const seedFiles: SeedFile[] = [
  { name: "ticketmaster.json", model: "tmEvent" },
  // Add more models here as needed
];

// ---------------Data Clearing Function-------------------
/**
 * Clears all existing data from the specified models in the database.
 * This ensures a clean slate before seeding new data.
 */
async function clearData(): Promise<void> {
  for (const { model } of seedFiles) {
    await (prisma[model] as any).deleteMany();
    console.log(`Cleared data from ${String(model)}`);
  }
}

// ---------------Data Seeding Function-------------------
/**
 * Reads data from JSON files and seeds it into the database.
 * Each JSON file corresponds to a specific model in the database.
 */
async function seedData(): Promise<void> {
  for (const { name, model } of seedFiles) {
    const filePath = path.join(dataDirectory, name);
    const rawData = JSON.parse(await fs.readFile(filePath, "utf-8"));
    


    let data = rawData;
    // Transform Ticketmaster data if needed
    if (model === "tmEvent") {
      data = TmEventTransformer.transformBatch(rawData._embedded.events);
    }
    
    await (prisma[model] as any).createMany({
      data: data,
      skipDuplicates: true,
    });
    
    console.log(`Seeded ${String(model)} with data from ${name}`);
  }
}

// ---------------Main Execution Function-------------------
/**
 * Main function to orchestrate the seeding process.
 * It clears existing data, seeds new data, and handles any errors that occur.
 */
async function main(): Promise<void> {
  try {
    await clearData();
    await seedData();
  } catch (error) {
    console.error("Error during seeding:", error);
    process.exit(1);
  } finally {
    // Ensure the Prisma client is disconnected after seeding
    await prisma.$disconnect();
  }
}

// Execute the main function
main();
