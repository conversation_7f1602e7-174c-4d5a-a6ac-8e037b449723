'use client';
import React from 'react';
import { EventCard } from './EventCard';
import { TmEvent } from '../types/tm.types';


interface EventListProps {
  events: TmEvent[];
    onEventClick: (event: TmEvent) => void;
}

// EventList component to display a grid of event cards
export const EventList: React.FC<EventListProps> = ({ events, onEventClick }) => {
  return (
    // Updated grid layout with increased gap and padding
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8 p-4">
      {events.map((event) => (
        // Added wrapper div with group class for potential hover effects
        <div key={event.id} className="group/card relative">
          <EventCard event={event} onEventClick={onEventClick} />
        </div>
      ))}
    </div>
  );
};
