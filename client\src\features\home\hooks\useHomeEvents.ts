"use client"
// Hooks for fetching and filtering events based on category and popularity
import { useAppSelector } from '@/app/redux';
import { EventCategory } from '@/state/priorityEventsSlice';
// import { useDelayedData } from '@/hooks/useDelayedData'; //For testing only

interface UseHomeEventsOptions {
  popularOnly?: boolean;
}

export const useHomeEvents = (category: EventCategory, options: UseHomeEventsOptions = {}) => {
  const { popularOnly = false } = options;

  const events = useAppSelector((state) => state.priorityEvents[category]);

  // console.log(`useHomeEvents - Category: ${category}, popularOnly: ${popularOnly}`);
  // console.log('useHomeEvents - All Events:', events);

  const filteredEvents = events?.filter(event => event.isPopular === popularOnly) || [];
  // console.log('useHomeEvents - Filtered Events:', filteredEvents);

  return {
    events: filteredEvents, // Only return the filtered events. No isLoading.
  };
};

