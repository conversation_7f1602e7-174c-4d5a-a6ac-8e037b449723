// Centralized Redis Client Configuration
// This module initializes and exports a single Redis client instance
// for use across the application, ensuring consistent connection management.

import { createClient, RedisClientType } from 'redis';
import { NODE_ENV } from '@/constants'; // Assuming constants file exists

//---------------Redis Configuration Section-------------------
// Redis connection parameters with fallback to local instance
// const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379'; // Original fallback, keeping specific params for clarity
// Cloud-specific configuration parameters
const redisPassword = process.env.REDIS_PASSWORD;
const redisHost = process.env.REDIS_HOST;
const redisPort = process.env.REDIS_PORT;

// Log configuration being used (optional but helpful for debugging)
if (NODE_ENV === 'development') {
  console.log("⚙️ Redis Configuration:", {
    host: redisHost || 'localhost (default)',
    port: redisPort || '6379 (default)',
    passwordProvided: !!redisPassword
  });
}

// Initialize Redis client with cloud configuration if available
// Use explicit types for better IntelliSense and safety
const redisClient: RedisClientType = createClient({
  password: redisPassword,
  socket: {
    host: redisHost, // Defaults handled by redis library if undefined
    port: redisPort ? Number(redisPort) : undefined // Ensure port is a number if provided
  }
});

//---------------Connection Management Section-------------------
// Flag to prevent multiple connection attempts
let isConnecting = false;
let connectionAttempted = false;

// Monitor Redis connection status for debugging and error handling
redisClient.on('connect', () => {
  isConnecting = false;
  if (NODE_ENV === 'development') {
    console.log('✅ Redis client connected');
  }
});

redisClient.on('error', (err) => {
  isConnecting = false; // Reset flag on error too
  console.error('❌ Redis client error:', err);
  // Optional: Implement retry logic or specific error handling here
});

redisClient.on('end', () => {
  if (NODE_ENV === 'development') {
    console.log('🔌 Redis client connection closed');
  }
});

// Ensure single connection instance and handle potential errors
const connectRedis = async (): Promise<void> => {
  if (!redisClient.isOpen && !isConnecting && !connectionAttempted) {
    isConnecting = true;
    connectionAttempted = true; // Mark that we've tried to connect at least once
    try {
      if (NODE_ENV === 'development') {
        console.log('⏳ Attempting to connect to Redis...');
      }
      await redisClient.connect();
    } catch (err) {
      console.error('❌ Failed to connect Redis client during initial setup:', err);
      // Decide how to handle critical connection failure - exit, retry, log?
      // For now, we log the error, the client remains disconnected.
      isConnecting = false; // Ensure flag is reset even on failure
    }
  }
};

// Initiate the connection when the module loads
connectRedis();

//---------------Export Centralized Client-------------------
// Export the configured client instance for use in other modules
export { redisClient };

//--------------- Graceful Shutdown (Optional but Recommended) --------
// Function to be called on application shutdown
export const disconnectRedis = async (): Promise<void> => {
  if (redisClient.isOpen) {
    try {
      await redisClient.quit();
      if (NODE_ENV === 'development') {
        console.log('🛑 Redis client disconnected gracefully.');
      }
    } catch (error) {
      console.error('❌ Error disconnecting Redis client:', error);
    }
  }
};

// Example: Hook into process exit signals (implement in your main server file e.g., index.ts)
// process.on('SIGINT', async () => {
//   await disconnectRedis();
//   process.exit(0);
// });
// process.on('SIGTERM', async () => {
//   await disconnectRedis();
//   process.exit(0);
// });
