import { Request, Response } from "express";
import { AuthService } from "./credential.service";
import { RegisterDTO, LoginDTO } from "./credential.types";
import { asyncHandler } from "../../../utils/asyncHandler";
import ApiError from "../../../utils/ApiError";
import { generateToken } from "../../../config/jwt.config";

const authService = new AuthService();

export const register = asyncHandler(
  async (req: Request<{}, {}, RegisterDTO>, res: Response) => {
    try {
      console.log("Registration attempt for email:", req.body.email);

      if (!req.body.email || !req.body.password) {
        throw ApiError.badRequest("Email and password are required");
      }

      // Create user and generate token
      const user = await authService.createUser(req.body);
      console.log("User created successfully:", user.id);

      const token = generateToken(user);
      console.log("Token generated for user:", user.id);

      // Return user data and token
      res.status(201).json({
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          fullName: user.fullName,
        },
        token,
      });
    } catch (error) {
      console.error("Registration error:", error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw ApiError.internal("Failed to register user");
    }
  }
);

export const login = asyncHandler(
  async (req: Request<{}, {}, LoginDTO>, res: Response) => {
    try {
      console.log("Login attempt for email:", req.body.email);

      if (!req.body.email || !req.body.password) {
        throw ApiError.badRequest("Email and password are required");
      }

      // Validate user and generate token
      const user = await authService.validateUser(
        req.body.email,
        req.body.password
      );
      console.log("User validated successfully:", user.id);

      const token = generateToken(user);
      console.log("Token generated for user:", user.id);

      // Return user data and token
      res.json({
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          fullName: user.fullName,
        },
        token,
      });
    } catch (error) {
      console.error("Login error:", error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw ApiError.unauthorized("Invalid credentials");
    }
  }
);

export const forgotPassword = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      console.log("Forgot password request received:", req.body);
      const { email } = req.body;

      await authService.initiatePasswordReset(email);

      res.status(200).json({
        success: true,
        message: "Password reset email sent successfully",
      });
    } catch (error) {
      console.error("Password reset error:", error);
      throw error instanceof ApiError
        ? error
        : ApiError.internal("Password reset failed");
    }
  }
);

/**
 * @desc    Verify reset password token
 * @route   POST /api/v1/auth/verify-reset-token
 * @access  Public
 */
export const verifyResetToken = asyncHandler(
  async (req: Request, res: Response) => {
    console.log("Token verification request received:", req.body);

    const { token } = req.body;
    if (!token) {
      throw ApiError.badRequest("Reset token is required");
    }

    const isValid = await authService.verifyResetToken(token);
    console.log("Token validation result:", isValid);

    res.status(200).json({
      success: true,
      isValid,
    });
  }
);

/**
 * @desc    Reset password with token
 * @route   POST /api/v1/auth/reset-password
 * @access  Public
 */
export const resetPassword = asyncHandler(
  async (req: Request, res: Response) => {
    const { token, newPassword } = req.body;

    await authService.resetPassword(token, newPassword);

    res.status(200).json({
      success: true,
      message: "Password reset successful",
    });
  }
);
