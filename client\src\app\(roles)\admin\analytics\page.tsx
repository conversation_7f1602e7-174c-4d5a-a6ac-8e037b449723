import { ProtectedRoute } from "@/components/roleAuthProtection/ProtectedRoute";
import { AdminAnalytics } from "@/features/modules/admin-portal/analytics/AdminAnalytics";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Analytics Dashboard | Admin",
  description: "Comprehensive analytics and insights for platform performance"
};

export default function AnalyticsPage() {
  return (
    <ProtectedRoute allowedRoles={["ADMIN"]}>
      <AdminAnalytics />
    </ProtectedRoute>
  );
}
