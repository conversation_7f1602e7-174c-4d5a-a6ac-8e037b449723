import React from 'react';
import { format } from 'date-fns';
import { CalendarIcon, MapPinIcon, BuildingIcon } from 'lucide-react';
import { ManagerInventoryItem } from '../types/inventory.types';
import { ApprovalStatusBadge } from './shared/ApprovalStatusBadge';
import { EventStatusBadge } from './shared/EventStatusBadge';
import { ManagerInventoryActions } from './ManagerInventoryActions';

interface InventoryEventCardProps {
  item: ManagerInventoryItem;
  toggleActive: (params: { eventId: string }) => void;
  isToggling: boolean;
  deleteEvent: (params: { eventId: string }) => void;
  isDeleting: boolean;
  onViewDetails: (item: ManagerInventoryItem) => void;
  onEditInventory: (item: ManagerInventoryItem) => void;
}

export const InventoryEventCard: React.FC<InventoryEventCardProps> = ({
  item,
  toggleActive,
  isToggling,
  deleteEvent,
  isDeleting,
  onViewDetails,
  onEditInventory,
}) => {
  return (
    <div className={`
      border rounded-lg shadow-md
      bg-white dark:bg-gray-800 transition-all
      ${!item.isActive ? 'opacity-70' : ''}
      p-3
    `}>
      {/* Card content */}
      <div className="space-y-2">
        {/* Event name and actions */}
        <div className="flex justify-between items-start">
          <h3 className="font-medium text-base truncate pr-2" title={item.name}>
            {item.name}
          </h3>
          <ManagerInventoryActions
            item={item}
            toggleActive={toggleActive}
            isToggling={isToggling}
            deleteEvent={deleteEvent}
            isDeleting={isDeleting}
            onViewDetails={onViewDetails}
            onEditInventory={onEditInventory}
          />
        </div>
        
        {/* Event details in a row */}
        <div className="flex flex-wrap items-center gap-x-3 gap-y-1 text-xs text-gray-600 dark:text-gray-400">
          <div className="flex items-center">
            <CalendarIcon className="h-3 w-3 mr-1" />
            <span>{format(item.date, 'MMM d, yyyy')}</span>
          </div>
          
          <div className="flex items-center">
            <BuildingIcon className="h-3 w-3 mr-1" />
            <span className="truncate max-w-[80px]" title={item.venue}>
              {item.venue}
            </span>
          </div>
          
          <div className="flex items-center">
            <MapPinIcon className="h-3 w-3 mr-1" />
            <span>{item.city}</span>
          </div>
        </div>
        
        {/* Status indicators */}
        <div className="pt-2 mt-1 border-t border-gray-100 dark:border-gray-700 flex flex-wrap gap-x-2 gap-y-1">
          <div className="flex items-center">
            <span className="text-xs text-gray-500 mr-1">Approval:</span>
            <ApprovalStatusBadge status={item.approvalStatus} />
          </div>
          
          <div className="flex items-center">
            <span className="text-xs text-gray-500 mr-1">Status:</span>
            <EventStatusBadge status={item.status} isActive={item.isActive} />
          </div>
          
          <div className="flex items-center ml-auto">
            <span className="text-xs text-gray-500 mr-1">Active:</span>
            <span className={`text-xs font-semibold px-1.5 py-0.5 rounded-full ${
              item.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {item.isActive ? 'Yes' : 'No'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
