import { DateTime } from 'luxon';

export const parseDate = (date: string | Date): Date => {
  if (date instanceof Date) return date;
  
  // Try parsing ISO format first
  const parsedDate = DateTime.fromISO(date);
  
  // If valid, return JS Date object
  if (parsedDate.isValid) {
    return parsedDate.toJSDate();
  }
  
  // Fallback for other formats (YYYY-MM-DD)
  const fallbackDate = DateTime.fromFormat(date, 'yyyy-MM-dd');
  
  if (fallbackDate.isValid) {
    return fallbackDate.toJSDate();
  }
  
  throw new Error(`Invalid date format: ${date}`);
};
