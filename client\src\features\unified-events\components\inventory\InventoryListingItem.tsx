// Component to display a single inventory item listing with quantity selection
import React, { useState, useMemo, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { MinusIcon, PlusIcon } from "lucide-react";
import { InventoryItem } from "@/features/event-listing/types/eventListing";
import { validateSelection } from "../../helpers/inventoryValidation";
import { formatSeats } from "../../helpers/seatFormatting";

interface InventoryListingItemProps {
  item: InventoryItem;
  // Add a callback to report quantity changes to the parent
  onQuantityChange: (itemId: string, quantity: number) => void;
  // Pass down the initial quantity if needed (e.g., from parent state)
  initialQuantity?: number;
}

export const InventoryListingItem: React.FC<InventoryListingItemProps> = ({
  item,
  onQuantityChange,
  initialQuantity = 1,
}) => {
  // Initialize state with the value passed from the parent, default to 0
  const [selectedQuantity, setSelectedQuantity] = useState(initialQuantity);

  // Use useEffect to report changes *after* state update is confirmed
  useEffect(() => {
    onQuantityChange(item.id, selectedQuantity);
    // console.log(`🛒 Item ${item.id} quantity changed to: ${selectedQuantity}`); 
  }, [selectedQuantity, item.id, onQuantityChange]);

  const hasDisclosures = item.disclosures && item.disclosures.length > 0;
  const hasAttributes = item.attributes && item.attributes.length > 0;
  const hasFeatures = hasDisclosures || hasAttributes;

  const validation = useMemo(() => {
    return validateSelection(
      selectedQuantity,
      item.quantity,
      item.sellingPreference
    );
  }, [selectedQuantity, item.quantity, item.sellingPreference]);

  const getSellingPreferenceText = (
    pref: InventoryItem["sellingPreference"]
  ): string => {
    switch (pref) {
      case "Any":
        return "Any Quantity";
      case "Pairs":
        return "Pairs Only";
      case "Full":
        return "Sell Full Lot Only";
      case "Avoid Leaving Single":
        return "No Single Seats Left";
      default:
        return pref; // Fallback
    }
  };

  const handleDecrement = () => {
    // Prevent quantity from going below 0
    setSelectedQuantity((prev) => Math.max(0, prev - 1));
  };

  const handleIncrement = () => {
    // Prevent incrementing beyond available
    if (selectedQuantity < item.quantity) {
      setSelectedQuantity((prev) => prev + 1);
    }
  };

  // Determine button states based on validation
  const canDecrement = selectedQuantity > 0;
  const canIncrement = selectedQuantity < item.quantity;
  // Keep 'Add' button logic, though its action isn't used in this step
  const canAdd = selectedQuantity > 0 && validation.isValid;

  // Calculate potential subtotal for this item
  const subtotal = selectedQuantity * item.listPrice;
  const displaySubtotal = selectedQuantity > 0;

  return (
    <div
      className={`border rounded-lg overflow-hidden shadow-sm transition-all duration-200 
                  ${
                    selectedQuantity > 0
                      ? "border-primary/40 bg-primary/5"
                      : "border-border bg-background"
                  } 
                  ${
                    selectedQuantity > 0 && !validation.isValid
                      ? "border-destructive/40"
                      : ""
                  }`}
    >
      {/* Modern Card Layout */}
      <div className="p-4 space-y-3">
        {/* Top Section with Price and Controls */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
          <div className="space-y-1">
            <div className="font-semibold text-base sm:text-lg flex items-baseline gap-1.5">
              ${item.listPrice.toFixed(2)}
              <span className="text-xs text-muted-foreground">per ticket</span>
            </div>

            {/* Display subtotal when something is selected */}
            {displaySubtotal && (
              <div
                className={`text-sm font-medium transition-all duration-300 
                              ${
                                validation.isValid
                                  ? "text-primary"
                                  : "text-muted-foreground line-through"
                              }`}
              >
                Subtotal: ${subtotal.toFixed(2)}
              </div>
            )}
          </div>

          {/* Modern Quantity Controls */}
          <div className="flex items-center gap-3">
            <div className="flex items-center border rounded-md overflow-hidden shadow-sm bg-background">
              <Button
                variant="ghost"
                size="icon"
                className={`h-9 w-9 rounded-none ${
                  !canDecrement ? "opacity-50" : "hover:bg-muted"
                }`}
                onClick={handleDecrement}
                disabled={!canDecrement}
              >
                <MinusIcon className="h-4 w-4" />
              </Button>

              <div className="w-12 text-center font-medium" aria-live="polite">
                {selectedQuantity}
              </div>

              <Button
                variant="ghost"
                size="icon"
                className={`h-9 w-9 rounded-none ${
                  !canIncrement ? "opacity-50" : "hover:bg-muted"
                }`}
                onClick={handleIncrement}
                disabled={!canIncrement}
              >
                <PlusIcon className="h-4 w-4" />
              </Button>
            </div>

            <Button
              size="sm"
              className={`h-9 px-4 transition-all ${
                canAdd ? "opacity-100" : "opacity-70"
              }`}
              disabled={!canAdd}
            >
              Add
            </Button>
          </div>
        </div>

        {/* Validation Message */}
        {selectedQuantity > 0 && validation.message && (
          <div
            className={`text-xs px-1 py-1 rounded ${
              validation.isValid
                ? "text-primary bg-primary/10 border border-primary/20"
                : "text-destructive bg-destructive/10 border border-destructive/20"
            } animate-fadeIn`}
          >
            {validation.message}
          </div>
        )}
      </div>

      {/* Details Section */}
      <div className="bg-muted/50 p-4 space-y-3 text-sm border-t">
        {/* Seating Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-x-4 gap-y-2">
          <div>
            <span className="text-muted-foreground font-medium">
              Qty Avail:
            </span>{" "}
            {item.quantity}
          </div>{" "}
          {/* Corrected label */}
          <div>
            <span className="text-muted-foreground font-medium">Section:</span>{" "}
            {item.section || "N/A"}
          </div>
          <div>
            <span className="text-muted-foreground font-medium">Row:</span>{" "}
            {item.row || "N/A"}
          </div>
          <div className="md:col-span-3">
            <span className="text-muted-foreground font-medium">Seats:</span>{" "}
            {formatSeats(item)}
          </div>
        </div>

        {/* --- Format & Preference (Responsive) --- */}
        {/* Defaults to 1 column, becomes 2 columns at sm breakpoint */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-3 gap-y-1.5 pt-1">
          <div>
            <span className="font-medium text-muted-foreground">Format:</span>{" "}
            {item.ticketFormat || "N/A"}
          </div>
          <div>
            <span className="font-medium text-muted-foreground">Rule:</span>{" "}
            {getSellingPreferenceText(item.sellingPreference)}
          </div>
        </div>

        {/* Features Section */}
        {hasFeatures && (
          <>
            <Separator className="my-2" />
            <div className="space-y-2">
              {hasDisclosures && (
                <div>
                  <div className="text-muted-foreground font-medium text-xs mb-1.5">
                    Disclosures:
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {item.disclosures.map((disc, i) => (
                      <Badge
                        key={i}
                        variant="outline"
                        className="text-xs font-normal px-1.5 py-0.5 bg-muted/70"
                      >
                        {disc}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {hasAttributes && (
                <div>
                  <div className="text-muted-foreground font-medium text-xs mb-1.5">
                    Attributes:
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {item.attributes.map((attr, i) => (
                      <Badge
                        key={i}
                        variant="secondary"
                        className="text-xs font-normal px-1.5 py-0.5"
                      >
                        {attr}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};
