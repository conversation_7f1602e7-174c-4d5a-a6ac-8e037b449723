import NextAuth from "next-auth";
import { authOptions } from "@/lib/auth";

// Add initial debug log outside of any handler
console.log("🔍 NextAuth route file is being loaded by Vercel");

// Initialize the handler with explicit debug/logging
const handler = NextAuth({
  ...authOptions,
  secret: process.env.NEXTAUTH_SECRET,
  debug: true,
  logger: {
    error(code, metadata) {
      console.error(`NextAuth error 🔴🔴🔴: ${code}`, metadata);
    },
    warn(code) {
      console.warn(`NextAuth warning🟡🟡🟡: ${code}`);
    },
    debug(code, metadata) {
      console.log(`NextAuth debug🐛🐛🐛🔵🔵: ${code}`, metadata);
    },
  },
});


// export async function GET(request:any) {
//   console.log("👉 GET request received in [...nextauth]/route.ts");
//   return handler(request);
// }

// export async function POST(request:any) {
//   console.log("👉 POST request received in [...nextauth]/route.ts");
//   return handler(request);
// }

export { handler as GET, handler as POST };
