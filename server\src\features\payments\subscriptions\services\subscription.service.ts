/**
 * Subscription Service
 * 
 * Manages user subscription operations and retrieval
 */

import { MembershipTier, User } from '@prisma/client';
import { prisma } from '@/lib/prisma';
import ApiError from '@/utils/ApiError';
import { SubscriptionPlanType, SubscriptionStatusResponse } from '../types/subscription.types';
import { StripePaymentsService } from '../../processors/stripe/services/stripe-payments.service';

export class SubscriptionService {
  /**
   * Get subscription information for a user
   */
  static async getUserSubscription(userId: string): Promise<SubscriptionStatusResponse> {
    try {
      // Get user to check if they have a Stripe customer ID and membership tier
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          stripeCustomerId: true,
          membershipTier: true,
        }
      });

      if (!user) {
        throw new ApiError(404, 'User not found');
      }

      // If no Stripe customer ID, user has no subscription
      if (!user.stripeCustomerId) {
        return {
          active: false,
          planType: null,
        };
      }

      // Map membership tier to subscription plan type
      let planType: SubscriptionPlanType | null = null;
      if (user.membershipTier === MembershipTier.VIP) {
        planType = SubscriptionPlanType.VIP;
      } else if (user.membershipTier === MembershipTier.SUBSCRIBER) {
        planType = SubscriptionPlanType.MONTHLY;
      }

      // If user has a membership tier but no associated plan type, default to subscription inactive
      if (!planType) {
        return {
          active: false,
          planType: null,
        };
      }

      // For more detailed information, we would retrieve the subscription from Stripe
      // Here's a placeholder for that logic which you can expand
      const stripeSubscriptionDetails = await this.getStripeSubscriptionDetails(user.stripeCustomerId);

      return {
        active: true,
        planType,
        currentPeriodEnd: stripeSubscriptionDetails?.currentPeriodEnd,
        cancelAtPeriodEnd: stripeSubscriptionDetails?.cancelAtPeriodEnd,
        trialEnd: stripeSubscriptionDetails?.trialEnd,
        stripeSubscriptionId: stripeSubscriptionDetails?.subscriptionId,
      };
    } catch (error) {
      console.error(`❌ Error getting subscription for user ${userId}:`, error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError(500, `Failed to retrieve subscription: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Cancel a user's subscription
   * @param userId User ID
   * @param immediately Whether to cancel immediately or at period end
   */
  static async cancelSubscription(userId: string, immediately = false): Promise<boolean> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          stripeCustomerId: true,
          membershipTier: true,
        }
      });

      if (!user) {
        throw new ApiError(404, 'User not found');
      }

      if (!user.stripeCustomerId || 
          (user.membershipTier !== MembershipTier.SUBSCRIBER && 
           user.membershipTier !== MembershipTier.VIP)) {
        throw new ApiError(400, 'User does not have an active subscription');
      }

      // Cancel subscription in Stripe
      // This would call StripePaymentsService to cancel the subscription
      // For now, we'll just update the user's membership tier
      await prisma.user.update({
        where: { id: userId },
        data: {
          membershipTier: MembershipTier.STANDARD
        }
      });

      return true;
    } catch (error) {
      console.error(`❌ Error canceling subscription for user ${userId}:`, error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError(500, `Failed to cancel subscription: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get Stripe subscription details
   * This is a helper method that would call Stripe API to get subscription details
   * @param stripeCustomerId Stripe Customer ID
   */
  private static async getStripeSubscriptionDetails(stripeCustomerId: string): Promise<{
    subscriptionId: string;
    currentPeriodEnd: string;
    cancelAtPeriodEnd: boolean;
    trialEnd?: string;
  } | null> {
    // This would actually call the Stripe API to get subscription details
    // For now, returning placeholder data
    return {
      subscriptionId: 'sub_123456',
      currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
      cancelAtPeriodEnd: false,
    };
  }

  /**
   * Create a subscription checkout session
   * This is a wrapper around the existing Stripe service method
   */
  static async createSubscriptionCheckout(userId: string, planType: SubscriptionPlanType, successUrl: string, cancelUrl: string) {
    try {
      // Get the corresponding Stripe price ID based on plan type
      const priceId = this.getPriceIdForPlan(planType);

      // Delegate to the Stripe service
      return await StripePaymentsService.createSubscriptionCheckoutSession(
        userId,
        priceId,
        successUrl,
        cancelUrl
      );
    } catch (error) {
      console.error(`❌ Error creating subscription checkout for user ${userId}:`, error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError(500, `Failed to create subscription checkout: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get the appropriate Stripe price ID for a plan type
   */
  private static getPriceIdForPlan(planType: SubscriptionPlanType): string {
    switch (planType) {
      case SubscriptionPlanType.MONTHLY:
        return process.env.STRIPE_MONTHLY_PRICE_ID || '';
      case SubscriptionPlanType.VIP:
        return process.env.STRIPE_VIP_PRICE_ID || '';
      default:
        throw new ApiError(400, `Invalid subscription plan type: ${planType}`);
    }
  }

  /**
   * Process a webhook event for subscription updates
   * This would be called by the Stripe webhook handler
   */
  static async processSubscriptionWebhook(event: any) {
    // Implementation would depend on your Stripe webhook structure
    // This would update the user's membership tier based on subscription events
    console.log(`Processing subscription webhook event: ${event.type}`);
    
    // Example logic:
    // if (event.type === 'customer.subscription.updated' || event.type === 'customer.subscription.created') {
    //   const subscription = event.data.object;
    //   const customerId = subscription.customer;
    //   
    //   // Find user by Stripe customer ID
    //   const user = await prisma.user.findFirst({
    //     where: { stripeCustomerId: customerId }
    //   });
    //
    //   if (user) {
    //     // Update user membership tier based on price ID
    //     const priceId = subscription.items.data[0].price.id;
    //     let membershipTier = MembershipTier.STANDARD;
    //     
    //     if (priceId === process.env.STRIPE_VIP_PRICE_ID) {
    //       membershipTier = MembershipTier.VIP;
    //     } else if (priceId === process.env.STRIPE_MONTHLY_PRICE_ID) {
    //       membershipTier = MembershipTier.SUBSCRIBER;
    //     }
    //     
    //     await prisma.user.update({
    //       where: { id: user.id },
    //       data: { membershipTier }
    //     });
    //   }
    // }
  }
}