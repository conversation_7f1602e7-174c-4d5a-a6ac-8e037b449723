// 🔄 Fixed OnboardingTrigger with correct Button sizes
"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON>rkles, CheckCircle2, ArrowRight, HelpCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useOnboardingCheck } from '../hooks/useOnboardingCheck';
import { OnboardingModal } from './OnboardingModal';

interface OnboardingTriggerProps {
  variant?: 'button' | 'icon' | 'minimal';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  showProgress?: boolean;
  children?: React.ReactNode;
}

export const OnboardingTrigger: React.FC<OnboardingTriggerProps> = ({
  variant = 'button',
  size = 'default',
  className,
  showProgress = true,
  children
}) => {
  const [modalOpen, setModalOpen] = useState(false);
  const { onboardingStatus } = useOnboardingCheck();
  
  const isComplete = onboardingStatus.currentStep === 'complete';
  const hasProgress = onboardingStatus.completedSteps > 0;

  // Icon-only variant
  if (variant === 'icon') {
    return (
      <>
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="relative"
        >
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setModalOpen(true)}
            className={cn(
              "relative",
              "hover:bg-primary/10 transition-colors",
              isComplete ? "text-emerald-600" : "text-primary",
              className
            )}
          >
            {isComplete ? (
              <CheckCircle2 className="h-5 w-5" />
            ) : (
              <Sparkles className="h-5 w-5" />
            )}
          </Button>
          
          {/* Progress indicator dot */}
          {!isComplete && hasProgress && showProgress && (
            <div className="absolute -top-1 -right-1">
              <Badge variant="secondary" className="h-5 w-5 p-0 text-xs font-bold rounded-full">
                {onboardingStatus.completedSteps}
              </Badge>
            </div>
          )}
        </motion.div>

        <OnboardingModal 
          open={modalOpen} 
          onOpenChange={setModalOpen}
        />
      </>
    );
  }

  // Minimal variant
  if (variant === 'minimal') {
    return (
      <>
        <Button
          variant="ghost"
          size={size}
          onClick={() => setModalOpen(true)}
          className={cn(
            "gap-2",
            isComplete ? "text-emerald-600" : "text-primary",
            className
          )}
        >
          {isComplete ? (
            <CheckCircle2 className="h-4 w-4" />
          ) : (
            <HelpCircle className="h-4 w-4" />
          )}
          {children || (isComplete ? 'Setup Complete' : 'Getting Started')}
        </Button>

        <OnboardingModal 
          open={modalOpen} 
          onOpenChange={setModalOpen}
        />
      </>
    );
  }

  // Full button variant (default)
  return (
    <>
      <motion.div
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <Button
          variant={isComplete ? "outline" : "default"}
          size={size}
          onClick={() => setModalOpen(true)}
          className={cn(
            "gap-2 relative",
            className
          )}
        >
          {isComplete ? (
            <>
              <CheckCircle2 className="h-4 w-4" />
              {children || 'View Setup'}
            </>
          ) : (
            <>
              <Sparkles className="h-4 w-4" />
              {children || 'Complete Setup'}
              <ArrowRight className="h-4 w-4" />
            </>
          )}
          
          {/* Progress badge */}
          {!isComplete && hasProgress && showProgress && (
            <Badge variant="secondary" className="ml-1 text-xs">
              {onboardingStatus.completedSteps}/{onboardingStatus.totalSteps}
            </Badge>
          )}
        </Button>
      </motion.div>

      <OnboardingModal 
        open={modalOpen} 
        onOpenChange={setModalOpen}
      />
    </>
  );
};