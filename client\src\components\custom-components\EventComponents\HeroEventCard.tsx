import Image from "next/image";
import { PriorityEventData } from "@/features/settings/components/PriorityEvents/types/priority-events.types";
import { Calendar, MapPin, Rocking<PERSON>hair } from "lucide-react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { EventPlaceholder } from "@/components/ui/icons/EventPlaceholder";

interface HeroEventCardProps {
  event: PriorityEventData;
  onEventClick?: (event: PriorityEventData) => void;
}

export const HeroEventCard: React.FC<HeroEventCardProps> = ({ event, onEventClick }) => {
  const eventDate = new Date(event.date);
  const formattedDateTime = eventDate.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });

  const handleClick = () => {
    if (onEventClick) {
      onEventClick(event);
    }
  };

  return (
    <motion.div
      className="relative w-full h-full rounded-2xl overflow-hidden shadow-xl"
      initial={{ opacity: 0, scale: 0.98 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.98 }}
      transition={{ duration: 0.4, ease: "easeInOut" }}
      onClick={handleClick}
    >
      {/* Circular Ticket Button */}
      {/* <div className="absolute top-4 right-4 z-10">
        <Button
          className="rounded-full p-0 w-12 h-12 
     backdrop-blur-sm
    hover:bg-purple-100 hover:scale-110 
    hover:shadow-lg hover:shadow-purple-500/20
    transform-gpu transition-all duration-300 ease-out
    active:scale-95"
          aria-label="Get Tickets"
        >
          <RockingChair className="hover:rotate-6 transition-transform duration-300" />
        </Button>
      </div> */}

      {/* Background Image */}
      {event.image ? (
        <div className="absolute inset-0">
          <Image
            src={event.image}
            alt={event.name}
            fill
            className="object-cover w-full h-full"
            priority
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-black/40 to-transparent sm:bg-gradient-to-t sm:from-black/80 sm:via-black/40 sm:to-transparent"></div>
        </div>
      ) : (
        <div className="absolute inset-0 bg-gradient-to-br from-purple-700 via-blue-700 to-violet-700 flex items-center justify-center">
          <div className="transform transition-transform duration-300 group-hover:scale-110">
            <EventPlaceholder />
          </div>
        </div>
      )}

      {/* Content Overlay */}
      <div className="absolute flex flex-col w-full p-6 sm:p-8 text-white bottom-0 left-0 sm:max-w-md sm:h-auto sm:w-fit sm:top-1/2 sm:left-0 sm:transform sm:-translate-y-1/2">
        <div className="absolute bottom-0 left-0 right-0 backdrop-blur-md bg-black/20 rounded-xl p-4 sm:p-6 space-y-4 sm:relative sm:top-auto sm:left-auto sm:right-auto sm:transform-none">
          <div className="flex flex-wrap gap-2">
            <Badge
              variant="secondary"
              className="bg-white/20 backdrop-blur-md text-white border-none w-fit px-3 py-1.5 rounded-full"
            >
              {event.category}
            </Badge>
            <Badge
              variant="secondary"
              className="bg-white/10 backdrop-blur-md text-white border-none w-fit px-3 py-1.5 rounded-full flex items-center gap-2"
            >
              <MapPin className="h-4 w-4" />
              <span className="line-clamp-1">
                {event.venue}, {event.city}
              </span>
            </Badge>
          </div>

          <Badge
            variant="secondary"
            className="bg-white/10 backdrop-blur-md text-white border-none w-fit px-3 py-1.5 rounded-full flex items-center gap-2"
          >
            <Calendar className="h-4 w-4" />
            <span>{formattedDateTime}</span>
          </Badge>

          <h2 className="text-2xl sm:text-4xl font-bold leading-tight line-clamp-1">
            {event.name}
          </h2>

          {/* Original Button (Commented Out) */}
          <Button
            className="bg-white text-purple-600 hover:bg-purple-100 hover:scale-105 transition-all duration-200 w-full sm:w-auto"
            onClick={(e) => {
              e.stopPropagation();
              handleClick();
            }}
          >
            Get Tickets
          </Button>
        </div>
      </div>
    </motion.div>
  );
};
