// Handles creation of Stripe Payment Intents for checkout sessions.
import <PERSON><PERSON> from "stripe";
import { CheckoutSessionStatus, Prisma } from "@prisma/client";
import ApiError from "@/utils/ApiError";
import { prisma } from "@/lib/prisma";
import { StripeBaseService } from "./stripe-base.service"; // Import base service
import { StripePaymentIntentRequest, StripePaymentIntentResponse } from "../types/stripe-payments.types";
import { SupportedCurrency } from "../../../common/types/payment-common.types";

export class StripePaymentIntentService { // RENAME CLASS

    /**
     * Creates a Stripe PaymentIntent based on an application checkout session.
     * This service method focuses SOLELY on creating the intent.
     * Webhook simulation is handled in the controller (for dev).
     */
    static async createPaymentIntentFromCheckoutSession(
        sessionId: string,
        userId: string,
        options: Partial<StripePaymentIntentRequest> = {}
    ): Promise<StripePaymentIntentResponse> {
        const stripe = StripeBaseService.getStripeClient(); // Get initialized client
        try {
            const checkoutSession = await prisma.checkoutSession.findUnique({
                where: { id: sessionId },
                include: { user: { select: { email: true } } },
            });

            if (!checkoutSession) throw new ApiError(404, `Checkout session not found: ${sessionId}`);
            if (checkoutSession.userId !== userId) throw new ApiError(403, "Forbidden access to checkout session");
            if (checkoutSession.status !== CheckoutSessionStatus.RESERVED) { // Use Prisma enum
                throw new ApiError(400, `Cannot process payment for status: ${checkoutSession.status}`);
            }

            console.log(`💰 StripePaymentIntentService: Creating payment intent for session: ${sessionId}`);
            const customerEmail = checkoutSession.user?.email;
            if (!customerEmail) throw new ApiError(400, "User email required for payment");

            // Use Base Service for customer handling
            const stripeCustomerId = await StripeBaseService.getOrCreateStripeCustomer(userId, customerEmail);

            // Use checkout session total (converted to cents)
            const amountInCents = Math.round(checkoutSession.total * 100);

            const paymentIntentParams: Stripe.PaymentIntentCreateParams = {
                amount: amountInCents,
                currency: (checkoutSession.currency || "USD").toLowerCase(),
                customer: stripeCustomerId,
                setup_future_usage: options.setupFutureUsage || "on_session",
                payment_method_types: options.paymentMethodTypes || ["card"],
                metadata: {
                    userId,
                    checkoutSessionId: sessionId,
                    ...(options.metadata || {}),
                },
                receipt_email: options.receiptEmail || customerEmail,
                description: options.description || `Purchase for Event ID: ${checkoutSession.eventId}`,
            };
            if (options.statementDescriptor) paymentIntentParams.statement_descriptor = options.statementDescriptor.substring(0, 22);
            if (options.shipping) paymentIntentParams.shipping = options.shipping;

            // Consider adding idempotency key in production here if needed
            // paymentIntentParams.idempotencyKey = ...;

            const paymentIntent = await stripe.paymentIntents.create(paymentIntentParams);

            // Update the checkout session with the payment intent ID (crucial step)
            await prisma.checkoutSession.update({
                where: { id: sessionId },
                data: { paymentIntentId: paymentIntent.id },
            });
            console.log(`✅ StripePaymentIntentService: Created payment intent ${paymentIntent.id} for session ${sessionId}`);

            // Return details needed by the client
            return {
                success: true,
                clientSecret: paymentIntent.client_secret || undefined,
                paymentIntentId: paymentIntent.id,
                amount: paymentIntent.amount,
                currency: paymentIntent.currency.toUpperCase() as SupportedCurrency,
                status: paymentIntent.status,
            };
        } catch (error) {
            console.error(`❌ StripePaymentIntentService: Error creating PI for session ${sessionId}:`, error);
            // Ensure errors are thrown or returned consistently
            if (error instanceof ApiError) throw error;
             if (error instanceof Stripe.errors.StripeError) {
                 // Throwing Stripe errors directly is fine, controller can catch them
                  throw new ApiError(400, `Stripe Error: ${error.message} (Code: ${error.code})`);
             }
            throw new ApiError(500, `Failed to create payment intent: ${error instanceof Error ? error.message : "Unknown server error"}`);
        }
    }

     /**
      * Helper method to retrieve a PaymentIntent from Stripe.
      * Useful for the controller's dev simulation.
      */
     static async retrievePaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent> {
         const stripe = StripeBaseService.getStripeClient();
         try {
             return await stripe.paymentIntents.retrieve(paymentIntentId);
         } catch (error) {
             console.error(`❌ StripePaymentIntentService: Error retrieving PI ${paymentIntentId}:`, error);
             if (error instanceof Stripe.errors.StripeError) {
                 throw new ApiError(404, `Stripe Error retrieving PI: ${error.message} (Code: ${error.code})`);
             }
             throw new ApiError(500, `Failed to retrieve Payment Intent: ${error instanceof Error ? error.message : "Unknown"}`);
         }
     }
}