"use client";
import { Ticket } from "lucide-react";
import { Bento<PERSON>ox } from "../../../modules/shared/widgets/BentoBox";
import { MetricCard } from "../../../modules/shared/stats/MetricCard";

export const TicketStatusBento = () => {
  return (
    <BentoBox
      title="My Tickets"
      className="col-span-1 row-span-1"
      header={<Ticket className="h-5 w-5 text-primary" />}
    >
      <div className="space-y-4">
        <MetricCard
          title="Active Tickets"
          value="3"
          trend={{ value: 2, isPositive: true }}
        />
        <div className="divide-y">
          {/* Recent Ticket List */}
          {[1, 2].map((index) => (
            <div key={index} className="py-2">
              <p className="text-sm font-medium">Ticket #{index}</p>
              <p className="text-xs text-muted-foreground">
                Valid until Dec 2024
              </p>
            </div>
          ))}
        </div>
      </div>
    </BentoBox>
  );
};
