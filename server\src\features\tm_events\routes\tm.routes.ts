// Routes for TM Events
import { Router } from 'express';
import { TmEventController } from '../controllers/tm.controller';
import { rateLimitMiddleware } from '@/middleware/rateLimit.middleware';

const router = Router();

// Get events with rate limiting
router.get('/', rateLimitMiddleware, TmEventController.getEvents);

// Invalidate cache
router.delete('/cache', TmEventController.invalidateCache)

export default router;
