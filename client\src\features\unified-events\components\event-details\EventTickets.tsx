import React, { useMemo } from "react";
import { UnifiedEvent } from "../../adapters/eventAdapter";
import { InventoryItem } from "@/features/event-listing/types/eventListing";
import { TicketIcon, ExternalLink, InfoIcon, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { InventoryListingItem } from "../inventory/InventoryListingItem";

interface EventTicketsProps {
  event: UnifiedEvent;
  selections: Record<string, number>;
  onQuantityChange: (itemId: string, quantity: number) => void;
}

export const EventTickets: React.FC<EventTicketsProps> = ({ 
  event, 
  selections, 
  onQuantityChange 
}) => {
  // Get inventory data for manager events
  const managerInventory: InventoryItem[] = useMemo(() =>
    (event?.source === 'manager' && Array.isArray(event.originalEvent?.inventory))
      ? event.originalEvent.inventory
      : [],
    [event]
  );
  const hasManagerInventory = managerInventory.length > 0;

  return (
    <div>
      <h3 className="text-base lg:text-lg font-semibold flex items-center border-b pb-2 mb-4">
        <TicketIcon className="h-5 w-5 mr-2 text-primary" /> Available Tickets
      </h3>

      {/* Manager Inventory Display */}
      {event.source === 'manager' && (
        hasManagerInventory ? (
          <div className="space-y-4">
            {managerInventory.map((invItem) => (
              <InventoryListingItem
                key={invItem.id || `inv-${invItem.section}-${invItem.row}`}
                item={invItem}
                onQuantityChange={onQuantityChange}
                initialQuantity={selections[invItem.id] || 0}
              />
            ))}
          </div>
        ) : (
          <div className="text-sm text-center text-muted-foreground p-6 bg-muted/20 border rounded-lg flex flex-col items-center justify-center min-h-[150px]">
            <TicketIcon className="h-10 w-10 mb-3 text-muted-foreground/50" />
            <p className="font-medium">No ticket listings found.</p>
            <p className="text-xs">Listings from this seller are not currently available.</p>
          </div>
        )
      )}

      {/* External Event Information Display */}
      {event.source === 'ticketmaster' && (
        <div className="text-sm space-y-4 bg-blue-50/50 p-4 rounded-lg border border-blue-200">
          <div className="flex items-start space-x-3">
            <div className="bg-blue-100 p-2 rounded-full mt-1">
              <InfoIcon className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h4 className="font-semibold text-base text-blue-900 mb-1">No Tickets Available</h4>
              <p className="text-blue-800/90">
                This is an external event. No tickets are currently available through our platform.
              </p>
              {event.priceRange && (
                <p className="mt-2 text-xs">
                  <span className="text-blue-700/80">Est. Price Range:</span>{' '}
                  <span className="font-semibold text-blue-800">{event.priceRange.currency || '$'}{event.priceRange.min} - {event.priceRange.max}</span>
                </p>
              )}
              <p className="mt-3 text-sm font-medium text-blue-700">
                If you&apos;re interested in selling tickets for events like this, join our platform as a manager and start selling your tickets today!
              </p>
            </div>
          </div>
          
       
        </div>
      )}
    </div>
  );
};
