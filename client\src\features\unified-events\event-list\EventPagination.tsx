import React from "react";
import {
  Pa<PERSON><PERSON>,
  Pa<PERSON>ationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

interface EventPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export const EventPagination: React.FC<EventPaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange
}) => {
  // Force totalPages to be at least 3 if we have enough events
  const effectiveTotalPages = Math.max(totalPages, 1);
  
  // Pagination rendering helper
  const renderPaginationItems = () => {
    const items = [];
    
    // Show max 5 pages with current page in the middle when possible
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(effectiveTotalPages, startPage + 4);
    
    // Adjust start if we're near the end
    if (endPage - startPage < 4 && effectiveTotalPages > 5) {
      startPage = Math.max(1, endPage - 4);
    }
    
    // Add first page and ellipsis if needed
    if (startPage > 1) {
      items.push(
        <PaginationItem key="first">
          <PaginationLink
            href="#"
            onClick={(e) => {
              e.preventDefault();
              onPageChange(1);
            }}
          >
            1
          </PaginationLink>
        </PaginationItem>
      );
      
      if (startPage > 2) {
        items.push(
          <PaginationItem key="start-ellipsis">
            <PaginationEllipsis />
          </PaginationItem>
        );
      }
    }
    
    // Add page numbers
    for (let i = startPage; i <= endPage; i++) {
      items.push(
        <PaginationItem key={i}>
          <PaginationLink
            href="#"
            onClick={(e) => {
              e.preventDefault();
              onPageChange(i);
            }}
            isActive={i === currentPage}
          >
            {i}
          </PaginationLink>
        </PaginationItem>
      );
    }
    
    // Add last page and ellipsis if needed
    if (endPage < effectiveTotalPages) {
      if (endPage < effectiveTotalPages - 1) {
        items.push(
          <PaginationItem key="end-ellipsis">
            <PaginationEllipsis />
          </PaginationItem>
        );
      }
      
      items.push(
        <PaginationItem key="last">
          <PaginationLink
            href="#"
            onClick={(e) => {
              e.preventDefault();
              onPageChange(effectiveTotalPages);
            }}
          >
            {effectiveTotalPages}
          </PaginationLink>
        </PaginationItem>
      );
    }
    
    return items;
  };

  console.log("Rendering pagination with:", { currentPage, totalPages: effectiveTotalPages });

  return (
    <div className="mt-8">
      <Pagination>
        <PaginationContent>
          <PaginationPrevious
            href="#"
            onClick={(e) => {
              e.preventDefault();
              currentPage > 1 && onPageChange(currentPage - 1);
            }}
            className={currentPage <= 1 ? "pointer-events-none opacity-50" : ""}
          />
          
          {renderPaginationItems()}
          
          <PaginationNext
            href="#"
            onClick={(e) => {
              e.preventDefault();
              currentPage < effectiveTotalPages && onPageChange(currentPage + 1);
            }}
            className={
              currentPage >= effectiveTotalPages 
                ? "pointer-events-none opacity-50" 
                : ""
            }
          />
        </PaginationContent>
      </Pagination>
    </div>
  );
};
