"use client";

import {
  Ticket,
  Mail,
  Lock,
  Eye,
  EyeOff,
  User,
  Phone,
  Facebook,
  Apple,
  Github,
  CheckCircle2,
  XCircle,
  Loader2,
} from "lucide-react";

import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import { toast } from "sonner"; //https://www.npmjs.com/package/sonner

import { useState, useEffect } from "react";

import { containerVariants, itemVariants } from "./animations";
import { PasskeyOptions } from "./PasskeyOptions";
import { checkPasswordStrength } from "./checkPasswordStrength";
import { RoleSelector } from "./RoleSelector";

import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import { usePasswordReset } from "../../hooks/usePasswordReset";
import { useAuth } from "../../hooks/useAuth";
import { OAuthProviderButton } from "../oauth/OAuthProviderButton";

// Types for props
interface AuthModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const AuthModal = ({ open, onOpenChange }: AuthModalProps) => {
  const [showPassword, setShowPassword] = useState(false);
  const [authMode, setAuthMode] = useState<"signin" | "signup">("signin");
  const [authMethod, setAuthMethod] = useState<"password" | "passkey">(
    "password"
  );
  const [selectedRole, setSelectedRole] = useState("VISITOR");
  const [fullName, setFullName] = useState("");
  const [phone, setPhone] = useState("");

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isForgotPassword, setIsForgotPassword] = useState(false);
  const [rememberEmail, setRememberEmail] = useState(false);
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState({
    score: 0,
    criteria: {
      hasUpperCase: false,
      hasLowerCase: false,
      hasNumber: false,
      hasSpecialChar: false,
      isLongEnough: false,
    },
    label: "",
    color: "",
  });

  // Auth hook
  const { login, register, socialLogin, isLoading } = useAuth();

  // Password reset hook
  const { requestReset, isLoading: isResetting } = usePasswordReset();

  // Password strength effect
  useEffect(() => {
    if (password) {
      setPasswordStrength(checkPasswordStrength(password));
    }
  }, [password]);

  // Form submission handler
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // const normalizedEmail = email.toLowerCase(); // use for dobule layer for email must be in lowercase
    //! testing
    console.log("Modal Form submission:", { authMode, email });
    try {
      if (authMode === "signin") {
        await login({ email, password });
        console.log("😊signin submission:", { authMode, email });
        toast.success("Successfully signed in!");
      } else {
        await register({
          email,
          password,
          role: selectedRole,
          fullName,
          mobile: phone, // Include phone number
        });
        toast.success("Account created successfully!");
      }
      onOpenChange(false);
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Authentication failed"
      );
    }
  };

  //forgot password handler
  const handleForgotPassword = async () => {
    if (!email) {
      toast.error("Please enter your email address");
      return;
    }

    const success = await requestReset(email);
    if (success) {
      setIsForgotPassword(false);
    }
  };

  // Social login handler
  const handleSocialLogin = async (provider: string) => {
    try {
      await socialLogin(provider);
      onOpenChange(false);
    } catch (error) {
      toast.error(`${provider} login failed`);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="font-bold text-white bg-transparent border-0 sm:max-w-md">
        {/* Logo Section */}

        <motion.div
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={containerVariants}
          className="w-full space-y-4 bg-gradient-to-br from-primary/90 via-primary/80 to-primary/90 p-6 rounded-lg shadow-xl
          overflow-y-auto max-h-[80vh] max-w-full"
        >
          <DialogTitle>
            <div className="flex gap-2 justify-center items-center">
              <Ticket className="w-8 h-8 text-emerald-300" />
              <span className="text-2xl font-bold text-orange-400">
                FanSeatmaster
              </span>
            </div>
          </DialogTitle>
          {/* Welcome Text */}
          <motion.h2
            variants={itemVariants}
            className="text-xl font-semibold text-center text-white"
          >
            {/* Only show this forgot password UI if in signin mode AND isForgotPassword is true */}
            {authMode === "signin" && isForgotPassword ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-4 space-y-4 rounded-lg bg-white/5"
              >
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-white">
                    Reset Password
                  </h3>
                  <p className="mt-2 text-sm text-blue-200">
                    Enter your email to receive reset link
                  </p>
                </div>

                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value.toLowerCase())}
                  placeholder="Enter your email"
                  className="text-white bg-white/10 border-white/20"
                  required
                />

                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    className="w-1/2 text-blue-200"
                    onClick={() => setIsForgotPassword(false)}
                    disabled={isResetting}
                  >
                    Cancel
                  </Button>
                  <Button
                    className="w-1/2 bg-blue-500 hover:bg-blue-600"
                    onClick={handleForgotPassword}
                    disabled={!email || isResetting}
                  >
                    {isResetting ? (
                      <div className="flex gap-2 items-center">
                        <Loader2 className="w-4 h-4 animate-spin" />
                        Sending...
                      </div>
                    ) : (
                      "Send Reset Link"
                    )}
                  </Button>
                </div>
              </motion.div>
            ) : (
              /* Show the normal welcome text if not in forgot password state */
              authMode === "signin" ? "Welcome Back!" : "Create Your Account"
            )}
          </motion.h2>

          {/* Auth Toggle */}
          <div className="flex justify-center p-1 space-x-2 rounded-lg bg-white/5">
            {["signin", "signup"].map((mode) => (
              <Button
                key={mode}
                onClick={() => setAuthMode(mode as "signin" | "signup")}
                variant={authMode === mode ? "default" : "ghost"}
                className={authMode === mode ? "bg-blue-500" : "text-blue-200"}
              >
                {mode === "signin" ? "Sign In" : "Sign Up"}
              </Button>
            ))}
          </div>

          {/* Form */}
          <motion.form
            onSubmit={handleSubmit}
            variants={itemVariants}
            className="space-y-4"
          >
            {/* Only show registration fields in signup mode, and when not in forgot password state */}
            {authMode === "signup" && !isForgotPassword && (
              <motion.div 
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                className="p-4 space-y-4 rounded-lg border border-white/20 bg-white/5"
              >
                <RoleSelector
                  selectedRole={selectedRole}
                  onRoleSelect={setSelectedRole}
                />

                {/* Full Name Input */}
                <div className="space-y-2">
                  <label className="flex gap-2 items-center text-sm text-blue-200">
                    <User className="w-4 h-4" /> Full Name
                  </label>
                  <Input
                    type="text"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    placeholder="Enter your full name"
                    className="text-white bg-white/10 border-white/20"
                    required
                  />
                </div>

                {/* Phone Input */}
                <div className="space-y-2">
                  <label className="flex gap-2 items-center text-sm text-blue-200">
                    <Phone className="w-4 h-4" /> Mobile Number
                  </label>
                  <PhoneInput
                    country={"in"}
                    value={phone}
                    onChange={(phone) => setPhone(phone)}
                    inputStyle={{
                      width: "100%",
                      height: "40px",
                      fontSize: "14px",
                      backgroundColor: "rgba(255, 255, 255, 0.1)",
                      border: "1px solid rgba(255, 255, 255, 0.2)",
                      color: "white",
                    }}
                    dropdownStyle={{
                      backgroundColor: "#1a1a1a",
                      color: "white",
                    }}
                    buttonStyle={{
                      backgroundColor: "rgba(255, 255, 255, 0.1)",
                      borderColor: "rgba(255, 255, 255, 0.2)",
                    }}
                  />
                </div>
              </motion.div>
            )}

            {/* ---------------------------------------------- */}

            {/* Only show these fields when not in forgot password state (already shown in forgot password UI) */}
            {(!isForgotPassword || authMode === "signup") && (
              <>
                {/* Email Field */}
                <div className="space-y-2">
                  <label className="flex gap-2 items-center text-sm text-blue-200">
                    <Mail className="w-4 h-4" /> Email
                  </label>
                  <Input
                    type="email"
                    value={email}
                    // Converts email to lowercase as user types
                    onChange={(e) => setEmail(e.target.value.toLowerCase())}
                    placeholder="Enter your email"
                    className="text-white bg-white/10 border-white/20"
                    required
                  />
                </div>
                {/* Auth Method Selection */}
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-blue-200">
                      Authentication method:
                    </span>
                    <div className="flex flex-row space-x-2">
                      {["password", "passkey"].map((method) => (
                        <Button
                          key={method}
                          type="button"
                          onClick={() =>
                            setAuthMethod(method as "password" | "passkey")
                          }
                          variant={authMethod === method ? "default" : "ghost"}
                          className={
                            authMethod === method ? "bg-blue-500" : "text-blue-200"
                          }
                          disabled={method === "passkey"} //! Disable the passkey button
                        >
                          {method.charAt(0).toUpperCase() + method.slice(1)}
                        </Button>
                      ))}
                    </div>
                  </div>

                  <AnimatePresence mode="wait">
                    {authMethod === "passkey" ? (
                      <PasskeyOptions />
                    ) : (
                      <motion.div
                        key="password"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="space-y-2"
                      >
                        {/* Password Field */}
                        <label className="flex gap-2 items-center text-sm text-blue-200">
                          <Lock className="w-4 h-4" /> Password
                        </label>
                        <div className="relative">
                          <Input
                            type={showPassword ? "text" : "password"}
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            placeholder="Enter password"
                            className="pr-10 text-white bg-white/10 border-white/20"
                          />
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 -translate-y-1/2"
                          >
                            {showPassword ? (
                              <EyeOff className="w-4 h-4 text-blue-200" />
                            ) : (
                              <Eye className="w-4 h-4 text-blue-200" />
                            )}
                          </button>
                        </div>

                        {/* Password Strength Indicator */}
                        {authMode === "signup" &&
                          password &&
                          passwordStrength.score < 4 && (
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: "auto" }}
                              exit={{ opacity: 0, height: 0 }}
                              className="p-3 mt-2 space-y-2 rounded-lg bg-white/5"
                            >
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-blue-200">
                                  Password Strength:
                                </span>
                                <span
                                  className={`text-sm font-medium text-${passwordStrength.color}-400`}
                                >
                                  {passwordStrength.label}
                                </span>
                              </div>
                              {/* Password strength criteria */}
                              <div className="space-y-1">
                                {Object.entries(passwordStrength.criteria).map(
                                  ([key, met]) => (
                                    <div
                                      key={key}
                                      className="flex gap-2 items-center"
                                    >
                                      {met ? (
                                        <CheckCircle2 className="w-4 h-4 text-green-400" />
                                      ) : (
                                        <XCircle className="w-4 h-4 text-red-400" />
                                      )}
                                      <span className="text-xs text-blue-200">
                                        {key === "hasUpperCase" &&
                                          "Uppercase letter"}
                                        {key === "hasLowerCase" &&
                                          "Lowercase letter"}
                                        {key === "hasNumber" && "Number"}
                                        {key === "hasSpecialChar" &&
                                          "Special character"}
                                        {key === "isLongEnough" &&
                                          "Minimum 8 characters"}
                                      </span>
                                    </div>
                                  )
                                )}
                              </div>
                            </motion.div>
                          )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </>
            )}

            {/* Remember Me & Forgot Password */}
            {/* <div className="flex justify-between items-center">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="remember"
                  checked={rememberEmail}
                  onCheckedChange={(checked) =>
                    setRememberEmail(checked as boolean)
                  }
                />
                <label htmlFor="remember" className="text-sm text-blue-200">
                  Remember me
                </label>
              </div>
              {authMode === "signin" && (
                <Button
                  variant="link"
                  className="text-sm text-blue-400 hover:text-blue-300"
                >
                  Forgot password?
                </Button>
              )}
            </div> */}

            {/* //!------------ passoword reset--end---------- */}

            {/* ----------------------- */}

            {/* Existing Forgot Password Link - Only show when in signin mode and not already in forgot password view */}
            {authMode === "signin" && !isForgotPassword && (
              <Button
                type="button"
                variant="link"
                className="text-sm text-blue-400 hover:text-blue-300"
                onClick={() => setIsForgotPassword(true)}
              >
                Forgot password?
              </Button>
            )}

            {/* Terms and Conditions */}
            {authMode === "signup" && !isForgotPassword && (
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="terms"
                  checked={acceptTerms}
                  onCheckedChange={(checked) =>
                    setAcceptTerms(checked as boolean)
                  }
                />
                <label htmlFor="terms" className="text-sm text-blue-200">
                  I accept the{" "}
                  <Button
                    type="button"
                    variant="link"
                    className="p-0 text-blue-400 hover:text-blue-300"
                  >
                    Terms and Conditions
                  </Button>
                </label>
              </div>
            )}

            {/* Submit Button - Only show when not in forgot password mode (signin) or always show in signup */}
            {(!isForgotPassword || authMode === "signup") && (
              <Button
                type="submit"
                className="relative w-full bg-blue-500 hover:bg-blue-600 transition-all duration-300"
                disabled={isLoading || (authMode === "signup" && !acceptTerms)}
              >
                {isLoading ? (
                  <div className="flex gap-2 items-center">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    {authMode === "signin"
                      ? "Signing in..."
                      : "Creating account..."}
                  </div>
                ) : (
                  <span>
                    {authMode === "signin" ? "Sign In" : "Create Account"}
                  </span>
                )}
              </Button>
            )}

            {/* Social Login Divider */}
            {/* <div className="relative">
              <div className="flex absolute inset-0 items-center">
                <div className="w-full border-t border-blue-400/30" />
              </div>
              <div className="flex relative justify-center text-sm">
                <span className="px-2 text-blue-200 bg-blue-900">
                  or continue with
                </span>
              </div>
            </div> */}

            {/* Social Login Buttons */}
            {/* <div className="grid grid-cols-3 gap-3">
              {[
                { icon: Facebook, provider: "facebook" },
                { icon: Apple, provider: "apple" },
                { icon: Github, provider: "github" },
              ].map(({ icon: Icon, provider }) => (
                <Button
                  key={provider}
                  type="button"
                  onClick={() => handleSocialLogin(provider)}
                  variant="outline"
                  className="relative bg-white/5 border-blue-400/30 hover:bg-white/10"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <Loader2 className="w-5 h-5 text-blue-300 animate-spin" />
                  ) : (
                    <Icon className="w-5 h-5 text-blue-300" />
                  )}
                </Button>
              ))}
            </div> */}

            {/* <div className="grid grid-cols-3 gap-3">
              <OAuthProviderButton
                provider="google"
                className="relative bg-white/5 border-blue-400/30 hover:bg-white/10"
                onSuccess={() => {
                  toast.success("Successfully signed in!");
                  onOpenChange(false);
                }}
                onError={(error) => {
                  toast.error(error.message || "Failed to sign in");
                }}
              />
              <OAuthProviderButton
                provider="github"
                className="relative bg-white/5 border-blue-400/30 hover:bg-white/10"
                onSuccess={() => {
                  toast.success("Successfully signed in!");
                  onOpenChange(false);
                }}
                onError={(error) => {
                  toast.error(error.message || "Failed to sign in");
                }}
              />
              <OAuthProviderButton
              provider="apple"  
              className="relative bg-white/5 border-blue-400/30 hover:bg-white/10"
              onSuccess={() => {
                toast.success("Successfully signed in!");
                onOpenChange(false);
              }}
              onError={(error) => {
                toast.error(error.message || "Failed to sign in");
              }}
              />
            </div> */}

            {/* Loading State */}
            {isLoading && (
              <div className="text-center text-blue-200">
                <span className="animate-pulse">Authenticating...</span>
              </div>
            )}

            {/* Error Messages */}
            {/* {(loginError || registerError) && (
              <div className="text-sm text-center text-red-400">
                {loginError?.message || registerError?.message}
              </div>
            )} */}
          </motion.form>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
};