"use client";
import { CalendarCheck, AlertCircle } from "lucide-react";
import { BentoBox } from "../../../shared/widgets/BentoBox";
import { Progress } from "@/components/ui/progress";

export const EventMonitoringBento = () => {
  const events = [
    {
      id: "1",
      name: "Summer Festival 2024",
      manager: "<PERSON>",
      status: "live",
      totalTickets: 1000,
      soldTickets: 750,
      revenue: 37500,
      alerts: 0,
    },
    {
      id: "2",
      name: "Tech Conference",
      manager: "<PERSON>",
      status: "upcoming",
      totalTickets: 500,
      soldTickets: 200,
      revenue: 10000,
      alerts: 2,
    },
    {
      id: "3",
      name: "Sports Championship",
      manager: "<PERSON>",
      status: "live",
      totalTickets: 2000,
      soldTickets: 1800,
      revenue: 90000,
      alerts: 1,
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "live":
        return "text-green-600 bg-green-100";
      case "upcoming":
        return "text-blue-600 bg-blue-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  return (
    <BentoBox
      title="Event Monitoring"
      className="col-span-2 row-span-2"
      header={<CalendarCheck className="h-5 w-5 text-primary" />}
    >
      <div className="space-y-4 mt-4">
        {events.map((event) => (
          <div
            key={event.id}
            className="p-4 rounded-lg bg-accent/50 hover:bg-accent/70 transition-colors"
          >
            <div className="flex justify-between items-start mb-3">
              <div>
                <div className="flex items-center gap-2">
                  <h4 className="font-medium">{event.name}</h4>
                  {event.alerts > 0 && (
                    <div className="flex items-center text-amber-600">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      <span className="text-xs">{event.alerts} alerts</span>
                    </div>
                  )}
                </div>
                <p className="text-sm text-muted-foreground">
                  Managed by: {event.manager}
                </p>
              </div>
              <span
                className={`px-2 py-1 rounded-full text-xs ${getStatusColor(
                  event.status
                )}`}
              >
                {event.status}
              </span>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Ticket Sales</span>
                <span>
                  {event.soldTickets}/{event.totalTickets}
                </span>
              </div>
              <Progress
                value={(event.soldTickets / event.totalTickets) * 100}
                className="h-2"
              />
              <div className="flex justify-between text-sm mt-2">
                <span className="text-muted-foreground">Revenue</span>
                <span className="font-medium">
                  ${event.revenue.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </BentoBox>
  );
};
