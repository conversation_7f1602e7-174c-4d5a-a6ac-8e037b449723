// Controller for handling search requests
// This controller validates incoming requests and uses search service
import { Request, Response, NextFunction } from 'express';
import { SearchService } from '../services/searchService';
import { asyncHandler } from '@/utils/asyncHandler';
// import { OpenCTXRequest } from '@/lib/openctx/types';
// import { ParsedQs } from 'qs';

// Controller for handling search requests
export class SearchController {
  // Asynchronous handler for search operation
  static search = asyncHandler(async (req: Request, res: Response) => {
    // Perform search using SearchService
    const results = await SearchService.search({
      query: req.query as any,
      userRole: req.user?.role
    });

    // Return data in consistent format
    res.status(200).json({
      success: true,
      message: 'Search results fetched successfully',
      data: results // Array of Context objects
    });
  });
}
