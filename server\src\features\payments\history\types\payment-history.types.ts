/**
 * Payment history related types
 */
import { PaymentProcessor, PaymentRecordStatus } from '@prisma/client';

/**
 * Payment record summary for client display
 */
export interface PaymentHistorySummary {
  id: string;
  amount: number;
  currency: string;
  status: PaymentRecordStatus;
  processor: PaymentProcessor;
  processedAt: string; // ISO date string
  description: string | null;
  paymentMethodDetails: string | null;
  transactionId: string; // processorPaymentId
  receiptUrl?: string;
  refundedAmount?: number;
  refundedAt?: string; // ISO date string
}

/**
 * Response for listing user payment history
 */
export interface PaymentHistoryResponse {
  success: boolean;
  data: {
    payments: PaymentHistorySummary[];
    total: number;
  };
  error?: string;
}

/**
 * Request parameters for listing payment history
 */
export interface ListPaymentHistoryRequest {
  page?: number;
  limit?: number;
  sortBy?: 'processedAt' | 'amount';
  sortDirection?: 'asc' | 'desc';
  status?: PaymentRecordStatus;
}