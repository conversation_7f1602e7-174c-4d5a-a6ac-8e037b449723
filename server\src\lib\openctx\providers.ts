// Defines the providers for fetching data using the adapters
// Providers fetch the data and returns it back

import { databaseAdapter } from './adapters/databaseAdapter';
import { tmApiAdapter } from './adapters/tmApiAdapter';
import { OpenCTXRequest, Provider } from './types';

// Provider for fetching data from the database
export const databaseProvider: Provider<any> = {
  fetch: async (query: OpenCTXRequest) => {
    return await databaseAdapter.fetch(query);
  },
};

// Provider for fetching data from the Ticketmaster API
export const tmApiProvider: Provider<any> = {
  fetch: async (query: OpenCTXRequest) => {
      return await tmApiAdapter.fetch(query);
  },
};
