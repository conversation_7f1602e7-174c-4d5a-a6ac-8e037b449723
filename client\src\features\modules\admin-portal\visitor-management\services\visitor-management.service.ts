import { Visitor, VisitorFilters } from "../types/visitor-management.types";

const dummyVisitors: Visitor[] = [
  {
    id: "1",
    fullName: "<PERSON>",
    email: "<EMAIL>",
    mobile: "+1234567890",
    joinDate: "2023-12-01",
    lastActive: "2024-01-15",
    status: "active",
    totalBookings: 15,
    totalSpent: 2500,
    location: "New York, USA",
    avatarUrl: "https://api.dicebear.com/7.x/avataaars/svg?seed=John"
  },
  {
    id: "2",
    fullName: "<PERSON>",
    email: "<EMAIL>",
    mobile: "+1234567891",
    joinDate: "2023-11-15",
    lastActive: "2024-01-14",
    status: "active",
    totalBookings: 8,
    totalSpent: 1200,
    location: "London, UK",
    avatarUrl: "https://api.dicebear.com/7.x/avataaars/svg?seed=Alice"
  },
  {
    id: "3",
    fullName: "<PERSON>",
    email: "<EMAIL>",
    mobile: "+1234567892",
    joinDate: "2023-10-20",
    lastActive: "2024-01-10",
    status: "inactive",
    totalBookings: 3,
    totalSpent: 450,
    location: "Toronto, Canada",
    avatarUrl: "https://api.dicebear.com/7.x/avataaars/svg?seed=Bob"
  },
  {
    id: "4",
    fullName: "Emma Davis",
    email: "<EMAIL>",
    mobile: "+1234567893",
    joinDate: "2023-12-15",
    lastActive: "2024-01-15",
    status: "suspended",
    totalBookings: 1,
    totalSpent: 150,
    location: "Sydney, Australia",
    avatarUrl: "https://api.dicebear.com/7.x/avataaars/svg?seed=Emma"
  }
];

export const visitorService = {
  async getVisitors(filters: VisitorFilters): Promise<Visitor[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    let filteredVisitors = [...dummyVisitors];

    // Apply filters
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filteredVisitors = filteredVisitors.filter(visitor =>
        visitor.fullName.toLowerCase().includes(searchLower) ||
        visitor.email.toLowerCase().includes(searchLower)
      );
    }

    if (filters.status !== 'all') {
      filteredVisitors = filteredVisitors.filter(visitor =>
        visitor.status === filters.status
      );
    }

    return filteredVisitors;
  }
};
